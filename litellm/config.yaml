model_list:
  - model_name: us-claude-sonnet-4
    litellm_params:
      model: bedrock/us.anthropic.claude-sonnet-4-20250514-v1:0
      aws_access_key_id: os.environ/AWS_ACCESS_KEY_ID
      aws_secret_access_key: os.environ/AWS_SECRET_ACCESS_KEY
      aws_region_name: "us-east-1"
      extra_headers:
        {
          "anthropic-beta": "fine-grained-tool-streaming-2025-05-14,token-efficient-tools-2025-02-19,extended-cache-ttl-2025-04-11",
        }
    temperature: 0.1
    max_tokens: 8192
    reasoning_effort: "low"

  # - model_name: anthropic-sonnet-4
  #   litellm_params:
  #     model: claude-sonnet-4-20250514
  #     api_key: os.environ/ANTHROPIC_API_KEY
  #     extra_headers:
  #       {
  #         "anthropic-beta": "fine-grained-tool-streaming-2025-05-14,token-efficient-tools-2025-02-19,extended-cache-ttl-2025-04-11",
  #       }
  #   temperature: 0.1
  #   max_tokens: 8192
  #   reasoning_effort: "low"

  - model_name: apac-claude-sonnet-4
    litellm_params:
      model: bedrock/apac.anthropic.claude-sonnet-4-20250514-v1:0
      aws_access_key_id: os.environ/AWS_ACCESS_KEY_ID
      aws_secret_access_key: os.environ/AWS_SECRET_ACCESS_KEY
      aws_region_name: "ap-southeast-1"
      extra_headers:
        {
          "anthropic-beta": "fine-grained-tool-streaming-2025-05-14,token-efficient-tools-2025-02-19,extended-cache-ttl-2025-04-11",
        }
    temperature: 0.1
    max_tokens: 8192
    reasoning_effort: "low"

  - model_name: apac-claude-sonnet-3.5-v2
    litellm_params:
      model: bedrock/apac.anthropic.claude-3-5-sonnet-20241022-v2:0
      aws_access_key_id: os.environ/AWS_ACCESS_KEY_ID
      aws_secret_access_key: os.environ/AWS_SECRET_ACCESS_KEY
      aws_region_name: "ap-southeast-1"
    temperature: 0.1
    max_tokens: 8192

  - model_name: us-claude-haiku-3.5
    litellm_params:
      model: bedrock/us.anthropic.claude-3-5-haiku-20241022-v1:0
      aws_access_key_id: os.environ/AWS_ACCESS_KEY_ID
      aws_secret_access_key: os.environ/AWS_SECRET_ACCESS_KEY
      aws_region_name: "us-east-1"
    temperature: 0.1
    max_tokens: 8192

  - model_name: apac-nova-lite
    litellm_params:
      model: bedrock/apac.amazon.nova-lite-v1:0
      aws_access_key_id: os.environ/AWS_ACCESS_KEY_ID
      aws_secret_access_key: os.environ/AWS_SECRET_ACCESS_KEY
      aws_region_name: "ap-southeast-1"
    temperature: 0.1
    max_tokens: 8192

  # # Text only model
  # - model_name: bedrock-gpt-oss-120b
  #   litellm_params:
  #     model: bedrock/openai.gpt-oss-120b-1:0
  #     aws_access_key_id: os.environ/AWS_ACCESS_KEY_ID
  #     aws_secret_access_key: os.environ/AWS_SECRET_ACCESS_KEY
  #     aws_region_name: "us-west-2" # Only available in us-west-2 (Oregon)
  #   temperature: 0.1
  #   max_tokens: 8192

  # # Text only model
  # - model_name: bedrock-gpt-oss-20b
  #   litellm_params:
  #     model: bedrock/openai.gpt-oss-20b-1:0
  #     aws_access_key_id: os.environ/AWS_ACCESS_KEY_ID
  #     aws_secret_access_key: os.environ/AWS_SECRET_ACCESS_KEY
  #     aws_region_name: "us-west-2" # Only available in us-west-2 (Oregon)
  #   temperature: 0.1
  #   max_tokens: 8192

  # - model_name: openrouter-qwen3-coder
  #   litellm_params:
  #     model: openrouter/qwen/qwen3-coder:cerebras/fp8
  #     api_key: os.environ/OPENROUTER_API_KEY
  #     base_url: https://openrouter.ai/api/v1
  #     supports_assistant_prefill: true
  #     supports_response_schema: true
  #     supports_function_calling: true
  #     stream: true
  #     supports_streaming: true
  #   temperature: 0.1
  #   max_tokens: 8192

  - model_name: apac-cohere-embed-multilingual-v3
    litellm_params:
      model: bedrock/cohere.embed-multilingual-v3
      aws_access_key_id: os.environ/AWS_ACCESS_KEY_ID
      aws_secret_access_key: os.environ/AWS_SECRET_ACCESS_KEY
      aws_region_name: "ap-southeast-1"

  - model_name: us-cohere-embed-multilingual-v3
    litellm_params:
      model: bedrock/cohere.embed-multilingual-v3
      aws_access_key_id: os.environ/AWS_ACCESS_KEY_ID
      aws_secret_access_key: os.environ/AWS_SECRET_ACCESS_KEY
      aws_region_name: "us-east-1"

router_settings:
  allowed_fails: 3
  retry_policy:
    {
      "AuthenticationErrorRetries": 3,
      "TimeoutErrorRetries": 3,
      "RateLimitErrorRetries": 3,
      "ContentPolicyViolationErrorRetries": 3,
      "InternalServerErrorRetries": 3,
    }
  fallbacks: [
      {
        "us-claude-sonnet-4":
          ["apac-claude-sonnet-4", "apac-claude-sonnet-3.5-v2"],
      },
      { "apac-claude-sonnet-4": ["apac-claude-sonnet-3.5-v2"] },
      { "us-claude-haiku-3.5": ["apac-nova-lite"] },
      # { "openrouter-qwen3-coder": ["us-claude-haiku-3.5", "apac-nova-lite"] },
      {
        "apac-cohere-embed-multilingual-v3":
          ["us-cohere-embed-multilingual-v3"],
      },
    ]

litellm_settings:
  request_timeout: 600
  cache: False
  # Enable usage tracking - critical for token usage reporting
  enable_usage_tracking: true
  # callbacks: ["langfuse_otel"]
  # NOTE: We don't log from litellm anymore, we log from the langgraph
  # success_callback: ["langfuse"]
  # failure_callback: ["langfuse"]

general_settings:
  background_health_checks: True
  health_check_interval: 300
