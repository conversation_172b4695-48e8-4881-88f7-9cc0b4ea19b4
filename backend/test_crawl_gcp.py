#!/usr/bin/env python3
"""
Simple test script for GCP Compute Engine crawler
Uses gcloud to get credentials and project information
"""

import subprocess
import sys
import uuid
from pathlib import Path

from rich import print

# Add the backend directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from app.logger import logger
from app.modules.resource_crawlers.gcp.cloud_functions import CloudFunctionsCrawler
from app.modules.resource_crawlers.gcp.cloud_storage import CloudStorageCrawler
from app.modules.resource_crawlers.gcp.compute_engine import ComputeEngineCrawler


def run_gcloud_command(command: list[str]) -> str:
    """Run a gcloud command and return the output"""
    try:
        result = subprocess.run(command, capture_output=True, text=True, check=True)
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        print(f"Error running gcloud command: {e}")
        print(f"stderr: {e.stderr}")
        sys.exit(1)


def get_gcp_project_id() -> str:
    """Get the current GCP project ID using gcloud"""
    print("Getting GCP project ID...")
    project_id = run_gcloud_command(["gcloud", "config", "get-value", "project"])
    print(f"Project ID: {project_id}")
    return project_id


def check_gcloud_auth() -> bool:
    """Check if gcloud is authenticated"""
    print("Checking gcloud authentication...")
    try:
        # Try to get current account
        account = run_gcloud_command(
            [
                "gcloud",
                "auth",
                "list",
                "--filter=status:ACTIVE",
                "--format=value(account)",
            ]
        )
        if account:
            print(f"Authenticated as: {account}")
            return True
        else:
            print("No active gcloud authentication found")
            return False
    except Exception as e:
        print(f"Error checking authentication: {e}")
        return False


def get_service_account_key() -> str | None:
    """Get service account key from existing key.json file"""
    print("Looking for existing service account key...")

    key_file = Path("key.json")
    if key_file.exists():
        print(f"Found existing key file: {key_file}")
        try:
            with open(key_file) as f:
                service_account_key = f.read()
            print("✅ Successfully loaded service account key from key.json")
            return service_account_key
        except Exception as e:
            print(f"❌ Error reading key.json: {e}")
            return None
    else:
        print("❌ key.json not found in current directory")
        print("Please ensure key.json exists in the backend directory")
        return None


def test_gcp_crawler():
    """Test the GCP Compute Engine crawler"""
    print("\n" + "=" * 50)
    print("Testing GCP Compute Engine Crawler")
    print("=" * 50)

    # Check authentication
    if not check_gcloud_auth():
        print("Please authenticate with gcloud first:")
        print("  gcloud auth login")
        print("  gcloud auth application-default login")
        return

    # Get project ID
    project_id = get_gcp_project_id()

    # Get service account key from existing file
    service_account_key = get_service_account_key()
    if not service_account_key:
        print("❌ Cannot proceed without service account key")
        return

    # Create a test workspace ID
    workspace_id = uuid.uuid4()

    print("\nInitializing GCP crawler...")
    print(f"Workspace ID: {workspace_id}")
    print(f"Project ID: {project_id}")
    print("Using service account key: Yes (from key.json)")

    try:
        # Initialize the crawler
        crawler = ComputeEngineCrawler(
            workspace_id=workspace_id,
            project_id=project_id,
            service_account_key=service_account_key,
            region="us-central1",  # Default region
            max_retries=3,
            retry_delay=5,
        )

        print("✅ GCP crawler initialized successfully")

        # Test credential validation
        print("\nTesting credential validation...")
        if crawler.validate_credentials():
            print("✅ GCP credentials are valid")
        else:
            print("❌ GCP credentials validation failed")
            return

        # Test crawling (without database)
        print("\nTesting Compute Engine instance discovery...")
        instances = crawler._crawl_compute_instances()

        print(f"✅ Found {len(instances)} Compute Engine instances")

        if instances:
            print("\nSample instance data:")
            sample_instance = instances[0]
            print(f"  Name: {sample_instance.name}")
            print(f"  Zone: {sample_instance.zone}")
            print(f"  Status: {sample_instance.status}")
            print(f"  Machine Type: {sample_instance.machine_type}")

            # Test resource mapping
            print("\nTesting resource mapping...")
            resource = crawler._map_compute_to_resource(sample_instance)
            print("✅ Successfully mapped to Resource model:")
            print(f"  Resource ID: {resource.resource_id}")
            print(f"  Name: {resource.name}")
            print(f"  Type: {resource.type}")
            print(f"  Status: {resource.status}")
            print(f"  Region: {resource.region}")
        else:
            print("ℹ️  No Compute Engine instances found in this project")

        print("\n" + "=" * 50)
        print("✅ GCP Compute Engine crawler test completed successfully!")
        print("=" * 50)

    except Exception as e:
        print(f"\n❌ Error testing GCP crawler: {e}")
        logger.exception("GCP crawler test failed")
        return


def test_credential_validation():
    """Test credential validation separately"""
    print("\n" + "=" * 50)
    print("Testing GCP Credential Validation")
    print("=" * 50)

    project_id = get_gcp_project_id()
    service_account_key = get_service_account_key()

    if not service_account_key:
        print("❌ Cannot test without service account key")
        return

    workspace_id = uuid.uuid4()

    try:
        # Test with ComputeEngineCrawler (which implements the abstract method)
        crawler = ComputeEngineCrawler(
            workspace_id=workspace_id,
            project_id=project_id,
            service_account_key=service_account_key,
            region="us-central1",
        )

        print("✅ GCP crawler initialized successfully")
        print(f"Provider name: {crawler.get_provider_name()}")

        # Test credential validation
        if crawler.validate_credentials():
            print("✅ GCP credentials are valid")
        else:
            print("❌ GCP credentials validation failed")

    except Exception as e:
        print(f"❌ Error testing credential validation: {e}")
        logger.exception("Credential validation test failed")


def test_cloud_storage_crawler():
    """Test the GCP Cloud Storage crawler"""
    print("\n" + "=" * 50)
    print("Testing GCP Cloud Storage Crawler")
    print("=" * 50)

    # Check authentication
    if not check_gcloud_auth():
        print("Please authenticate with gcloud first:")
        print("  gcloud auth login")
        print("  gcloud auth application-default login")
        return

    # Get project ID
    project_id = get_gcp_project_id()

    # Get service account key from existing file
    service_account_key = get_service_account_key()
    if not service_account_key:
        print("❌ Cannot proceed without service account key")
        return

    # Create a test workspace ID
    workspace_id = uuid.uuid4()

    print("\nInitializing GCP Cloud Storage crawler...")
    print(f"Workspace ID: {workspace_id}")
    print(f"Project ID: {project_id}")
    print("Using service account key: Yes (from key.json)")

    try:
        # Initialize the crawler
        crawler = CloudStorageCrawler(
            workspace_id=workspace_id,
            project_id=project_id,
            service_account_key=service_account_key,
            region="us-central1",  # Default region
            max_retries=3,
            retry_delay=5,
        )

        print("✅ GCP Cloud Storage crawler initialized successfully")

        # Test credential validation
        print("\nTesting credential validation...")
        if crawler.validate_credentials():
            print("✅ GCP Cloud Storage credentials are valid")
        else:
            print("❌ GCP Cloud Storage credentials validation failed")
            return

        # Test crawling (without database)
        print("\nTesting Cloud Storage bucket discovery...")
        buckets = crawler._crawl_storage_buckets()

        print(f"✅ Found {len(buckets)} Cloud Storage buckets")

        if buckets:
            print("\nSample bucket data:")
            sample_bucket = buckets[0]
            print(f"  Name: {sample_bucket.name}")
            print(f"  Location: {sample_bucket.extra.get('location', 'N/A')}")
            print(f"  Storage Class: {sample_bucket.extra.get('storage_class', 'N/A')}")

            # Test resource mapping
            print("\nTesting resource mapping...")
            resource = crawler._map_bucket_to_resource(sample_bucket)
            print("✅ Successfully mapped to Resource model:")
            print(f"  Resource ID: {resource.resource_id}")
            print(f"  Name: {resource.name}")
            print(f"  Type: {resource.type}")
            print(f"  Status: {resource.status}")
            print(f"  Region: {resource.region}")
        else:
            print("ℹ️  No Cloud Storage buckets found in this project")

        print("\n" + "=" * 50)
        print("✅ GCP Cloud Storage crawler test completed successfully!")
        print("=" * 50)

    except Exception as e:
        print(f"\n❌ Error testing GCP Cloud Storage crawler: {e}")
        logger.exception("GCP Cloud Storage crawler test failed")
        return


def test_cloud_functions_crawler():
    """Test the GCP Cloud Functions crawler"""
    print("\n" + "=" * 50)
    print("Testing GCP Cloud Functions Crawler")
    print("=" * 50)

    # Check authentication
    if not check_gcloud_auth():
        print("Please authenticate with gcloud first:")
        print("  gcloud auth login")
        print("  gcloud auth application-default login")
        return

    # Get project ID
    project_id = get_gcp_project_id()

    # Get service account key from existing file
    service_account_key = get_service_account_key()
    if not service_account_key:
        print("❌ Cannot proceed without service account key")
        return

    # Create a test workspace ID
    workspace_id = uuid.uuid4()

    print("\nInitializing GCP Cloud Functions crawler...")
    print(f"Workspace ID: {workspace_id}")
    print(f"Project ID: {project_id}")
    print("Using service account key: Yes (from key.json)")

    try:
        # Initialize the crawler
        crawler = CloudFunctionsCrawler(
            workspace_id=workspace_id,
            project_id=project_id,
            service_account_key=service_account_key,
            region="us-central1",  # Default region
            max_retries=3,
            retry_delay=5,
        )

        print("✅ GCP Cloud Functions crawler initialized successfully")

        # Test credential validation
        print("\nTesting credential validation...")
        if crawler.validate_credentials():
            print("✅ GCP Cloud Functions credentials are valid")
        else:
            print("❌ GCP Cloud Functions credentials validation failed")
            return

        # Test crawling (without database)
        print("\nTesting Cloud Functions discovery...")
        functions = crawler._crawl_cloud_functions()

        print(f"✅ Found {len(functions)} Cloud Functions")

        if functions:
            print("\nSample function data:")
            sample_function = functions[0]
            print(f"  Name: {sample_function.get('name', 'N/A')}")
            print(f"  State: {sample_function.get('state', 'N/A')}")
            print(f"  Runtime: {sample_function.get('runtime', 'N/A')}")
            print(f"  Region: {sample_function.get('region', 'N/A')}")

            # Test resource mapping
            print("\nTesting resource mapping...")
            resource = crawler._map_function_to_resource(sample_function)
            print("✅ Successfully mapped to Resource model:")
            print(f"  Resource ID: {resource.resource_id}")
            print(f"  Name: {resource.name}")
            print(f"  Type: {resource.type}")
            print(f"  Status: {resource.status}")
            print(f"  Region: {resource.region}")
        else:
            print("ℹ️  No Cloud Functions found in this project")

        print("\n" + "=" * 50)
        print("✅ GCP Cloud Functions crawler test completed successfully!")
        print("=" * 50)

    except Exception as e:
        print(f"\n❌ Error testing GCP Cloud Functions crawler: {e}")
        logger.exception("GCP Cloud Functions crawler test failed")
        return


if __name__ == "__main__":
    print("GCP Resource Crawler Test")
    print("Using existing key.json file for authentication")

    # Test credential validation first
    test_credential_validation()

    # Test Compute Engine crawler
    test_gcp_crawler()

    # Test Cloud Storage crawler
    # test_cloud_storage_crawler()

    # Test Cloud Functions crawler
    # test_cloud_functions_crawler()
