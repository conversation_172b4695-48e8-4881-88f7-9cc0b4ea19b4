###### configuring what to type check and where to import from

# check all files in "."
project-includes = ["."]
# exclude dotfiles
project-excludes = ["**/.[!/.]*", "**/*venv/**"]
# import project files from "app"
search-path = ["."]
# do not include any third-party packages
site-package-path = [".venv/lib/python3.12/site-packages"]

###### configuring your python environment

# assume we're running on linux, regardless of the actual current platform
python-platform = "linux"
python-version = "3.12"
python-interpreter = ".venv/bin/python3"

#### configuring your type check settings

replace-imports-with-any = []
ignore-errors-in-generated-code = true
use-untyped-imports = true
ignore-missing-source = true

# disable `bad-assignment` and `invalid-argument` for the whole project
[errors]
bad-assignment = false
invalid-argument = true

#### performance optimization settings

[performance]
# Cache type information between runs for faster subsequent checks
use-cache = true
# Enable parallel processing for large codebases
parallel = true
