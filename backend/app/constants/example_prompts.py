from app.models.resources import AWSResourceType, AzureResourceType, GCPResourceType

# Example prompts organized by template type
# Template types correspond to resource types from resources.py


class ExamplePrompt:
    def __init__(self, title: str, context: str, service: str):
        self.title = title
        self.context = context
        self.service = service


# General prompts (when template_type is None)
GENERAL_PROMPTS = [
    ExamplePrompt(
        title="Cost Optimization Analysis",
        context="Analyze my cloud infrastructure to identify cost optimization opportunities across all services and provide actionable recommendations.",
        service="BILLING",
    ),
    ExamplePrompt(
        title="Security Audit Overview",
        context="Perform a comprehensive security audit of my cloud resources to identify vulnerabilities and compliance issues.",
        service="SECURITY",
    ),
    ExamplePrompt(
        title="Performance Monitoring",
        context="Monitor and analyze the performance metrics of my cloud infrastructure to identify potential bottlenecks and optimization areas.",
        service="MONITORING",
    ),
    ExamplePrompt(
        title="Resource Health Check",
        context="Conduct a health check across all my cloud resources to identify issues, unused resources, and optimization opportunities.",
        service="MONITORING",
    ),
]

# AWS Resource Type Specific Prompts
AWS_PROMPTS: dict[str, list[ExamplePrompt]] = {
    AWSResourceType.EC2: [
        ExamplePrompt(
            title="EC2 Instance Optimization",
            context="Analyze EC2 instance utilization metrics and recommend right-sizing opportunities to reduce costs while maintaining performance.",
            service="BILLING",
        ),
        ExamplePrompt(
            title="EC2 Security Assessment",
            context="Review EC2 instance security configurations including security groups, key pairs, and access patterns for potential vulnerabilities.",
            service="SECURITY",
        ),
        ExamplePrompt(
            title="EC2 Performance Analysis",
            context="Monitor EC2 instance performance metrics including CPU, memory, and network utilization to identify optimization opportunities.",
            service="MONITORING",
        ),
        ExamplePrompt(
            title="EC2 Reserved Instance Planning",
            context="Analyze EC2 usage patterns to recommend Reserved Instance purchases for maximum cost savings.",
            service="BILLING",
        ),
    ],
    AWSResourceType.RDS: [
        ExamplePrompt(
            title="RDS Cost Optimization",
            context="Analyze RDS database instances for cost optimization opportunities including right-sizing, storage optimization, and Reserved Instance recommendations.",
            service="BILLING",
        ),
        ExamplePrompt(
            title="RDS Security Review",
            context="Assess RDS security configurations including encryption, backup settings, security groups, and access controls.",
            service="SECURITY",
        ),
        ExamplePrompt(
            title="RDS Performance Tuning",
            context="Analyze RDS performance metrics and query patterns to identify optimization opportunities and potential bottlenecks.",
            service="MONITORING",
        ),
        ExamplePrompt(
            title="RDS Backup and Recovery",
            context="Review RDS backup configurations, retention policies, and disaster recovery readiness across all database instances.",
            service="SECURITY",
        ),
    ],
    AWSResourceType.EKS: [
        ExamplePrompt(
            title="EKS Cluster Optimization",
            context="Analyze EKS cluster configuration, node utilization, and pod resource allocation to optimize costs and performance.",
            service="BILLING",
        ),
        ExamplePrompt(
            title="EKS Security Assessment",
            context="Review EKS cluster security including RBAC configuration, network policies, and container security best practices.",
            service="SECURITY",
        ),
        ExamplePrompt(
            title="EKS Resource Management",
            context="Analyze EKS workload resource requests, limits, and auto-scaling configurations for optimal resource utilization.",
            service="MONITORING",
        ),
        ExamplePrompt(
            title="EKS Cost Allocation",
            context="Implement cost allocation strategies for EKS workloads to track and optimize spending by team or application.",
            service="BILLING",
        ),
    ],
    AWSResourceType.LAMBDA: [
        ExamplePrompt(
            title="Lambda Function Optimization",
            context="Analyze Lambda function performance, memory allocation, and execution patterns to optimize costs and reduce cold starts.",
            service="BILLING",
        ),
        ExamplePrompt(
            title="Lambda Security Review",
            context="Review Lambda function security including IAM permissions, environment variables, and VPC configurations.",
            service="SECURITY",
        ),
        ExamplePrompt(
            title="Lambda Performance Analysis",
            context="Monitor Lambda function execution metrics, error rates, and duration to identify performance optimization opportunities.",
            service="MONITORING",
        ),
        ExamplePrompt(
            title="Lambda Cost Analysis",
            context="Analyze Lambda usage patterns and costs to identify opportunities for Reserved Concurrency and Provisioned Concurrency optimization.",
            service="BILLING",
        ),
    ],
    AWSResourceType.S3: [
        ExamplePrompt(
            title="S3 Storage Optimization",
            context="Analyze S3 bucket storage classes, lifecycle policies, and access patterns to optimize storage costs.",
            service="BILLING",
        ),
        ExamplePrompt(
            title="S3 Security Audit",
            context="Review S3 bucket security configurations including public access settings, encryption, and access policies.",
            service="SECURITY",
        ),
        ExamplePrompt(
            title="S3 Performance Analysis",
            context="Analyze S3 request patterns, transfer speeds, and CloudFront integration opportunities for performance optimization.",
            service="MONITORING",
        ),
        ExamplePrompt(
            title="S3 Lifecycle Management",
            context="Implement intelligent tiering and lifecycle policies to automatically optimize S3 storage costs based on access patterns.",
            service="BILLING",
        ),
    ],
    AWSResourceType.DYNAMODB: [
        ExamplePrompt(
            title="DynamoDB Cost Optimization",
            context="Analyze DynamoDB table capacity settings, auto-scaling configuration, and access patterns for cost optimization.",
            service="BILLING",
        ),
        ExamplePrompt(
            title="DynamoDB Security Review",
            context="Review DynamoDB security including encryption settings, access policies, and VPC endpoint configurations.",
            service="SECURITY",
        ),
        ExamplePrompt(
            title="DynamoDB Performance Tuning",
            context="Analyze DynamoDB performance metrics, throttling events, and query patterns to optimize table performance.",
            service="MONITORING",
        ),
        ExamplePrompt(
            title="DynamoDB Backup Strategy",
            context="Review and optimize DynamoDB backup configurations, point-in-time recovery settings, and cross-region replication.",
            service="SECURITY",
        ),
    ],
}

# GCP Resource Type Specific Prompts
GCP_PROMPTS: dict[str, list[ExamplePrompt]] = {
    GCPResourceType.COMPUTE_ENGINE: [
        ExamplePrompt(
            title="Compute Engine Optimization",
            context="Analyze GCP Compute Engine instance utilization and recommend right-sizing, preemptible instances, and sustained use discounts.",
            service="BILLING",
        ),
        ExamplePrompt(
            title="Compute Engine Security",
            context="Review Compute Engine security configurations including firewall rules, service accounts, and OS security settings.",
            service="SECURITY",
        ),
        ExamplePrompt(
            title="Compute Engine Performance",
            context="Monitor Compute Engine performance metrics and analyze network, disk, and CPU utilization patterns.",
            service="MONITORING",
        ),
        ExamplePrompt(
            title="Compute Engine Cost Analysis",
            context="Analyze Compute Engine usage patterns to recommend committed use discounts and custom machine type optimization.",
            service="BILLING",
        ),
    ],
    GCPResourceType.GKE: [
        ExamplePrompt(
            title="GKE Cluster Optimization",
            context="Analyze GKE cluster configuration, node pool utilization, and workload resource allocation for cost and performance optimization.",
            service="BILLING",
        ),
        ExamplePrompt(
            title="GKE Security Assessment",
            context="Review GKE cluster security including Workload Identity, network policies, and binary authorization configurations.",
            service="SECURITY",
        ),
        ExamplePrompt(
            title="GKE Resource Management",
            context="Optimize GKE resource requests, limits, and cluster autoscaling configurations for efficient resource utilization.",
            service="MONITORING",
        ),
        ExamplePrompt(
            title="GKE Cost Allocation",
            context="Implement GKE cost allocation and monitoring strategies to track spending by namespace, team, and application.",
            service="BILLING",
        ),
    ],
    GCPResourceType.CLOUD_SQL: [
        ExamplePrompt(
            title="Cloud SQL Optimization",
            context="Analyze Cloud SQL instances for right-sizing opportunities, storage optimization, and automated backup cost management.",
            service="BILLING",
        ),
        ExamplePrompt(
            title="Cloud SQL Security Review",
            context="Review Cloud SQL security configurations including SSL certificates, authorized networks, and database flags.",
            service="SECURITY",
        ),
        ExamplePrompt(
            title="Cloud SQL Performance",
            context="Monitor Cloud SQL performance metrics, query insights, and connection patterns to optimize database performance.",
            service="MONITORING",
        ),
        ExamplePrompt(
            title="Cloud SQL High Availability",
            context="Review Cloud SQL high availability configuration, backup strategies, and disaster recovery planning.",
            service="SECURITY",
        ),
    ],
    GCPResourceType.CLOUD_STORAGE: [
        ExamplePrompt(
            title="Cloud Storage Optimization",
            context="Analyze Cloud Storage bucket storage classes, lifecycle policies, and access patterns to optimize costs.",
            service="BILLING",
        ),
        ExamplePrompt(
            title="Cloud Storage Security",
            context="Review Cloud Storage security including IAM policies, uniform bucket-level access, and encryption configurations.",
            service="SECURITY",
        ),
        ExamplePrompt(
            title="Cloud Storage Performance",
            context="Analyze Cloud Storage request patterns, transfer performance, and CDN integration opportunities.",
            service="MONITORING",
        ),
        ExamplePrompt(
            title="Cloud Storage Lifecycle",
            context="Implement intelligent storage class transitions and lifecycle policies based on object access patterns.",
            service="BILLING",
        ),
    ],
}

# Azure Resource Type Specific Prompts
AZURE_PROMPTS: dict[str, list[ExamplePrompt]] = {
    AzureResourceType.VIRTUAL_MACHINES: [
        ExamplePrompt(
            title="VM Cost Optimization",
            context="Analyze Azure Virtual Machine utilization and recommend right-sizing, Reserved Instances, and Spot Instance opportunities.",
            service="BILLING",
        ),
        ExamplePrompt(
            title="VM Security Assessment",
            context="Review Azure VM security configurations including Network Security Groups, disk encryption, and endpoint protection.",
            service="SECURITY",
        ),
        ExamplePrompt(
            title="VM Performance Monitoring",
            context="Monitor Azure VM performance metrics including CPU, memory, disk, and network utilization patterns.",
            service="MONITORING",
        ),
        ExamplePrompt(
            title="VM Availability Planning",
            context="Review Azure VM availability sets, zones, and backup configurations to ensure high availability and disaster recovery.",
            service="SECURITY",
        ),
    ],
    AzureResourceType.AKS: [
        ExamplePrompt(
            title="AKS Cluster Optimization",
            context="Analyze AKS cluster configuration, node pool scaling, and workload resource allocation for cost optimization.",
            service="BILLING",
        ),
        ExamplePrompt(
            title="AKS Security Review",
            context="Review AKS security including Azure RBAC, network policies, Azure Policy integration, and container security.",
            service="SECURITY",
        ),
        ExamplePrompt(
            title="AKS Resource Management",
            context="Optimize AKS resource quotas, limits, and cluster autoscaler configurations for efficient resource utilization.",
            service="MONITORING",
        ),
        ExamplePrompt(
            title="AKS Cost Management",
            context="Implement cost allocation and monitoring for AKS workloads using Azure Cost Management and billing tags.",
            service="BILLING",
        ),
    ],
    AzureResourceType.SQL_DATABASE: [
        ExamplePrompt(
            title="SQL Database Optimization",
            context="Analyze Azure SQL Database performance tiers, auto-scaling settings, and Reserved Capacity opportunities for cost optimization.",
            service="BILLING",
        ),
        ExamplePrompt(
            title="SQL Database Security",
            context="Review Azure SQL Database security including Transparent Data Encryption, firewall rules, and Advanced Threat Protection.",
            service="SECURITY",
        ),
        ExamplePrompt(
            title="SQL Database Performance",
            context="Monitor Azure SQL Database performance using Query Performance Insight and recommend index and query optimizations.",
            service="MONITORING",
        ),
        ExamplePrompt(
            title="SQL Database Backup",
            context="Review Azure SQL Database backup retention policies, geo-redundancy settings, and point-in-time recovery configurations.",
            service="SECURITY",
        ),
    ],
    AzureResourceType.BLOB_STORAGE: [
        ExamplePrompt(
            title="Blob Storage Optimization",
            context="Analyze Azure Blob Storage access tiers, lifecycle policies, and replication settings to optimize storage costs.",
            service="BILLING",
        ),
        ExamplePrompt(
            title="Blob Storage Security",
            context="Review Azure Blob Storage security including access policies, encryption at rest, and Azure AD integration.",
            service="SECURITY",
        ),
        ExamplePrompt(
            title="Blob Storage Performance",
            context="Analyze Azure Blob Storage performance patterns, hot/cool tier usage, and CDN integration opportunities.",
            service="MONITORING",
        ),
        ExamplePrompt(
            title="Blob Storage Lifecycle",
            context="Implement automated lifecycle management policies to transition blobs between access tiers based on usage patterns.",
            service="BILLING",
        ),
    ],
}

# Combine all prompts for easy lookup
ALL_PROMPTS = {
    **{f"aws.{k}": v for k, v in AWS_PROMPTS.items()},
    **{f"gcp.{k}": v for k, v in GCP_PROMPTS.items()},
    **{f"azure.{k}": v for k, v in AZURE_PROMPTS.items()},
}


def get_prompts_for_template_type(template_type: str | None) -> list[ExamplePrompt]:
    """
    Get example prompts for a given template type.

    Args:
        template_type: Resource type like 'ec2', 'rds', 'gke', etc. or None for general prompts

    Returns:
        List of ExamplePrompt objects
    """
    if template_type is None:
        return GENERAL_PROMPTS

    # Try to find prompts for the template type (case insensitive)
    template_type_lower = template_type.lower()

    # First try direct lookup
    for key, prompts in ALL_PROMPTS.items():
        if key.lower().endswith(f".{template_type_lower}"):
            return prompts

    # If no direct match, try partial matching (e.g., 'ec2' matches 'EC2')
    for resource_type, prompts in AWS_PROMPTS.items():
        if resource_type.lower() == template_type_lower:
            return prompts

    for resource_type, prompts in GCP_PROMPTS.items():
        if resource_type.lower() == template_type_lower:
            return prompts

    for resource_type, prompts in AZURE_PROMPTS.items():
        if resource_type.lower() == template_type_lower:
            return prompts

    # If no match found, return general prompts
    return GENERAL_PROMPTS
