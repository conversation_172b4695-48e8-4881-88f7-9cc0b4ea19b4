from enum import Enum

from app.models.clouds import CloudProvider


class AWSRegion(str, Enum):
    # US Regions
    US_EAST_1 = "us-east-1"  # US East (N. Virginia)
    US_EAST_2 = "us-east-2"  # US East (Ohio)
    US_WEST_1 = "us-west-1"  # US West (N. California)
    US_WEST_2 = "us-west-2"  # US West (Oregon)

    # Canada Regions
    CA_CENTRAL_1 = "ca-central-1"  # Canada (Central)
    CA_WEST_1 = "ca-west-1"  # Canada West (Calgary)

    # Europe Regions
    EU_CENTRAL_1 = "eu-central-1"  # Europe (Frankfurt)
    EU_CENTRAL_2 = "eu-central-2"  # Europe (Zurich)
    EU_WEST_1 = "eu-west-1"  # Europe (Ireland)
    EU_WEST_2 = "eu-west-2"  # Europe (London)
    EU_WEST_3 = "eu-west-3"  # Europe (Paris)
    EU_NORTH_1 = "eu-north-1"  # Europe (Stockholm)
    EU_SOUTH_1 = "eu-south-1"  # Europe (Milan)
    EU_SOUTH_2 = "eu-south-2"  # Europe (Spain)

    # Asia Pacific Regions
    AP_EAST_1 = "ap-east-1"  # Asia Pacific (Hong Kong)
    AP_EAST_2 = "ap-east-2"  # Asia Pacific (Taipei)
    AP_SOUTH_1 = "ap-south-1"  # Asia Pacific (Mumbai)
    AP_SOUTH_2 = "ap-south-2"  # Asia Pacific (Hyderabad)
    AP_SOUTHEAST_1 = "ap-southeast-1"  # Asia Pacific (Singapore)
    AP_SOUTHEAST_2 = "ap-southeast-2"  # Asia Pacific (Sydney)
    AP_SOUTHEAST_3 = "ap-southeast-3"  # Asia Pacific (Jakarta)
    AP_SOUTHEAST_4 = "ap-southeast-4"  # Asia Pacific (Melbourne)
    AP_SOUTHEAST_5 = "ap-southeast-5"  # Asia Pacific (Malaysia)
    AP_SOUTHEAST_7 = "ap-southeast-7"  # Asia Pacific (Thailand)
    AP_NORTHEAST_1 = "ap-northeast-1"  # Asia Pacific (Tokyo)
    AP_NORTHEAST_2 = "ap-northeast-2"  # Asia Pacific (Seoul)
    AP_NORTHEAST_3 = "ap-northeast-3"  # Asia Pacific (Osaka)

    # Africa Regions
    AF_SOUTH_1 = "af-south-1"  # Africa (Cape Town)

    # Middle East Regions
    ME_SOUTH_1 = "me-south-1"  # Middle East (Bahrain)
    ME_CENTRAL_1 = "me-central-1"  # Middle East (UAE)

    # Israel Region
    IL_CENTRAL_1 = "il-central-1"  # Israel (Tel Aviv)

    # Mexico Region
    MX_CENTRAL_1 = "mx-central-1"  # Mexico (Central)

    # South America Regions
    SA_EAST_1 = "sa-east-1"  # South America (São Paulo)


class GCPRegion(str, Enum):
    # North America - US
    US_CENTRAL1 = "us-central1"  # Council Bluffs, Iowa
    US_EAST1 = "us-east1"  # Moncks Corner, South Carolina
    US_EAST4 = "us-east4"  # Ashburn, Virginia
    US_WEST1 = "us-west1"  # The Dalles, Oregon
    US_WEST2 = "us-west2"  # Los Angeles, California
    US_WEST3 = "us-west3"  # Salt Lake City, Utah
    US_WEST4 = "us-west4"  # Las Vegas, Nevada

    # North America - Canada
    NORTHAMERICA_NORTHEAST1 = "northamerica-northeast1"  # Montréal, Québec
    NORTHAMERICA_NORTHEAST2 = "northamerica-northeast2"  # Toronto, Ontario

    # Europe
    EUROPE_CENTRAL2 = "europe-central2"  # Warsaw, Poland
    EUROPE_NORTH1 = "europe-north1"  # Hamina, Finland
    EUROPE_SOUTHWEST1 = "europe-southwest1"  # Madrid, Spain
    EUROPE_WEST1 = "europe-west1"  # St. Ghislain, Belgium
    EUROPE_WEST2 = "europe-west2"  # London, England
    EUROPE_WEST3 = "europe-west3"  # Frankfurt, Germany
    EUROPE_WEST4 = "europe-west4"  # Eemshaven, Netherlands
    EUROPE_WEST6 = "europe-west6"  # Zurich, Switzerland
    EUROPE_WEST8 = "europe-west8"  # Milan, Italy
    EUROPE_WEST9 = "europe-west9"  # Paris, France

    # Asia Pacific
    ASIA_EAST1 = "asia-east1"  # Changhua County, Taiwan
    ASIA_EAST2 = "asia-east2"  # Hong Kong
    ASIA_NORTHEAST1 = "asia-northeast1"  # Tokyo, Japan
    ASIA_NORTHEAST2 = "asia-northeast2"  # Osaka, Japan
    ASIA_NORTHEAST3 = "asia-northeast3"  # Seoul, South Korea
    ASIA_SOUTH1 = "asia-south1"  # Mumbai, India
    ASIA_SOUTH2 = "asia-south2"  # Delhi, India
    ASIA_SOUTHEAST1 = "asia-southeast1"  # Jurong West, Singapore

    # Australia
    AUSTRALIA_SOUTHEAST1 = "australia-southeast1"  # Sydney, Australia
    AUSTRALIA_SOUTHEAST2 = "australia-southeast2"  # Melbourne, Australia

    # South America
    SOUTHAMERICA_EAST1 = "southamerica-east1"  # Osasco, São Paulo


class AzureLocation(str, Enum):
    # United States
    EAST_US = "East US"  # Virginia
    EAST_US_2 = "East US 2"  # Virginia
    WEST_US = "West US"  # California
    WEST_US_2 = "West US 2"  # Washington
    WEST_US_3 = "West US 3"  # Phoenix
    CENTRAL_US = "Central US"  # Iowa
    NORTH_CENTRAL_US = "North Central US"  # Illinois
    SOUTH_CENTRAL_US = "South Central US"  # Texas
    WEST_CENTRAL_US = "West Central US"  # Wyoming

    # Canada
    CANADA_CENTRAL = "Canada Central"  # Toronto
    CANADA_EAST = "Canada East"  # Quebec

    # Europe
    NORTH_EUROPE = "North Europe"  # Ireland
    WEST_EUROPE = "West Europe"  # Netherlands
    UK_SOUTH = "UK South"  # London
    UK_WEST = "UK West"  # Cardiff
    FRANCE_CENTRAL = "France Central"  # Paris
    FRANCE_SOUTH = "France South"  # Marseille
    GERMANY_WEST_CENTRAL = "Germany West Central"  # Frankfurt
    GERMANY_NORTH = "Germany North"  # Berlin
    NORWAY_EAST = "Norway East"  # Norway
    NORWAY_WEST = "Norway West"  # Norway
    SWEDEN_CENTRAL = "Sweden Central"  # Gävle
    SWEDEN_SOUTH = "Sweden South"  # Sweden
    SWITZERLAND_NORTH = "Switzerland North"  # Zurich
    SWITZERLAND_WEST = "Switzerland West"  # Geneva
    SPAIN_CENTRAL = "Spain Central"  # Madrid
    POLAND_CENTRAL = "Poland Central"  # Warsaw
    ITALY_NORTH = "Italy North"  # Milan
    AUSTRIA_EAST = "Austria East"  # Vienna

    # Asia Pacific
    EAST_ASIA = "East Asia"  # Hong Kong SAR
    SOUTHEAST_ASIA = "Southeast Asia"  # Singapore
    JAPAN_EAST = "Japan East"  # Tokyo, Saitama
    JAPAN_WEST = "Japan West"  # Osaka
    KOREA_CENTRAL = "Korea Central"  # Seoul
    KOREA_SOUTH = "Korea South"  # Busan
    CENTRAL_INDIA = "Central India"  # Pune
    SOUTH_INDIA = "South India"  # Chennai
    WEST_INDIA = "West India"  # Mumbai
    INDONESIA_CENTRAL = "Indonesia Central"  # Jakarta
    MALAYSIA_WEST = "Malaysia West"  # Malaysia

    # Australia & New Zealand
    AUSTRALIA_EAST = "Australia East"  # New South Wales
    AUSTRALIA_SOUTHEAST = "Australia Southeast"  # Victoria
    AUSTRALIA_CENTRAL = "Australia Central"  # Canberra
    AUSTRALIA_CENTRAL_2 = "Australia Central 2"  # Canberra
    NEW_ZEALAND_NORTH = "New Zealand North"  # Auckland

    # South America
    BRAZIL_SOUTH = "Brazil South"  # Sao Paulo State
    BRAZIL_SOUTHEAST = "Brazil Southeast"  # Rio
    CHILE_CENTRAL = "Chile Central"  # Santiago

    # Middle East & Africa
    ISRAEL_CENTRAL = "Israel Central"  # Israel
    UAE_NORTH = "UAE North"  # Dubai
    UAE_CENTRAL = "UAE Central"  # Abu Dhabi
    QATAR_CENTRAL = "Qatar Central"  # Doha
    SOUTH_AFRICA_NORTH = "South Africa North"  # Johannesburg
    SOUTH_AFRICA_WEST = "South Africa West"  # Cape Town

    # Mexico
    MEXICO_CENTRAL = "Mexico Central"  # Querétaro State


REGIONS_MAPPING = {
    CloudProvider.AWS: AWSRegion,
    CloudProvider.GCP: GCPRegion,
    CloudProvider.AZURE: AzureLocation,
}
