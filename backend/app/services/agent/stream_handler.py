import json
from typing import Any

from langchain_core.runnables.schema import (
    CustomStreamEvent,
    EventData,
    StandardStreamEvent,
)
from sqlmodel import Session
from sqlmodel.ext.asyncio.session import AsyncSession

from app.repositories.message_component import MessageComponentRepository
from app.schemas.chat import Chat<PERSON>xecutionContext, ChatServiceContext, TokenUsage

from .message_handler import MessageHandler


class StreamHandler:
    def __init__(self, session: Session, async_session: AsyncSession):
        self.session = session
        self.message_handler = MessageHandler(self.session, async_session)
        self.message_component_repository = MessageComponentRepository(self.session)
        self.ignore_tool_streams = ["planning"]
        self.tool_streams = {}

    def _handle_chat_stream(
        self,
        event_data: EventData | Any,
        execution_ctx: ChatExecutionContext,
    ) -> tuple[ChatExecutionContext, dict[str, Any] | None]:
        """Handle chat model streaming events."""
        content = event_data["chunk"].content
        result = None
        response_text = execution_ctx.response_text
        response_text_pointer = execution_ctx.response_text_pointer
        cached_tool_reasoning = execution_ctx.cached_tool_reasoning

        if isinstance(content, list):
            for chunk in content:
                if "type" in chunk:
                    if chunk["type"] == "text":
                        response_text += chunk["text"]
        else:
            response_text += content

        process_text = response_text[response_text_pointer:]
        if "<topic>" in process_text and "</topic>" in process_text:
            topic_start = process_text.find("<topic>")
            topic_end = process_text.find("</topic>")
            if topic_start != -1 and topic_end != -1:
                topic_text = process_text[
                    topic_start + len("<topic>") : topic_end
                ].strip()
                result = {"type": "thinking", "content": topic_text + "..."}
                response_text_pointer = (
                    response_text_pointer + topic_end + len("</topic>")
                )

        process_text = response_text[response_text_pointer:]
        if "<tool_brief>" in process_text and "</tool_brief>" in process_text:
            tool_brief_start = process_text.find("<tool_brief>")
            tool_brief_end = process_text.find("</tool_brief>")
            if tool_brief_start != -1 and tool_brief_end != -1:
                tool_brief_text = process_text[
                    tool_brief_start + len("<tool_brief>") : tool_brief_end
                ].strip()
                result = {"type": "thinking", "content": tool_brief_text + "..."}
                response_text_pointer = (
                    response_text_pointer + tool_brief_end + len("</tool_brief>")
                )

        process_text = response_text[response_text_pointer:]
        if "<tool_reasoning>" in response_text and "</tool_reasoning>" in response_text:
            tool_reasoning_start = response_text.find("<tool_reasoning>")
            tool_reasoning_end = response_text.find("</tool_reasoning>")
            if tool_reasoning_start != -1 and tool_reasoning_end != -1:
                tool_reasoning_text = response_text[
                    tool_reasoning_start + len("<tool_reasoning>") : tool_reasoning_end
                ].strip()
                cached_tool_reasoning = tool_reasoning_text
                response_text_pointer = (
                    response_text_pointer
                    + tool_reasoning_end
                    + len("</tool_reasoning>")
                )

        execution_ctx.cached_tool_reasoning = cached_tool_reasoning
        execution_ctx.response_text = response_text
        execution_ctx.response_text_pointer = response_text_pointer

        return execution_ctx, result

    def _handle_tool_use_streaming(
        self,
        event: CustomStreamEvent | StandardStreamEvent,
        execution_ctx: ChatExecutionContext,
    ) -> tuple[ChatExecutionContext, dict[str, Any] | None]:
        """Handle streaming tool use chunks with enhanced phases."""
        tool_call_chunk = event["data"]["chunk"].tool_call_chunks[0]
        tool_name = tool_call_chunk.get("name")
        tool_call_id = tool_call_chunk.get("id")
        partial_args = tool_call_chunk.get("args", "")
        conversation_id = execution_ctx.current_message.conversation_id

        if tool_name in self.ignore_tool_streams:
            return execution_ctx, None

        # Has tool call id -> new tool call
        if tool_call_id:
            tool_stream = {
                "name": tool_name,
                "id": tool_call_id,
                "accumulated_input": partial_args,
            }

            self.tool_streams[conversation_id] = tool_stream

            return (
                execution_ctx,
                {
                    "type": "function_stream",
                    "name": tool_stream["name"],
                    "id": tool_stream["id"],
                    "content": partial_args,
                },
            )
        else:
            if self.tool_streams.get(conversation_id):
                # No tool call id -> update existing tool call
                tool_stream = self.tool_streams[conversation_id]
                if partial_args:
                    # Initialize accumulated_input as empty string if it's None
                    if tool_stream["accumulated_input"] is None:
                        tool_stream["accumulated_input"] = ""
                    tool_stream["accumulated_input"] += partial_args
                    return (
                        execution_ctx,
                        {
                            "type": "function_stream",
                            "name": tool_stream["name"],
                            "id": tool_stream["id"],
                            "content": partial_args,
                        },
                    )
            return execution_ctx, None

    def _handle_tool_start(
        self,
        event: CustomStreamEvent | StandardStreamEvent,
        execution_ctx: ChatExecutionContext,
    ) -> tuple[ChatExecutionContext, dict[str, Any] | None]:
        """Handle tool start events."""
        tool_name = event["name"]

        # Skip ignored tools
        if tool_name in self.ignore_tool_streams:
            return execution_ctx, None

        tool_input: dict = event["data"].get("input")
        position = (
            execution_ctx.current_message.components[-1].position + 1
            if execution_ctx.current_message.components
            else 0
        )

        if isinstance(tool_input, str):
            try:
                tool_input = json.loads(tool_input)
            except json.JSONDecodeError:
                tool_input = {"input": tool_input}

        self.message_component_repository.create_message_component_with_tool(
            message_id=execution_ctx.current_message.id,
            position=position,
            tool_name=event["name"],
            tool_input=tool_input,
            tool_output="",
            tool_reasoning="",
            tool_runtime=0.0,
        )
        self.session.refresh(execution_ctx.current_message)

        result = {
            "type": "function_call",
            "name": event["name"],
            "content": tool_input,
            "reasoning": execution_ctx.cached_tool_reasoning or "",
            "position": position,
        }

        return execution_ctx, result

    def _handle_tool_end(
        self,
        event: CustomStreamEvent | StandardStreamEvent,
        execution_ctx: ChatExecutionContext,
    ) -> tuple[ChatExecutionContext, dict[str, Any] | None]:
        """Handle tool end events."""
        tool_name = event["name"]
        conversation_id = execution_ctx.current_message.conversation_id

        # Skip ignored tools
        if tool_name in self.ignore_tool_streams:
            return execution_ctx, None

        if conversation_id in self.tool_streams:
            del self.tool_streams[conversation_id]

        try:
            observation = json.loads(event["data"].get("output", ""))
        except Exception:
            observation = event["data"].get("output")

        self.message_component_repository.update_tool_component(
            message_component_id=execution_ctx.current_message.components[-1].id,
            tool_output=json.dumps(observation)
            if isinstance(observation, dict)
            else str(observation),
            tool_reasoning=execution_ctx.cached_tool_reasoning or "",
            tool_runtime=0.0,
        )
        self.session.refresh(execution_ctx.current_message)
        execution_ctx = ChatExecutionContext.new_context(
            execution_ctx.current_message,
            preserve_token_usage=execution_ctx.token_usage,
        )

        return (
            execution_ctx,
            {
                "type": "function_result",
                "name": event["name"],
                "content": observation,
            },
        )

    def get_interrupt_message(self, debug_data: dict[str, Any]) -> str | None:
        """Check for interrupts in debug data."""
        # Handle regular interrupts
        if (
            debug_data["type"] == "task_result"
            and len(debug_data["payload"]["interrupts"]) > 0
        ):
            interrupt_message = debug_data["payload"]["interrupts"][-1]["value"]
            return interrupt_message
        return None

    def handle_token_usage(
        self,
        event: CustomStreamEvent | StandardStreamEvent,
        execution_ctx: ChatExecutionContext,
    ) -> ChatExecutionContext:
        if event["event"] == "on_chat_model_stream":
            usage_metadata = event["data"]["chunk"].usage_metadata
            if usage_metadata:
                input_tokens = usage_metadata["input_tokens"]
                output_tokens = usage_metadata["output_tokens"]
                total_tokens = usage_metadata["total_tokens"]
                input_token_details = usage_metadata["input_token_details"]
                output_token_details = (
                    usage_metadata["output_token_details"]
                    if "output_token_details" in usage_metadata
                    else {}
                )
                from rich import print

                print(usage_metadata)

                token_usage = TokenUsage(
                    input_tokens=input_tokens,
                    output_tokens=output_tokens,
                    total_tokens=total_tokens,
                    input_token_details=input_token_details,
                    output_token_details=output_token_details,
                )
                execution_ctx.token_usage = token_usage

        return execution_ctx

    def handle_chain_stream_debug(
        self,
        event_data: dict[str, Any],
        execution_ctx: ChatExecutionContext,
        ctx: ChatServiceContext,
    ) -> tuple[ChatExecutionContext, dict[str, Any] | None]:
        """Handle debug events in the chain stream.

        Returns:
            tuple containing (first_checkpoint_id, last_checkpoint_id, interrupt_result)
        """
        if event_data["type"] == "checkpoint":
            if execution_ctx.first_checkpoint_id is None and not ctx.resume:
                execution_ctx.first_checkpoint_id = event_data["payload"]["config"][
                    "configurable"
                ]["checkpoint_id"]

            execution_ctx.last_checkpoint_id = event_data["payload"]["config"][
                "configurable"
            ]["checkpoint_id"]

        interrupt_message = self.get_interrupt_message(event_data)
        if interrupt_message:
            interrupt_result = {
                "type": "interrupt",
                "content": interrupt_message,
                "namespace": execution_ctx.current_message.role,
            }

            self.message_handler.handle_interrupt(
                response_message=execution_ctx.current_message,
                interrupt_message=interrupt_message,
                interrupt_reasoning=execution_ctx.cached_tool_reasoning or "",
            )
        else:
            interrupt_result = None

        return execution_ctx, interrupt_result
