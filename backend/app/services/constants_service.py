from typing import cast

from pydantic import BaseModel
from sqlmodel.ext.asyncio.session import AsyncSession

from app.constants.cloud_regions import REGIONS_MAPPING
from app.logger import logger
from app.models import AuthorizedUser, TaskServiceEnum
from app.models.clouds import CloudProvider
from app.services.builtin_conn_service import BuiltinConnectionService


class CloudRegionPublic(BaseModel):
    data: list[str]
    total: int


def get_cloud_regions(provider: CloudProvider) -> CloudRegionPublic:
    try:
        region_enum = REGIONS_MAPPING.get(provider)
        if not region_enum:
            return CloudRegionPublic(data=[], total=0)

        return CloudRegionPublic(
            data=[region.value for region in region_enum.__members__.values()],
            total=len([region.value for region in region_enum.__members__.values()]),
        )
    except Exception as e:
        logger.error(f"Error getting category data for '{provider}': {str(e)}")
        raise


class QuickStartTemplate(BaseModel):
    title: str
    context: str
    service: TaskServiceEnum


class QuickStartTemplatesPublic(BaseModel):
    data: list[QuickStartTemplate]
    total: int


async def get_quick_start_templates(
    *, session: AsyncSession, current_user: AuthorizedUser
) -> QuickStartTemplatesPublic:
    """Build quick start templates based on installed connections.

    Rules:
    - Minimum 4 templates, maximum 10 templates
    - If 1-2 connections installed -> 4 per connection
    - If 3 connections installed -> 3 per connection
    - If 4-5 connections installed -> 2 per connection
    - If 6+ installed -> 1 per connection (capped at 10 total)
    """

    # 1) Get builtin connections with is_connected flag
    conn_service = BuiltinConnectionService(session)
    builtin = await conn_service.get_builtin_connections(
        user_id=current_user.id,
        workspace_id=current_user.current_workspace_id,
        filter_by_provider=True,
        mask_env=True,
    )

    installed = [c for c in builtin.data if c.is_connected]
    num_installed = len(installed)

    # 2) Determine per-connection allocation
    def per_connection_count(n: int) -> int:
        if n <= 0:
            return 0
        if n <= 2:
            return 4
        if n == 3:
            return 3
        if n <= 5:
            return 2
        return 1

    per_conn = per_connection_count(num_installed)

    # 3) Load examples from migration config if available
    #    Fallback to generic examples when not provided.
    try:
        from app.migrate_builtin_connection import BUILTIN_CONNECTIONS
    except Exception:  # pragma: no cover - defensive
        BUILTIN_CONNECTIONS = []  # type: ignore

    prefix_to_examples: dict[str, list[dict[str, object]]] = {}
    for item in BUILTIN_CONNECTIONS or []:
        # expected optional structure: { 'prefix': 'aws', 'examples': [ { 'title': str, 'question': str } ] }
        prefix = item.get("prefix")
        examples = cast(list[dict[str, object]], item.get("examples") or [])
        if isinstance(prefix, str):
            prefix_to_examples[prefix] = examples

    generic_examples = [
        {
            "title": "Cost Optimization",
            "context": "Analyze EC2 instance utilization metrics to identify opportunities for cost optimization and resource right-sizing.",
            "service": TaskServiceEnum.BILLING,
        },
        {
            "title": "Security Audit",
            "context": "Perform a comprehensive security audit of S3 bucket configurations, permissions, and access patterns.",
            "service": TaskServiceEnum.SECURITY,
        },
        {
            "title": "Performance Monitoring",
            "context": "Monitor and analyze the performance metrics of your web application load balancer for any anomalies or issues.",
            "service": TaskServiceEnum.MONITORING,
        },
        {
            "title": "Complex Cost Optimization Report",
            "context": "Conduct comprehensive cost optimization analysis with detailed billing data extraction, assessment roadmap creation, team delegation, and consolidated reporting with prioritized recommendations.",
            "service": TaskServiceEnum.BILLING,
        },
    ]

    # 4) Build the list
    items: list[QuickStartTemplate] = []

    if num_installed == 0:
        # Fallback to generic prompts
        for ex in generic_examples[:4]:
            items.append(
                QuickStartTemplate(
                    title=str(ex.get("title", "")),
                    context=str(ex.get("context", "")),
                    service=cast(TaskServiceEnum, ex.get("service")),
                )
            )
    else:
        for conn in installed:
            examples_u = prefix_to_examples.get(conn.prefix) or generic_examples
            take = per_conn
            for ex in cast(list[dict[str, object]], examples_u)[:take]:
                title = str(ex.get("title") or conn.name)
                context = str(
                    ex.get("context") or ex.get("description") or conn.description
                )

                items.append(
                    QuickStartTemplate(
                        title=title,
                        context=context,
                        service=cast(TaskServiceEnum, ex.get("service")),
                    )
                )

    # Cap to 10, ensure minimum 4
    if len(items) > 10:
        items = items[:10]
    elif len(items) < 4:
        # top up with generics
        remaining = 4 - len(items)
        for ex in generic_examples[:remaining]:
            items.append(
                QuickStartTemplate(
                    title=str(ex.get("title", "")),
                    context=str(ex.get("context", "")),
                    service=cast(TaskServiceEnum, ex.get("service")),
                )
            )

    return QuickStartTemplatesPublic(
        data=items,
        total=len(items),
    )


def get_example_prompts(
    template_type: str | None = None,
) -> QuickStartTemplatesPublic:
    """Get example prompts based on template type.

    Args:
        template_type: Resource type like 'ec2', 'rds', 'eks', etc.
                      If None, returns general prompts.

    Returns:
        QuickStartTemplatesPublic with example prompts
    """
    from app.constants.example_prompts import get_prompts_for_template_type
    from app.models.resources import AWSResourceType, AzureResourceType, GCPResourceType

    # Validate template_type against known resource types if provided
    if template_type is not None:
        template_type = template_type.strip().upper()

        # Check if it's a valid resource type
        valid_types = set()
        valid_types.update([rt.value for rt in AWSResourceType.__members__.values()])
        valid_types.update([rt.value for rt in GCPResourceType.__members__.values()])
        valid_types.update([rt.value for rt in AzureResourceType.__members__.values()])

        if template_type not in valid_types:
            logger.warning(
                f"Invalid template_type '{template_type}'. Using general prompts."
            )
            template_type = None
        else:
            # Convert back to original format for lookup
            template_type = template_type.lower()

    # Get prompts from the constants file
    example_prompts = get_prompts_for_template_type(template_type)

    # Convert to the expected format
    templates = []
    for prompt in example_prompts:
        templates.append(
            QuickStartTemplate(
                title=prompt.title,
                context=prompt.context,
                service=cast(TaskServiceEnum, prompt.service),
            )
        )

    return QuickStartTemplatesPublic(data=templates, total=len(templates))
