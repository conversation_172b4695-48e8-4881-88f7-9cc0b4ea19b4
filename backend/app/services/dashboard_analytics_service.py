from datetime import UTC, datetime

from sqlmodel.ext.asyncio.session import AsyncSession

from app.repositories.dashboard_analytics import DashboardAnalyticsRepository
from app.repositories.recommendation import RecommendationRepository
from app.schemas.dashboard_analytics import (
    DashboardAnalyticsQuery,
    DashboardAnalyticsResponse,
    HighConsumingServicesResponse,
    TopRecommendationsResponse,
)


class DashboardAnalyticsService:
    """Service for dashboard analytics operations"""

    def __init__(self, session: AsyncSession):
        self.session = session
        self.dashboard_repo = DashboardAnalyticsRepository(session)
        self.recommendation_repo = RecommendationRepository(async_session=session)

    async def get_dashboard_analytics(
        self, query_params: DashboardAnalyticsQuery
    ) -> DashboardAnalyticsResponse:
        """Get complete dashboard analytics data"""
        try:
            # Get cost overview from existing recommendation service
            cost_overview = (
                await self.recommendation_repo.get_recommendation_overal_async(
                    query_params.workspace_id
                )
            )

            # Get high consuming services
            high_consuming_services = (
                await self.dashboard_repo.get_high_consuming_services(query_params)
            )

            # Get top recommendations
            top_recommendations = await self.dashboard_repo.get_top_recommendations(
                query_params
            )

            return DashboardAnalyticsResponse(
                cost_overview=cost_overview.model_dump(),
                high_consuming_services=high_consuming_services,
                top_recommendations=top_recommendations,
                last_updated=datetime.now(UTC).isoformat(),
            )

        except Exception as e:
            raise Exception(f"Error getting dashboard analytics: {str(e)}") from e

    async def get_high_consuming_services(
        self, query_params: DashboardAnalyticsQuery
    ) -> HighConsumingServicesResponse:
        """Get high consuming services data only"""
        return await self.dashboard_repo.get_high_consuming_services(query_params)

    async def get_top_recommendations(
        self, query_params: DashboardAnalyticsQuery
    ) -> TopRecommendationsResponse:
        """Get top recommendations data only"""
        return await self.dashboard_repo.get_top_recommendations(query_params)
