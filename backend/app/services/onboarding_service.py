from datetime import UTC, datetime
from uuid import UUID

from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from sqlmodel import select
from sqlmodel.ext.asyncio.session import AsyncSession

from app.logger import logger
from app.models import CloudProvider, UserOnboarding
from app.models.connections import BuiltinConnectionType
from app.schemas.onboarding import (
    AWSOnboardingCreate,
    AzureOnboardingCreate,
    GCPOnboardingCreate,
    OnboardingStatus,
)
from app.services.builtin_conn_service import BuiltinConnectionService


class OnboardingService:
    def __init__(self, session: AsyncSession):
        self.session = session

    async def get_onboarding_status(self, user_id: UUID) -> OnboardingStatus:
        """Get the current onboarding status for a user."""
        stmt = select(UserOnboarding).where(UserOnboarding.user_id == user_id)
        scala_result = await self.session.exec(stmt)
        result = scala_result.first()

        if not result:
            return OnboardingStatus(is_completed=False, current_step=0)

        return OnboardingStatus(
            is_completed=result.is_completed, current_step=result.current_step
        )

    async def get_selected_provider(self, user_id: UUID) -> CloudProvider | None:
        """Get the selected cloud provider for a user from the database."""
        stmt = select(UserOnboarding).where(UserOnboarding.user_id == user_id)
        result = await self.session.exec(stmt)
        user_onboarding = result.first()

        if not user_onboarding:
            return None

        return user_onboarding.selected_provider

    async def mark_step_completed(
        self, user_id: UUID, step: int, selected_provider: CloudProvider | None = None
    ) -> None:
        """Mark a specific onboarding step as completed."""
        stmt = select(UserOnboarding).where(UserOnboarding.user_id == user_id)
        result = await self.session.exec(stmt)
        user_onboarding = result.first()

        if not user_onboarding:
            user_onboarding = UserOnboarding(
                user_id=user_id,
                current_step=step,
                is_completed=step >= 3,
                selected_provider=selected_provider,
            )
            self.session.add(user_onboarding)
        else:
            user_onboarding.current_step = max(user_onboarding.current_step, step)
            user_onboarding.is_completed = user_onboarding.current_step >= 3
            user_onboarding.updated_at = datetime.now(UTC)
            if selected_provider and not user_onboarding.selected_provider:
                user_onboarding.selected_provider = selected_provider
            if user_onboarding.is_completed:
                user_onboarding.completed_at = datetime.now(UTC)

        await self.session.commit()
        await self.session.refresh(user_onboarding)

    async def get_builtin_cloud_connection_by_provider_name(
        self, user_id: UUID, workspace_id: UUID, provider_name: str
    ) -> UUID:
        """Get builtin connection ID by cloud type (aws, gcp, azure)."""
        try:
            builtin_conn_service = BuiltinConnectionService(self.session)

            # Get cloud connections from default workspace (workspace_id=None)
            builtin_connections = await builtin_conn_service.get_builtin_connections(
                user_id=user_id,
                workspace_id=workspace_id,
                builtin_connection_types=[BuiltinConnectionType.CLOUD],
                filter_by_provider=False,
            )

            # Filter by cloud type prefix
            for connection in builtin_connections.data:
                if connection.prefix == provider_name.lower():
                    return connection.id

            raise HTTPException(
                status_code=404,
                detail=f"No builtin connection found for provider: {provider_name}",
            )
        except Exception as e:
            logger.exception(
                f"Error getting builtin connection for {provider_name}: {e}"
            )
            raise HTTPException(
                status_code=500,
                detail=f"Error retrieving builtin connection for {provider_name}",
            )

    async def connect_provider(
        self,
        onboarding_data: AWSOnboardingCreate
        | AzureOnboardingCreate
        | GCPOnboardingCreate,
        workspace_id: UUID,
        user_id: UUID,
        provider_name: str,
    ):
        """Common logic for connecting cloud providers."""
        # Get the builtin connection ID based on provider type
        builtin_cloud_connection_id = (
            await self.get_builtin_cloud_connection_by_provider_name(
                user_id=user_id,
                workspace_id=workspace_id,
                provider_name=provider_name,
            )
        )

        builtin_conn_service = BuiltinConnectionService(self.session)
        if isinstance(onboarding_data, GCPOnboardingCreate):
            env = [
                {
                    "key": "GOOGLE_SERVICE_ACCOUNT_KEY",
                    "value": onboarding_data.google_service_account_key,
                    "type": "json",
                }
            ]
        elif isinstance(onboarding_data, AzureOnboardingCreate):
            env = [
                {
                    "key": "APP_ID",
                    "value": onboarding_data.app_id,
                    "type": "string",
                },
                {
                    "key": "CLIENT_SECRET",
                    "value": onboarding_data.client_secret,
                    "type": "string",
                },
                {
                    "key": "TENANT_ID",
                    "value": onboarding_data.tenant_id,
                    "type": "string",
                },
            ]
        elif isinstance(onboarding_data, AWSOnboardingCreate):
            env = [
                {
                    "key": "AWS_ACCESS_KEY_ID",
                    "value": onboarding_data.aws_access_key_id,
                    "type": "string",
                },
                {
                    "key": "AWS_SECRET_ACCESS_KEY",
                    "value": onboarding_data.aws_secret_access_key,
                    "type": "string",
                },
                {
                    "key": "AWS_DEFAULT_REGION",
                    "value": onboarding_data.aws_default_region,
                    "type": "string",
                },
            ]
        else:
            raise HTTPException(status_code=400, detail="Invalid onboarding data")

        installed_conn = await builtin_conn_service.install_builtin_connection(
            workspace_id=workspace_id,
            builtin_connection_id=builtin_cloud_connection_id,
            env=env,
        )

        return installed_conn

    async def connect_sandbox(self, workspace_id: UUID):
        """Connect sandbox to the workspace."""
        # Get the builtin connection ID based on provider type
        conn_service = BuiltinConnectionService(self.session)
        installed_conn = await conn_service.install_sandbox_connection(
            workspace_id=workspace_id,
            filter_by_conn_ids=False,
        )

        return installed_conn
