from datetime import UTC, datetime
from uuid import UUID

from sqlmodel.ext.asyncio.session import AsyncSession

from app.core.redis.redis_manager import RedisManager
from app.exceptions import RateLimitExceededException
from app.logger import logger
from app.models import (
    RESOURCE_TYPE_MAPPINGS,
    BuiltinConnectionType,
    CloudProvider,
    CloudSyncConfigCreate,
    CloudSyncConfigPublic,
    CloudSyncConfigUpdate,
    Connection,
    ConnectionType,
    ResourceTypesResponse,
)
from app.repositories.cloud_sync_config import CloudSyncConfigRepository
from app.repositories.connection import ConnectionRepository
from app.tasks.multicloud_scan_tasks import scan_multicloud_resources_by_workspace


class CloudSyncConfigService:
    def __init__(self, session: AsyncSession):
        self.session = session
        self.repository = CloudSyncConfigRepository(session)
        self.connection_repository = ConnectionRepository(session)
        self.redis_manager = RedisManager()

        # Rate limiting configuration: 5 scans per 10 minutes = 600 seconds
        self.SCAN_RATE_LIMIT_TTL = 600
        self.SCAN_RATE_LIMIT_COUNT = 5

    async def get_resource_types_for_provider(
        self, cloud_provider: str
    ) -> ResourceTypesResponse:
        """Get available resource types for a specific cloud provider"""
        try:
            # Convert string to CloudProvider enum
            try:
                provider = CloudProvider(cloud_provider.upper())
            except ValueError:
                raise ValueError(f"Unsupported cloud provider: {cloud_provider}")

            # Get resource type mappings for the provider
            resource_mappings = RESOURCE_TYPE_MAPPINGS.get(provider, {})
            if not resource_mappings:
                raise ValueError(
                    f"No resource types available for provider: {cloud_provider}"
                )

            # Convert to list of ResourceTypeInfo
            resource_types = list(resource_mappings.values())

            return ResourceTypesResponse(
                cloud_provider=provider, resource_types=resource_types
            )
        except Exception as e:
            logger.error(
                f"Error getting resource types for provider {cloud_provider}: {str(e)}"
            )
            raise

    async def _validate_connection(
        self, connection_id: UUID, workspace_id: UUID
    ) -> Connection:
        """Validate that the connection exists and is a cloud connection"""
        connection = await self.connection_repository.get_connection(
            conn_id=connection_id, workspace_id=workspace_id
        )

        if not connection:
            raise ValueError("Connection not found")

        if (
            connection.connection_type != ConnectionType.BUILTIN
            and connection.builtin_connection_type != BuiltinConnectionType.CLOUD
        ):
            raise ValueError("Connection must be a cloud connection")

        return connection

    async def _validate_selected_resources(
        self, selected_resources: list[str], connection: Connection
    ) -> None:
        """Validate that selected resources are valid for the connection's cloud provider"""
        if not selected_resources:
            return  # Empty list is valid

        # Determine cloud provider from connection prefix
        provider_mapping = {
            "aws": CloudProvider.AWS,
            "gcp": CloudProvider.GCP,
            "azure": CloudProvider.AZURE,
        }

        provider = provider_mapping.get(connection.prefix)
        if not provider:
            raise ValueError(
                f"Unknown cloud provider for connection prefix: {connection.prefix}"
            )

        # Get valid resource types for this provider
        valid_resource_types = set(RESOURCE_TYPE_MAPPINGS.get(provider, {}).keys())
        valid_resource_type_strings = {rt.value for rt in valid_resource_types}

        # Check if all selected resources are valid
        invalid_resources = set(selected_resources) - valid_resource_type_strings
        if invalid_resources:
            raise ValueError(
                f"Invalid resource types for {provider.value}: {', '.join(invalid_resources)}"
            )

    async def update_or_create_config(
        self, workspace_id: UUID, data: CloudSyncConfigCreate
    ) -> CloudSyncConfigPublic:
        """Create or update a cloud sync configuration"""
        try:
            # Validate the connection
            connection = await self._validate_connection(
                data.connection_id, workspace_id
            )

            # Validate selected resources
            await self._validate_selected_resources(data.selected_resources, connection)

            # Create or update the configuration
            config = await self.repository.update_or_create(
                workspace_id=workspace_id, connection_id=data.connection_id, data=data
            )

            # Convert to public model first, while in session context
            public_config = CloudSyncConfigPublic.model_validate(config)

            # Trigger initial scan if configuration is enabled
            if config.is_enabled:
                await self._trigger_initial_scan_with_rate_limit(
                    workspace_id, data.connection_id
                )

            return public_config
        except Exception as e:
            logger.error(f"Error creating/updating cloud sync config: {str(e)}")
            raise

    async def get_config_by_workspace(
        self, workspace_id: UUID
    ) -> list[CloudSyncConfigPublic]:
        """Get all cloud sync configurations for a workspace"""
        try:
            configs = await self.repository.get_by_workspace(workspace_id)
            return [CloudSyncConfigPublic.model_validate(config) for config in configs]
        except Exception as e:
            logger.error(f"Error getting cloud sync configs for workspace: {str(e)}")
            raise

    async def update_config(
        self, workspace_id: UUID, config_id: UUID, data: CloudSyncConfigUpdate
    ) -> CloudSyncConfigPublic:
        """Update a specific cloud sync configuration"""
        try:
            # Get existing config to validate workspace ownership
            existing_config = await self.repository.get_config_by_id(config_id)
            if not existing_config:
                raise ValueError("Configuration not found")

            if existing_config.workspace_id != workspace_id:
                raise ValueError("Configuration does not belong to this workspace")

            # Validate selected resources if provided
            if data.selected_resources is not None:
                connection = await self.connection_repository.get_connection(
                    conn_id=existing_config.connection_id, workspace_id=workspace_id
                )
                if connection:
                    await self._validate_selected_resources(
                        data.selected_resources, connection
                    )

            # Update the configuration
            updated_config = await self.repository.update_config(config_id, data)
            if not updated_config:
                raise ValueError("Failed to update configuration")

            # Convert to public model first, while in session context
            public_config = CloudSyncConfigPublic.model_validate(updated_config)

            # Trigger scan if configuration was enabled
            if data.is_enabled is True or (
                data.is_enabled is None and updated_config.is_enabled
            ):
                await self._trigger_initial_scan_with_rate_limit(
                    workspace_id, updated_config.connection_id
                )

            return public_config
        except Exception as e:
            logger.error(f"Error updating cloud sync config: {str(e)}")
            raise

    async def delete_config(self, workspace_id: UUID, config_id: UUID) -> bool:
        """Delete a cloud sync configuration"""
        try:
            # Validate workspace ownership
            existing_config = await self.repository.get_config_by_id(config_id)
            if not existing_config:
                return False

            if existing_config.workspace_id != workspace_id:
                raise ValueError("Configuration does not belong to this workspace")

            return await self.repository.delete_config(config_id)
        except Exception as e:
            logger.error(f"Error deleting cloud sync config: {str(e)}")
            raise

    async def _trigger_initial_scan(
        self, workspace_id: UUID, connection_id: UUID
    ) -> None:
        """Trigger an initial resource scan for the workspace"""
        try:
            # Get the connection to determine cloud provider
            connection = await self.connection_repository.get_connection(
                conn_id=connection_id, workspace_id=workspace_id
            )

            if not connection:
                logger.warning(
                    f"Connection {connection_id} not found, skipping initial scan"
                )
                return

            # Map connection prefix to provider string
            provider_mapping = {
                "aws": "AWS",
                "gcp": "GCP",
                "azure": "AZURE",
            }

            provider_str = provider_mapping.get(connection.prefix)
            if not provider_str:
                logger.warning(
                    f"Unknown provider for connection prefix {connection.prefix}"
                )
                return

            # Trigger the multicloud scan task asynchronously
            scan_multicloud_resources_by_workspace.delay(
                workspace_id=workspace_id,
                providers=[provider_str],
                types=None,  # Will use configuration settings
            )

            logger.info(
                f"Triggered initial scan for workspace {workspace_id} with provider {provider_str}"
            )
        except Exception as e:
            logger.error(f"Error triggering initial scan: {str(e)}")
            # Don't raise the exception as this is a background operation

    def _get_scan_rate_limit_key(self, workspace_id: UUID) -> str:
        """Generate Redis key for scan rate limiting"""
        return f"scan_rate_limit:workspace:{workspace_id}"

    def _check_scan_rate_limit(self, workspace_id: UUID) -> None:
        """Check if workspace is rate limited for scanning"""
        rate_limit_key = self._get_scan_rate_limit_key(workspace_id)

        if self.redis_manager.is_rate_limited(
            rate_limit_key, self.SCAN_RATE_LIMIT_COUNT
        ):
            retry_after = self.redis_manager.get_rate_limit_ttl(rate_limit_key)
            current_count = self.redis_manager.get_rate_limit_count(rate_limit_key)
            raise RateLimitExceededException(
                f"Scan rate limit exceeded for workspace {workspace_id}. "
                f"You have used {current_count}/{self.SCAN_RATE_LIMIT_COUNT} scans in the current 10-minute window. "
                f"Please wait {retry_after} seconds before triggering another scan.",
                retry_after=retry_after,
            )

    def _set_scan_rate_limit(self, workspace_id: UUID) -> None:
        """Increment rate limit counter for workspace scanning"""
        rate_limit_key = self._get_scan_rate_limit_key(workspace_id)

        is_allowed, current_count = self.redis_manager.increment_rate_limit_counter(
            rate_limit_key, self.SCAN_RATE_LIMIT_TTL, self.SCAN_RATE_LIMIT_COUNT
        )

        if not is_allowed:
            retry_after = self.redis_manager.get_rate_limit_ttl(rate_limit_key)
            raise RateLimitExceededException(
                f"Scan rate limit exceeded for workspace {workspace_id}. "
                f"You have used {current_count}/{self.SCAN_RATE_LIMIT_COUNT} scans in the current 10-minute window. "
                f"Please wait {retry_after} seconds before triggering another scan.",
                retry_after=retry_after,
            )

        logger.info(
            f"Scan #{current_count}/{self.SCAN_RATE_LIMIT_COUNT} triggered for workspace {workspace_id}"
        )

    async def _trigger_initial_scan_with_rate_limit(
        self, workspace_id: UUID, connection_id: UUID
    ) -> None:
        """Trigger an initial resource scan with rate limiting"""
        try:
            # Check and increment rate limit counter (this will raise exception if limit exceeded)
            self._set_scan_rate_limit(workspace_id)

            # Trigger the actual scan
            await self._trigger_initial_scan(workspace_id, connection_id)

            # Update last sync time for the configuration
            await self._update_last_sync_time_for_connection(
                workspace_id, connection_id
            )

        except RateLimitExceededException:
            # Re-raise rate limit exceptions
            raise
        except Exception as e:
            logger.error(f"Error triggering initial scan with rate limit: {str(e)}")
            # Don't raise the exception as this is a background operation

    async def trigger_manual_sync(self, workspace_id: UUID, config_id: UUID) -> None:
        """Trigger a manual sync for a specific configuration with rate limiting"""
        try:
            # Get the configuration to validate it exists and belongs to the workspace
            existing_config = await self.repository.get_config_by_id(config_id)
            if not existing_config:
                raise ValueError("Configuration not found")

            if existing_config.workspace_id != workspace_id:
                raise ValueError("Configuration does not belong to this workspace")

            if not existing_config.is_enabled:
                raise ValueError("Configuration is disabled")

            # Trigger scan with rate limiting
            await self._trigger_initial_scan_with_rate_limit(
                workspace_id, existing_config.connection_id
            )

        except RateLimitExceededException:
            # Re-raise rate limit exceptions
            raise
        except Exception as e:
            logger.error(f"Error triggering manual sync: {str(e)}")
            raise

    async def _update_last_sync_time_for_connection(
        self, workspace_id: UUID, connection_id: UUID
    ) -> None:
        """Update last sync time for a configuration by workspace and connection"""
        try:
            config = await self.repository.get_by_workspace_and_connection(
                workspace_id, connection_id
            )
            if config:
                await self.repository.update_last_sync_time(
                    config.id, datetime.now(UTC)
                )
        except Exception as e:
            logger.error(f"Error updating last sync time: {str(e)}")
            # Don't raise as this is supplementary to the main operation

    async def update_last_sync_time(self, config_id: UUID) -> None:
        """Update the last sync timestamp for a configuration"""
        try:
            await self.repository.update_last_sync_time(config_id, datetime.now(UTC))
        except Exception as e:
            logger.error(
                f"Error updating last sync time for config {config_id}: {str(e)}"
            )
            raise
