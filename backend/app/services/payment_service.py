import hashlib
import json
from uuid import UUID

from fastapi import Request

from app.core.redis.redis_manager import RedisManager
from app.modules.payment.exceptions import PaymentServiceError
from app.modules.payment.payment_client import PaymentClient
from app.modules.payment.schema import (
    CheckoutSessionResponse,
    CustomerQuotaResponse,
    CustomerResponse,
    EnterpriseEnquiryRequest,
    EnterpriseEnquiryResponse,
    InvoiceResponse,
    PaymentMethodResponse,
    PlanChangeRequestCreate,
    PlanChangeRequestResponse,
    ProductResponse,
    SubscriptionStatus,
)


class PaymentService:
    def __init__(self, payment_client: PaymentClient):
        self.client = payment_client
        self.redis = RedisManager()

    def _get_cache_key(self, method: str, *args) -> str:
        """Generate cache key for payment service method"""
        key_data = f"payment:{method}:{':'.join(str(arg) for arg in args)}"
        return hashlib.md5(key_data.encode()).hexdigest()

    def _cache_get(self, key: str) -> dict | None:
        """Get from cache with error handling"""
        try:
            return self.redis.get(key)
        except Exception:
            return None

    def _cache_set(self, key: str, value: dict, ttl: int = 300) -> bool:
        """Set cache with error handling (default 5min TTL)"""
        try:
            return self.redis.set(key, value, ttl)
        except Exception:
            return False

    async def create_customer_for_user(
        self, user_id: UUID, email: str
    ) -> CustomerResponse:
        """Create a customer record for a user"""
        try:
            return await self.client.create_customer(user_id=user_id, email=email)
        except Exception as e:
            raise PaymentServiceError(f"Failed to create customer: {str(e)}")

    async def get_customer_by_user_id(self, user_id: UUID) -> CustomerResponse | None:
        """Get customer details by user ID with caching"""
        cache_key = self._get_cache_key("customer_by_user_id", str(user_id))
        
        # Try cache first
        cached_result = self._cache_get(cache_key)
        if cached_result:
            try:
                return CustomerResponse.model_validate(cached_result)
            except Exception:
                pass  # Cache corruption, continue to fresh call

        try:
            result = await self.client.get_customer_list(user_id)
            customer = result[0] if result else None
            
            # Cache the result for 10 minutes
            if customer:
                self._cache_set(cache_key, customer.model_dump(), ttl=600)
            
            return customer

        except Exception as e:
            raise PaymentServiceError(f"Failed to get customer by user ID: {str(e)}")

    async def get_user_subscription(
        self, customer_id: UUID
    ) -> list[SubscriptionStatus]:
        """Get user's subscription status"""
        try:
            return await self.client.get_subscription_status(customer_id)
        except Exception as e:
            raise PaymentServiceError(f"Failed to get subscription status: {str(e)}")

    async def get_user_quota(self, user_id: UUID) -> CustomerQuotaResponse:
        """Get user's quota with caching"""
        cache_key = self._get_cache_key("user_quota", str(user_id))
        
        # Try cache first (shorter TTL for quota data)
        cached_result = self._cache_get(cache_key)
        if cached_result:
            try:
                return CustomerQuotaResponse.model_validate(cached_result)
            except Exception:
                pass  # Cache corruption, continue to fresh call

        try:
            customer = await self.get_customer_by_user_id(user_id)
            if not customer:
                raise PaymentServiceError("Customer not found")

            quota_response = await self.client.get_customer_and_quota(UUID(customer.id))
            
            # Cache quota for 2 minutes (more frequent updates needed)
            self._cache_set(cache_key, quota_response.model_dump(), ttl=120)
            
            return quota_response
        except Exception as e:
            raise PaymentServiceError(f"Failed to get user quota: {str(e)}")

    async def create_subscription_checkout(
        self,
        customer_id: UUID,
        price_id: UUID,
        success_url: str,
        cancel_url: str,
    ) -> CheckoutSessionResponse:
        """Create a checkout session for subscription"""
        try:
            return await self.client.create_checkout_session(
                customer_id=customer_id,
                price_id=price_id,
                success_url=success_url,
                cancel_url=cancel_url,
            )
        except Exception as e:
            raise PaymentServiceError(f"Failed to create checkout session: {str(e)}")

    async def get_available_plans(self) -> list[ProductResponse]:
        """Get available subscription plans"""
        try:
            return await self.client.get_available_plans()
        except Exception as e:
            raise PaymentServiceError(f"Failed to get available plans: {str(e)}")

    async def get_plan_by_id(self, plan_id: UUID) -> ProductResponse:
        """Get a specific plan by ID"""
        try:
            return await self.client.get_plan_by_id(plan_id)
        except Exception as e:
            raise PaymentServiceError(f"Failed to get plan details: {str(e)}")

    async def submit_enterprise_enquiry(
        self,
        enquiry: EnterpriseEnquiryRequest,
        customer_id: UUID,
    ) -> EnterpriseEnquiryResponse:
        """Submit an enterprise plan enquiry"""
        try:
            return await self.client.submit_enterprise_enquiry(
                enquiry=enquiry, customer_id=customer_id
            )
        except Exception as e:
            raise PaymentServiceError(f"Failed to submit enterprise enquiry: {str(e)}")

    async def get_customer_enterprise_enquiries(
        self,
        customer_id: UUID,
        status: str | None = None,
        product_id: UUID | None = None,
    ) -> list[EnterpriseEnquiryResponse]:
        """Get enterprise enquiries for a customer"""
        try:
            return await self.client.get_customer_enterprise_enquiries(
                customer_id=customer_id, status=status, product_id=product_id
            )
        except Exception as e:
            raise PaymentServiceError(
                f"Failed to get customer enterprise enquiries: {str(e)}"
            )

    async def get_customer_payment_methods(
        self, customer_id: UUID, payment_type: str = "card"
    ) -> list[PaymentMethodResponse]:
        """Get customer's payment methods"""
        try:
            return await self.client.get_customer_payment_methods(
                customer_id, payment_type
            )
        except Exception as e:
            raise PaymentServiceError(
                f"Failed to get customer payment methods: {str(e)}"
            )

    async def get_customer_invoices(
        self, customer_id: UUID, limit: int = 10, status: str | None = None
    ) -> list[InvoiceResponse]:
        """Get customer's invoices"""
        try:
            return await self.client.get_customer_invoices(customer_id, limit, status)
        except Exception as e:
            raise PaymentServiceError(f"Failed to get customer invoices: {str(e)}")

    async def submit_plan_change_request(
        self,
        request: PlanChangeRequestCreate,
        customer_id: UUID,
        requested_product_id: UUID,
    ) -> PlanChangeRequestResponse:
        """Submit a plan change request"""
        try:
            return await self.client.submit_plan_change_request(
                request=request,
                customer_id=customer_id,
                requested_product_id=requested_product_id,
            )
        except Exception as e:
            raise PaymentServiceError(f"Failed to submit plan change request: {str(e)}")

    async def get_customer_plan_change_requests(
        self,
        customer_id: UUID,
        status: str | None = None,
    ) -> list[PlanChangeRequestResponse]:
        """Get plan change requests for a customer"""
        try:
            return await self.client.get_customer_plan_change_requests(
                customer_id=customer_id, status=status
            )
        except Exception as e:
            raise PaymentServiceError(
                f"Failed to get customer plan change requests: {str(e)}"
            )

    async def handle_webhook(self, request: Request) -> dict:
        """Handle webhook events from payment provider"""
        try:
            return await self.client.forward_webhook(request)
        except Exception as e:
            raise PaymentServiceError(f"Failed to handle webhook: {str(e)}")

    async def cancel_subscription(self, customer_id: UUID) -> dict:
        """Cancel subscription for a customer"""
        try:
            return await self.client.cancel_subscription(customer_id)
        except Exception as e:
            raise PaymentServiceError(f"Failed to cancel subscription: {str(e)}")
