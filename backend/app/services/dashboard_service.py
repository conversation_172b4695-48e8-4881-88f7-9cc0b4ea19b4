import json
from typing import cast
from uuid import UUID

from app.api.deps import get_async_session
from app.exceptions.legacy import RepositoryError
from app.logger import logger
from app.modules.multi_agents.tools.dashboard.schema import (
    DashboardWidget,
    GridConfig,
    LayoutConfig,
)
from app.repositories.dashboard import DashboardRepository


class DashboardService:
    """Service for managing dashboard operations with direct database interaction."""

    @staticmethod
    def _transform_widgets_data(
        widgets: list[DashboardWidget] | list[dict] | None,
    ) -> list[dict]:
        """Transform widgets to JSON serializable dicts."""
        if not widgets:
            return []

        transformed: list[dict] = []
        for widget in widgets:
            if isinstance(widget, dict):
                transformed.append(widget.copy())
            else:
                transformed.append(widget.model_dump())
        return transformed

    @staticmethod
    def _extract_positions(
        widgets: list[dict],
    ) -> set[tuple[int, int]]:
        """Build a set of occupied grid cells from widgets' layouts."""
        occupied: set[tuple[int, int]] = set()
        for widget in widgets:
            layout = widget.get("layout") or {}
            if not all(k in layout for k in ("x", "y", "w", "h")):
                continue
            x, y, w, h = layout["x"], layout["y"], layout["w"], layout["h"]
            xi = DashboardService._safe_int(x)
            yi = DashboardService._safe_int(y)
            wi = DashboardService._safe_int(w)
            hi = DashboardService._safe_int(h)
            if None in (xi, yi, wi, hi):
                continue
            wii = cast(int, wi)
            hii = cast(int, hi)
            xii = cast(int, xi)
            yii = cast(int, yi)
            for dx in range(wii):
                for dy in range(hii):
                    occupied.add((xii + dx, yii + dy))
        return occupied

    @staticmethod
    def _safe_int(value: object) -> int | None:
        """Convert common number-like types to int; return None if not convertible."""
        if isinstance(value, bool):
            return int(value)
        if isinstance(value, int):
            return value
        if isinstance(value, float):
            return int(value)
        if isinstance(value, str):
            try:
                return int(value)
            except ValueError:
                return None
        return None

    @staticmethod
    async def get_or_create_dashboard(
        workspace_id: UUID,
        conversation_id: UUID,
    ) -> str:
        """Get existing dashboard or create a new one."""
        try:
            async for async_session in get_async_session():
                repo = DashboardRepository(async_session)
                try:
                    dashboard = await repo.get_by_conversation_id(
                        conversation_id, workspace_id
                    )
                except RepositoryError as e:
                    if e.status_code == 404:
                        dashboard = await repo.create_or_update(
                            {
                                "title": "",
                                "description": "",
                                "grid_config": {"columns": 12},
                                "widgets": [],
                            },
                            workspace_id,
                            conversation_id,
                        )
                    else:
                        raise

                return json.dumps(
                    {
                        "status": "success",
                        "message": "Dashboard fetched successfully",
                        "data": dashboard.model_dump(mode="json"),
                    }
                )
        except Exception as e:
            logger.error(f"Failed to fetch dashboard: {e}")
            return json.dumps(
                {
                    "status": "error",
                    "message": f"Failed to fetch dashboard: {str(e)}",
                }
            )
        # Fallback return for type-checkers
        return json.dumps({"status": "error", "message": "Failed to fetch dashboard"})

    @staticmethod
    async def create_dashboard(
        workspace_id: UUID,
        conversation_id: UUID,
        title: str | None,
        description: str | None,
        grid_config: GridConfig | None,
        widgets: list[DashboardWidget] | list[dict] | None,
    ) -> str:
        if not title:
            return json.dumps(
                {"status": "error", "message": "Title is required to create dashboard"}
            )

        try:
            async for async_session in get_async_session():
                repo = DashboardRepository(async_session)
                dashboard_data = {
                    "title": title or "",
                    "description": description or "",
                    "grid_config": (
                        grid_config.model_dump() if grid_config else {"columns": 12}
                    ),
                    "widgets": DashboardService._transform_widgets_data(widgets),
                }
                dashboard = await repo.create_or_update(
                    dashboard_data, workspace_id, conversation_id
                )
                return json.dumps(
                    {
                        "status": "success",
                        "message": "Dashboard created successfully",
                        "data": dashboard.model_dump(mode="json"),
                    }
                )
        except Exception as e:
            logger.error(f"Failed to create dashboard: {e}")
            return json.dumps(
                {
                    "status": "error",
                    "message": f"Failed to create dashboard: {str(e)}",
                }
            )
        return json.dumps({"status": "error", "message": "Failed to create dashboard"})

    @staticmethod
    async def add_widgets(
        workspace_id: UUID,
        conversation_id: UUID,
        widgets: list[DashboardWidget] | list[dict] | None,
    ) -> str:
        if not widgets:
            return json.dumps(
                {"status": "error", "message": "No widgets provided to add"}
            )

        try:
            async for async_session in get_async_session():
                repo = DashboardRepository(async_session)
                current = await repo.get_by_conversation_id(
                    conversation_id, workspace_id
                )
                current_widgets: list[dict] = list(current.widgets or [])

                new_widgets = DashboardService._transform_widgets_data(widgets)

                occupied = DashboardService._extract_positions(current_widgets)
                for widget in new_widgets:
                    layout = widget.get("layout") or {}
                    x, y, w, h = (
                        layout.get("x"),
                        layout.get("y"),
                        layout.get("w"),
                        layout.get("h"),
                    )
                    if None in (x, y, w, h):
                        continue
                    xi = DashboardService._safe_int(x)
                    yi = DashboardService._safe_int(y)
                    wi = DashboardService._safe_int(w)
                    hi = DashboardService._safe_int(h)
                    if None in (xi, yi, wi, hi):
                        continue
                    wii = cast(int, wi)
                    hii = cast(int, hi)
                    xii = cast(int, xi)
                    yii = cast(int, yi)
                    for dx in range(wii):
                        for dy in range(hii):
                            pos = (xii + dx, yii + dy)
                            if pos in occupied:
                                return json.dumps(
                                    {
                                        "status": "error",
                                        "message": f"Widget overlaps with existing widget at position {pos}",
                                    }
                                )
                    # reserve
                    for dx in range(wii):
                        for dy in range(hii):
                            occupied.add((xii + dx, yii + dy))

                updated_widgets = current_widgets + new_widgets
                dashboard = await repo.create_or_update(
                    {
                        "title": current.title,
                        "description": current.description,
                        "grid_config": current.grid_config or {"columns": 12},
                        "widgets": updated_widgets,
                    },
                    workspace_id,
                    conversation_id,
                )

                return json.dumps(
                    {
                        "status": "success",
                        "message": f"Added {len(new_widgets)} widget(s) successfully",
                        "data": dashboard.model_dump(mode="json"),
                    }
                )
        except Exception as e:
            logger.error(f"Failed to add widgets: {e}")
            return json.dumps(
                {"status": "error", "message": f"Failed to add widgets: {str(e)}"}
            )
        return json.dumps({"status": "error", "message": "Failed to add widgets"})

    @staticmethod
    async def update_widgets(
        workspace_id: UUID,
        conversation_id: UUID,
        widgets: list[DashboardWidget] | list[dict] | None,
    ) -> str:
        if not widgets:
            return json.dumps(
                {"status": "error", "message": "No widgets provided to update"}
            )

        try:
            async for async_session in get_async_session():
                repo = DashboardRepository(async_session)
                current = await repo.get_by_conversation_id(
                    conversation_id, workspace_id
                )
                current_widgets: list[dict] = list(current.widgets or [])

                updated_count = 0
                incoming: list[dict] = DashboardService._transform_widgets_data(widgets)

                # Build a new list without in-place item assignment to satisfy strict typing
                new_current_widgets: list[dict] = []
                for existing_widget in current_widgets:
                    existing_layout = (existing_widget or {}).get("layout", {})
                    replaced = False
                    for new_widget in incoming:
                        new_layout = (new_widget or {}).get("layout", {})
                        if existing_layout.get("x") == new_layout.get(
                            "x"
                        ) and existing_layout.get("y") == new_layout.get("y"):
                            new_current_widgets.append(new_widget)
                            updated_count += 1
                            replaced = True
                            break
                    if not replaced:
                        new_current_widgets.append(existing_widget)

                dashboard = await repo.create_or_update(
                    {
                        "title": current.title,
                        "description": current.description,
                        "grid_config": current.grid_config or {"columns": 12},
                        "widgets": new_current_widgets,
                    },
                    workspace_id,
                    conversation_id,
                )

                return json.dumps(
                    {
                        "status": "success",
                        "message": f"Updated {updated_count} widget(s) successfully",
                        "data": dashboard.model_dump(mode="json"),
                    }
                )
        except Exception as e:
            logger.error(f"Failed to update widgets: {e}")
            return json.dumps(
                {
                    "status": "error",
                    "message": f"Failed to update widgets: {str(e)}",
                }
            )
        return json.dumps({"status": "error", "message": "Failed to update widgets"})

    @staticmethod
    async def remove_widgets_by_positions(
        workspace_id: UUID,
        conversation_id: UUID,
        widget_positions: list[LayoutConfig] | list[dict] | None,
    ) -> str:
        if not widget_positions:
            return json.dumps(
                {
                    "status": "error",
                    "message": "No widget positions provided to remove",
                }
            )

        try:
            async for async_session in get_async_session():
                repo = DashboardRepository(async_session)
                current = await repo.get_by_conversation_id(
                    conversation_id, workspace_id
                )

                to_remove: set[tuple[int, int]] = set()
                for pos in widget_positions:
                    if isinstance(pos, dict):
                        x, y = pos.get("x"), pos.get("y")
                    else:
                        x, y = pos.x, pos.y
                    if x is not None and y is not None:
                        to_remove.add((int(x), int(y)))

                filtered = [
                    w
                    for w in (current.widgets or [])
                    if (w.get("layout", {}).get("x"), w.get("layout", {}).get("y"))
                    not in to_remove
                ]

                dashboard = await repo.create_or_update(
                    {
                        "title": current.title,
                        "description": current.description,
                        "grid_config": current.grid_config or {"columns": 12},
                        "widgets": filtered,
                    },
                    workspace_id,
                    conversation_id,
                )

                removed_count = len(current.widgets or []) - len(filtered)
                return json.dumps(
                    {
                        "status": "success",
                        "message": f"Removed {removed_count} widget(s) successfully",
                        "data": dashboard.model_dump(mode="json"),
                    }
                )
        except Exception as e:
            logger.error(f"Failed to remove widgets: {e}")
            return json.dumps(
                {
                    "status": "error",
                    "message": f"Failed to remove widgets: {str(e)}",
                }
            )
        return json.dumps({"status": "error", "message": "Failed to remove widgets"})

    @staticmethod
    async def update_grid(
        workspace_id: UUID,
        conversation_id: UUID,
        grid_config: GridConfig | None,
    ) -> str:
        if not grid_config:
            return json.dumps(
                {"status": "error", "message": "No grid configuration provided"}
            )

        try:
            async for async_session in get_async_session():
                repo = DashboardRepository(async_session)
                current = await repo.get_by_conversation_id(
                    conversation_id, workspace_id
                )

                dashboard = await repo.create_or_update(
                    {
                        "title": current.title,
                        "description": current.description,
                        "grid_config": grid_config.model_dump(),
                        "widgets": current.widgets or [],
                    },
                    workspace_id,
                    conversation_id,
                )

                return json.dumps(
                    {
                        "status": "success",
                        "message": "Dashboard grid configuration updated successfully",
                        "data": dashboard.model_dump(mode="json"),
                    }
                )
        except Exception as e:
            logger.error(f"Failed to update grid: {e}")
            return json.dumps(
                {
                    "status": "error",
                    "message": f"Failed to update grid: {str(e)}",
                }
            )
        return json.dumps({"status": "error", "message": "Failed to update grid"})
