"""
Utility classes and helper functions for KB services
"""

import base64
import re
from typing import Any
from urllib.parse import urlparse

from app.logger import logger


class ContentProcessor:
    """Utility class for processing various content types"""

    @staticmethod
    def decode_base64_image(image_data: str) -> bytes:
        """
        Safely decode base64 image data

        Args:
            image_data: Base64 encoded image string

        Returns:
            Decoded image bytes

        Raises:
            ValueError: If decoding fails
        """
        try:
            return base64.b64decode(image_data)
        except Exception as e:
            logger.error(f"Failed to decode base64 image: {str(e)}")
            raise ValueError("Invalid base64 image data")

    @staticmethod
    def extract_text_from_nodes(nodes: list) -> list[str]:
        """
        Extract text content from a list of nodes

        Args:
            nodes: List of node objects

        Returns:
            List of text strings
        """
        text_nodes = []

        for node in nodes:
            node_content = node.node if hasattr(node, "node") else node

            if hasattr(node_content, "text"):
                text_content = node_content.text
            else:
                text_content = str(node_content)

            if text_content and text_content.strip():
                text_nodes.append(text_content)

        return text_nodes

    @staticmethod
    def format_context_text(text_nodes: list[str], separator: str = "\n\n") -> str:
        """
        Format text nodes into a single context string

        Args:
            text_nodes: List of text strings
            separator: String to join text nodes

        Returns:
            Formatted context string
        """
        return separator.join(text_nodes)


class ValidationUtils:
    """Utility class for validation operations"""

    @staticmethod
    def validate_url(url: str) -> None:
        """
        Comprehensive URL validation for security

        Args:
            url: URL string to validate

        Raises:
            ValueError: If URL is invalid or unsafe
        """
        if not url or len(url) > 2048:
            raise ValueError("URL is empty or too long")

        if not url.startswith(("http://", "https://")):
            raise ValueError("URL must start with http:// or https://")

        try:
            parsed = urlparse(url)
            hostname = parsed.hostname

            if not hostname:
                raise ValueError("Invalid URL hostname")

            # Block localhost and private IPs
            hostname_lower = hostname.lower()
            if (
                hostname_lower in ["localhost", "127.0.0.1", "::1"]
                or re.match(r"^10\.", hostname_lower)
                or re.match(r"^192\.168\.", hostname_lower)
                or re.match(r"^172\.(1[6-9]|2[0-9]|3[0-1])\.", hostname_lower)
                or re.match(r"^169\.254\.", hostname_lower)
            ):
                raise ValueError("Private or localhost URLs are not allowed")

            # Check for suspicious patterns
            suspicious_patterns = [
                r"javascript:",
                r"data:",
                r"vbscript:",
                r"<script",
                r"onload=",
                r"onerror=",
            ]
            for pattern in suspicious_patterns:
                if re.search(pattern, url, re.IGNORECASE):
                    raise ValueError("URL contains suspicious content")

        except Exception as e:
            if isinstance(e, ValueError):
                raise
            raise ValueError("Invalid URL format")

    @staticmethod
    def validate_kb_ids(kb_ids: list[str]) -> list[str]:
        """
        Validate and clean knowledge base IDs

        Args:
            kb_ids: List of KB ID strings

        Returns:
            Deduplicated list of valid KB IDs

        Raises:
            ValueError: If no valid KB IDs provided
        """
        if not kb_ids:
            raise ValueError("At least one KB ID must be provided")

        # Remove duplicates and empty strings
        clean_ids = list(
            set(kb_id.strip() for kb_id in kb_ids if kb_id and kb_id.strip())
        )

        if not clean_ids:
            raise ValueError("No valid KB IDs provided")

        return clean_ids

    @staticmethod
    def validate_workspace_id(workspace_id: str) -> str:
        """
        Validate workspace ID

        Args:
            workspace_id: Workspace identifier

        Returns:
            Cleaned workspace ID

        Raises:
            ValueError: If workspace ID is invalid
        """
        if not workspace_id or not workspace_id.strip():
            raise ValueError("Workspace ID cannot be empty")

        return workspace_id.strip()


class MetricsCollector:
    """Utility class for collecting performance and usage metrics"""

    def __init__(self):
        self.metrics = {}

    def start_timer(self, operation: str) -> None:
        """Start timing an operation"""
        import time

        self.metrics[f"{operation}_start_time"] = time.time()

    def end_timer(self, operation: str) -> float:
        """End timing an operation and return duration"""
        import time

        start_time = self.metrics.get(f"{operation}_start_time")
        if start_time:
            duration = time.time() - start_time
            self.metrics[f"{operation}_duration"] = duration
            return duration
        return 0.0

    def record_metric(self, key: str, value: Any) -> None:
        """Record a metric value"""
        self.metrics[key] = value

    def get_metrics(self) -> dict[str, Any]:
        """Get all collected metrics"""
        return self.metrics.copy()

    def reset(self) -> None:
        """Reset all metrics"""
        self.metrics.clear()


class ConfigurationManager:
    """Utility class for managing service configurations"""

    @staticmethod
    def get_search_config() -> dict[str, Any]:
        """Get search service configuration from settings"""
        from app.core.config import settings

        return {
            "search_limit": getattr(settings, "KB_SEARCH_LIMIT", 20),
            "sparse_top_k": getattr(settings, "SPARSE_TOP_K", 10),
            "score_threshold": getattr(settings, "KB_SEARCH_SCORE_THRESHOLD", 0.7),
            "rerank_top_n": getattr(settings, "KB_RERANK_TOP_N", 10),
            "rerank_model": getattr(settings, "KB_RERANK_MODEL_ID", ""),
            "rerank_region": getattr(settings, "KB_RERANK_REGION_NAME", "us-east-1"),
            "synthesize_model": getattr(settings, "KB_SYNTHESIZE_MODEL", ""),
            "embedding_region": getattr(settings, "EMBEDDING_REGION_NAME", "us-east-1"),
        }

    @staticmethod
    def get_ingestion_config() -> dict[str, Any]:
        """Get ingestion service configuration from settings"""
        from app.core.config import settings

        return {
            "kb_bucket": getattr(settings, "KB_BUCKET", ""),
            "collection_name": getattr(settings, "QDRANT_COLLECTION_NAME", ""),
            "chunker_config": {
                "include_metadata": True,
                "include_prev_next_rel": True,
            },
        }

    @staticmethod
    def get_deletion_config() -> dict[str, Any]:
        """Get deletion service configuration from settings"""
        from app.core.config import settings

        return {
            "collection_name": getattr(settings, "QDRANT_COLLECTION_NAME", ""),
            "wait_for_completion": True,
        }
