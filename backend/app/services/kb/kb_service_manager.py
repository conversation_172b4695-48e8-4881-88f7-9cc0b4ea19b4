"""
KBServiceManager - Facade that coordinates all KB services
"""

from typing import Any, Callable
from uuid import UUID

from sqlmodel.ext.asyncio.session import AsyncSession

from .deletion_service import DeletionService
from .ingestion_service import IngestionService
from .search_service import SearchService


class KBServiceManager:
    """
    Facade that provides a unified interface to all KB services

    This class maintains backward compatibility with the original KBService
    while delegating to specialized service classes.
    """

    def __init__(
        self,
        search_service: SearchService,
        ingestion_service: IngestionService,
        deletion_service: DeletionService,
    ):
        self.search_service = search_service
        self.ingestion_service = ingestion_service
        self.deletion_service = deletion_service

    # Search operations
    async def search(
        self,
        query: str,
        kb_ids: list[str],
        workspace_id: str,
    ) -> dict[str, Any]:
        """
        Search knowledge bases and return synthesized response

        Delegates to SearchService.search()
        """
        return await self.search_service.search(query, kb_ids, workspace_id)

    # Ingestion operations
    async def ingest_from_website(
        self,
        session: AsyncSession,
        doc_ids: list[str],
        kb_id: str,
        call_back: Callable,
        user_id: UUID | None = None,
        workspace_id: UUID | None = None,
    ) -> dict[str, Any]:
        """
        Ingest documents from website URLs

        Delegates to IngestionService.ingest_from_website()
        """
        return await self.ingestion_service.ingest_from_website(
            session=session,
            doc_ids=doc_ids,
            kb_id=kb_id,
            callback=call_back,
            user_id=user_id,
            workspace_id=workspace_id,
        )

    async def ingest_from_files(
        self,
        session: AsyncSession,
        doc_ids: list[str],
        kb_id: str,
        call_back: Callable,
        user_id: UUID | None = None,
        workspace_id: UUID | None = None,
    ) -> dict[str, Any]:
        """
        Ingest documents from uploaded files

        Delegates to IngestionService.ingest_from_files()
        """
        return await self.ingestion_service.ingest_from_files(
            session=session,
            doc_ids=doc_ids,
            kb_id=kb_id,
            callback=call_back,
            user_id=user_id,
            workspace_id=workspace_id,
        )

    # Deletion operations
    async def delete_workspace_documents(self, workspace_id: str) -> bool:
        """
        Delete all documents for a workspace from the vector store

        Delegates to DeletionService.delete_workspace_documents()
        """
        return await self.deletion_service.delete_workspace_documents(workspace_id)

    async def delete_documents_by_document_id(self, document_id: str) -> bool:
        """
        Delete documents by document ID from vector store

        Delegates to DeletionService.delete_documents_by_document_id()
        """
        return await self.deletion_service.delete_documents_by_document_id(document_id)

    async def delete_kb_documents(self, workspace_id: str, kb_id: str) -> bool:
        """
        Delete all documents for a specific KB from the vector store

        Delegates to DeletionService.delete_kb_documents()
        """
        return await self.deletion_service.delete_kb_documents(workspace_id, kb_id)

    # Service access for advanced usage
    def get_search_service(self) -> SearchService:
        """Get direct access to search service"""
        return self.search_service

    def get_ingestion_service(self) -> IngestionService:
        """Get direct access to ingestion service"""
        return self.ingestion_service

    def get_deletion_service(self) -> DeletionService:
        """Get direct access to deletion service"""
        return self.deletion_service
