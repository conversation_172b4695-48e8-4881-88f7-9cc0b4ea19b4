"""
Base classes and dependency injection for KB services
"""

from typing import Any
from weakref import We<PERSON><PERSON>eyDictionary

from sqlmodel.ext.asyncio.session import AsyncSession

from app.services.base_vector_store import BaseVectorStore

# Constants
IMAGE_MIME_TYPE = "image/png"

# Cache for KB services - using WeakKeyDictionary to avoid memory leaks
_kb_services_cache: WeakKeyDictionary = WeakKeyDictionary()


def clear_kb_services_cache() -> None:
    """Clear the KB services cache - useful for testing"""
    global _kb_services_cache
    _kb_services_cache.clear()


def get_cache_stats() -> dict[str, int]:
    """Get cache statistics - useful for monitoring and debugging"""
    total_sessions = len(_kb_services_cache)
    total_entries = sum(
        len(session_cache) for session_cache in _kb_services_cache.values()
    )
    return {
        "total_sessions": total_sessions,
        "total_entries": total_entries,
    }


class BaseKBService:
    """Base class for all KB services"""

    def __init__(self, vector_store: BaseVectorStore, session: AsyncSession):
        self.vector_store = vector_store
        self.session = session


def create_kb_services(
    vector_store: BaseVectorStore | None = None,
    session: AsyncSession | None = None,
) -> dict[str, Any]:
    """
    Create KB services with dependencies (cached)

    Args:
        vector_store: Vector store instance (creates new if None)
        session: Database session (required)

    Returns:
        Dictionary with all KB services

    Raises:
        ValueError: If session is not provided
    """
    if session is None:
        raise ValueError("Database session is required")

    if vector_store is None:
        vector_store = BaseVectorStore()

    # Create cache key using tuple of object ids
    cache_key = (id(vector_store), id(session))
    # Check if services are already cached for this session
    if session in _kb_services_cache and cache_key in _kb_services_cache[session]:
        return _kb_services_cache[session][cache_key]

    # Import services here to avoid circular imports
    from .deletion_service import DeletionService
    from .ingestion_service import IngestionService
    from .kb_service_manager import KBServiceManager
    from .search_service import SearchService

    # Create services
    search_service = SearchService(vector_store, session)
    ingestion_service = IngestionService(vector_store, session)
    deletion_service = DeletionService(vector_store, session)

    # Create manager
    service_manager = KBServiceManager(
        search_service=search_service,
        ingestion_service=ingestion_service,
        deletion_service=deletion_service,
    )

    services = {
        "search": search_service,
        "ingestion": ingestion_service,
        "deletion": deletion_service,
        "manager": service_manager,
    }

    # Cache the services
    if session not in _kb_services_cache:
        _kb_services_cache[session] = {}
    _kb_services_cache[session][cache_key] = services

    return services
