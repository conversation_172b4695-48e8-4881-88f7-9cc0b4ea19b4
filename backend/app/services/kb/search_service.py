"""
SearchService - Handles vector search and result synthesis for knowledge bases
"""

import base64
import time
from typing import Any

from llama_index.core import VectorStoreIndex
from llama_index.core.base.llms.types import ChatMessage, ImageBlock, TextBlock
from llama_index.core.schema import NodeWithScore, QueryBundle
from llama_index.core.vector_stores.types import (
    FilterCondition,
    MetadataFilter,
    MetadataFilters,
    VectorStoreQueryMode,
)
from llama_index.llms.langchain import LangChainLLM

# from llama_index.llms.bedrock import Bedrock
# from llama_index.llms.cohere import Cohere
from llama_index.postprocessor.bedrock_rerank import AWSBedrockRerank

from app.core.config import settings
from app.exceptions.kb_exceptions import (
    ImageProcessingError,
    SearchServiceError,
    SynthesisError,
)
from app.logger import logger
from app.modules.multi_agents.core.utils import load_chat_model

from .base import IMAGE_MIME_TYPE, BaseKBService


class SearchService(BaseKBService):
    """Service responsible for vector search and result synthesis"""

    async def search(
        self,
        query: str,
        kb_ids: list[str],
        workspace_id: str,
    ) -> dict[str, Any]:
        """
        Search knowledge bases and return synthesized response

        Args:
            query: Search query string
            kb_ids: List of knowledge base IDs to search
            workspace_id: Workspace identifier for filtering

        Returns:
            Dictionary containing response, metadata, and search statistics

        Raises:
            SearchServiceError: If search operation fails
        """
        start_time = time.time()
        kb_ids = list(set(kb_ids))  # Remove duplicates

        logger.info(
            f"Searching knowledge bases {kb_ids} {workspace_id} with query {query}"
        )

        try:
            vector_store = await self.vector_store.get_vector_store()
            index = VectorStoreIndex.from_vector_store(
                vector_store=vector_store,
                embed_model=self.vector_store.embed_model_query,
            )

            # Build metadata filters
            filters = self._build_search_filters(kb_ids, workspace_id)

            # Step 1: Retrieve all nodes without similarity cutoff
            all_nodes = self._retrieve_nodes(index, query, kb_ids, filters)

            # Step 2: Filter nodes by similarity cutoff
            filtered_nodes = self._filter_nodes_by_similarity(all_nodes)

            # Step 3: Rerank the filtered nodes
            reranked_nodes = self._rerank_nodes(query, filtered_nodes)

            # Step 4: Synthesize the final response
            response = await self._synthesize_response(query, reranked_nodes)

            search_time = time.time() - start_time
            logger.info(f"Response generation time: {search_time:.2f} seconds")

            return {
                "response": response,
                "searched_kbs": len(kb_ids),
                "total_nodes": len(reranked_nodes),
                "search_time": search_time,
                "errors": None,
            }

        except Exception as e:
            logger.error(f"Error searching knowledge bases: {str(e)}")
            raise SearchServiceError("Search service temporarily unavailable")

    def _build_search_filters(
        self, kb_ids: list[str], workspace_id: str
    ) -> MetadataFilters:
        """Build metadata filters for search query"""

        # KB filters - any of the specified KBs
        kb_filters = MetadataFilters(
            filters=[MetadataFilter(key="kb_id", value=kb_id) for kb_id in kb_ids],
            condition=FilterCondition.OR,
        )

        # Workspace filter - must be from this workspace
        workspace_filter = MetadataFilter(
            key="workspace_id",
            value=workspace_id,
        )

        # Combine filters: workspace AND (kb1 OR kb2 OR kb3...)
        return MetadataFilters(
            filters=[workspace_filter, kb_filters],
            condition=FilterCondition.AND,
        )

    def _retrieve_nodes(
        self,
        index: VectorStoreIndex,
        query: str,
        kb_ids: list[str],
        filters: MetadataFilters,
    ) -> list[NodeWithScore]:
        """Retrieve all nodes without similarity cutoff"""

        query_engine = index.as_query_engine(
            llm=LangChainLLM(llm=load_chat_model(settings.KB_SYNTHESIZE_MODEL)),
            similarity_top_k=settings.KB_SEARCH_LIMIT * len(kb_ids),
            sparse_top_k=settings.SPARSE_TOP_K,
            vector_store_query_mode=VectorStoreQueryMode.HYBRID,
            similarity_cutoff=0.0,  # No cutoff at retrieval stage
            filters=filters,
        )

        nodes = query_engine.retrieve(QueryBundle(query_str=query))
        logger.info(f"Retrieved {len(nodes)} nodes before filtering")
        return nodes

    def _filter_nodes_by_similarity(
        self, nodes: list[NodeWithScore]
    ) -> list[NodeWithScore]:
        """Filter nodes by similarity cutoff threshold"""

        filtered_nodes = [
            node
            for node in nodes
            if node.score is not None
            and node.score >= settings.KB_SEARCH_SCORE_THRESHOLD
        ]

        logger.info(
            f"Filtered to {len(filtered_nodes)} nodes above threshold "
            f"{settings.KB_SEARCH_SCORE_THRESHOLD}"
        )
        return filtered_nodes

    def _rerank_nodes(
        self, query: str, nodes: list[NodeWithScore]
    ) -> list[NodeWithScore]:
        """Rerank the filtered nodes using AWS Bedrock Rerank"""

        if not nodes:
            return nodes

        reranker = AWSBedrockRerank(
            top_n=settings.KB_RERANK_TOP_N,
            model=settings.KB_RERANK_MODEL_ID,
            region_name=settings.KB_RERANK_REGION_NAME,
        )

        reranked_nodes = reranker.postprocess_nodes(nodes, QueryBundle(query_str=query))

        logger.info(f"Reranked to top {len(reranked_nodes)} nodes")
        for i, node in enumerate(reranked_nodes):
            logger.info(f"Reranked Node {i} with score {node.score}")

        return reranked_nodes

    async def _synthesize_response(self, query: str, nodes: list[NodeWithScore]) -> str:
        """Synthesize final response from ranked nodes"""

        try:
            # Separate image and text nodes
            text_nodes, image_content = self._separate_nodes_by_type(nodes)

            # Create content blocks based on content type
            content_blocks = self._create_content_blocks(
                query, text_nodes, image_content
            )

            # Generate response from model
            return await self._generate_model_response(content_blocks)

        except (ImageProcessingError, SynthesisError):
            raise
        except Exception as e:
            logger.error(f"Error in response synthesis: {str(e)}")
            # Fallback to text-only synthesis
            return self._create_fallback_response(query, nodes)

    def _separate_nodes_by_type(
        self, nodes: list[NodeWithScore]
    ) -> tuple[list[str], list[dict[str, Any]]]:
        """Separate nodes into text and image content"""

        text_nodes = []
        image_content = []

        for node in nodes:
            node_content = node.node if hasattr(node, "node") else node

            if hasattr(node_content, "metadata") and node_content.metadata:
                metadata = node_content.metadata
                is_image = metadata.get("is_image", False)

                if is_image and "image_base64" in metadata:
                    # This is an image node
                    image_data = metadata["image_base64"]
                    page_info = metadata.get("page_info", "1")
                    file_name = metadata.get("file_name", "document")

                    image_content.append(
                        {
                            "type": "image",
                            "source_type": "base64",
                            "data": image_data,
                            "mime_type": IMAGE_MIME_TYPE,
                            "metadata": {
                                "source": f"{file_name}_{page_info}",
                                "file_name": file_name,
                                "page_info": page_info,
                            },
                        }
                    )
                    logger.info(f"Found image node: {file_name}_{page_info}")
                else:
                    # Regular text node
                    text_content = (
                        node_content.text
                        if hasattr(node_content, "text")
                        else str(node_content)
                    )
                    if text_content.strip():
                        text_nodes.append(text_content)
            else:
                # Fallback to text
                text_content = (
                    node_content.text
                    if hasattr(node_content, "text")
                    else str(node_content)
                )
                if text_content.strip():
                    text_nodes.append(text_content)

        logger.info(
            f"Processing {len(text_nodes)} text nodes and "
            f"{len(image_content)} image nodes"
        )
        return text_nodes, image_content

    def _create_content_blocks(
        self,
        query: str,
        text_nodes: list[str],
        image_content: list[dict[str, Any]],
    ) -> list[TextBlock] | list[ImageBlock]:
        """Create content blocks based on available content types"""

        if text_nodes and not image_content:
            # Text-only case
            return self._create_text_only_content(query, text_nodes)
        elif image_content and not text_nodes:
            # Image-only case
            return self._create_image_only_content(query, image_content)
        elif text_nodes and image_content:
            # Mixed content case
            return self._create_mixed_content(query, text_nodes, image_content)
        else:
            # No content case
            return self._create_no_content_blocks(query)

    def _create_text_only_content(
        self, query: str, text_nodes: list[str]
    ) -> list[TextBlock]:
        """Create content blocks for text-only synthesis"""

        context_text = "\n\n".join(text_nodes)
        prompt = (
            f"Question: {query}\n\n"
            f"Relevant context from documents:\n{context_text}\n\n"
            f"Please provide a comprehensive answer based on the context above."
        )
        return [TextBlock(text=prompt)]

    def _create_image_only_content(
        self, query: str, image_content: list[dict[str, Any]]
    ) -> list[TextBlock] | list[ImageBlock]:
        """Create content blocks for image-only synthesis"""

        content_blocks = []
        prompt = (
            f"Question: {query}\n\n"
            f"Please analyze the following images from a PDF document "
            f"and provide an answer based on what you see:"
        )
        content_blocks.append(TextBlock(text=prompt))

        # Add image blocks
        for img in image_content:
            try:
                image_data = base64.b64decode(img["data"])
                content_blocks.append(
                    ImageBlock(image=image_data, image_mimetype=IMAGE_MIME_TYPE)
                )
                logger.info(f"Added image block for {img['metadata']['source']}")
            except Exception as e:
                logger.error(f"Error processing image block: {str(e)}")
                raise ImageProcessingError("Failed to process image content")

        return content_blocks

    def _create_mixed_content(
        self,
        query: str,
        text_nodes: list[str],
        image_content: list[dict[str, Any]],
    ) -> list[TextBlock] | list[ImageBlock]:
        """Create content blocks for mixed text and image synthesis"""

        content_blocks = []
        context_text = "\n\n".join(text_nodes)
        prompt = (
            f"Question: {query}\n\n"
            f"Relevant context from documents:\n{context_text}\n\n"
            f"Additionally, please analyze the following images from the document:"
        )
        content_blocks.append(TextBlock(text=prompt))

        # Add image blocks
        for img in image_content:
            try:
                image_data = base64.b64decode(img["data"])
                content_blocks.append(
                    ImageBlock(image=image_data, image_mimetype=IMAGE_MIME_TYPE)
                )
                logger.info(f"Added image block for {img['metadata']['source']}")
            except Exception as e:
                logger.error(f"Error processing image block: {str(e)}")
                raise ImageProcessingError("Failed to process image content")

        return content_blocks

    def _create_no_content_blocks(self, query: str) -> list[TextBlock]:
        """Create content blocks when no relevant content is available"""

        prompt = f"I don't have relevant context to answer the question: {query}"
        return [TextBlock(text=prompt)]

    async def _generate_model_response(
        self, content_blocks: list[TextBlock] | list[ImageBlock]
    ) -> str:
        """Generate response from the model using content blocks"""

        try:
            llama_index_model = LangChainLLM(
                llm=load_chat_model(settings.KB_SYNTHESIZE_MODEL)
            )
            message = ChatMessage(role="user", blocks=content_blocks)
            response = await llama_index_model.achat([message])

            if hasattr(response, "message") and hasattr(response.message, "content"):
                content = response.message.content
                return content if content is not None else "No response generated"
            else:
                return str(response)

        except Exception as e:
            logger.error(f"Error generating model response: {str(e)}")
            raise SynthesisError("Failed to generate response from model")

    def _create_fallback_response(self, query: str, nodes: list[NodeWithScore]) -> str:
        """Create a fallback text-only response when synthesis fails"""

        text_content = "\n\n".join(
            [
                node.node.text
                if hasattr(node, "node") and hasattr(node.node, "text")
                else str(node)
                for node in nodes
            ]
        )

        return f"Based on the available context: {text_content}\n\nAnswer: {query}"
