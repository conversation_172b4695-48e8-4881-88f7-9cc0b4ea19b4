"""
DeletionService - Handles document and KB deletion operations
"""

from llama_index.core.vector_stores.types import (
    FilterCondition,
    MetadataFilter,
    MetadataFilters,
)
from qdrant_client.models import FieldCondition, Filter, FilterSelector, MatchValue

from app.core.config import settings
from app.exceptions.kb_exceptions import DocumentDeletionError
from app.logger import logger

from .base import BaseKBService


class DeletionService(BaseKBService):
    """Service responsible for document and KB deletion operations"""

    async def delete_workspace_documents(self, workspace_id: str) -> bool:
        """
        Delete all documents for a workspace from the vector store

        Args:
            workspace_id: Workspace identifier

        Returns:
            True if deletion was successful

        Raises:
            DocumentDeletionError: If deletion fails
        """
        try:
            collection_name = settings.QDRANT_COLLECTION_NAME

            if not await self.vector_store.aqdrant_client.collection_exists(
                collection_name=collection_name
            ):
                logger.warning(f"Collection {collection_name} not found in Qdrant")
                return False

            # Delete points using workspace_id filter
            await self.vector_store.aqdrant_client.delete(
                collection_name=collection_name,
                points_selector=FilterSelector(
                    filter=Filter(
                        must=[
                            FieldCondition(
                                key="workspace_id",
                                match=MatchValue(value=workspace_id),
                            )
                        ]
                    )
                ),
                wait=True,  # Wait for deletion to complete
            )

            logger.info(f"Deleted all documents for workspace {workspace_id}")
            return True

        except Exception as e:
            logger.error(f"Error deleting workspace documents from Qdrant: {str(e)}")
            raise DocumentDeletionError("Failed to delete workspace documents")

    async def delete_documents_by_document_id(self, document_id: str) -> bool:
        """
        Delete documents by document ID from vector store

        Args:
            document_id: Document identifier

        Returns:
            True if deletion was successful

        Raises:
            DocumentDeletionError: If deletion fails
        """
        try:
            collection_name = settings.QDRANT_COLLECTION_NAME

            # Delete points using document_id filter
            await self.vector_store.aqdrant_client.delete(
                collection_name=collection_name,
                points_selector=FilterSelector(
                    filter=Filter(
                        must=[
                            FieldCondition(
                                key="document_id",
                                match=MatchValue(value=document_id),
                            )
                        ]
                    )
                ),
                wait=True,  # Wait for deletion to complete
            )
            return True

        except Exception as e:
            logger.error(f"Error deleting documents by document_id: {str(e)}")
            raise DocumentDeletionError("Failed to delete documents by ID")

    async def delete_kb_documents(self, workspace_id: str, kb_id: str) -> bool:
        """
        Delete all documents for a specific KB from the vector store

        Args:
            workspace_id: Workspace identifier
            kb_id: Knowledge base identifier

        Returns:
            True if deletion was successful

        Raises:
            DocumentDeletionError: If deletion fails
        """
        try:
            vector_store = await self.vector_store.get_vector_store()

            # Get all nodes with this kb_id and workspace_id
            nodes_to_delete = await vector_store.aget_nodes(
                filters=MetadataFilters(
                    filters=[
                        MetadataFilter(
                            key="kb_id",
                            value=str(kb_id),
                        ),
                        MetadataFilter(
                            key="workspace_id",
                            value=str(workspace_id),
                        ),
                    ],
                    condition=FilterCondition.AND,
                )
            )

            # Delete the nodes
            if nodes_to_delete:
                node_ids_to_delete = [node.node_id for node in nodes_to_delete]
                await vector_store.adelete_nodes(node_ids=node_ids_to_delete)

                logger.info(
                    f"Deleted {len(node_ids_to_delete)} documents for KB {kb_id} "
                    f"in workspace {workspace_id}"
                )
                return True
            else:
                logger.info(
                    f"No documents found for KB {kb_id} in workspace {workspace_id}"
                )
                return True

        except Exception as e:
            logger.error(f"Error deleting KB documents from vector store: {str(e)}")
            raise DocumentDeletionError("Failed to delete KB documents")
