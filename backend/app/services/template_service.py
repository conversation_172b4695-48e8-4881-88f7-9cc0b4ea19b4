import json
from uuid import <PERSON>UID

import boto3
from botocore.exceptions import ClientError
from langchain.output_parsers import PydanticOutputParser
from langchain.prompts import PromptTemplate
from sqlmodel import func, or_, select
from sqlmodel.ext.asyncio.session import AsyncSession

from app.exceptions.legacy import ServiceError, TemplateParsingError
from app.logger import logger
from app.models import CloudProvider, TaskTemplate
from app.models.tasks import TaskCategoryEnum, TaskCouldEnum, TaskServiceEnum
from app.schemas.task_template import (
    TaskTemplateCreate,
    TaskTemplateResponse,
    TaskTemplateUpdate,
)


class TaskTemplateGenerateService:
    def __init__(self, region_name: str = "us-east-1"):
        """Initialize Bedrock client and Langchain parsers"""
        self.bedrock_runtime = boto3.client(
            service_name="bedrock-runtime", region_name=region_name
        )
        self.model_id = "us.anthropic.claude-3-5-sonnet-20241022-v2:0"

        # Initialize Langchain parsers
        self.parser = PydanticOutputParser(pydantic_object=TaskTemplateResponse)

        # Initialize prompt template with Langchain
        self.prompt_template = self._create_prompt_template()

    def _create_prompt_template(self) -> PromptTemplate:
        """Create Langchain prompt template"""
        template = """
CloudThinker is your AI-powered cloud optimization platform that transforms how organizations manage and optimize their cloud infrastructure. By combining intelligent automation with deep cloud expertise, we help you maximize efficiency, reduce costs, and streamline operations - all while maintaining complete control and visibility.

You has been tasked to generate the task template for end-user to work with CloudThinker on AWS Cloud.

<task_guidelines>
Respond with valid JSON matching this format:
{format_instructions}

Guidelines:
1. Action: Verb-noun phrase from input
2. Category: Single lowercase word (e.g., billing, security)
3. RunMode: "autonomous" (scheduled) or "agent" (conversational)
4. Schedule: Cron syntax if autonomous
5. Context: Clear task description
</task_guidelines>

<input_example>
Input: "EC2 instance right sizing for Development Environment (Singapore Region)"
Output:
{{
  "Task": "EC2 Cost Optimization for Development Environment (Singapore Region)",
  "Category": "cost-optimization",
  "RunMode": "autonomous",
  "Schedule": "0 0 * * *",
  "Context": `Mission: Perform right-sizing of EC2 instances in Singapore

Goal: Ensure all development instances are cost-optimized based on usage patterns

Instructions:
1. Analyze all EC2 instances tagged Environment=Development
2. Identify instances with average CPU usage below 30%
3. Perform right-sizing ASAP
4. Maintain current instance family type
`
}}
</input_example>

<validation_rules>
- Task starts with action verb
- Category: lowercase, 1 word
- Schedule required ONLY for autonomous
- Context explains both purpose and benefit
- Must be valid JSON format
</validation_rules>

Input: {input}
Output:"""

        return PromptTemplate(
            template=template,
            input_variables=["input"],
            partial_variables={
                "format_instructions": self.parser.get_format_instructions()
            },
        )

    def _invoke_model(self, prompt: str) -> dict:
        """Invoke Claude Haiku model"""
        try:
            body = json.dumps(
                {
                    "anthropic_version": "bedrock-2023-05-31",
                    "max_tokens": 512,
                    "temperature": 0,
                    "messages": [{"role": "user", "content": prompt}],
                }
            )

            response = self.bedrock_runtime.invoke_model(
                modelId=self.model_id, body=body
            )

            response_body = json.loads(response.get("body").read())
            return response_body

        except ClientError as e:
            raise TemplateParsingError(f"Error invoking Bedrock model: {str(e)}")

    def _parse_response(self, response: dict) -> TaskTemplateResponse:
        """Parse and structure the model response using Langchain"""
        try:
            content = response.get("content")[0].get("text")
            if not content:
                raise TemplateParsingError("Empty response from model")

            # Try parsing with Langchain parser
            try:
                parsed_data = self.parser.parse(content)
                return TaskTemplateResponse.model_validate(parsed_data.model_dump())
            except Exception:
                # Fallback to basic JSON parsing with sanitization
                return self._fallback_parsing(content)

        except Exception as e:
            raise TemplateParsingError(f"Error parsing model response: {str(e)}")

    def _fallback_parsing(self, content: str) -> TaskTemplateResponse:
        """Fallback parsing method using basic JSON parsing with sanitization"""
        try:
            # Remove any leading/trailing whitespace and find JSON content
            content = content.strip()
            import re

            json_match = re.search(r"\{.*\}", content, re.DOTALL)
            if json_match:
                content = json_match.group()

            # Parse JSON
            data = json.loads(content)

            # Normalize data
            if "RunMode" in data and data["RunMode"] == "autonomous":
                data["Schedule"] = data.get("Schedule") or "* * * * *"

            return TaskTemplateResponse.model_validate(data)
        except Exception as e:
            raise TemplateParsingError(f"Fallback parsing failed: {str(e)}")

    def generate_template(self, input: str) -> TaskTemplateResponse:
        """Generate task template from input"""
        # Format prompt using Langchain template
        prompt = self.prompt_template.format(input=input)

        # Invoke model
        response = self._invoke_model(prompt)

        print(response)
        # Parse response
        return self._parse_response(response)


class TaskTemplateService:
    def __init__(self, session: AsyncSession):
        self.session = session

    async def create_template(
        self, workspace_id: UUID, data: TaskTemplateCreate, is_default: bool = False
    ) -> TaskTemplate:
        template = TaskTemplate(
            **data.dict(),
            workspace_id=None if is_default else workspace_id,
            is_default=is_default,
        )
        self.session.add(template)
        try:
            await self.session.commit()
            await self.session.refresh(template)
            return template
        except Exception as e:
            logger.exception(f"Failed to create template: {str(e)}")
            raise ServiceError("Failed to create template.", 500)

    async def get_template(
        self, template_id: int, workspace_id: UUID
    ) -> TaskTemplate | None:
        query = (
            select(TaskTemplate)
            .where(TaskTemplate.id == template_id)
            .where(
                or_(
                    TaskTemplate.workspace_id == workspace_id,
                    TaskTemplate.is_default == True,
                )
            )
        )
        result = await self.session.execute(query)
        return result.scalar_one_or_none()

    async def list_templates(
        self,
        workspace_id: UUID,
        skip: int = 0,
        limit: int = 100,
        category: list[TaskCategoryEnum] | None = None,
        services: list[TaskServiceEnum] | None = None,
        include_defaults: bool = True,
        search_query: str | None = None,
        workspace_provider: CloudProvider | None = None,
    ) -> tuple[list[TaskTemplate], int]:
        query = select(TaskTemplate)

        # Filter by workspace or default templates
        if include_defaults:
            query = query.where(
                or_(
                    TaskTemplate.workspace_id == workspace_id,
                    TaskTemplate.is_default == True,
                )
            )
        else:
            query = query.where(TaskTemplate.workspace_id == workspace_id)

        # Apply category filter if provided
        if category:
            query = query.where(TaskTemplate.category.in_(category))

        # Apply service filter if provided
        if services:
            query = query.where(TaskTemplate.service.in_(services))

        if search_query:
            query = query.where(
                or_(
                    TaskTemplate.context.ilike(f"%{search_query}%"),
                    TaskTemplate.task.ilike(f"%{search_query}%"),
                )
            )

        # Auto-filter by workspace cloud provider
        if workspace_provider:
            # Map CloudProvider to TaskCouldEnum
            provider_mapping = {
                CloudProvider.AWS: TaskCouldEnum.AWS,
                CloudProvider.GCP: TaskCouldEnum.GCP,
                CloudProvider.AZURE: TaskCouldEnum.AZURE,
            }

            task_cloud_provider = provider_mapping.get(workspace_provider)
            if task_cloud_provider:
                # Filter by workspace provider or ALL (universal templates)
                query = query.where(
                    or_(
                        TaskTemplate.cloud == task_cloud_provider,
                        TaskTemplate.cloud == TaskCouldEnum.ALL,
                    )
                )

        total = await self.session.execute(select(func.count()).select_from(query))
        total = total.scalar() or 0

        query = query.offset(skip).limit(limit)
        result = await self.session.execute(query)
        return result.scalars().all(), total

    async def update_template(
        self,
        template_id: int,
        workspace_id: UUID,
        data: TaskTemplateUpdate,
    ) -> TaskTemplate | None:
        template = await self.get_template(template_id, workspace_id)
        if not template:
            return None
        # Regular users can only modify their workspace templates
        if not template.is_default and template.workspace_id != workspace_id:
            logger.error(
                f"Cannot modify templates from other workspaces: {template.workspace_id} != {workspace_id}"
            )
            raise ServiceError("Cannot modify templates from other workspaces", 400)

        update_data = data.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(template, key, value)

        try:
            await self.session.commit()
            await self.session.refresh(template)
            return template
        except Exception as e:
            logger.exception(f"Failed to update template: {str(e)}")
            raise ServiceError("Failed to update template.", 500)

    async def delete_template(self, template_id: int, workspace_id: UUID) -> bool:
        template = await self.get_template(template_id, workspace_id)
        if not template:
            return False

        # Regular users can only delete their workspace templates
        if not template.is_default and template.workspace_id != workspace_id:
            logger.error(
                f"Cannot delete templates from other workspaces: {template.workspace_id} != {workspace_id}"
            )
            raise ServiceError("Cannot delete templates from other workspaces", 400)

        try:
            await self.session.delete(template)
            await self.session.commit()
            return True
        except Exception as e:
            logger.exception(f"Failed to delete template: {str(e)}")
            raise ServiceError("Failed to delete template.", 500)
