import json
from uuid import UUID

from sqlmodel.ext.asyncio.session import AsyncSession

from app.exceptions.connection_exceptions import BuiltinConnectionTestError
from app.models import (
    BuiltinConnectionType,
    CloudProvider,
    ConnectionBase,
    ConnectionStatus,
    ConnectionStatusResponse,
    ConnectionType,
)
from app.modules.connectors.connection_client import MC<PERSON>anagerClient
from app.modules.multi_agents.tools.executor import call_executor_helper
from app.repositories.connection import ConnectionRepository


class BuiltinConnectionTestService:
    def __init__(self, async_session: AsyncSession):
        self.conn_repository = ConnectionRepository(async_session)
        self.mcp_manager_client = MCPManagerClient()

    async def connect(
        self, conn: ConnectionBase, workspace_id: UUID
    ) -> ConnectionStatusResponse:
        if conn.connection_type == ConnectionType.BUILTIN:
            if conn.builtin_connection_type == BuiltinConnectionType.CLOUD:
                if conn.prefix == "aws":
                    env = {e["key"]: e["value"] for e in json.loads(conn.env)}
                    body = {
                        "script": "aws sts get-caller-identity",
                        "workspace_id": str(workspace_id),
                        "cloud_credentials": {
                            "provider": CloudProvider.AWS,
                            "aws_access_key_id": env["AWS_ACCESS_KEY_ID"],
                            "aws_secret_access_key": env["AWS_SECRET_ACCESS_KEY"],
                            "aws_default_region": env["AWS_DEFAULT_REGION"],
                        },
                        "k8s_credentials": None,
                    }
                    result = await call_executor_helper(body, timeout=5)

                    if result["return_code"] == 0:
                        return ConnectionStatusResponse(
                            status=ConnectionStatus.CONNECTED,
                            status_message="Cloud connection with predefined tools",
                            tools=conn.tools,
                            tool_schemas=conn.tool_schemas,
                        )
                    else:
                        raise BuiltinConnectionTestError(
                            "Failed to connect to AWS", 400
                        )
                elif conn.prefix == "gcp":
                    env = {e["key"]: e["value"] for e in json.loads(conn.env)}
                    body = {
                        "script": "gcloud auth list",
                        "workspace_id": str(workspace_id),
                        "cloud_credentials": {
                            "provider": CloudProvider.GCP,
                            "service_account_key": env["GOOGLE_SERVICE_ACCOUNT_KEY"],
                        },
                        "k8s_credentials": None,
                    }
                    result = await call_executor_helper(body, timeout=5)
                    if result["return_code"] == 0:
                        return ConnectionStatusResponse(
                            status=ConnectionStatus.CONNECTED,
                            status_message="Cloud connection with predefined tools",
                            tools=conn.tools,
                            tool_schemas=conn.tool_schemas,
                        )
                    else:
                        raise BuiltinConnectionTestError(
                            "Failed to connect to GCP", 400
                        )
                elif conn.prefix == "azure":
                    env = {e["key"]: e["value"] for e in json.loads(conn.env)}
                    body = {
                        "script": "az account show",
                        "workspace_id": str(workspace_id),
                        "cloud_credentials": {
                            "provider": CloudProvider.AZURE,
                            "username": env["APP_ID"],
                            "password": env["CLIENT_SECRET"],
                            "tenant": env["TENANT_ID"],
                        },
                        "k8s_credentials": None,
                    }
                    result = await call_executor_helper(body, timeout=5)
                    if result["return_code"] == 0:
                        return ConnectionStatusResponse(
                            status=ConnectionStatus.CONNECTED,
                            status_message="Cloud connection with predefined tools",
                            tools=conn.tools,
                            tool_schemas=conn.tool_schemas,
                        )
                    else:
                        raise BuiltinConnectionTestError(
                            "Failed to connect to Azure", 400
                        )
                else:
                    raise BuiltinConnectionTestError("Not implemented", 400)
            elif conn.builtin_connection_type == BuiltinConnectionType.ORCHESTRATION:
                if conn.prefix == "k8s":
                    env = {e["key"]: e["value"] for e in json.loads(conn.env)}
                    body = {
                        "script": "kubectl get nodes",
                        "workspace_id": str(workspace_id),
                        "cloud_credentials": None,
                        "k8s_credentials": {"kubeconfig": env["KUBECONFIG"]},
                    }
                    result = await call_executor_helper(body, timeout=5)
                    if result["return_code"] == 0:
                        return ConnectionStatusResponse(
                            status=ConnectionStatus.CONNECTED,
                            status_message="Orchestration connection with predefined tools",
                            tools=conn.tools,
                            tool_schemas=conn.tool_schemas,
                        )
                    else:
                        raise BuiltinConnectionTestError(
                            "Failed to connect to K8s", 400
                        )
            else:
                # Use MCP manager client for builtin connections
                connection_result = await self.mcp_manager_client.test_connection(conn)

                return ConnectionStatusResponse(
                    status=connection_result["status"],
                    status_message=connection_result["status_message"],
                    tools=connection_result["tools"],
                    tool_schemas=connection_result["tool_schemas"],
                )
        else:
            raise BuiltinConnectionTestError("Not implemented", 400)
