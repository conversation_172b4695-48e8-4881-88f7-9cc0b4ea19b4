import base64
import time
from typing import Any, Callable
from uuid import UUID

from llama_index.core import VectorStoreIndex
from llama_index.core.base.llms.types import ChatMessage, ImageBlock, TextBlock
from llama_index.core.ingestion import IngestionPipeline
from llama_index.core.node_parser import MarkdownNodeParser
from llama_index.core.schema import NodeWithScore, QueryBundle
from llama_index.core.vector_stores.types import (
    FilterCondition,
    MetadataFilter,
    MetadataFilters,
    VectorStoreQueryMode,
)
from llama_index.llms.bedrock import Bedrock
from llama_index.llms.cohere import Cohere
from llama_index.postprocessor.bedrock_rerank import AWSBedrockRerank
from qdrant_client.models import FieldCondition, Filter, FilterSelector, MatchValue
from sqlmodel.ext.asyncio.session import AsyncSession

from app.core.config import settings
from app.exceptions.kb_exceptions import (
    DocumentDeletionError,
    ImageProcessingError,
    PointLimitExceeded,
    SearchServiceError,
    SynthesisError,
)
from app.logger import logger
from app.models import AsyncTaskStatus
from app.modules.knowledge_base.readers import (
    file_reader,
    website_reader,
)
from app.repositories.kb import KBRepository
from app.repositories.object_storage.provider import get_object_storage_repository
from app.services.base_vector_store import BaseVectorStore
from app.services.point_limit_service import PointLimitService

# Constants
IMAGE_MIME_TYPE = "image/png"


class KBService(BaseVectorStore):
    def __init__(self, base_vector_store: BaseVectorStore | None = None):
        if base_vector_store:
            # Use dependency injection for better testability
            self.aqdrant_client = base_vector_store.aqdrant_client
            self.qdrant_client = base_vector_store.qdrant_client
            self.embed_model_doc = base_vector_store.embed_model_doc
            self.embed_model_query = base_vector_store.embed_model_query
        else:
            super().__init__()

        self.chunker = MarkdownNodeParser(
            include_metadata=True,
            include_prev_next_rel=True,
        )

    async def ingest_from_website(
        self,
        session: AsyncSession,
        doc_ids: list[str],
        kb_id: str,
        call_back: Callable,
        user_id: UUID | None = None,
        workspace_id: UUID | None = None,
    ) -> dict[str, Any]:
        # Check point limit first before any processing
        if user_id and workspace_id:
            point_service = PointLimitService(session)
            (
                can_add,
                current_points,
                limit,
            ) = await point_service.check_user_point_limit(user_id, workspace_id)

            if not can_add:
                kb_repo = KBRepository(session)
                docs_to_ingest = await kb_repo.get_documents(UUID(kb_id), doc_ids)
                # Update documents to failed status
                for doc_to_ingest in docs_to_ingest:
                    doc_to_ingest.embed_status = AsyncTaskStatus.FAILED
                await kb_repo.update_documents(docs_to_ingest)

                raise PointLimitExceeded(current_points, limit)

        kb_repo = KBRepository(session)
        os_repo = get_object_storage_repository()
        docs_to_ingest = await kb_repo.get_documents(UUID(kb_id), doc_ids)

        call_back(
            state=AsyncTaskStatus.IN_PROGRESS,
            meta={
                "progress": 30,
                "message": "Crawling urls",
            },
        )
        docs, docs_to_ingest, children_docs = await website_reader.read(
            docs_to_ingest,
            call_back,
        )

        # Update status of docs_to_ingest and create children docs
        await kb_repo.update_documents(docs_to_ingest)
        await kb_repo.create_documents(children_docs)

        # Run ingestion and upload in parallel
        call_back(
            state=AsyncTaskStatus.IN_PROGRESS,
            meta={
                "progress": 90,
                "message": "Embedding and uploading documents",
            },
        )

        # Add workspace_id to document metadata for filtering
        for doc in docs:
            doc.metadata["workspace_id"] = str(workspace_id)

        # Prepare vector store ingestion
        vector_store = await self.get_vector_store()

        async def ingest_to_vector_store():
            pipeline = IngestionPipeline(
                transformations=[self.embed_model_doc],
                vector_store=vector_store,
            )
            await pipeline.arun(documents=docs, show_progress=True)

        async def upload_to_object_storage():
            for doc in docs:
                await os_repo.upload_bytes(
                    data=doc.text.encode(),
                    object_name=doc.metadata.get("object_name", ""),
                    bucket_name=settings.KB_BUCKET,
                )

        # Run both operations in parallel
        import asyncio

        await asyncio.gather(
            ingest_to_vector_store(),
            upload_to_object_storage(),
        )

        return {"status": AsyncTaskStatus.COMPLETED}

    async def ingest_from_files(
        self,
        session: AsyncSession,
        doc_ids: list[str],
        kb_id: str,
        call_back: Callable,
        user_id: UUID | None = None,
        workspace_id: UUID | None = None,
    ) -> dict[str, Any]:
        # Check point limit first before any processing
        if user_id and workspace_id:
            point_service = PointLimitService(session)
            (
                can_add,
                current_points,
                limit,
            ) = await point_service.check_user_point_limit(user_id, workspace_id)

            if not can_add:
                kb_repo = KBRepository(session)
                docs_to_ingest = await kb_repo.get_documents(UUID(kb_id), doc_ids)
                # Update documents to failed status
                for doc_to_ingest in docs_to_ingest:
                    doc_to_ingest.embed_status = AsyncTaskStatus.FAILED
                await kb_repo.update_documents(docs_to_ingest)

                raise PointLimitExceeded(current_points, limit)

        call_back(
            state=AsyncTaskStatus.IN_PROGRESS,
            meta={"progress": 30, "message": "Ingesting files"},
        )
        kb_repo = KBRepository(session)
        docs_to_ingest = await kb_repo.get_documents(UUID(kb_id), doc_ids)
        documents, docs_to_ingest = await file_reader.read(docs_to_ingest)

        if len(documents) == 0:
            return {"status": AsyncTaskStatus.COMPLETED}

        for doc_to_ingest in docs_to_ingest:
            doc_to_ingest.embed_status = AsyncTaskStatus.COMPLETED

        # Update status of docs_to_ingest
        await kb_repo.update_documents(docs_to_ingest)

        # Add workspace_id to document metadata for filtering
        for doc in documents:
            doc.metadata["workspace_id"] = str(workspace_id)

        call_back(
            state=AsyncTaskStatus.IN_PROGRESS,
            meta={
                "progress": 90,
                "message": "Chunking and storing in vector database",
            },
        )
        vector_store = await self.get_vector_store()

        pipeline = IngestionPipeline(
            transformations=[self.embed_model_doc],
            vector_store=vector_store,
        )
        await pipeline.arun(documents=documents, show_progress=True)

        return {"status": AsyncTaskStatus.COMPLETED}

    async def delete_workspace_documents(self, workspace_id: str) -> bool:
        """Delete all documents for a workspace from the vector store"""
        try:
            collection_name = settings.QDRANT_COLLECTION_NAME
            if not await self.aqdrant_client.collection_exists(
                collection_name=collection_name
            ):
                logger.warning(f"Collection {collection_name} not found in Qdrant")
                return False

            # Delete points using workspace_id filter
            await self.aqdrant_client.delete(
                collection_name=collection_name,
                points_selector=FilterSelector(
                    filter=Filter(
                        must=[
                            FieldCondition(
                                key="workspace_id",
                                match=MatchValue(value=workspace_id),
                            )
                        ]
                    )
                ),
                wait=True,  # Wait for deletion to complete
            )
            logger.info(f"Deleted all documents for workspace {workspace_id}")
            return True
        except Exception:
            logger.error("Error deleting workspace documents from Qdrant")
            raise DocumentDeletionError("Failed to delete workspace documents")

    async def delete_documents_by_document_id(self, document_id: str) -> bool:
        try:
            collection_name = settings.QDRANT_COLLECTION_NAME
            # Delete points using document_id filter with qdrant_client directly
            await self.aqdrant_client.delete(
                collection_name=collection_name,
                points_selector=FilterSelector(
                    filter=Filter(
                        must=[
                            FieldCondition(
                                key="document_id",
                                match=MatchValue(value=document_id),
                            )
                        ]
                    )
                ),
                wait=True,  # Wait for deletion to complete
            )
            return True

        except Exception:
            logger.error("Error deleting documents by document_id")
            raise DocumentDeletionError("Failed to delete documents by ID")

    async def delete_kb_documents(self, workspace_id: str, kb_id: str) -> bool:
        """Delete all documents for a specific KB from the vector store"""
        try:
            vector_store = await self.get_vector_store()

            # Get all nodes with this kb_id and workspace_id
            nodes_to_delete = await vector_store.aget_nodes(
                filters=MetadataFilters(
                    filters=[
                        MetadataFilter(
                            key="kb_id",
                            value=str(kb_id),
                        ),
                        MetadataFilter(
                            key="workspace_id",
                            value=str(workspace_id),
                        ),
                    ],
                    condition=FilterCondition.AND,
                )
            )

            # Delete the nodes
            if nodes_to_delete:
                node_ids_to_delete = [node.node_id for node in nodes_to_delete]
                await vector_store.adelete_nodes(node_ids=node_ids_to_delete)
                logger.info(
                    f"Deleted {len(node_ids_to_delete)} documents for KB {kb_id} in workspace {workspace_id}"
                )
                return True
            else:
                logger.info(
                    f"No documents found for KB {kb_id} in workspace {workspace_id}"
                )
                return True

        except Exception:
            logger.error("Error deleting KB documents from vector store")
            raise DocumentDeletionError("Failed to delete KB documents")

    async def search(
        self,
        query: str,
        kb_ids: list[str],
        workspace_id: str,
    ) -> dict:
        # Validate and sanitize the search query
        start_time = time.time()
        kb_ids = list(set(kb_ids))
        logger.info(
            f"Searching knowledge bases {kb_ids} {workspace_id} with query {query}"
        )

        try:
            vector_store = await self.get_vector_store()
            index = VectorStoreIndex.from_vector_store(
                vector_store=vector_store,
                embed_model=self.embed_model_query,
            )

            kb_filters = MetadataFilters(
                filters=[
                    MetadataFilter(
                        key="kb_id",
                        value=kb_id,
                    )
                    for kb_id in kb_ids
                ],
                condition=FilterCondition.OR,
            )

            # Add workspace filter - all documents must be from this workspace
            workspace_filter = MetadataFilter(
                key="workspace_id",
                value=workspace_id,
            )

            # Combine workspace filter with kb filters
            # Workspace AND (kb1 OR kb2 OR kb3...)
            all_filters = MetadataFilters(
                filters=[workspace_filter, kb_filters],
                condition=FilterCondition.AND,
            )

            # Step 1: Retrieve all nodes without similarity cutoff
            all_nodes = self._retrieve_nodes(index, query, kb_ids, all_filters)

            # Step 2: Filter nodes by similarity cutoff
            filtered_nodes = self._filter_nodes_by_similarity(all_nodes)

            # Step 3: Rerank the filtered nodes
            reranked_nodes = self._rerank_nodes(query, filtered_nodes)

            # Step 4: Synthesize the final response
            response = await self._custom_synthesize(query, reranked_nodes)
            logger.info(
                f"Response generation time: {time.time() - start_time:.2f} seconds"
            )

            return {
                "response": response,
                "searched_kbs": len(kb_ids),
                "total_nodes": len(reranked_nodes),
                "errors": None,
            }

        except Exception:
            logger.error("Error searching knowledge bases")
            raise SearchServiceError("Search service temporarily unavailable")

    def _separate_nodes_by_type(self, nodes: list) -> tuple[list[str], list[dict]]:
        """Separate nodes into text and image content."""
        text_nodes = []
        image_content = []

        for node in nodes:
            node_content = node.node if hasattr(node, "node") else node

            # Check if this is an image node based on the actual metadata structure
            if hasattr(node_content, "metadata") and node_content.metadata:
                metadata = node_content.metadata
                is_image = metadata.get("is_image", False)

                if is_image and "image_base64" in metadata:
                    # This is an image node - extract image data
                    image_data = metadata["image_base64"]
                    page_info = metadata.get("page_info", "1")
                    file_name = metadata.get("file_name", "document")

                    image_content.append(
                        {
                            "type": "image",
                            "source_type": "base64",
                            "data": image_data,
                            "mime_type": IMAGE_MIME_TYPE,
                            "metadata": {
                                "source": f"{file_name}_{page_info}",
                                "file_name": file_name,
                                "page_info": page_info,
                            },
                        }
                    )
                    logger.info(f"Found image node: {file_name}_{page_info}")
                else:
                    # Regular text node
                    text_content = (
                        node_content.text
                        if hasattr(node_content, "text")
                        else str(node_content)
                    )
                    if text_content.strip():  # Only add non-empty text
                        text_nodes.append(text_content)
            else:
                # Fallback to text
                text_content = (
                    node_content.text
                    if hasattr(node_content, "text")
                    else str(node_content)
                )
                if text_content.strip():
                    text_nodes.append(text_content)

        logger.info(
            f"Processing {len(text_nodes)} text nodes and {len(image_content)} image nodes"
        )
        return text_nodes, image_content

    def _create_text_only_content(self, query: str, text_nodes: list[str]) -> list:
        """Create content blocks for text-only synthesis."""
        context_text = "\n\n".join(text_nodes)
        prompt = f"Question: {query}\n\nRelevant context from documents:\n{context_text}\n\nPlease provide a comprehensive answer based on the context above."
        return [TextBlock(text=prompt)]

    def _create_image_only_content(self, query: str, image_content: list[dict]) -> list:
        """Create content blocks for image-only synthesis."""
        content_blocks = []
        prompt = f"Question: {query}\n\nPlease analyze the following images from a PDF document and provide an answer based on what you see:"
        content_blocks.append(TextBlock(text=prompt))

        # Add actual image blocks
        for img in image_content:
            try:
                # Decode base64 image data
                image_data = base64.b64decode(img["data"])
                content_blocks.append(
                    ImageBlock(image=image_data, image_mimetype=IMAGE_MIME_TYPE)
                )
                logger.info(f"Added image block for {img['metadata']['source']}")
            except Exception:
                logger.error("Error processing image block")
                raise ImageProcessingError("Failed to process image content")

        return content_blocks

    def _create_mixed_content(
        self, query: str, text_nodes: list[str], image_content: list[dict]
    ) -> list:
        """Create content blocks for mixed text and image synthesis."""
        content_blocks = []
        context_text = "\n\n".join(text_nodes)
        prompt = f"Question: {query}\n\nRelevant context from documents:\n{context_text}\n\nAdditionally, please analyze the following images from the document:"
        content_blocks.append(TextBlock(text=prompt))

        # Add image blocks
        for img in image_content:
            try:
                image_data = base64.b64decode(img["data"])
                content_blocks.append(
                    ImageBlock(image=image_data, image_mimetype=IMAGE_MIME_TYPE)
                )
                logger.info(f"Added image block for {img['metadata']['source']}")
            except Exception:
                logger.error("Error processing image block")
                raise ImageProcessingError("Failed to process image content")

        return content_blocks

    def _create_no_content_blocks(self, query: str) -> list:
        """Create content blocks when no relevant content is available."""
        prompt = f"I don't have relevant context to answer the question: {query}"
        return [TextBlock(text=prompt)]

    async def _generate_model_response(self, content_blocks: list) -> str:
        """Generate response from the model using content blocks."""
        try:
            # Create a Bedrock model
            model = Bedrock(
                model=settings.KB_SYNTHESIZE_MODEL,
                region_name=settings.EMBEDDING_REGION_NAME,
            )

            # Create ChatMessage with blocks and call achat
            message = ChatMessage(role="user", blocks=content_blocks)
            response = await model.achat([message])

            # Handle ChatResponse
            if hasattr(response, "message") and hasattr(response.message, "content"):
                content = response.message.content
                return content if content is not None else "No response generated"
            else:
                return str(response)
        except Exception:
            logger.error("Error generating model response")
            raise SynthesisError("Failed to generate response from model")

    def _create_fallback_response(self, query: str, nodes: list) -> str:
        """Create a fallback text-only response when synthesis fails."""
        text_content = "\n\n".join(
            [
                node.node.text
                if hasattr(node, "node") and hasattr(node.node, "text")
                else str(node)
                for node in nodes
            ]
        )
        return f"Based on the available context: {text_content}\n\nAnswer: {query}"

    async def _custom_synthesize(self, query: str, nodes: list) -> str:
        """Custom synthesis function that handles both text and image nodes."""
        try:
            # Separate image and text nodes
            text_nodes, image_content = self._separate_nodes_by_type(nodes)

            # Create content blocks based on content type
            if text_nodes and not image_content:
                # Text-only case
                content_blocks = self._create_text_only_content(query, text_nodes)
            elif image_content and not text_nodes:
                # Image-only case - send actual images
                content_blocks = self._create_image_only_content(query, image_content)
            elif text_nodes and image_content:
                # Mixed content - both text and images
                content_blocks = self._create_mixed_content(
                    query, text_nodes, image_content
                )
            else:
                # No content case
                content_blocks = self._create_no_content_blocks(query)

            # Generate response from model
            return await self._generate_model_response(content_blocks)

        except (ImageProcessingError, SynthesisError):
            raise
        except Exception:
            logger.error("Error in custom synthesis")
            # Fallback to text-only synthesis
            return self._create_fallback_response(query, nodes)

    def _retrieve_nodes(
        self,
        index: VectorStoreIndex,
        query: str,
        kb_ids: list[str],
        filters: MetadataFilters,
    ) -> list[NodeWithScore]:
        """Retrieve all nodes without similarity cutoff."""
        query_engine = index.as_query_engine(
            llm=Cohere(model="command-light"),
            similarity_top_k=settings.KB_SEARCH_LIMIT * len(kb_ids),
            sparse_top_k=settings.SPARSE_TOP_K,
            vector_store_query_mode=VectorStoreQueryMode.HYBRID,
            similarity_cutoff=0.0,  # No cutoff at retrieval stage
            filters=filters,
        )
        nodes = query_engine.retrieve(QueryBundle(query_str=query))
        logger.info(f"Retrieved {len(nodes)} nodes before filtering")
        return nodes

    def _filter_nodes_by_similarity(
        self, nodes: list[NodeWithScore]
    ) -> list[NodeWithScore]:
        """Filter nodes by similarity cutoff threshold."""
        filtered_nodes = [
            node
            for node in nodes
            if node.score is not None
            and node.score >= settings.KB_SEARCH_SCORE_THRESHOLD
        ]
        logger.info(
            f"Filtered to {len(filtered_nodes)} nodes above threshold {settings.KB_SEARCH_SCORE_THRESHOLD}"
        )
        return filtered_nodes

    def _rerank_nodes(
        self, query: str, nodes: list[NodeWithScore]
    ) -> list[NodeWithScore]:
        """Rerank the filtered nodes using CohereRerank."""
        if not nodes:
            return nodes

        reranker = AWSBedrockRerank(
            top_n=settings.KB_RERANK_TOP_N,
            model=settings.KB_RERANK_MODEL_ID,
            region_name=settings.KB_RERANK_REGION_NAME,
        )

        # Rerank the nodes
        reranked_nodes = reranker.postprocess_nodes(nodes, QueryBundle(query_str=query))
        logger.info(f"Reranked to top {len(reranked_nodes)} nodes")
        for i, node in enumerate(reranked_nodes):
            logger.info(f"Reranked Node {i} with score {node.score}")

        return reranked_nodes
