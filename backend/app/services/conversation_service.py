from uuid import UUI<PERSON>

from sqlalchemy.orm import Session
from sqlmodel.ext.asyncio.session import AsyncSession

from app.exceptions.conversation_exceptions import ConversationNotFound
from app.models import AgentType, Conversation, MessagePublicList
from app.repositories.agent import AgentRepository
from app.repositories.conversation import ConversationRepository
from app.services.agent.message_handler import Message<PERSON><PERSON>ler


class ConversationService:
    def __init__(self, session: Session, async_session: AsyncSession):
        self.conversation_repository = ConversationRepository(
            session=session, async_session=async_session
        )
        self.agent_repository = AgentRepository(async_session=async_session)
        self.message_handler = MessageHandler(session, async_session)

    async def create_conversation(
        self,
        agent_id: UUID,
        workspace_id: UUID,
        resource_id: UUID | None = None,
    ) -> Conversation:
        return await self.conversation_repository.create_conversation(
            agent_id=agent_id,
            workspace_id=workspace_id,
            resource_id=resource_id,
        )

    async def get_conversations(
        self,
        workspace_id: UUID,
        agent_id: UUID | None = None,
        resource_id: UUID | None = None,
        search: str | None = None,
        skip: int | None = None,
        limit: int | None = None,
    ) -> tuple[list[Conversation], int]:
        if agent_id is None:
            agents = await self.agent_repository.get_agents(workspace_id)
            agents = [
                agent for agent in agents if agent.type == AgentType.AUTONOMOUS_AGENT
            ]
            if len(agents) == 0:
                return [], 0
            agent_id = agents[0].id

        conversations, total = await self.conversation_repository.get_conversations(
            agent_id=agent_id,
            workspace_id=workspace_id,
            resource_id=resource_id,
            search=search,
            skip=skip,
            limit=limit,
        )
        return conversations, total

    async def get_conversation_history(
        self, conversation_id: UUID, workspace_id: UUID
    ) -> MessagePublicList:
        return await self.conversation_repository.get_conversation_history(
            conversation_id=conversation_id,
            workspace_id=workspace_id,
        )

    async def get_conversation_plans(
        self, conversation_id: UUID, workspace_id: UUID
    ) -> list[dict]:
        conversation = self.conversation_repository.get_conversation(
            conversation_id=conversation_id
        )
        if not conversation:
            raise ConversationNotFound(conversation_id=str(conversation_id))

        await self.conversation_repository.check_conversation_permission(
            conversation.agent_id, workspace_id
        )
        plans = await self.message_handler.get_latest_message_plans(
            str(conversation_id)
        )
        return plans

    async def get_conversation_by_share_id(self, share_id: UUID) -> Conversation | None:
        return await self.conversation_repository.get_conversation_by_share_id(share_id)

    async def rename_conversation(
        self, conversation_id: UUID, name: str, workspace_id: UUID
    ) -> Conversation:
        return await self.conversation_repository.rename_conversation(
            workspace_id=workspace_id,
            conversation_id=conversation_id,
            name=name,
        )

    async def delete_conversation(
        self, conversation_id: UUID, workspace_id: UUID
    ) -> None:
        return await self.conversation_repository.delete_conversation(
            workspace_id=workspace_id,
            conversation_id=conversation_id,
        )
