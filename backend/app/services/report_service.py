import json
from uuid import UUID

from app.api.deps import get_async_session
from app.exceptions.legacy import RepositoryError
from app.logger import logger
from app.models.reports import Report
from app.modules.multi_agents.tools.report.schema import (
    ExecutiveSummary,
    ReportSection,
)
from app.repositories.report import ReportRepository


class ReportService:
    """Service for managing report operations with direct database interaction."""

    @staticmethod
    def _serialize_section(section: ReportSection | dict) -> dict:
        """Convert a ReportSection or raw dict into a JSON-serializable dict."""
        if isinstance(section, ReportSection):
            return section.model_dump(mode="json")
        return dict(section)

    @staticmethod
    def _serialize_sections(
        sections: list[ReportSection] | list[dict] | None,
    ) -> list[dict]:
        if not sections:
            return []
        return [ReportService._serialize_section(s) for s in sections]

    @staticmethod
    def _extract_index(section_dict: dict) -> int | None:
        idx = section_dict.get("index")
        if isinstance(idx, int):
            return idx
        if isinstance(idx, str):
            try:
                return int(idx)
            except ValueError:
                return None
        return None

    @staticmethod
    async def get_or_create_report(
        workspace_id: UUID,
        conversation_id: UUID,
    ) -> str:
        """Get existing report or create a new one."""
        try:
            async for async_session in get_async_session():
                repo = ReportRepository(async_session)
                try:
                    report = await repo.get_by_conversation_id(
                        conversation_id, workspace_id
                    )
                except RepositoryError as e:
                    if e.status_code == 404:
                        # Create new empty report
                        empty_report = {
                            "title": "",
                            "description": "",
                            "sections": [],
                            "executive_summary": {},
                        }
                        report = await repo.create_or_update(
                            empty_report, workspace_id, conversation_id
                        )
                    else:
                        raise

                return json.dumps(
                    {
                        "status": "success",
                        "message": "Report fetched successfully",
                        "data": report.model_dump(mode="json"),
                    }
                )
        except Exception as e:
            logger.exception("Failed to fetch report: {}", str(e))
            return json.dumps(
                {
                    "status": "error",
                    "message": f"Failed to fetch report: {str(e)}",
                }
            )
        # Fallback to satisfy static analyzers
        return json.dumps({"status": "error", "message": "Unknown error"})

    @staticmethod
    async def create_outline(
        workspace_id: UUID,
        conversation_id: UUID,
        title: str | None = None,
        description: str | None = None,
        sections: list[ReportSection] | None = None,
    ) -> str:
        """Create or update report outline."""
        try:
            async for async_session in get_async_session():
                repo = ReportRepository(async_session)

                # Check if report already exists for this conversation
                # try:
                #     existing_report = await repo.get_by_conversation_id(
                #         conversation_id, workspace_id
                #     )
                #     # Report already exists
                #     return json.dumps(
                #         {
                #             "status": "info",
                #             "message": "Report already exists for this conversation (maybe another agent is working on it). Here is the report.",
                #             "data": existing_report.model_dump(mode="json"),
                #         }
                #     )
                # except RepositoryError as e:
                #     if e.status_code != 404:
                #         raise  # Re-raise if it's not a "not found" error
                #     # Report doesn't exist, proceed with creation

                report_data = {
                    "title": title or "",
                    "description": description or "",
                    "sections": ReportService._serialize_sections(sections),
                    "executive_summary": {},
                }

                report = await repo.create_or_update(
                    report_data, workspace_id, conversation_id
                )

                return json.dumps(
                    {
                        "status": "success",
                        "message": "Report outline created successfully",
                        "data": report.model_dump(mode="json"),
                    }
                )
        except Exception as e:
            logger.exception("Failed to create report outline: {}", str(e))
            return json.dumps(
                {
                    "status": "error",
                    "message": f"Failed to create report outline: {str(e)}",
                }
            )
        # Fallback to satisfy static analyzers
        return json.dumps({"status": "error", "message": "Unknown error"})

    @staticmethod
    async def update_sections(
        workspace_id: UUID,
        conversation_id: UUID,
        sections: list[ReportSection] | None,
    ) -> str:
        """Update specific sections."""
        if not sections:
            return json.dumps(
                {
                    "status": "error",
                    "message": "No sections provided to update",
                }
            )

        try:
            async for async_session in get_async_session():
                repo = ReportRepository(async_session)

                # Get current report or create a new empty one
                try:
                    current_report = await repo.get_by_conversation_id(
                        conversation_id, workspace_id
                    )
                except RepositoryError as e:
                    if e.status_code == 404:
                        empty_report = {
                            "title": "",
                            "description": "",
                            "sections": [],
                            "executive_summary": {},
                        }
                        current_report = await repo.create_or_update(
                            empty_report, workspace_id, conversation_id
                        )
                    else:
                        raise

                current_sections = current_report.sections or []

                # Transform new sections into dicts and index map
                serialized_new_sections: list[dict] = ReportService._serialize_sections(
                    sections  # type: ignore[arg-type]
                )
                new_sections_dict: dict[int, dict] = {}
                for s in serialized_new_sections:
                    idx = ReportService._extract_index(s)
                    if idx is not None:
                        new_sections_dict[idx] = s

                # Map existing sections by index
                existing_sections: dict[int, dict] = {}
                for section in current_sections:
                    idx = ReportService._extract_index(section)
                    if idx is not None:
                        existing_sections[idx] = dict(section)

                for index, new_section in new_sections_dict.items():
                    if index in existing_sections:
                        # Merge: update only non-None values
                        filtered_section = {
                            k: v for k, v in new_section.items() if v is not None
                        }
                        existing_sections[index].update(filtered_section)
                    else:
                        # Add new section
                        existing_sections[index] = new_section

                # Convert back to list, sorted by index
                updated_sections = [
                    existing_sections[index]
                    for index in sorted(existing_sections.keys())
                ]

                report_data = {
                    "title": current_report.title,
                    "description": current_report.description,
                    "sections": updated_sections,
                    "executive_summary": current_report.executive_summary or {},
                }

                report = await repo.create_or_update(
                    report_data, workspace_id, conversation_id
                )
                is_completed = ReportService.calculate_completion_status(report)

                return json.dumps(
                    {
                        "status": "success",
                        "is_completed": is_completed,
                        "message": "Report section updated successfully",
                        "data": report.model_dump(mode="json"),
                    }
                )
        except Exception as e:
            logger.exception("Failed to update sections: {}", str(e))
            return json.dumps(
                {
                    "status": "error",
                    "message": f"Failed to update sections: {str(e)}",
                }
            )
        # Fallback to satisfy static analyzers
        return json.dumps({"status": "error", "message": "Unknown error"})

    @staticmethod
    async def remove_sections_by_objects(
        workspace_id: UUID,
        conversation_id: UUID,
        sections: list[ReportSection] | None,
    ) -> str:
        """Remove sections by ReportSection objects."""
        if not sections:
            return json.dumps(
                {
                    "status": "error",
                    "message": "No sections provided to remove",
                }
            )

        # Handle both ReportSection objects and dicts
        indices = []
        for section in sections:
            if isinstance(section, dict):
                idx = section.get("index")
                if isinstance(idx, int):
                    indices.append(idx)
            else:
                indices.append(section.index)
        return await ReportService.remove_sections(
            workspace_id, conversation_id, indices
        )

    @staticmethod
    async def remove_sections(
        workspace_id: UUID,
        conversation_id: UUID,
        section_indices: list[int],
    ) -> str:
        """Remove specific sections by their indices."""
        try:
            async for async_session in get_async_session():
                repo = ReportRepository(async_session)

                # Get current report or create a new empty one
                try:
                    current_report = await repo.get_by_conversation_id(
                        conversation_id, workspace_id
                    )
                except RepositoryError as e:
                    if e.status_code == 404:
                        empty_report = {
                            "title": "",
                            "description": "",
                            "sections": [],
                            "executive_summary": {},
                        }
                        current_report = await repo.create_or_update(
                            empty_report, workspace_id, conversation_id
                        )
                    else:
                        raise

                current_sections = current_report.sections or []

                # Filter out sections with indices to remove
                updated_sections = [
                    section
                    for section in current_sections
                    if section.get("index") not in section_indices
                ]

                report_data = {
                    "title": current_report.title,
                    "description": current_report.description,
                    "sections": updated_sections,
                    "executive_summary": current_report.executive_summary or {},
                }

                report = await repo.create_or_update(
                    report_data, workspace_id, conversation_id
                )

                return json.dumps(
                    {
                        "status": "success",
                        "message": "Report section removed successfully",
                        "data": report.model_dump(mode="json"),
                    }
                )
        except Exception as e:
            logger.exception("Failed to remove sections: {}", str(e))
            return json.dumps(
                {
                    "status": "error",
                    "message": f"Failed to remove sections: {str(e)}",
                }
            )
        # Fallback to satisfy static analyzers
        return json.dumps({"status": "error", "message": "Unknown error"})

    @staticmethod
    async def update_executive_summary(
        workspace_id: UUID,
        conversation_id: UUID,
        executive_summary: ExecutiveSummary | None,
    ) -> str:
        """Update executive summary."""
        if not executive_summary:
            return json.dumps(
                {
                    "status": "error",
                    "message": "No executive summary provided",
                }
            )

        try:
            async for async_session in get_async_session():
                repo = ReportRepository(async_session)

                # Get current report or create a new empty one
                try:
                    current_report = await repo.get_by_conversation_id(
                        conversation_id, workspace_id
                    )
                except RepositoryError as e:
                    if e.status_code == 404:
                        empty_report = {
                            "title": "",
                            "description": "",
                            "sections": [],
                            "executive_summary": {},
                        }
                        current_report = await repo.create_or_update(
                            empty_report, workspace_id, conversation_id
                        )
                    else:
                        raise

                report_data = {
                    "title": current_report.title,
                    "description": current_report.description,
                    "sections": current_report.sections or [],
                    "executive_summary": executive_summary.model_dump(mode="json"),
                }

                report = await repo.create_or_update(
                    report_data, workspace_id, conversation_id
                )
                is_completed = ReportService.calculate_completion_status(report)

                return json.dumps(
                    {
                        "status": "success",
                        "is_completed": is_completed,
                        "message": "Report executive summary created successfully",
                        "data": report.model_dump(mode="json"),
                    }
                )
        except Exception as e:
            logger.exception("Failed to update executive summary: {}", str(e))
            return json.dumps(
                {
                    "status": "error",
                    "message": f"Failed to update executive summary: {str(e)}",
                }
            )
        # Fallback to satisfy static analyzers
        return json.dumps({"status": "error", "message": "Unknown error"})

    @staticmethod
    def calculate_completion_status(report: Report) -> str:
        """Calculate report completion status."""
        sections = report.sections or []

        none_sections = [
            f"Index {section.get('index', 'N/A')} - {section.get('header', 'N/A')}"
            for section in sections
            if not section.get("content")
        ]

        executive_sum_exist = bool(report.executive_summary)

        if none_sections:
            return f"These sessions are currently none, remember to populate it later.\n{'\n'.join(none_sections)}"
        else:
            return (
                "completed"
                if executive_sum_exist
                else "the body did complete but the executive summary is missing"
            )
