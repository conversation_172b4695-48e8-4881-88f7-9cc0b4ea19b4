import json
from datetime import datetime
from uuid import UUID

from sqlmodel.ext.asyncio.session import AsyncSession

from app.exceptions.connection_exceptions import ConnectionServiceError
from app.models import (
    UTC,
    ConnectionBase,
    ConnectionEnvConfig,
    ConnectionMCPConfig,
    ConnectionStatus,
    ConnectionStatusResponse,
    ConnectionToolConfig,
    ConnectionType,
    MCPConnectionCreate,
    MCPConnectionPublic,
    MCPConnectionsPublic,
    MCPConnectionUpdate,
)
from app.modules.connectors.mcp_client.mcp_client import MCPClientConnector
from app.repositories.connection import ConnectionRepository
from app.services.workspace_service import WorkspaceService


class MCPConnectionService:
    def __init__(self, async_session: AsyncSession):
        self.conn_repository = ConnectionRepository(async_session)
        self.workspace_service = WorkspaceService(async_session)

    async def connect(self, conn: ConnectionBase) -> ConnectionStatusResponse:
        mcp_client = MCPClientConnector(mcp_servers=[conn])
        try:
            tool_schemas, tools = await mcp_client.get_tool_schemas(conn.name)
        except Exception:
            tool_schemas = []
            tools = []
            pass

        connection_status = mcp_client.get_connection_status()
        return ConnectionStatusResponse(
            status=connection_status[conn.name]["status"],
            status_message=connection_status[conn.name]["status_message"],
            tools=[
                {"name": tool.name, "is_enabled": True, "is_required_permission": False}
                for tool in tools
            ],
            tool_schemas=tool_schemas,
        )

    async def create_mcp_connection(
        self,
        workspace_id: UUID,
        data: MCPConnectionCreate,
    ) -> MCPConnectionPublic:
        conn = ConnectionBase(
            name=data.name,
            prefix=data.prefix,
            connection_type=ConnectionType.MCP,
            mcp_transport_type=data.mcp_transport_type,
            config=data.config.model_dump(),
            env=json.dumps([env.model_dump() for env in data.env]),
        )

        connection_result = await self.connect(conn)
        if connection_result.status == ConnectionStatus.CONNECTED:
            conn.tools = connection_result.tools
            conn.tool_schemas = connection_result.tool_schemas
            conn.status = ConnectionStatus.CONNECTED
            conn.status_message = connection_result.status_message
            conn.status_updated_at = datetime.now(UTC)
        else:
            conn.status = ConnectionStatus.ERROR
            conn.status_message = connection_result.status_message
            conn.status_updated_at = datetime.now(UTC)

        mcp_conn = await self.conn_repository.create_mcp_connection(
            workspace_id=workspace_id, conn_data=conn
        )
        return MCPConnectionPublic(
            id=mcp_conn.id,
            name=mcp_conn.name,
            prefix=mcp_conn.prefix,
            mcp_transport_type=mcp_conn.mcp_transport_type,
            config=ConnectionMCPConfig(**mcp_conn.config),
            env=[ConnectionEnvConfig(**env) for env in json.loads(mcp_conn.env)],
            tools=[ConnectionToolConfig(**tool) for tool in mcp_conn.tools],
            status=mcp_conn.status,
            status_message=mcp_conn.status_message,
        )

    async def get_mcp_connections(
        self,
        workspace_id: UUID,
    ) -> MCPConnectionsPublic:
        conns = await self.conn_repository.get_connections_by_type(
            workspace_id=workspace_id, connection_type=ConnectionType.MCP
        )
        if not conns:
            return MCPConnectionsPublic(data=[], count=0)

        server_responses = [
            MCPConnectionPublic(
                id=conn.id,
                name=conn.name,
                prefix=conn.prefix,
                mcp_transport_type=conn.mcp_transport_type,
                config=ConnectionMCPConfig(**conn.config),
                env=[ConnectionEnvConfig(**env) for env in json.loads(conn.env)],
                tools=[ConnectionToolConfig(**tool) for tool in conn.tools],
                status=conn.status,
                status_message=conn.status_message,
            )
            for conn in conns
        ]
        return MCPConnectionsPublic(data=server_responses, count=len(server_responses))

    async def update_mcp_connection(
        self,
        workspace_id: UUID,
        conn_id: UUID,
        data: MCPConnectionUpdate,
    ) -> MCPConnectionPublic:
        conn = await self.conn_repository.get_connection(
            conn_id=conn_id, workspace_id=workspace_id
        )

        if conn is None:
            raise ConnectionServiceError("Connection not found", 404)

        if data.config:
            conn.config = data.config.model_dump()

        if data.env:
            conn.env = json.dumps([env.model_dump() for env in data.env])

        if data.mcp_transport_type:
            conn.mcp_transport_type = data.mcp_transport_type

        if data.name:
            conn.name = data.name

        if data.prefix:
            conn.prefix = data.prefix

        if data.config or data.mcp_transport_type or data.env:
            test_result = await self.connect(conn)
            conn.tools = test_result.tools
            conn.tool_schemas = test_result.tool_schemas
            conn.status = test_result.status
            conn.status_message = test_result.status_message
            conn.status_updated_at = datetime.now(UTC)

        mcp_conn = await self.conn_repository.update_connection(conn=conn)
        return MCPConnectionPublic(
            id=mcp_conn.id,
            name=mcp_conn.name,
            prefix=mcp_conn.prefix,
            mcp_transport_type=mcp_conn.mcp_transport_type,
            config=ConnectionMCPConfig(**mcp_conn.config),
            env=[ConnectionEnvConfig(**env) for env in json.loads(mcp_conn.env)],
            tools=[ConnectionToolConfig(**tool) for tool in mcp_conn.tools],
            status=mcp_conn.status,
            status_message=mcp_conn.status_message,
        )

    async def delete_mcp_connection(
        self,
        workspace_id: UUID,
        conn_id: UUID,
    ) -> None:
        return await self.conn_repository.delete_connection(
            conn_id=conn_id, workspace_id=workspace_id
        )

    async def update_mcp_connection_tool(
        self,
        workspace_id: UUID,
        connection_id: UUID,
        tool_name: str,
        is_enabled: bool,
        is_required_permission: bool,
    ) -> MCPConnectionPublic:
        """Share update the tools for mcp connections."""

        # Get the mcp connection from default workspace
        conn = await self.conn_repository.get_connection(
            workspace_id=workspace_id,
            conn_id=connection_id,
        )
        if not conn:
            raise ConnectionServiceError("MCP connection not found", 404)

        # Update the tools for the mcp connection
        updated_tools = []
        for tool in conn.tools:
            if tool["name"] == tool_name:
                updated_tools.append(
                    {
                        "name": tool_name,
                        "is_enabled": is_enabled,
                        "is_required_permission": is_required_permission,
                    }
                )
            else:
                updated_tools.append(tool)
        conn.tools = updated_tools

        # Update the connection in user's workspace
        conn = await self.conn_repository.update_connection(conn=conn)

        return MCPConnectionPublic(
            id=conn.id,
            name=conn.name,
            prefix=conn.prefix,
            mcp_transport_type=conn.mcp_transport_type,
            config=ConnectionMCPConfig(**conn.config),
            env=[ConnectionEnvConfig(**env) for env in json.loads(conn.env)],
            tools=[ConnectionToolConfig(**tool) for tool in conn.tools],
            status=conn.status,
            status_message=conn.status_message,
        )
