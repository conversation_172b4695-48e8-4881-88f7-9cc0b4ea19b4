import json
import time
from contextlib import contextmanager
from uuid import UUI<PERSON>

from sqlalchemy import text
from sqlmodel import Session, select

from app.core.celery_app import LOW_PRIORITY, celery_app
from app.core.db import engine
from app.exceptions.legacy import ScanningError
from app.logger import logger
from app.models import (
    BuiltinConnectionType,
    CloudProvider,
    CloudSyncConfig,
    Connection,
    ConnectionType,
    Workspace,
)
from app.modules.resource_crawlers.multicloud_crawler_factory import (
    MultiCloudCrawlerFactory,
)
from app.modules.resource_crawlers.multicloud_scanning_context import (
    ScanningContextType,
    create_scanning_context,
)
from app.repositories.workspaces import WorkspaceRepository

# Maximum number of operations to process in a single batch
MAX_BATCH_SIZE = 5
# Timeout for database operations in seconds
DB_OPERATION_TIMEOUT = 300  # 5 minutes


@contextmanager
def get_scan_session(timeout: int = DB_OPERATION_TIMEOUT):
    """Get a session for scanning operations with timeout"""
    with Session(engine) as session:
        try:
            # Set statement timeout
            session.execute(text(f"SET statement_timeout = {timeout * 1000}"))
            yield session
        except Exception:
            session.rollback()
            raise


class MultiCloudResourceScanner:
    """Unified resource scanner for multiple cloud providers"""

    def __init__(self, session: Session):
        self.session = session
        self.workspace_repo = WorkspaceRepository(session)

    def _get_enabled_cloud_sync_configs(
        self, workspace_id: UUID
    ) -> list[CloudSyncConfig]:
        """Get enabled cloud sync configurations for a workspace"""
        try:
            statement = select(CloudSyncConfig).where(
                CloudSyncConfig.workspace_id == workspace_id,
                CloudSyncConfig.is_enabled == True,
            )
            result = self.session.exec(statement)
            return list(result.all())
        except Exception as e:
            logger.error(
                f"Error getting cloud sync configs for workspace {workspace_id}: {str(e)}"
            )
            return []

    def _get_connection(self, connection_id: UUID) -> Connection | None:
        """Get connection by ID"""
        try:
            statement = select(Connection).where(Connection.id == connection_id)
            result = self.session.exec(statement)
            return result.first()
        except Exception as e:
            logger.error(f"Error getting connection {connection_id}: {str(e)}")
            return None

    def _validate_workspace_with_config(
        self, workspace: Workspace, config: CloudSyncConfig, connection: Connection
    ) -> ScanningContextType | None:
        """Validate workspace configuration using CloudSyncConfig and return scanning context"""
        try:
            if not config.is_enabled:
                logger.info(f"Skipping disabled cloud sync config {config.id}")
                return None

            if not config.selected_resources:
                logger.warning(f"No resources selected in config {config.id}")
                return None

            # Determine provider from connection prefix
            provider_mapping = {
                "aws": CloudProvider.AWS,
                "gcp": CloudProvider.GCP,
                "azure": CloudProvider.AZURE,
            }

            provider = provider_mapping.get(connection.prefix)
            if not provider:
                raise ScanningError(
                    f"Unknown cloud provider for connection prefix: {connection.prefix}"
                )

            # Get credentials from connection config
            connection_env = {
                env["key"]: env["value"] for env in json.loads(connection.env)
            }

            # Get regions from connection config with fallback to defaults
            def get_regions_for_provider(
                provider: CloudProvider, connection_env: dict
            ) -> list[str]:
                """Get regions from connection config with provider-specific defaults"""
                if provider == CloudProvider.AWS:
                    region = connection_env.get("AWS_DEFAULT_REGION")
                    return [region] if region else ["us-east-1"]
                elif provider == CloudProvider.GCP:
                    region = connection_env.get("GCP_DEFAULT_REGION")
                    return [region] if region else ["us-central1"]
                elif provider == CloudProvider.AZURE:
                    region = connection_env.get("AZURE_DEFAULT_REGION")
                    return [region] if region else ["eastus"]
                else:
                    return ["us-east-1"]

            regions = get_regions_for_provider(provider, connection_env)

            # Create scanning context based on provider
            if provider == CloudProvider.AWS:
                aws_access_key_id = connection_env.get("AWS_ACCESS_KEY_ID")
                aws_secret_access_key = connection_env.get("AWS_SECRET_ACCESS_KEY")

                if not aws_access_key_id or not aws_secret_access_key:
                    raise ScanningError(
                        "Missing AWS credentials in connection configuration"
                    )

                return create_scanning_context(
                    provider=provider,
                    workspace_id=workspace.id,
                    regions=regions,
                    resource_types=config.selected_resources,
                    include_stopped_resources=config.include_stopped_resources,
                    aws_account_id=None,  # We'll use connection-based credentials
                    aws_access_key_id=aws_access_key_id,
                    aws_secret_access_key=aws_secret_access_key,
                )

            elif provider == CloudProvider.GCP:
                service_account_key = connection_env.get("GOOGLE_SERVICE_ACCOUNT_KEY")
                project_id = connection_env.get("GCP_PROJECT_ID")

                if not service_account_key:
                    raise ScanningError(
                        "Missing GCP service account key in connection configuration"
                    )

                if not project_id:
                    raise ScanningError(
                        "Missing GCP project ID in connection configuration"
                    )

                return create_scanning_context(
                    provider=provider,
                    workspace_id=workspace.id,
                    regions=regions,
                    resource_types=config.selected_resources,
                    include_stopped_resources=config.include_stopped_resources,
                    project_id=project_id,
                    service_account_key=service_account_key,
                )

            elif provider == CloudProvider.AZURE:
                # TODO: Implement Azure support when models and credentials are ready
                raise ScanningError("Azure support not yet implemented")

            else:
                raise ScanningError(f"Unsupported cloud provider: {provider}")

        except Exception as e:
            logger.exception(
                f"Unexpected error validating workspace {workspace.id} with config {config.id}: {str(e)}"
            )
            return None

    def _scan_service_in_region_with_session(
        self,
        session: Session,
        context: ScanningContextType,
        service: str,
        region: str,
    ):
        """Scan specific service resources in a region using the provided session"""
        try:
            # Get the appropriate crawler based on the provider
            if context.provider == CloudProvider.AWS:
                crawler = MultiCloudCrawlerFactory.get_crawler_for_resource_type_string(
                    provider=context.provider,
                    workspace_id=context.workspace_id,
                    resource_type_string=service,
                    aws_account_id=context.aws_account_id,
                    aws_access_account_id=str(context.aws_account_id),
                    aws_access_key_id=context.aws_access_key_id,
                    aws_secret_access_key=context.aws_secret_access_key,
                    region=region,
                    include_stopped_resources=context.include_stopped_resources,
                )
            elif context.provider == CloudProvider.GCP:
                crawler = MultiCloudCrawlerFactory.get_crawler_for_resource_type_string(
                    provider=context.provider,
                    workspace_id=context.workspace_id,
                    resource_type_string=service,
                    project_id=context.project_id,
                    service_account_key=context.service_account_key,
                    region=region,
                    include_stopped_resources=context.include_stopped_resources,
                )
            elif context.provider == CloudProvider.AZURE:
                # This shouldn't be reached as Azure contexts aren't created yet
                raise NotImplementedError("Azure crawlers not yet implemented")
            else:
                raise ValueError(f"Unsupported provider: {context.provider}")

            # Use the provided session instead of creating a new one
            crawler.crawl_resources_and_metrics(session)
            return True

        except Exception as e:
            logger.error(
                f"Failed to scan {service} resources in {region} for {context.provider}: {str(e)}"
            )
            return False

    def scan_resources(
        self,
        selected_workspaces: list[UUID] | None = None,
        providers: list[CloudProvider] | None = None,
        types: list[str] | None = None,
    ):
        """Scan configured resources across workspaces and cloud providers using CloudSyncConfig"""
        # Get all workspaces to process
        workspaces = self.workspace_repo.get_all_workspaces(selected_workspaces)

        # Collect all scan operations to perform
        scan_operations = []

        # Step 1: Prepare all operations first
        for workspace in workspaces:
            logger.info(f"Preparing resource scan for workspace {workspace.id}")

            # Get enabled cloud sync configurations for this workspace
            cloud_sync_configs = self._get_enabled_cloud_sync_configs(workspace.id)

            if not cloud_sync_configs:
                logger.info(
                    f"No enabled cloud sync configurations found for workspace {workspace.id}"
                )
                continue

            for config in cloud_sync_configs:
                try:
                    # Get the connection for this configuration
                    connection = self._get_connection(config.connection_id)
                    if not connection:
                        logger.warning(
                            f"Connection {config.connection_id} not found for config {config.id}"
                        )
                        continue

                    if (
                        connection.connection_type != ConnectionType.BUILTIN
                        and connection.builtin_connection_type
                        != BuiltinConnectionType.CLOUD
                    ):
                        logger.warning(
                            f"Connection {config.connection_id} is not a cloud connection"
                        )
                        continue

                    # Validate workspace and get scanning context
                    context = self._validate_workspace_with_config(
                        workspace, config, connection
                    )
                    if not context:
                        logger.warning(
                            f"Skipping workspace {workspace.id} config {config.id} due to invalid configuration"
                        )
                        continue

                    # Filter services by types if specified (from API call)
                    services_to_scan = context.services
                    if types:
                        # Filter to only requested types that are supported
                        filtered_services = set()
                        for service in services_to_scan:
                            if service.value in types:
                                filtered_services.add(service)
                        services_to_scan = filtered_services

                    if not services_to_scan:
                        logger.warning(
                            f"No matching services to scan for workspace {workspace.id} config {config.id}"
                        )
                        continue

                    # Add all service-region combinations to the operations list
                    for service in services_to_scan:
                        for region in context.regions:
                            scan_operations.append((context, service.value, region))

                except Exception as e:
                    logger.error(
                        f"Error preparing scan for workspace {workspace.id} config {config.id}: {str(e)}"
                    )
                    continue

        # Log the total number of operations
        total_operations = len(scan_operations)
        logger.info(
            f"Prepared {total_operations} scan operations across {len(workspaces)} workspaces and {len(providers) if providers else 0} providers"
        )

        # Step 2: Process operations in batches to limit concurrent connections
        successful_operations = 0
        failed_operations = 0

        # Process in batches
        for i in range(0, len(scan_operations), MAX_BATCH_SIZE):
            batch = scan_operations[i : i + MAX_BATCH_SIZE]
            batch_start_time = time.time()

            logger.info(
                f"Processing batch {i // MAX_BATCH_SIZE + 1}/{(total_operations - 1) // MAX_BATCH_SIZE + 1} with {len(batch)} operations"
            )

            # Use a single session for each batch with timeout
            with get_scan_session() as batch_session:
                for context, service, region in batch:
                    operation_start_time = time.time()

                    try:
                        logger.info(
                            f"Scanning {service} in {region} for workspace {context.workspace_id} provider {context.provider}"
                        )
                        success = self._scan_service_in_region_with_session(
                            batch_session, context, service, region
                        )

                        if success:
                            successful_operations += 1
                        else:
                            failed_operations += 1

                        operation_duration = time.time() - operation_start_time
                        logger.info(
                            f"Scan of {service} in {region} for {context.provider} completed in {operation_duration:.2f}s"
                        )

                    except Exception as e:
                        failed_operations += 1
                        logger.error(
                            f"Error scanning {service} in {region} for {context.provider}: {str(e)}"
                        )
                        # Continue with next operation without failing the entire batch

                # Session commit is handled automatically by get_task_session_sync
                batch_duration = time.time() - batch_start_time
                logger.info(f"Batch completed in {batch_duration:.2f}s")

        # Log summary
        logger.info(
            f"Multicloud resource scan completed: {successful_operations} successful, "
            f"{failed_operations} failed operations"
        )


@celery_app.task(
    name="multicloud_scan_tasks.scan_multicloud_resources_by_workspace",
    priority=LOW_PRIORITY,
    time_limit=1800,  # 30 minutes timeout
    soft_time_limit=1700,  # 28 minutes soft timeout
)
def scan_multicloud_resources_by_workspace(
    workspace_id: UUID,
    providers: list[str] | None = None,
    types: list[str] | None = None,
):
    """Consolidated Celery task for multicloud resource scanning for a specific workspace"""
    logger.info(f"Starting multicloud resource scan for workspace {workspace_id}")
    try:
        # Convert string provider names to CloudProvider enums
        cloud_providers = None
        if providers:
            cloud_providers = []
            for provider_str in providers:
                try:
                    provider = CloudProvider(provider_str)
                    cloud_providers.append(provider)
                except ValueError:
                    logger.warning(f"Unsupported provider: {provider_str}, skipping")

        with get_scan_session() as session:
            scanner = MultiCloudResourceScanner(session)
            scanner.scan_resources(
                selected_workspaces=[workspace_id],
                providers=cloud_providers,
                types=types,
            )
        logger.info(f"Completed multicloud resource scan for workspace {workspace_id}")
    except Exception as e:
        logger.error(
            f"Multicloud resource scanning failed for workspace {workspace_id}: {str(e)}"
        )
        raise
