import asyncio
from datetime import UTC, datetime, timedelta
from uuid import UUID

from app.core.celery_app import LOW_PRIORITY, celery_app
from app.core.db_session import get_task_session
from app.logger import logger
from app.models import BuiltinConnectionType, Connection, ConnectionType
from app.models.clouds import CloudProvider
from app.repositories.cloud_sync_config import CloudSyncConfigRepository
from app.repositories.connection import ConnectionRepository
from app.tasks.multicloud_scan_tasks import scan_multicloud_resources_by_workspace
from app.utils import ensure_utc


async def get_connection_for_config(
    connection_id: UUID, workspace_id: UUID, session
) -> Connection | None:
    """Get connection for a cloud sync config"""
    try:
        connection_repository = ConnectionRepository(session)
        connection = await connection_repository.get_connection(
            conn_id=connection_id, workspace_id=workspace_id
        )

        if (
            connection
            and connection.connection_type == ConnectionType.BUILTIN
            and connection.builtin_connection_type == BuiltinConnectionType.CLOUD
        ):
            return connection
        return None
    except Exception as e:
        logger.error(f"Error getting connection {connection_id}: {str(e)}")
        return None


@celery_app.task(
    name="cloud_sync_scheduler_tasks.process_scheduled_syncs",
    priority=LOW_PRIORITY,
    time_limit=300,  # 5 minutes timeout
    soft_time_limit=270,  # 4.5 minutes soft timeout
)
def process_scheduled_syncs():
    """Process all enabled cloud sync configurations and trigger scans based on refresh_interval"""
    try:
        asyncio.run(_process_scheduled_syncs())
        logger.info("Completed processing scheduled syncs")
    except Exception as e:
        logger.error(f"Error processing scheduled syncs: {str(e)}")
        raise


async def _process_scheduled_syncs():
    """Async implementation of scheduled sync processing"""
    async with get_task_session() as session:
        try:
            repository = CloudSyncConfigRepository(session)
            enabled_configs = await repository.get_enabled_configs()

            if not enabled_configs:
                logger.info("No enabled cloud sync configurations found")
                return

            current_time = datetime.now(UTC)
            triggered_count = 0

            logger.info(
                f"Processing {len(enabled_configs)} enabled cloud sync configurations"
            )

            for config in enabled_configs:
                try:
                    # Calculate next scan time based on refresh_interval
                    if config.last_sync_at:
                        # Ensure last_sync_at is timezone-aware
                        last_sync_at_aware = ensure_utc(config.last_sync_at)
                        next_scan_time = last_sync_at_aware + timedelta(
                            minutes=config.refresh_interval
                        )
                    else:
                        # If never synced, scan immediately
                        next_scan_time = current_time

                    # Check if it's time to scan
                    if current_time >= next_scan_time:
                        # Get connection to determine provider
                        connection = await get_connection_for_config(
                            config.connection_id, config.workspace_id, session
                        )

                        if connection:
                            provider_str = CloudProvider(
                                connection.prefix.upper()
                            ).value
                            if provider_str:
                                # Trigger the scan
                                scan_multicloud_resources_by_workspace.delay(
                                    workspace_id=config.workspace_id,
                                    providers=[provider_str],
                                    types=config.selected_resources
                                    if config.selected_resources
                                    else None,
                                )

                                # Update last sync time
                                await repository.update_last_sync_time(
                                    config.id, current_time
                                )
                                triggered_count += 1

                                logger.info(
                                    f"Triggered scheduled sync for workspace {config.workspace_id}, "
                                    f"connection {config.connection_id}, provider {provider_str}"
                                )
                            else:
                                logger.warning(
                                    f"Unknown provider prefix {connection.prefix} for connection {config.connection_id}"
                                )
                        else:
                            logger.warning(
                                f"Connection {config.connection_id} not found or not a cloud connection"
                            )
                    else:
                        logger.debug(
                            f"Skipping config {config.id} - next scan at {next_scan_time}, "
                            f"current time {current_time}"
                        )

                except Exception as e:
                    logger.error(
                        f"Error processing config {config.id} for workspace {config.workspace_id}: {str(e)}"
                    )
                    # Continue processing other configs
                    continue

            logger.info(f"Triggered {triggered_count} scheduled syncs")

        except Exception as e:
            logger.error(f"Error in scheduled sync processing: {str(e)}")
            raise
