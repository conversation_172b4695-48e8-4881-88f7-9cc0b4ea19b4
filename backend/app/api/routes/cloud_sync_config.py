import uuid
from typing import Any

from fastapi import APIRouter, HTTPException, Query
from starlette.responses import JSONResponse

from app.api.deps import CurrentUser, SessionAsyncDep
from app.exceptions import RateLimitExceededException
from app.logger import logger
from app.models import (
    CloudSyncConfigCreate,
    CloudSyncConfigPublic,
    CloudSyncConfigUpdate,
    Message,
    ResourceTypesResponse,
)
from app.services.cloud_sync_config_service import CloudSyncConfigService

router = APIRouter()


@router.get("/resource-types", response_model=ResourceTypesResponse)
async def get_resource_types(
    *,
    session: SessionAsyncDep,
    current_user: CurrentUser,
    cloud_provider: str = Query(..., description="Cloud provider (AWS, GCP, AZURE)"),
) -> Any:
    """Get available resource types for a specific cloud provider."""
    try:
        service = CloudSyncConfigService(session)
        resource_types = await service.get_resource_types_for_provider(cloud_provider)
        return resource_types
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception:
        logger.exception("Error retrieving resource types.")
        raise HTTPException(
            status_code=500, detail="Resource type service temporarily unavailable"
        )


@router.post("/", response_model=CloudSyncConfigPublic)
async def create_or_update_config(
    *,
    session: SessionAsyncDep,
    current_user: CurrentUser,
    config_data: CloudSyncConfigCreate,
) -> Any:
    """Create or update a cloud sync configuration."""
    try:
        service = CloudSyncConfigService(session)
        config = await service.update_or_create_config(
            workspace_id=current_user.current_workspace_id, data=config_data
        )
        return config
    except RateLimitExceededException as e:
        raise HTTPException(
            status_code=429,
            detail=e.message,
            headers={"Retry-After": str(e.retry_after)},
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception:
        logger.exception("Error creating/updating cloud sync configuration.")
        raise HTTPException(
            status_code=500, detail="Configuration service temporarily unavailable"
        )


@router.get("/", response_model=list[CloudSyncConfigPublic])
async def get_workspace_configs(
    *,
    session: SessionAsyncDep,
    current_user: CurrentUser,
) -> Any:
    """Get all cloud sync configurations for the current workspace."""
    try:
        service = CloudSyncConfigService(session)
        configs = await service.get_config_by_workspace(
            workspace_id=current_user.current_workspace_id
        )
        return configs
    except Exception:
        logger.exception("Error retrieving cloud sync configurations.")
        raise HTTPException(
            status_code=500, detail="Configuration service temporarily unavailable"
        )


@router.put("/{config_id}", response_model=CloudSyncConfigPublic)
async def update_config(
    *,
    session: SessionAsyncDep,
    current_user: CurrentUser,
    config_id: uuid.UUID,
    config_data: CloudSyncConfigUpdate,
) -> Any:
    """Update a specific cloud sync configuration."""
    try:
        service = CloudSyncConfigService(session)
        config = await service.update_config(
            workspace_id=current_user.current_workspace_id,
            config_id=config_id,
            data=config_data,
        )
        return config
    except RateLimitExceededException as e:
        raise HTTPException(
            status_code=429,
            detail=e.message,
            headers={"Retry-After": str(e.retry_after)},
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception:
        logger.exception("Error updating cloud sync configuration.")
        raise HTTPException(
            status_code=500, detail="Configuration service temporarily unavailable"
        )


@router.delete("/{config_id}", response_model=Message)
async def delete_config(
    *,
    session: SessionAsyncDep,
    current_user: CurrentUser,
    config_id: uuid.UUID,
) -> Any:
    """Delete a cloud sync configuration."""
    try:
        service = CloudSyncConfigService(session)
        success = await service.delete_config(
            workspace_id=current_user.current_workspace_id, config_id=config_id
        )
        if not success:
            raise HTTPException(status_code=404, detail="Configuration not found")

        return JSONResponse(
            content={"message": "Configuration successfully deleted"},
            status_code=200,
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception:
        logger.exception("Error deleting cloud sync configuration.")
        raise HTTPException(
            status_code=500, detail="Configuration service temporarily unavailable"
        )


@router.post("/{config_id}/sync", response_model=Message)
async def trigger_manual_sync(
    *,
    session: SessionAsyncDep,
    current_user: CurrentUser,
    config_id: uuid.UUID,
) -> Any:
    """Trigger a manual sync for a specific configuration."""
    try:
        service = CloudSyncConfigService(session)

        # Trigger the scan with rate limiting
        await service.trigger_manual_sync(
            workspace_id=current_user.current_workspace_id, config_id=config_id
        )

        return JSONResponse(
            content={"message": "Manual sync triggered successfully"},
            status_code=200,
        )
    except RateLimitExceededException as e:
        raise HTTPException(
            status_code=429,
            detail=e.message,
            headers={"Retry-After": str(e.retry_after)},
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception:
        logger.exception("Error triggering manual sync.")
        raise HTTPException(
            status_code=500, detail="Sync service temporarily unavailable"
        )
