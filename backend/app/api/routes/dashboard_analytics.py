from fastapi import APIRouter, HTTPException, Query

from app.api.deps import CurrentUser, SessionAsyncDep
from app.models.clouds import CloudProvider
from app.models.resources import ResourceCategory
from app.schemas.dashboard_analytics import (
    DashboardAnalyticsQuery,
    DashboardAnalyticsResponse,
    HighConsumingServicesResponse,
    TopRecommendationsResponse,
)
from app.services.dashboard_analytics_service import DashboardAnalyticsService

router = APIRouter()


@router.get(
    "/analytics",
    response_model=DashboardAnalyticsResponse,
    summary="Get comprehensive dashboard analytics",
    description="Get complete dashboard analytics including cost overview, high-consuming services, and top recommendations",
)
async def get_dashboard_analytics(
    current_user: CurrentUser,
    session: SessionAsyncDep,
    cloud_provider: CloudProvider | None = Query(
        None, description="Filter by specific cloud provider"
    ),
    service_category: ResourceCategory | None = Query(
        None, description="Filter by service category"
    ),
    min_savings_amount: float | None = Query(
        None, description="Minimum monthly savings threshold", ge=0
    ),
    top_services_limit: int = Query(
        10, description="Number of top services to return", ge=5, le=20
    ),
) -> DashboardAnalyticsResponse:
    """Get comprehensive dashboard analytics for the main dashboard page"""

    if not current_user.current_workspace_id:
        raise HTTPException(status_code=400, detail="No workspace selected")

    # Create query parameters
    query_params = DashboardAnalyticsQuery(
        workspace_id=current_user.current_workspace_id,
        cloud_provider=cloud_provider,
        service_category=service_category,
        min_savings_amount=min_savings_amount,
        top_services_limit=top_services_limit,
    )

    # Get analytics data
    service = DashboardAnalyticsService(session)
    try:
        analytics = await service.get_dashboard_analytics(query_params)
        return analytics
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) from e


@router.get(
    "/high-consuming-services",
    response_model=HighConsumingServicesResponse,
    summary="Get high-consuming services",
    description="Get services with highest potential monthly savings opportunities",
)
async def get_high_consuming_services(
    current_user: CurrentUser,
    session: SessionAsyncDep,
    cloud_provider: CloudProvider | None = Query(
        None, description="Filter by specific cloud provider"
    ),
    service_category: ResourceCategory | None = Query(
        None, description="Filter by service category"
    ),
    min_savings_amount: float | None = Query(
        None, description="Minimum monthly savings threshold", ge=0
    ),
    top_services_limit: int = Query(
        10, description="Number of top services to return", ge=5, le=20
    ),
) -> HighConsumingServicesResponse:
    """Get high-consuming services with monthly savings opportunities"""

    if not current_user.current_workspace_id:
        raise HTTPException(status_code=400, detail="No workspace selected")

    # Create query parameters
    query_params = DashboardAnalyticsQuery(
        workspace_id=current_user.current_workspace_id,
        cloud_provider=cloud_provider,
        service_category=service_category,
        min_savings_amount=min_savings_amount,
        top_services_limit=top_services_limit,
    )

    # Get high consuming services data
    service = DashboardAnalyticsService(session)
    try:
        services = await service.get_high_consuming_services(query_params)
        return services
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) from e


@router.get(
    "/top-recommendations",
    response_model=TopRecommendationsResponse,
    summary="Get top 5 recommendations",
    description="Get top 5 potential monthly savings recommendations with effort and risk assessment",
)
async def get_top_recommendations(
    current_user: CurrentUser,
    session: SessionAsyncDep,
    cloud_provider: CloudProvider | None = Query(
        None, description="Filter by specific cloud provider"
    ),
    service_category: ResourceCategory | None = Query(
        None, description="Filter by service category"
    ),
    min_savings_amount: float | None = Query(
        None, description="Minimum monthly savings threshold", ge=0
    ),
) -> TopRecommendationsResponse:
    """Get top 5 potential monthly savings recommendations"""

    if not current_user.current_workspace_id:
        raise HTTPException(status_code=400, detail="No workspace selected")

    # Create query parameters (top_services_limit not relevant for recommendations)
    query_params = DashboardAnalyticsQuery(
        workspace_id=current_user.current_workspace_id,
        cloud_provider=cloud_provider,
        service_category=service_category,
        min_savings_amount=min_savings_amount,
        top_services_limit=10,  # Default value, not used in recommendations
    )

    # Get top recommendations data
    service = DashboardAnalyticsService(session)
    try:
        recommendations = await service.get_top_recommendations(query_params)
        return recommendations
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) from e
