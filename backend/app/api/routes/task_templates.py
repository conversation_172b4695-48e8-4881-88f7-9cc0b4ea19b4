import uuid
from typing import Any

from fastapi import APIRouter, HTTPException, Query

from app.api.deps import CurrentUser, SessionAsyncDep
from app.logger import logger
from app.models import TaskCategoryEnum, TaskServiceEnum
from app.schemas.task_template import (
    TaskTemplateCreate,
    TaskTemplateList,
    TaskTemplateResponse,
    TaskTemplateUpdate,
)
from app.services.template_service import (
    TaskTemplateGenerateService,
    TaskTemplateService,
)

router = APIRouter()


@router.post("/generate", response_model=TaskTemplateResponse)
def generate(input: str) -> TaskTemplateResponse:
    """
    Generate the task template based on user's input
    """
    try:
        template_service = TaskTemplateGenerateService()
        response = template_service.generate_template(input=input)
        return response
    except Exception:
        logger.exception("Error generating task template.")
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred while generating the task template.",
        )


@router.post("/", response_model=TaskTemplateResponse)
async def create_template(
    *,
    session: SessionAsyncDep,
    current_user: CurrentUser,
    template_in: TaskTemplateCreate,
    is_default: bool = Query(False),
) -> Any:
    """Create new task template."""
    service = TaskTemplateService(session)
    try:
        template = await service.create_template(
            workspace_id=current_user.current_workspace_id,
            data=template_in,
            is_default=is_default,
        )
        return template
    except Exception as e:
        logger.exception(f"Error creating template: {e}")
        raise HTTPException(status_code=500, detail="Error creating template")


@router.get("/", response_model=TaskTemplateList)
async def list_templates(
    session: SessionAsyncDep,
    current_user: CurrentUser,
    category: list[TaskCategoryEnum] | None = Query(None),
    services: list[TaskServiceEnum] | None = Query(None),
    include_defaults: bool = Query(True),
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=500),
    search_query: str | None = None,
) -> Any:
    """List task templates with optional category and service filters."""
    template_service = TaskTemplateService(session)
    try:
        # Get current workspace provider for auto-filtering
        current_workspace = next(
            (
                ws
                for ws in current_user.workspaces
                if ws.id == current_user.current_workspace_id
            ),
            None,
        )
        workspace_provider = current_workspace.provider if current_workspace else None
        print(f"workspace_provider: {workspace_provider}")
        templates, total = await template_service.list_templates(
            workspace_id=current_user.current_workspace_id,
            skip=skip,
            limit=limit,
            category=category,
            services=services,
            include_defaults=include_defaults,
            search_query=search_query,
            workspace_provider=workspace_provider,
        )
        return TaskTemplateList(data=templates, total=total)
    except Exception as e:
        logger.exception(f"Error listing templates: {e}")
        raise HTTPException(status_code=500, detail="Error listing templates")


@router.get("/{template_id}", response_model=TaskTemplateResponse)
async def get_template(
    template_id: uuid.UUID, session: SessionAsyncDep, current_user: CurrentUser
) -> Any:
    """Get template by ID."""
    service = TaskTemplateService(session)
    template = await service.get_template(
        template_id=template_id, workspace_id=current_user.current_workspace_id
    )
    if not template:
        raise HTTPException(status_code=404, detail="Template not found")
    return template


@router.put("/{template_id}", response_model=TaskTemplateResponse)
async def update_template(
    *,
    template_id: uuid.UUID,
    template_in: TaskTemplateUpdate,
    session: SessionAsyncDep,
    current_user: CurrentUser,
) -> Any:
    """Update template."""
    service = TaskTemplateService(session)
    try:
        template = await service.update_template(
            template_id=template_id,
            workspace_id=current_user.current_workspace_id,
            data=template_in,
        )
        if not template:
            raise HTTPException(status_code=404, detail="Template not found")
        return template
    except Exception as e:
        logger.exception(f"Error updating template: {e}")
        raise HTTPException(status_code=500, detail="Error updating template")


@router.delete("/{template_id}")
async def delete_template(
    session: SessionAsyncDep, current_user: CurrentUser, template_id: uuid.UUID
) -> Any:
    """Delete template."""
    service = TaskTemplateService(session)
    try:
        success = await service.delete_template(
            template_id=template_id, workspace_id=current_user.current_workspace_id
        )
        if not success:
            raise HTTPException(status_code=404, detail="Template not found")
        return {"message": "Template deleted successfully"}
    except Exception as e:
        logger.exception(f"Error deleting template: {e}")
        raise HTTPException(status_code=500, detail="Error deleting template")
