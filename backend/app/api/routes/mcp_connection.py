import uuid
from typing import Any

from fastapi import APIRouter, HTTPException

from app.api.deps import CurrentUser, SessionAsyncDep
from app.models import (
    MCPConnectionCreate,
    MCPConnectionPublic,
    MCPConnectionsPublic,
    MCPConnectionToolUpdateRequest,
    MCPConnectionUpdate,
)
from app.services.mcp_conn_service import MCPConnectionService

router = APIRouter()


@router.post("", response_model=MCPConnectionPublic)
async def create_mcp_connection(
    *,
    session: SessionAsyncDep,
    current_user: CurrentUser,
    mcp_conn_create: MCPConnectionCreate,
) -> Any:
    mcp_conn_service = MCPConnectionService(session)
    mcp_server = await mcp_conn_service.create_mcp_connection(
        workspace_id=current_user.current_workspace_id, data=mcp_conn_create
    )
    return mcp_server


@router.get("", response_model=MCPConnectionsPublic)
async def get_mcp_connections(
    *,
    session: SessionAsyncDep,
    current_user: Current<PERSON>ser,
) -> Any:
    mcp_conn_service = MCPConnectionService(session)
    conns = await mcp_conn_service.get_mcp_connections(
        current_user.current_workspace_id
    )
    return conns


@router.put("/{mcp_conn_id}", response_model=MCPConnectionPublic)
async def update_mcp_connection(
    *,
    session: SessionAsyncDep,
    current_user: CurrentUser,
    mcp_conn_id: uuid.UUID,
    mcp_conn_update: MCPConnectionUpdate,
) -> Any:
    mcp_conn_service = MCPConnectionService(session)
    updated_server = await mcp_conn_service.update_mcp_connection(
        workspace_id=current_user.current_workspace_id,
        conn_id=mcp_conn_id,
        data=mcp_conn_update,
    )
    if not updated_server:
        raise HTTPException(status_code=404, detail="Connection not found")
    return updated_server


@router.delete("/{mcp_conn_id}", response_model=dict)
async def delete_mcp_connection(
    *,
    session: SessionAsyncDep,
    current_user: CurrentUser,
    mcp_conn_id: uuid.UUID,
) -> Any:
    mcp_conn_service = MCPConnectionService(session)
    await mcp_conn_service.delete_mcp_connection(
        workspace_id=current_user.current_workspace_id, conn_id=mcp_conn_id
    )
    return {"message": "Connection successfully deleted"}


@router.patch("/{mcp_conn_id}/tool", response_model=MCPConnectionPublic)
async def update_mcp_connection_tools(
    session: SessionAsyncDep,
    current_user: CurrentUser,
    mcp_conn_id: uuid.UUID,
    request: MCPConnectionToolUpdateRequest,
) -> Any:
    """Update the tools for a mcp connection."""
    mcp_conn_service = MCPConnectionService(session)
    updated_conn = await mcp_conn_service.update_mcp_connection_tool(
        workspace_id=current_user.current_workspace_id,
        connection_id=mcp_conn_id,
        tool_name=request.tool_name,
        is_enabled=request.is_enabled,
        is_required_permission=request.is_required_permission,
    )
    return updated_conn
