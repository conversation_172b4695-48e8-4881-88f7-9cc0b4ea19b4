import uuid
from typing import Any

from fastapi import APIRouter

from app.api.deps import CurrentUser, SessionAsyncDep
from app.models import (
    BuiltinConnectionInstallRequest,
    BuiltinConnectionPublic,
    BuiltinConnectionsPublic,
    BuiltinConnectionToolUpdateRequest,
    BuiltinConnectionUpdateRequest,
    SandboxConnectionInstallRequest,
)
from app.services.builtin_conn_service import BuiltinConnectionService

router = APIRouter()


@router.get("", response_model=BuiltinConnectionsPublic)
async def get_builtin_connections(
    session: SessionAsyncDep,
    current_user: CurrentUser,
) -> Any:
    """Get all available builtin connections."""
    conn_service = BuiltinConnectionService(session)
    builtin_conns = await conn_service.get_builtin_connections(
        user_id=current_user.id,
        workspace_id=current_user.current_workspace_id,
        filter_by_provider=True,
    )
    return builtin_conns


@router.post("/{builtin_id}", response_model=BuiltinConnectionPublic)
async def install_builtin_connection(
    session: SessionAsyncDep,
    current_user: CurrentUser,
    builtin_id: uuid.UUID,
    request: BuiltinConnectionInstallRequest,
) -> Any:
    """Install a builtin connection to the user's workspace."""
    conn_service = BuiltinConnectionService(session)
    installed_conn = await conn_service.install_builtin_connection(
        workspace_id=current_user.current_workspace_id,
        builtin_connection_id=builtin_id,
        env=[env.model_dump() for env in request.env],
    )
    return installed_conn


@router.patch("/{builtin_id}", response_model=BuiltinConnectionPublic)
async def update_builtin_connection(
    session: SessionAsyncDep,
    current_user: CurrentUser,
    builtin_id: uuid.UUID,
    request: BuiltinConnectionUpdateRequest,
) -> Any:
    """Update a builtin connection to the user's workspace."""
    conn_service = BuiltinConnectionService(session)
    updated_conn = await conn_service.update_builtin_connection(
        workspace_id=current_user.current_workspace_id,
        builtin_connection_id=builtin_id,
        env=[env.model_dump() for env in request.env],
    )
    return updated_conn


@router.delete("/{builtin_id}", response_model=dict)
async def delete_builtin_connection(
    session: SessionAsyncDep,
    current_user: CurrentUser,
    builtin_id: uuid.UUID,
) -> Any:
    """Delete a builtin connection from the user's workspace."""
    conn_service = BuiltinConnectionService(session)
    await conn_service.delete_builtin_connection(
        workspace_id=current_user.current_workspace_id,
        builtin_connection_id=builtin_id,
    )
    return {"message": "Builtin connection deleted successfully"}


@router.patch("/{builtin_id}/tool", response_model=BuiltinConnectionPublic)
async def update_builtin_connection_tools(
    session: SessionAsyncDep,
    current_user: CurrentUser,
    builtin_id: uuid.UUID,
    request: BuiltinConnectionToolUpdateRequest,
) -> Any:
    """Update the tools for a builtin connection."""
    conn_service = BuiltinConnectionService(session)
    updated_conn = await conn_service.update_builtin_connection_tool(
        workspace_id=current_user.current_workspace_id,
        connection_id=builtin_id,
        tool_name=request.tool_name,
        is_enabled=request.is_enabled,
        is_required_permission=request.is_required_permission,
    )
    return updated_conn


@router.post("/sandbox/install", response_model=BuiltinConnectionsPublic)
async def install_sandbox_connection(
    session: SessionAsyncDep,
    current_user: CurrentUser,
    request: SandboxConnectionInstallRequest,
) -> Any:
    """Install a sandbox connection to the user's workspace."""
    conn_service = BuiltinConnectionService(session)
    installed_conn = await conn_service.install_sandbox_connection(
        workspace_id=current_user.current_workspace_id,
        sandbox_conn_ids=request.sandbox_conn_ids,
        filter_by_conn_ids=True,
    )
    return installed_conn
