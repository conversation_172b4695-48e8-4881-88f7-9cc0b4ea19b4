from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Request
from slowapi import Limiter
from slowapi.util import get_remote_address
from sqlmodel.ext.asyncio.session import AsyncSession

from app.api.deps import get_async_session
from app.core.config import settings
from app.exceptions.legacy import RepositoryError
from app.logger import logger
from app.models import DashboardShare, ReportShare
from app.repositories.dashboard import DashboardRepository
from app.repositories.report import ReportRepository

router = APIRouter()

# Initialize rate limiter
limiter = Limiter(key_func=get_remote_address)


@router.get("/reports/{share_id}")
@limiter.limit(f"{settings.RATE_LIMIT_SHARED_REPORTS_PER_MINUTE}/minute")
async def get_shared_report(
    request: Request,
    share_id: UUID,
    async_session: AsyncSession = Depends(get_async_session),
) -> ReportShare:
    """
    Get a publicly shared report by share_id. No authentication required.
    """
    try:
        repo = ReportRepository(async_session)
        report = await repo.get_by_share_id(share_id)
        return report

    except RepositoryError as e:
        raise HTTPException(status_code=e.status_code, detail=e.message)
    except Exception as e:
        logger.exception("Internal server error.")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/dashboards/{share_id}")
@limiter.limit(f"{settings.RATE_LIMIT_SHARED_DASHBOARDS_PER_MINUTE}/minute")
async def get_shared_dashboard(
    request: Request,
    share_id: UUID,
    async_session: AsyncSession = Depends(get_async_session),
) -> DashboardShare:
    """
    Get a publicly shared dashboard by share_id. No authentication required.
    """
    try:
        repo = DashboardRepository(async_session)
        dashboard = await repo.get_by_share_id(share_id)
        return dashboard

    except RepositoryError as e:
        raise HTTPException(status_code=e.status_code, detail=e.message)
    except Exception as e:
        logger.exception("Internal server error.")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
