from typing import Any

from fastapi import API<PERSON>outer, Depends, HTTPException, Query
from sqlmodel import Session

from app.api.deps import CurrentUser, get_db
from app.logger import logger
from app.services.sample_data import SampleDataService

router = APIRouter()


@router.post("/resources")
async def create_sample_resources(
    current_user: CurrentUser,
    session: Session = Depends(get_db),
    count: int = Query(
        default=50, ge=1, le=500, description="Number of sample resources to create"
    ),
) -> Any:
    """
    Create sample resources for the current workspace.

    Args:
        count: Number of resources to create (1-500, default: 50)

    Returns:
        Summary of created resources
    """
    try:
        # Ensure workspace_id is not None
        if current_user.current_workspace_id is None:
            raise HTTPException(status_code=400, detail="No workspace selected")

        # Initialize the sample data service
        sample_service = SampleDataService(session)

        logger.info(
            f"Creating {count} sample resources for workspace {current_user.current_workspace_id}"
        )

        # Create sample resources
        created_resources = sample_service.create_sample_resources(
            workspace_id=current_user.current_workspace_id, count=count
        )

        # Prepare summary statistics
        resource_types = {}
        regions = {}
        statuses = {}

        for resource in created_resources:
            # Count by type
            resource_types[resource.type] = resource_types.get(resource.type, 0) + 1
            # Count by region
            regions[resource.region] = regions.get(resource.region, 0) + 1
            # Count by status
            statuses[resource.status] = statuses.get(resource.status, 0) + 1

        return {
            "message": f"Successfully created {len(created_resources)} sample resources",
            "summary": {
                "total_created": len(created_resources),
                "workspace_id": str(current_user.current_workspace_id),
                "resource_types": resource_types,
                "regions": regions,
                "statuses": statuses,
            },
        }

    except Exception as e:
        logger.exception(f"Error creating sample resources: {e}")
        raise HTTPException(
            status_code=500, detail=f"Unable to create sample resources: {str(e)}"
        )


@router.delete("/resources")
async def clear_sample_resources(
    current_user: CurrentUser,
    session: Session = Depends(get_db),
    confirm: bool = Query(
        default=False, description="Confirm deletion of all resources"
    ),
) -> Any:
    """
    Clear all resources for the current workspace.

    ⚠️ WARNING: This will delete ALL resources in the workspace.
    Use with extreme caution!

    Args:
        confirm: Must be set to true to confirm the deletion

    Returns:
        Summary of deleted resources
    """
    try:
        # Ensure workspace_id is not None
        if current_user.current_workspace_id is None:
            raise HTTPException(status_code=400, detail="No workspace selected")

        if not confirm:
            raise HTTPException(
                status_code=400,
                detail="Confirmation required. Set 'confirm=true' to proceed with deletion.",
            )

        # Initialize the sample data service
        sample_service = SampleDataService(session)

        logger.warning(
            f"Clearing ALL resources for workspace {current_user.current_workspace_id}"
        )

        # Clear all resources
        deleted_count = sample_service.clear_sample_resources(
            workspace_id=current_user.current_workspace_id
        )

        return {
            "message": f"Successfully deleted {deleted_count} resources",
            "summary": {
                "total_deleted": deleted_count,
                "workspace_id": str(current_user.current_workspace_id),
            },
        }

    except Exception as e:
        logger.exception(f"Error clearing resources: {e}")
        raise HTTPException(
            status_code=500, detail=f"Unable to clear resources: {str(e)}"
        )


@router.get("/resources/preview")
async def preview_sample_resources(
    current_user: CurrentUser,
    count: int = Query(
        default=5, ge=1, le=20, description="Number of sample resources to preview"
    ),
) -> Any:
    """
    Preview sample resources without creating them in the database.

    Args:
        count: Number of resources to preview (1-20, default: 5)

    Returns:
        Preview of sample resources that would be created
    """
    try:
        # Ensure workspace_id is not None
        if current_user.current_workspace_id is None:
            raise HTTPException(status_code=400, detail="No workspace selected")

        # Initialize the sample data service (no session needed for preview)
        sample_service = SampleDataService(None)

        # Generate sample resources (without saving to DB)
        sample_resources = sample_service.generate_sample_resources(
            workspace_id=current_user.current_workspace_id, count=count
        )

        # Convert to dictionaries for response
        preview_data = []
        for resource in sample_resources:
            preview_data.append(
                {
                    "name": resource.name,
                    "type": resource.type,
                    "region": resource.region,
                    "status": resource.status,
                    "resource_id": resource.resource_id,
                    "description": resource.description,
                    "tags": resource.tags,
                    "configurations": resource.configurations,
                }
            )

        return {
            "message": f"Preview of {len(preview_data)} sample resources",
            "preview": preview_data,
            "workspace_id": str(current_user.current_workspace_id),
        }

    except Exception as e:
        logger.exception(f"Error previewing sample resources: {e}")
        raise HTTPException(
            status_code=500, detail=f"Unable to preview sample resources: {str(e)}"
        )
