from fastapi import APIRouter

from app.api.routes import (
    agent_connections,
    agents,
    agents_builtin_tools,
    alerts,
    attachments,
    auth,
    autonomous_agent,
    aws_accounts,
    builtin_connection,
    builtin_tools,
    cloud_sync_config,
    console_proxy,
    dashboard_analytics,
    dashboards,
    gcp_accounts,
    google_login,
    kb,
    login,
    mcp_connection,
    message_feedback,
    module_setting,
    notifications,
    onboarding,
    public,
    quotas,
    recommendations,
    reports,
    resources,
    sample_data,
    share_chat,
    subscriptions,
    task_templates,
    tasks,
    users,
    utils,
    workspaces,
)

api_router = APIRouter()

api_router.include_router(login.router, tags=["login"])
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(utils.router, prefix="/utils", tags=["utils"])
api_router.include_router(
    aws_accounts.router, prefix="/aws-accounts", tags=["aws-accounts"]
)
api_router.include_router(resources.router, prefix="/resources", tags=["resources"])
api_router.include_router(
    recommendations.router, prefix="/recommendations", tags=["recommendations"]
)
api_router.include_router(workspaces.router, prefix="/workspaces", tags=["workspaces"])
api_router.include_router(console_proxy.router, tags=["console-proxy"])
api_router.include_router(agents.router, prefix="/agents", tags=["agents"])
api_router.include_router(tasks.router, prefix="/tasks", tags=["tasks"])
api_router.include_router(
    autonomous_agent.router,
    prefix="/autonomous-agents",
    tags=["autonomous-agents"],
)
api_router.include_router(google_login.router, prefix="/google", tags=["google"])
api_router.include_router(quotas.router, prefix="/quotas", tags=["quotas"])
api_router.include_router(reports.router, prefix="/reports", tags=["reports"])
api_router.include_router(dashboards.router, prefix="/dashboards", tags=["dashboards"])
api_router.include_router(
    dashboard_analytics.router,
    prefix="/dashboard-analytics",
    tags=["dashboard-analytics"],
)
api_router.include_router(
    task_templates.router, prefix="/task_templates", tags=["task_templates"]
)
api_router.include_router(
    kb.router,
    prefix="/knowledge_base",
    tags=["knowledge_base"],
)
api_router.include_router(
    subscriptions.router, prefix="/subscriptions", tags=["subscriptions"]
)
api_router.include_router(
    module_setting.router, prefix="/module_setting", tags=["module_setting"]
)
api_router.include_router(alerts.router, prefix="/alerts", tags=["alerts"])
api_router.include_router(auth.router, prefix="/auth", tags=["auth"])
api_router.include_router(
    notifications.router, prefix="/notifications", tags=["notifications"]
)
api_router.include_router(
    message_feedback.router, prefix="/message-feedback", tags=["message-feedback"]
)
api_router.include_router(
    cloud_sync_config.router, prefix="/cloud-sync-config", tags=["cloud-sync-config"]
)
api_router.include_router(
    attachments.router, prefix="/attachments", tags=["attachments"]
)
api_router.include_router(
    builtin_tools.router, prefix="/builtin-tools", tags=["builtin-tools"]
)

# Share Chat
api_router.include_router(share_chat.router, prefix="/share-chat", tags=["share-chat"])

# GCP Accounts
api_router.include_router(
    gcp_accounts.router, prefix="/gcp-accounts", tags=["gcp-accounts"]
)

# Agents Builtin Tools
api_router.include_router(
    agents_builtin_tools.router,
    prefix="/agents-builtin-tools",
    tags=["agents-builtin-tools"],
)

# Agent Connections
api_router.include_router(
    agent_connections.router,
    prefix="/agents-connections",
    tags=["agents-connections"],
)

# Onboarding
api_router.include_router(
    onboarding.router,
    prefix="/onboarding",
    tags=["onboarding"],
)

# Sample Data
api_router.include_router(
    sample_data.router,
    prefix="/sample-data",
    tags=["sample-data"],
)

# Builtin Connection
api_router.include_router(
    builtin_connection.router,
    prefix="/builtin-connection",
    tags=["builtin-connection"],
)

# MCP Connection
api_router.include_router(
    mcp_connection.router, prefix="/mcp-connection", tags=["mcp-connection"]
)

# Public endpoints (no authentication required)
api_router.include_router(public.router, prefix="/public", tags=["public"])
