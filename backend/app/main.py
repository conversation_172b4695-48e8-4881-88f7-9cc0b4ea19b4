from contextlib import asynccontextmanager

import sentry_sdk
from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import ORJSONResponse
from fastapi.routing import APIRoute
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.errors import RateLimitExceeded
from slowapi.util import get_remote_address

from app.api.main import api_router
from app.core.config import settings
from app.logger import config, logger


def custom_generate_unique_id(route: APIRoute) -> str:
    return f"{route.tags[0]}-{route.name}"


if settings.SENTRY_DSN and settings.ENVIRONMENT != "local":
    sentry_sdk.init(dsn=str(settings.SENTRY_DSN), enable_tracing=True)


@asynccontextmanager
async def lifespan(app: FastAPI):
    logger.info("Starting lifespan")
    # from app.core.qdrant import initialize_qdrant
    from app.worker import celery_app

    app.state.celery = celery_app

    # Initialize services
    try:
        from app.modules.multi_agents.agents import AgentFactory

        await AgentFactory.initialize()
        logger.info("AgentFactory initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize AgentFactory: {e}")
        raise

    # Perform startup initialization
    # await initialize_qdrant()

    yield


app = FastAPI(
    default_response_class=ORJSONResponse,
    title=settings.PROJECT_NAME,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    docs_url="/docs" if settings.ENVIRONMENT in ["dev", "local"] else None,
    redoc_url="/redoc" if settings.ENVIRONMENT in ["dev", "local"] else None,
    generate_unique_id_function=custom_generate_unique_id,
    lifespan=lifespan,
    debug=True,
)

# Configure rate limiting
limiter = Limiter(key_func=get_remote_address)
app.state.limiter = limiter
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)  # type: ignore[arg-type]


# Set all CORS enabled origins
if settings.all_cors_origins:
    app.add_middleware(
        CORSMiddleware,  # type: ignore[arg-type]
        allow_origins=settings.all_cors_origins,
        allow_origin_regex=r"https?://192\.168\.\d{1,3}\.\d{1,3}(:\d+)?$",
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

# Add request timing middleware for duration logging
app.middleware("http")(config.request_timing_middleware)

app.include_router(api_router, prefix=settings.API_V1_STR)
