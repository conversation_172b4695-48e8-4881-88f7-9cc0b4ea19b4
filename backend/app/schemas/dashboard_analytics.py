from uuid import UUID

from sqlmodel import Field, SQLModel

from app.models.clouds import CloudProvider
from app.models.recommendations import RecommendationStatus
from app.models.resources import ResourceCategory


class ServiceConsumptionData(SQLModel):
    """Monthly savings opportunities by service type"""

    service_type: str = Field(description="Service type (e.g., EC2, RDS, S3)")
    service_category: ResourceCategory = Field(description="Resource category")
    cloud_provider: CloudProvider = Field(description="Cloud provider")
    total_monthly_cost: float = Field(
        description="Current monthly cost for this service"
    )
    potential_monthly_savings: float = Field(description="Potential monthly savings")
    resource_count: int = Field(description="Number of resources of this service type")
    optimization_opportunities: int = Field(
        description="Number of available recommendations"
    )
    savings_percentage: float = Field(description="Percentage of potential savings")


class HighConsumingServicesResponse(SQLModel):
    """High-consuming services with monthly savings opportunities"""

    services: list[ServiceConsumptionData] = Field(
        description="List of services ordered by potential savings"
    )
    total_monthly_cost: float = Field(
        description="Total monthly cost across all services"
    )
    total_potential_savings: float = Field(
        description="Total potential monthly savings"
    )
    total_resources: int = Field(description="Total number of resources")


class TopRecommendationData(SQLModel):
    """Top recommendation with effort and risk assessment"""

    id: UUID = Field(description="Recommendation ID")
    resource_id: UUID = Field(description="Associated resource ID")
    resource_name: str = Field(description="Resource name")
    resource_type: str = Field(description="Resource type")
    service_category: ResourceCategory = Field(description="Service category")
    cloud_provider: CloudProvider = Field(description="Cloud provider")
    title: str = Field(description="Recommendation title")
    description: str = Field(description="Recommendation description")
    potential_monthly_savings: float = Field(description="Monthly savings potential")
    effort: str = Field(description="Implementation effort (low/medium/high)")
    risk: str = Field(description="Implementation risk (low/medium/high)")
    status: RecommendationStatus = Field(description="Current recommendation status")

    # Priority scoring for ranking
    priority_score: float = Field(
        description="Calculated priority score based on savings, effort, and risk"
    )

    # Additional context
    region: str = Field(description="Resource region")
    estimated_implementation_time: str | None = Field(
        default=None, description="Estimated time to implement"
    )


class TopRecommendationsResponse(SQLModel):
    """Top 5 potential monthly savings recommendations"""

    recommendations: list[TopRecommendationData] = Field(
        description="Top recommendations ordered by priority score"
    )
    total_potential_savings: float = Field(
        description="Combined savings from top recommendations"
    )
    quick_wins_count: int = Field(
        description="Number of low-effort, low-risk recommendations"
    )
    high_impact_count: int = Field(description="Number of high-savings recommendations")


class DashboardAnalyticsResponse(SQLModel):
    """Combined dashboard analytics response"""

    cost_overview: dict = Field(
        description="Cost optimization overview from existing RecommendationOveralPublic"
    )
    high_consuming_services: HighConsumingServicesResponse = Field(
        description="Services with highest potential savings"
    )
    top_recommendations: TopRecommendationsResponse = Field(
        description="Top 5 priority recommendations"
    )
    last_updated: str = Field(description="ISO timestamp of last data update")


# Query parameters for filtering
class DashboardAnalyticsQuery(SQLModel):
    """Query parameters for dashboard analytics"""

    workspace_id: UUID
    cloud_provider: CloudProvider | None = Field(
        default=None, description="Filter by specific cloud provider"
    )
    service_category: ResourceCategory | None = Field(
        default=None, description="Filter by service category"
    )
    min_savings_amount: float | None = Field(
        default=None, description="Minimum monthly savings threshold"
    )
    top_services_limit: int = Field(
        default=10, description="Number of top services to return", ge=5, le=20
    )
