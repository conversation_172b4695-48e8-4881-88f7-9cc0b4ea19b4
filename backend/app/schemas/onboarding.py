from enum import Enum

from pydantic import BaseModel

from app.models import (
    CloudProvider,
)


class TaskComplexity(str, Enum):
    EASY = "Easy"
    MEDIUM = "Medium"


class TaskTemplateAction(str, Enum):
    SUBMIT = "submit"
    SKIP = "skip"


class OnboardingStatus(BaseModel):
    is_completed: bool
    current_step: int


class TaskTemplate(BaseModel):
    title: str
    description: str
    icon: str  # Lucide icon name
    impact: str  # 2-4 words describing the impact
    complexity: TaskComplexity
    duration: str  # Estimated time for the agent to run


class TaskTemplateResponse(BaseModel):
    cloud_provider: str
    is_sandbox: bool
    templates: list[TaskTemplate]


class TaskTemplateSelection(BaseModel):
    selected_template_title: str | None = None  # None if user skips
    action: str  # "submit" or "skip"


class AWSOnboardingCreate(BaseModel):
    aws_access_key_id: str
    aws_secret_access_key: str
    aws_default_region: str


class GCPOnboardingCreate(BaseModel):
    google_service_account_key: str
    provider_id: str
    # TODO: @khai.trinh google service account key is not enough, we need to get the region
    # gcp_default_region: str


class AzureOnboardingCreate(BaseModel):
    app_id: str
    client_secret: str
    tenant_id: str
    # azure_default_region: str


HARDCODE_TASK_TEMPLATES = {
    CloudProvider.AWS: [
        {
            "title": "EC2 instance right-sizing for Development Environment",
            "description": "Analyze all EC2 instances tagged Environment=Development in Singapore Region. Identify instances with average CPU usage below 30% and perform right-sizing while maintaining current instance family type.",
            "icon": "server",
            "impact": "Cost reduction",
            "complexity": TaskComplexity.EASY,
            "duration": "5-10 minutes",
        },
        {
            "title": "RDS database performance optimization",
            "description": "Review RDS instances with high memory utilization in production environment. Recommend instance upgrades or query optimization for databases exceeding 80% memory usage consistently.",
            "icon": "database",
            "impact": "Performance boost",
            "complexity": TaskComplexity.MEDIUM,
            "duration": "10-15 minutes",
        },
        {
            "title": "S3 storage class optimization for archival data",
            "description": "Analyze S3 buckets for objects not accessed in 90+ days. Migrate eligible objects to cheaper storage classes like IA or Glacier to reduce storage costs.",
            "icon": "archive",
            "impact": "Storage savings",
            "complexity": TaskComplexity.EASY,
            "duration": "3-8 minutes",
        },
    ],
    CloudProvider.GCP: [
        {
            "title": "Compute Engine instance scheduling for non-production",
            "description": "Schedule Compute Engine instances in dev/test environments to shut down during off-hours. Implement automated start/stop schedules to reduce compute costs by 60-70%.",
            "icon": "clock",
            "impact": "Automated savings",
            "complexity": TaskComplexity.MEDIUM,
            "duration": "8-12 minutes",
        },
        {
            "title": "BigQuery slot optimization for analytics workloads",
            "description": "Analyze BigQuery usage patterns and slot consumption. Recommend flat-rate pricing for consistent high-usage or on-demand optimization for variable workloads.",
            "icon": "bar-chart-3",
            "impact": "Query efficiency",
            "complexity": TaskComplexity.MEDIUM,
            "duration": "12-18 minutes",
        },
        {
            "title": "Cloud Storage lifecycle management for logs",
            "description": "Implement lifecycle policies for log storage buckets. Move logs older than 30 days to Nearline and older than 365 days to Coldline storage class.",
            "icon": "layers",
            "impact": "Storage optimization",
            "complexity": TaskComplexity.EASY,
            "duration": "6-10 minutes",
        },
    ],
    CloudProvider.AZURE: [
        {
            "title": "Virtual Machine sizing analysis for cost optimization",
            "description": "Evaluate VM performance metrics across all resource groups. Identify oversized VMs with low CPU/memory utilization and recommend appropriate downsizing.",
            "icon": "monitor",
            "impact": "VM optimization",
            "complexity": TaskComplexity.EASY,
            "duration": "7-12 minutes",
        },
        {
            "title": "Azure SQL Database elastic pool optimization",
            "description": "Analyze SQL Database performance and cost patterns. Recommend elastic pool configurations for databases with similar usage patterns to optimize licensing costs.",
            "icon": "database",
            "impact": "License savings",
            "complexity": TaskComplexity.MEDIUM,
            "duration": "15-20 minutes",
        },
        {
            "title": "Blob Storage access tier optimization",
            "description": "Review blob access patterns and move infrequently accessed data to Cool or Archive tiers. Implement automated policies for cost-effective data lifecycle management.",
            "icon": "hard-drive",
            "impact": "Storage efficiency",
            "complexity": TaskComplexity.EASY,
            "duration": "5-8 minutes",
        },
    ],
}
