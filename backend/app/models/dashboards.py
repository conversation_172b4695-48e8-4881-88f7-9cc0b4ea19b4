import uuid
from datetime import UTC, datetime
from uuid import UUID

from sqlmodel import (
    JSON,
    Column,
    DateTime,
    Field,
    SQLModel,
    UniqueConstraint,
)


class Dashboard(SQLModel, table=True):
    __tablename__ = "dashboards"  # type: ignore

    id: UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    conversation_id: UUID = Field(foreign_key="conversation.id")
    workspace_id: UUID = Field(foreign_key="workspace.id")
    title: str
    description: str | None = Field(default=None)
    # Use factories to avoid shared mutable defaults
    grid_config: dict = Field(
        default_factory=lambda: {"columns": 12}, sa_column=Column(JSON)
    )
    widgets: list[dict] = Field(default_factory=list, sa_column=Column(JSON))
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )

    # Sharing fields
    share_id: UUID | None = Field(default=None, unique=True, index=True)
    is_shared: bool = Field(default=False)
    shared_at: datetime | None = Field(default=None)
    shared_by: UUID | None = Field(default=None, foreign_key="user.id")

    __table_args__ = (
        UniqueConstraint("share_id", name="uq_dashboard_share_id"),
        UniqueConstraint(
            "conversation_id",
            "workspace_id",
            name="uq_dashboard_conversation_workspace",
        ),
    )


class DashboardShare(SQLModel):
    title: str
    description: str | None = None
    grid_config: dict
    widgets: list
    share_id: UUID | None
    is_shared: bool
    shared_at: datetime
    shared_by: UUID
