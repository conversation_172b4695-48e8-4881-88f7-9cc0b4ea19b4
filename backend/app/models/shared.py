from datetime import UTC, datetime
from enum import Enum

from sqlmodel import (
    JSON,
    Column,
    DateTime,
    Field,
    SQLModel,
)


class AsyncTaskStatus(str, Enum):
    PENDING = "PENDING"
    IN_PROGRESS = "PROGRESS"
    COMPLETED = "SUCCESS"
    FAILED = "FAILURE"


class PaginationMeta(SQLModel):
    page: int = Field(description="Current page number (1-based)")
    take: int = Field(description="Number of items per page")
    total_items: int = Field(description="Total number of items available")
    total_pages: int = Field(description="Total number of pages")
    has_previous: bool = Field(description="Whether there is a previous page")
    has_next: bool = Field(description="Whether there is a next page")
    start_index: int = Field(description="Starting index of current page items")
    end_index: int = Field(description="Ending index of current page items")


class ModuleSetting(SQLModel, table=True):
    key: str = Field(primary_key=True, index=True)
    value: dict = Field(default={}, sa_column=Column(JSON))
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )
    updated_at: datetime | None = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=True),
    )
