import uuid
from datetime import UTC, datetime
from enum import Enum
from typing import TYPE_CHECKING

from sqlalchemy_utils import EncryptedType
from sqlalchemy_utils.types.encrypted.encrypted_type import FernetEngine
from sqlmodel import Column, DateTime, Field, Relationship, SQLModel, String

from app.core.config import settings

if TYPE_CHECKING:
    from .workspaces import Workspace


class CloudProvider(str, Enum):
    AWS = "AWS"
    GCP = "GCP"
    AZURE = "AZURE"


class AWSAccountCredentials(SQLModel, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    aws_account_id: uuid.UUID = Field(
        foreign_key="awsaccount.id", ondelete="CASCADE", unique=True
    )
    aws_account: "AWSAccount" = Relationship(back_populates="credential")
    access_key_id: str = Field(
        sa_column=Column(
            EncryptedType(String, settings.SECRET_KEY, FernetEngine), nullable=False
        )
    )
    secret_access_key: str = Field(
        sa_column=Column(
            EncryptedType(String, settings.SECRET_KEY, FernetEngine), nullable=False
        )
    )


class GCPAccountCredentials(SQLModel, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    gcp_account_id: uuid.UUID = Field(
        foreign_key="gcpaccount.id", ondelete="CASCADE", unique=True
    )
    gcp_account: "GCPAccount" = Relationship(back_populates="credential")
    key: str = Field(
        sa_column=Column(
            EncryptedType(String, settings.SECRET_KEY, FernetEngine), nullable=False
        )
    )


class AccountEnvironement(str, Enum):
    PRODUCTION = "production"
    STAGING = "staging"
    DEVELOPMENT = "development"


class AccountBase(SQLModel):
    name: str = Field(min_length=1, max_length=255)
    description: str | None = Field(default=None, max_length=1000)
    environment: AccountEnvironement = Field(nullable=False)


class AWSAccount(AccountBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    account_id: str = Field(min_length=12, max_length=12)
    credential: AWSAccountCredentials = Relationship(
        back_populates="aws_account",
        sa_relationship_kwargs={"cascade": "all, delete-orphan", "uselist": False},
    )
    workspace_id: uuid.UUID = Field(
        foreign_key="workspace.id", ondelete="CASCADE", unique=True
    )
    workspace: "Workspace" = Relationship(back_populates="aws_account")
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )


class AWSAccountCreateUpdate(AccountBase):
    workspace_id: uuid.UUID
    account_id: str = Field(min_length=12, max_length=12)
    access_key_id: str = Field(max_length=128)
    secret_access_key: str = Field(max_length=256)
    regions: list[str] = Field(default=[], min_items=1)
    types: list[str] = Field(default=[], min_items=1)
    cron_pattern: str = Field(min_length=1, max_length=20)


class AWSAccountCreate(AWSAccountCreateUpdate):
    pass


class AWSAccountUpdate(AWSAccountCreateUpdate):
    account_id: str | None = Field(default=None, min_length=12, max_length=12)  # type: ignore
    access_key_id: str | None = Field(default=None, max_length=128)  # type: ignore
    secret_access_key: str | None = Field(default=None, max_length=256)  # type: ignore


class AWSAccountPublic(AccountBase):
    workspace_id: uuid.UUID
    id: uuid.UUID


class AWSAccountDetail(AWSAccountPublic):
    access_key_id: str
    secret_access_key: str
    account_id: str


class GCPAccount(AccountBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    credential: GCPAccountCredentials = Relationship(
        back_populates="gcp_account",
        sa_relationship_kwargs={"cascade": "all, delete-orphan", "uselist": False},
    )
    workspace_id: uuid.UUID = Field(
        foreign_key="workspace.id", ondelete="CASCADE", unique=True
    )
    workspace: "Workspace" = Relationship(back_populates="gcp_account")
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )


class GCPAccountCreateUpdate(AccountBase):
    workspace_id: uuid.UUID
    key: str = Field(max_length=4096)


class GCPAccountCreate(GCPAccountCreateUpdate):
    pass


class GCPAccountUpdate(GCPAccountCreateUpdate):
    pass


class GCPAccountPublic(AccountBase):
    workspace_id: uuid.UUID
    id: uuid.UUID


class GCPAccountDetail(GCPAccountPublic):
    key: str
