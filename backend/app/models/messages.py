import uuid
from datetime import UTC, datetime
from enum import Enum
from typing import TYPE_CHECKING, Optional
from uuid import UUID, uuid4

from pydantic import BaseModel
from sqlmodel import (
    JSON,
    Column,
    DateTime,
    Field,
    Float,
    Index,
    Relationship,
    SQLModel,
    Text,
    UniqueConstraint,
)

if TYPE_CHECKING:
    from .conversations import Conversation
    from .tokens import TokenUsage
    from .users import User


class MessageDisplayComponentType(str, Enum):
    """
    Enum for display component types (currently supporting only tables and charts)
    """

    TABLE = "table"
    CHART = "chart"


class ChartType(str, Enum):
    """
    Enum for different types of charts available in the system
    """

    LINE = "line"
    BAR = "bar"
    PIE = "pie"
    DOUGHNUT = "doughnut"
    AREA = "area"
    SCATTER = "scatter"
    RADAR = "radar"
    STEP_AREA = "step_area"
    SANKEY = "sankey"


class MessageBase(SQLModel):
    content: str = Field(sa_column=Column(Text))
    role: str = Field(
        max_length=50, default="user"
    )  # Can be "user", "assistant", "system", etc.
    is_interrupt: bool = Field(default=False)
    interrupt_message: str | None = Field(default=None)
    interrupt_reasoning: str | None = Field(default=None)


class Message(MessageBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    conversation_id: uuid.UUID = Field(
        foreign_key="conversation.id", ondelete="CASCADE", index=True
    )
    components: list["MessageComponent"] = Relationship(
        back_populates="message",
        sa_relationship_kwargs={
            "cascade": "all, delete-orphan",
            "order_by": "MessageComponent.position",
        },
    )
    conversation: "Conversation" = Relationship(back_populates="messages")
    checkpoint: Optional["MessageCheckpoint"] = Relationship(
        back_populates="message",
        sa_relationship_kwargs={"cascade": "all, delete-orphan"},
    )
    token_usage: "TokenUsage" = Relationship(
        back_populates="message",
        sa_relationship_kwargs={"cascade": "all, delete-orphan"},
    )
    feedback: Optional["MessageFeedback"] = Relationship(
        back_populates="message",
        sa_relationship_kwargs={"cascade": "all, delete-orphan", "uselist": False},
    )
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(
            DateTime(timezone=True), nullable=False, onupdate=datetime.now(UTC)
        ),
    )
    message_metadata: dict = Field(default={}, sa_column=Column(JSON))
    is_deleted: bool = Field(default=False, index=True)
    attachments: list["MessageAttachment"] = Relationship(back_populates="message")

    # Add composite index for efficient conversation message queries
    __table_args__ = (
        Index(
            "ix_message_conversation_deleted_created", 
            "conversation_id", 
            "is_deleted", 
            "created_at"
        ),
    )


class MessageComponentType(str, Enum):
    """
    Enum for different types of components
    """

    DISPLAY = "display"
    TOOL = "tool"
    THINKING = "thinking"


class MessageComponent(SQLModel, table=True):
    id: UUID = Field(default_factory=uuid4, primary_key=True)
    type: MessageComponentType
    position: int = Field(default=0)

    # Relationships
    message_id: UUID = Field(foreign_key="message.id", ondelete="CASCADE", index=True)

    message: "Message" = Relationship(back_populates="components")
    tool_component: Optional["MessageToolComponent"] = Relationship(
        back_populates="message_component",
        sa_relationship_kwargs={
            "lazy": "joined",
            "foreign_keys": "MessageToolComponent.message_component_id",
            "uselist": False,
        },
    )
    display_component: Optional["MessageDisplayComponent"] = Relationship(
        back_populates="message_component",
        sa_relationship_kwargs={
            "lazy": "joined",
            "foreign_keys": "MessageDisplayComponent.message_component_id",
            "uselist": False,
        },
    )

    # Audit
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(
            DateTime(timezone=True), nullable=False, onupdate=datetime.now(UTC)
        ),
    )


class MessageToolComponent(SQLModel, table=True):
    id: UUID = Field(default_factory=uuid4, primary_key=True)
    message_component_id: UUID = Field(
        foreign_key="messagecomponent.id", ondelete="CASCADE"
    )
    tool_name: str = Field(max_length=255)
    tool_input: dict = Field(default={}, sa_column=Column(JSON))
    tool_output: str = Field(default="", sa_column=Column(Text))
    tool_reasoning: str = Field(default="", sa_column=Column(Text))
    tool_runtime: float = Field(default=0, sa_column=Column(Float))
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(
            DateTime(timezone=True), nullable=False, onupdate=datetime.now(UTC)
        ),
    )

    # Relationship
    message_component: "MessageComponent" = Relationship(
        back_populates="tool_component",
        sa_relationship_kwargs={
            "foreign_keys": "MessageToolComponent.message_component_id"
        },
    )


class MessageDisplayComponent(SQLModel, table=True):
    """
    Model for storing display components (tables and charts) associated with messages
    """

    id: UUID = Field(default_factory=uuid4, primary_key=True)
    message_component_id: UUID = Field(
        foreign_key="messagecomponent.id", ondelete="CASCADE"
    )

    type: MessageDisplayComponentType
    chart_type: ChartType | None = Field(
        default=None, description="Specific type of chart when type is CHART"
    )
    title: str | None = Field(default=None, max_length=255)
    description: str | None = Field(default=None, sa_column=Column(Text))
    data: dict = Field(default={}, sa_column=Column(JSON))
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(
            DateTime(timezone=True),
            nullable=False,
            onupdate=datetime.now(UTC),
        ),
    )

    # Relationships
    message_component: "MessageComponent" = Relationship(
        back_populates="display_component",
        sa_relationship_kwargs={
            "foreign_keys": "MessageDisplayComponent.message_component_id"
        },
    )


class MessageAttachment(SQLModel, table=True):
    """Model for storing file attachments associated with messages"""

    __tablename__ = "message_attachments"  # type: ignore

    id: UUID = Field(default_factory=uuid4, primary_key=True)
    message_id: UUID | None = Field(
        default=None, foreign_key="message.id", ondelete="CASCADE", index=True
    )
    owner_id: UUID = Field(foreign_key="user.id")

    # File information
    filename: str = Field(max_length=255)
    original_filename: str = Field(max_length=255)
    file_type: str = Field(max_length=100)  # MIME type
    file_size: int = Field(description="File size in bytes")

    # Storage information
    storage_key: str = Field(max_length=500, description="Object storage key")
    thumbnail_key: str | None = Field(default=None, description="Thumbnail storage key")

    # Relationships
    message: Optional["Message"] = Relationship(back_populates="attachments")
    owner: "User" = Relationship(back_populates="attachments")

    # Audit
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )


class MessageCheckpoint(SQLModel, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    message_id: uuid.UUID = Field(foreign_key="message.id", ondelete="CASCADE")
    message: "Message" = Relationship(back_populates="checkpoint")
    start_checkpoint_id: str | None = Field(default=None)
    end_checkpoint_id: str | None = Field(default=None)


class MessageDisplayComponentPublic(SQLModel):
    id: UUID
    position: int
    type: MessageDisplayComponentType
    chart_type: ChartType | None
    title: str | None
    description: str | None
    data: dict
    created_at: datetime


class MessageToolComponentPublic(SQLModel):
    id: UUID
    position: int
    tool_name: str
    tool_input: dict
    tool_output: str
    tool_reasoning: str
    tool_runtime: float = 0
    created_at: datetime


class MessageAttachmentPublic(SQLModel):
    id: UUID
    filename: str
    original_filename: str
    file_type: str
    file_size: int
    storage_key: str
    thumbnail_key: str | None = None
    created_at: datetime


class MessagePublic(SQLModel):
    id: UUID
    content: str
    role: str
    is_interrupt: bool
    interrupt_message: str | None = None
    interrupt_reasoning: str | None = None
    tool_calls: list[MessageToolComponentPublic] | None = None
    display_components: list[MessageDisplayComponentPublic] | None = None
    attachments: list[MessageAttachmentPublic] | None = None
    created_at: datetime


class MessageStreamInput(SQLModel):
    content: str
    resume: bool
    approve: bool
    display_components: list[MessageDisplayComponentPublic] | None = None
    attachment_ids: list[uuid.UUID] | None = None
    resource_id: str | None = None


class MessagePublicList(BaseModel):
    messages: list[MessagePublic]
    resource_id: UUID | None = None
    has_report: bool = False
    has_dashboard: bool = False
    is_streaming_active: bool = False
    stream_status: str = "completed"
    last_stream_position: int = 0
    total: int


class MessagePlanList(BaseModel):
    plans: list[dict]


class FeedbackType(str, Enum):
    """
    Enumeration for feedback types on agent responses.
    """

    GOOD = "good"
    BAD = "bad"


class MessageFeedbackBase(SQLModel):
    """
    Base model for message feedback containing common attributes.
    """

    feedback_type: FeedbackType = Field(description="Type of feedback (good/bad)")
    reason: str | None = Field(
        default=None,
        max_length=1000,
        description="Optional reason for the feedback, required when feedback_type is BAD",
    )
    additional_comments: str | None = Field(
        default=None,
        max_length=2000,
        description="Additional optional comments from the user",
    )


class MessageFeedback(MessageFeedbackBase, table=True):
    __tablename__ = "message_feedback"  # type: ignore

    id: UUID = Field(
        default_factory=uuid4,
        primary_key=True,
    )

    message_id: UUID = Field(
        foreign_key="message.id",
    )

    user_id: UUID = Field(
        foreign_key="user.id",
    )

    created_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )

    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )

    # Relationships
    message: "Message" = Relationship(
        back_populates="feedback", sa_relationship_kwargs={"lazy": "joined"}
    )

    # Ensure one feedback per message
    __table_args__ = (UniqueConstraint("message_id", name="uq_message_feedback"),)


class MessageFeedbackCreate(MessageFeedbackBase):
    message_id: UUID


class MessageFeedbackUpdate(SQLModel):
    feedback_type: FeedbackType | None = None
    reason: str | None = None
    additional_comments: str | None = None


class MessageFeedbackPublic(MessageFeedbackBase):
    id: UUID
    message_id: UUID
    user_id: UUID
    created_at: datetime
    updated_at: datetime


class MessageFeedbackListResponse(SQLModel):
    data: list[MessageFeedbackPublic]
    count: int
