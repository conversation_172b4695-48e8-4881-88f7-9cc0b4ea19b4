from datetime import UTC, datetime
from typing import TYPE_CHECKING
from uuid import UUI<PERSON>, uuid4

from sqlalchemy import JSO<PERSON>
from sqlmodel import (
    Column,
    DateTime,
    Field,
    Relationship,
    SQLModel,
)

if TYPE_CHECKING:
    from .messages import Message


class TokenUsage(SQLModel, table=True):
    """
    Tracks token usage for individual messages in conversations.

    Attributes:
        id: Unique identifier for the token usage record
        message_id: Reference to the associated message
        input_tokens: Number of tokens in the input message
        output_tokens: Number of tokens in the output message
        total_tokens: Total number of tokens used
        input_token_details: Details of the input tokens
        output_token_details: Details of the output tokens
        created_at: Timestamp of record creation
        updated_at: Timestamp of last update
    """

    __tablename__ = "token_usage"  # type: ignore

    id: UUID = Field(
        default_factory=uuid4,
        primary_key=True,
        title="ID",
        description="Unique identifier for the token usage record",
    )

    message_id: UUID = Field(
        foreign_key="message.id",
        index=True,
        title="Message ID",
        description="Reference to the associated message",
    )

    message: "Message" = Relationship(
        back_populates="token_usage", sa_relationship_kwargs={"lazy": "joined"}
    )

    input_tokens: int = Field(default=0, ge=0)
    output_tokens: int = Field(default=0, ge=0)
    total_tokens: int = Field(default=0, ge=0)
    input_token_details: dict = Field(default={}, sa_column=Column(JSON))
    output_token_details: dict = Field(default={}, sa_column=Column(JSON))

    workspace_id: UUID = Field(
        foreign_key="workspace.id", index=True, title="Workspace ID", ondelete="CASCADE"
    )

    created_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )

    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )
