import uuid
from datetime import UTC, datetime
from enum import Enum
from typing import TYPE_CHECKING, Optional
from uuid import UUID

from pydantic import BaseModel
from sqlmodel import (
    Column,
    DateTime,
    Field,
    Relationship,
    SQLModel,
    UniqueConstraint,
)

if TYPE_CHECKING:
    from .agents import Agent
    from .messages import Message
    from .resources import Resource, ResourcePublicSimple
    from .tasks import TaskHistory


class ConversationBase(SQLModel):
    pass


class ConversationCreate(ConversationBase):
    agent_id: uuid.UUID


class SharedConversationStatus(str, Enum):
    ACTIVE = "active"
    EXPIRED = "expired"
    REVOKED = "revoked"


class Conversation(ConversationBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    name: str = Field(default="Untitled")
    agent_id: uuid.UUID = Field(foreign_key="agent.id", ondelete="CASCADE")
    agent: "Agent" = Relationship(back_populates="conversations")
    messages: list["Message"] = Relationship(
        back_populates="conversation",
        sa_relationship_kwargs={"cascade": "all, delete-orphan"},
    )
    task_history: "TaskHistory" = Relationship(
        back_populates="conversation",
        sa_relationship_kwargs={
            "lazy": "selectin",
            "cascade": "all, delete-orphan",
            "uselist": False,
        },
    )
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )

    resource_id: uuid.UUID | None = Field(default=None, foreign_key="resource.id")
    resource: Optional["Resource"] = Relationship(
        back_populates="conversations",
        sa_relationship_kwargs={"lazy": "selectin"},
    )
    is_deleted: bool = Field(default=False)

    # Sharing fields
    share_id: uuid.UUID | None = Field(default=None, unique=True)
    is_shared: bool = Field(default=False)
    shared_at: datetime | None = Field(default=None)
    shared_by: uuid.UUID | None = Field(default=None, foreign_key="user.id")

    __table_args__ = (UniqueConstraint("share_id", name="uq_conversation_share_id"),)


class ConversationPublic(SQLModel):
    id: uuid.UUID
    agent_id: uuid.UUID
    name: str
    resource: Optional["ResourcePublicSimple"] = None
    created_at: datetime


class ConversationsPublic(SQLModel):
    """Response model for paginated conversations list."""

    data: list[ConversationPublic]
    total: int


class ConversationCreateRequest(BaseModel):
    agent_id: UUID
    resource_id: UUID | None = None


class ConversationRenameRequest(BaseModel):
    name: str
