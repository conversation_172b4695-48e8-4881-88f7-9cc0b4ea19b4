import uuid
from datetime import UTC, datetime
from enum import Enum
from typing import TYPE_CHECKING
from uuid import UUID

from sqlalchemy.dialects.postgresql import ARRAY as PostgreSQLArray
from sqlalchemy.dialects.postgresql import UUID as sa_UUID
from sqlmodel import (
    Column,
    DateTime,
    Field,
    Relationship,
    SQLModel,
    String,
)

if TYPE_CHECKING:
    from .document_kbs import DocumentKB
    from .users import User
    from .workspaces import Workspace


class KBAccessLevel(str, Enum):
    PRIVATE = "private"  # Owner only
    SHARED = "shared"  # Specific users/teams


class KBUsageMode(str, Enum):
    MANUAL = "manual"
    AGENT_REQUESTED = "agent_requested"
    ALWAYS = "always"


class KBBase(SQLModel):
    title: str = Field(min_length=1, max_length=255)
    description: str | None = Field(default=None, max_length=1000)
    access_level: KBAccessLevel = Field(default=KBAccessLevel.PRIVATE)
    usage_mode: KBUsageMode = Field(default=KBUsageMode.MANUAL)
    tags: list[str] = Field(default=[])


class KB(KBBase, table=True):
    __tablename__ = "knowledge_bases"  # type: ignore

    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)

    # Workspace relationship: Which workspace this KB belongs to
    workspace_id: uuid.UUID | None = Field(
        foreign_key="workspace.id", ondelete="CASCADE"
    )
    workspace: "Workspace" = Relationship(back_populates="knowledge_bases")

    # Owner relationship: Who owns this KB
    owner_id: uuid.UUID | None = Field(foreign_key="user.id", ondelete="CASCADE")
    owner: "User" = Relationship(back_populates="knowledge_bases")

    # Access control: Who can view this KB
    allowed_users: list[UUID] = Field(
        default=[], sa_column=Column(PostgreSQLArray(sa_UUID))
    )

    # Audit fields
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )
    is_deleted: bool = Field(default=False, index=True)

    # Specify DB column types here using sa_column
    tags: list[str] = Field(default=[], sa_column=Column(PostgreSQLArray(String)))

    # Documents
    documents: list["DocumentKB"] = Relationship(back_populates="kb")


class KBCreate(KBBase):
    allowed_users: list[UUID] = Field(default=[])


class KBRead(KBBase):
    id: uuid.UUID
    created_at: datetime
    updated_at: datetime
    is_deleted: bool
    allowed_users: list[UUID] = Field(default_factory=list)
    owner_id: uuid.UUID


class KBUpdate(SQLModel):
    title: str | None = Field(default=None, max_length=255)
    description: str | None = Field(default=None, max_length=1000)
    access_level: KBAccessLevel | None = Field(default=None)
    tags: list[str] | None = Field(default=None)
    allowed_users: list[UUID] | None = Field(default=None)
    usage_mode: KBUsageMode | None = Field(default=None)


class KBsRead(SQLModel):
    data: list[KBRead]
    count: int


class AvailableUser(SQLModel):
    id: uuid.UUID
    email: str
    full_name: str


class AvailableUsersCurrentWorkspace(SQLModel):
    data: list[AvailableUser]
    count: int
