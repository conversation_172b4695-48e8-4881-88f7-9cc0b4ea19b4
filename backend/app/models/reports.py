import uuid
from datetime import UTC, datetime
from uuid import UUID

from sqlmodel import (
    JSON,
    Column,
    DateTime,
    Field,
    SQLModel,
    UniqueConstraint,
)


class Report(SQLModel, table=True):
    __tablename__ = "reports"  # type: ignore

    id: UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    conversation_id: UUID = Field(foreign_key="conversation.id")
    workspace_id: UUID = Field(foreign_key="workspace.id")
    title: str
    description: str | None = None
    # Store sections as a JSON array. Use default_factory to avoid mutable default pitfalls.
    sections: list[dict] = Field(default_factory=list, sa_column=Column(JSON))
    # Executive summary remains a JSON object with a safe default.
    executive_summary: dict = Field(default_factory=dict, sa_column=Column(JSON))
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )

    # Sharing fields
    share_id: UUID | None = Field(default=None, unique=True, index=True)
    is_shared: bool = Field(default=False)
    shared_at: datetime | None = Field(default=None)
    shared_by: UUID | None = Field(default=None, foreign_key="user.id")

    __table_args__ = (
        UniqueConstraint("share_id", name="uq_report_share_id"),
        UniqueConstraint(
            "conversation_id",
            "workspace_id",
            name="uq_report_conversation_workspace",
        ),
    )


class ReportShare(SQLModel):
    title: str
    description: str | None = None
    sections: dict | list
    executive_summary: dict
    share_id: UUID | None
    is_shared: bool
    shared_at: datetime
    shared_by: UUID
