import uuid
from datetime import UTC, datetime
from enum import Enum
from typing import TYPE_CHECKING

from sqlmodel import (
    Column,
    DateTime,
    Field,
    Relationship,
    SQLModel,
)

from .shared import AsyncTaskStatus

if TYPE_CHECKING:
    from .knowledge_bases import KB


class DocumentType(str, Enum):
    URL = "url"
    FILE = "file"


class DocumentKBBase(SQLModel):
    name: str = Field(min_length=0, max_length=255)
    type: DocumentType
    url: str | None = None
    deep_crawl: bool = Field(default=False)
    file_name: str | None = None
    file_type: str | None = None
    object_name: str | None = None
    embed_status: AsyncTaskStatus = Field(default=AsyncTaskStatus.PENDING)


class DocumentKBCreate(DocumentKBBase):
    kb_id: uuid.UUID
    parent_id: uuid.UUID | None = Field(default=None)


class DocumentKBRead(DocumentKBBase):
    id: uuid.UUID
    kb_id: uuid.UUID
    created_at: datetime
    updated_at: datetime
    is_deleted: bool
    parent_id: uuid.UUID | None = Field(default=None)
    children: list["DocumentKBRead"] = Field(default=[])


class DocumentKB(DocumentKBBase, table=True):
    __tablename__ = "document_kbs"  # type: ignore

    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)

    parent_id: uuid.UUID | None = Field(default=None, foreign_key="document_kbs.id")
    parent: "DocumentKB" = Relationship(
        back_populates="children",
        sa_relationship_kwargs={"remote_side": "DocumentKB.id"},
    )
    children: list["DocumentKB"] = Relationship(back_populates="parent")

    kb_id: uuid.UUID = Field(foreign_key="knowledge_bases.id", ondelete="CASCADE")
    kb: "KB" = Relationship(back_populates="documents")

    created_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )
    is_deleted: bool = Field(default=False, index=True)


class DocumentsKBRead(SQLModel):
    data: list[DocumentKBRead]
    count: int
