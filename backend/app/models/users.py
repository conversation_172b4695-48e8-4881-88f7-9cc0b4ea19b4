import uuid
from datetime import UTC, datetime
from typing import TYPE_CHECKING
from uuid import UUID

from pydantic import EmailStr
from sqlmodel import (
    Column,
    DateTime,
    Field,
    Relationship,
    SQLModel,
    UniqueConstraint,
)

if TYPE_CHECKING:
    from .knowledge_bases import KB
    from .messages import MessageAttachment
    from .notifications import Notification
    from .tasks import Task
    from .workspaces import Workspace

from .clouds import CloudProvider


class UserBase(SQLModel):
    email: EmailStr = Field(unique=True, index=True, max_length=255)
    is_active: bool = Field(default=False)
    is_email_verified: bool = Field(default=False)
    full_name: str = Field(max_length=255)
    is_superuser: bool = False
    last_login_time: datetime | None = None
    avatar_url: str | None = Field(default=None, max_length=1024)


class UserCreate(UserBase):
    password: str = Field(min_length=8, max_length=40)


class UserRegister(SQLModel):
    email: EmailStr = Field(max_length=255)
    password: str = Field(min_length=8, max_length=40)
    full_name: str | None = Field(default=None, max_length=255)


class UserUpdate(UserBase):
    email: EmailStr | None = Field(default=None, max_length=255)  # type: ignore
    password: str | None = Field(default=None, min_length=8, max_length=40)


class UserUpdateMe(SQLModel):
    full_name: str | None = Field(default=None, max_length=255)
    email: EmailStr | None = Field(default=None, max_length=255)
    avatar_url: str | None = Field(default=None, max_length=1024)


class UpdatePassword(SQLModel):
    current_password: str = Field(min_length=8, max_length=40)
    new_password: str = Field(min_length=8, max_length=40)


class User(UserBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    hashed_password: str
    workspaces: list["UserWorkspace"] = Relationship(
        back_populates="user", sa_relationship_kwargs={"cascade": "all, delete-orphan"}
    )
    activation_tokens: list["UserActivationToken"] = Relationship(
        back_populates="user", sa_relationship_kwargs={"cascade": "all, delete-orphan"}
    )

    owned_tasks: list["Task"] = Relationship(
        back_populates="owner",
        sa_relationship_kwargs={"foreign_keys": "[Task.owner_id]"},
    )

    own_workspaces: list["Workspace"] = Relationship(
        back_populates="owner", sa_relationship_kwargs={"cascade": "all, delete-orphan"}
    )

    usage_quota: "UsageQuota" = Relationship(
        back_populates="user", sa_relationship_kwargs={"cascade": "all, delete-orphan"}
    )

    notifications: list["Notification"] = Relationship(
        back_populates="user", sa_relationship_kwargs={"cascade": "all, delete-orphan"}
    )

    # Knowledge Base relationship
    knowledge_bases: list["KB"] = Relationship(
        back_populates="owner",
        sa_relationship_kwargs={
            "foreign_keys": "[KB.owner_id]",
            "cascade": "all, delete-orphan",
        },
    )

    attachments: list["MessageAttachment"] = Relationship(
        back_populates="owner",
        sa_relationship_kwargs={"cascade": "all, delete-orphan"},
    )

    onboarding: "UserOnboarding" = Relationship(
        back_populates="user",
        sa_relationship_kwargs={"cascade": "all, delete-orphan", "uselist": False},
    )


class UserPublic(UserBase):
    id: uuid.UUID


class AuthorizedUser(UserPublic):
    current_workspace_id: uuid.UUID
    workspaces: list["Workspace"]
    own_workspaces: list["Workspace"]


class AuthorizedUserOnboarding(UserPublic):
    pass


class UserDetail(UserPublic):
    workspaces: list["Workspace"] | None = None
    own_workspaces: list["Workspace"] | None = None


class UsersPublic(SQLModel):
    data: list[UserPublic]
    count: int


class Token(SQLModel):
    access_token: str
    token_type: str = "bearer"
    workspace_id: uuid.UUID | None = None
    is_first_login: bool = False
    slack_oauth: bool = False
    app_id: str | None = None
    team_id: str | None = None


class TokenPayload(SQLModel):
    sub: uuid.UUID
    workspace_id: uuid.UUID | None


class NewPassword(SQLModel):
    token: str
    new_password: str = Field(min_length=8, max_length=40)


class UsageQuota(SQLModel, table=True):
    __tablename__ = "usage_quota"  # type: ignore

    id: UUID = Field(default_factory=uuid.uuid4, primary_key=True)

    user_id: UUID = Field(foreign_key="user.id", ondelete="CASCADE", index=True)

    user: "User" = Relationship(back_populates="usage_quota")

    quota_used_messages: int = Field(default=0, ge=0)

    quota_used_tokens: int = Field(default=0, ge=0)

    reset_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )

    created_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )

    updated_at: datetime | None = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )


class UserActivationToken(SQLModel, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    user_id: uuid.UUID = Field(foreign_key="user.id", ondelete="CASCADE")
    token: str = Field(unique=True, index=True)
    expires_at: datetime
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )
    is_used: bool = Field(default=False)

    user: "User" = Relationship(
        back_populates="activation_tokens", sa_relationship_kwargs={"lazy": "joined"}
    )


class ActivationResponse(SQLModel):
    message: str
    expires_at: datetime


class ActivationResult(SQLModel):
    success: bool
    message: str
    redirect_url: str | None = None
    welcome_message: str | None = None


class ResendActivationRequest(SQLModel):
    email: EmailStr
    captcha_token: str


class UserWorkspace(SQLModel, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    user_id: uuid.UUID = Field(foreign_key="user.id", ondelete="CASCADE")
    user: "User" = Relationship(back_populates="workspaces")
    workspace_id: uuid.UUID = Field(foreign_key="workspace.id", ondelete="CASCADE")
    workspace: "Workspace" = Relationship(back_populates="users")
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )

    __table_args__ = (
        UniqueConstraint("user_id", "workspace_id", name="uq_user_workspace"),
    )


class UserOnboarding(SQLModel, table=True):
    __tablename__ = "user_onboarding"  # type: ignore

    id: UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    user_id: UUID = Field(
        foreign_key="user.id", ondelete="CASCADE", unique=True, index=True
    )
    current_step: int = Field(
        default=1, ge=1, le=3, description="Current onboarding step (1-3)"
    )
    is_completed: bool = Field(
        default=False, description="Whether onboarding is completed"
    )
    selected_provider: CloudProvider | None = Field(
        default=None, description="Selected cloud provider"
    )
    completed_at: datetime | None = Field(default=None)
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )

    user: "User" = Relationship(back_populates="onboarding")
