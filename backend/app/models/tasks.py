import uuid
from datetime import UTC, datetime
from enum import Enum
from typing import TYPE_CHECKING
from uuid import UUID, uuid4

from pydantic import field_validator
from sqlmodel import (
    JSON,
    Column,
    DateTime,
    Field,
    Integer,
    Relationship,
    SQLModel,
)

if TYPE_CHECKING:
    from .conversations import Conversation
    from .users import User
    from .workspaces import Workspace


# Task Management
class TaskPriority(str, Enum):
    """
    Enumeration of task priority levels.

    Attributes:
        LOW: Regular priority, no urgency
        MEDIUM: Moderate priority, should be done soon
        HIGH: High priority, urgent attention needed
        CRITICAL: Critical priority, requires immediate attention
    """

    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class TaskScheduledStatus(str, Enum):
    """
    Enumeration of scheduled statuses for task.
    """

    PENDING = "pending"
    SCHEDULED = "scheduled"


class TaskExecutionStatus(str, Enum):
    """
    Enumeration of execution statuses for task.

    Attributes:
        RUNNING: Currently executing
        SUCCEEDED: Successfully completed
        FAILED: Execution failed
        CANCELLED: Execution cancelled
        REQUIRED_APPROVAL: Execution requires approval
    """

    RUNNING = "running"
    SUCCEEDED = "succeeded"
    FAILED = "failed"
    CANCELLED = "cancelled"
    REQUIRED_APPROVAL = "required_approval"


class AuditMixin(SQLModel):
    """
    Mixin class providing audit fields for tracking entity changes.

    Attributes:
        created_at: Timestamp when the record was created (UTC)
        updated_at: Timestamp when the record was last updated (UTC)
        created_by: ID of the user who created the record
        updated_by: ID of the user who last updated the record
        is_deleted: Soft delete flag
        version: Record version for optimistic locking
    """

    created_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )
    created_by: uuid.UUID = Field(
        foreign_key="user.id",
        nullable=False,
        description="User ID who created this record",
    )
    updated_by: uuid.UUID = Field(
        foreign_key="user.id",
        nullable=False,
        description="User ID who last updated this record",
    )
    is_deleted: bool = Field(default=False, index=True, description="Soft delete flag")
    version: int = Field(
        default=1, nullable=False, description="Record version for optimistic locking"
    )


class TaskImpactEnum(str, Enum):
    """
    Enumeration of possible task impacts.
    """

    LOW = 0
    MEDIUM = 1
    HIGH = 2
    CRITICAL = 3


class TaskCategoryEnum(str, Enum):
    """
    Enumeration of possible task categories.
    """

    COST_OPTIMIZE = "COST_OPTIMIZE"
    OPERATIONAL = "OPERATIONAL"
    SCALABILITY = "SCALABILITY"
    SECURITY = "SECURITY"
    OPERATIONAL_EFFICIENCY = "OPERATIONAL_EFFICIENCY"
    OTHER = "OTHER"  # Default category for uncategorized tasks


class TaskCouldEnum(str, Enum):
    AWS = "AWS"
    AZURE = "AZURE"
    GCP = "GCP"
    ALL = "ALL"


class TaskServiceEnum(str, Enum):
    """
    Enumeration of possible task services.
    """

    ALL = "ALL"
    OTHER = "OTHER"  # Default service for uncategorized services
    COMPUTE = "COMPUTE"
    STORAGE = "STORAGE"
    SERVERLESS = "SERVERLESS"
    DATABASE = "DATABASE"
    NETWORK = "NETWORK"
    MESSAGING = "MESSAGING"
    MANAGEMENT = "MANAGEMENT"
    BILLING = "BILLING"
    CROSS_SERVICE = "CROSS_SERVICE"
    MONITORING = "MONITORING"
    STREAMING = "STREAMING"
    SECURITY = "SECURITY"


# Max length for title and description
MAX_TITLE_LENGTH = 255
MAX_DESCRIPTION_LENGTH = 2000


class TaskBase(SQLModel):
    """
    Base class for all task types with common fields and validations.

    Attributes:
        title: Task title (1-255 characters)
        description: Optional detailed description
        priority: Task priority level
        enable: Whether the task is enabled
        category: Task category
        service: Task service
        service_name: Task service name
        cloud: Task cloud provider
    """

    title: str = Field(
        max_length=MAX_TITLE_LENGTH,
        min_length=1,
        nullable=False,
        description="Task title (required, 1-255 characters)",
    )
    description: str | None = Field(
        default=None,
        max_length=MAX_DESCRIPTION_LENGTH,
        description="Detailed task description (optional)",
    )
    priority: TaskPriority = Field(
        default=TaskPriority.LOW,
        nullable=False,
        description="Task priority level",
    )
    enable: bool = Field(
        default=True,
        nullable=False,
        description="Whether the task is enabled",
    )
    category: TaskCategoryEnum = Field(
        default=TaskCategoryEnum.OTHER,
        description="Task category",
        nullable=True,
    )
    service: TaskServiceEnum = Field(
        default=TaskServiceEnum.OTHER,
        description="AWS service category",
        nullable=True,
    )
    service_name: str = Field(default="", description="Service name", nullable=True)
    cloud: TaskCouldEnum = Field(
        default=TaskCouldEnum.AWS,
        description="Cloud provider",
        nullable=True,
    )

    @field_validator("title")
    def validate_title(cls, v):
        """Validate task title."""
        v = v.strip()
        if not v:
            raise ValueError("Title cannot be empty or just whitespace")
        if len(v) > MAX_TITLE_LENGTH:
            raise ValueError(f"Title cannot exceed {MAX_TITLE_LENGTH} characters")
        return v

    @field_validator("description")
    def validate_description(cls, v):
        """Validate task description."""
        v = v.strip()
        if len(v) > MAX_DESCRIPTION_LENGTH:
            raise ValueError(
                f"Description cannot exceed {MAX_DESCRIPTION_LENGTH} characters"
            )
        return v


class TaskHistory(SQLModel, table=True):
    """
    Execution history of a task conversation.
    """

    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True, index=True)

    # Foreign key to Task
    task_id: uuid.UUID = Field(foreign_key="task.id", nullable=False)
    task: "Task" = Relationship(
        back_populates="task_history", sa_relationship_kwargs={"lazy": "selectin"}
    )

    # Conversation identifier
    conversation_id: uuid.UUID = Field(
        foreign_key="conversation.id",
        nullable=False,
        index=True,
    )
    conversation: "Conversation" = Relationship(
        back_populates="task_history", sa_relationship_kwargs={"lazy": "selectin"}
    )

    # Task status
    status: TaskExecutionStatus = Field(
        nullable=False,
        description="Current task status",
    )

    # Message from the task
    message: str | None = Field(
        default=None,
        description="Message from the task execution: error or required action",
    )

    # Celery task ID
    celery_task_id: uuid.UUID | None = Field(
        default=None,
        description="Celery task ID associated with the task",
    )

    # Audit
    start_time: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )
    end_time: datetime | None = Field(
        default=None,
        sa_column=Column(DateTime(timezone=True), nullable=True),
    )
    run_time: int | None = Field(sa_column=Column(Integer, nullable=True))


class Task(TaskBase, AuditMixin, table=True):
    """
    Unified task model supporting both manual and automated execution modes.
    """

    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True, index=True)

    # Last run status
    execution_status: TaskExecutionStatus = Field(
        default=None,
        nullable=True,
        description="Latest execution status of the task",
    )
    message: str | None = Field(
        default=None,
        description="Message from the latest execution",
    )
    last_run: datetime | None = Field(
        default=None,
        description="When this task was last executed/attempted",
    )
    # Celery task ID
    celery_task_id: uuid.UUID | None = Field(
        default=None,
        description="Celery task ID associated with the latest execution task",
    )

    # Scheduled status using for celery scheduler
    scheduled_status: TaskScheduledStatus = Field(
        default=TaskScheduledStatus.PENDING,
        nullable=False,
        description="Current scheduled status of the task",
    )

    # Next run
    next_run: datetime | None = Field(
        default=None,
        description="When this task is next scheduled to run",
        nullable=False,
    )

    # Task history
    task_history: list["TaskHistory"] = Relationship(
        back_populates="task",
        sa_relationship_kwargs={"cascade": "all, delete-orphan", "lazy": "selectin"},
    )

    # Scheduling fields
    schedule: str | None = Field(
        default=None, description="Cron expression for automated task scheduling"
    )

    # Execution fields
    agent_config: dict | None = Field(
        default={},
        sa_column=Column(JSON),
        description="Configuration for automated task execution",
    )

    # Relationships
    # Owner
    owner_id: uuid.UUID = Field(
        foreign_key="user.id",
        nullable=False,
        description="ID of the user who owns this task",
    )
    owner: "User" = Relationship(
        back_populates="owned_tasks",
        sa_relationship_kwargs={"lazy": "selectin", "foreign_keys": "[Task.owner_id]"},
    )
    # Workspace
    workspace_id: uuid.UUID = Field(
        foreign_key="workspace.id", nullable=False, ondelete="CASCADE"
    )
    workspace: "Workspace" = Relationship(
        back_populates="tasks",
        sa_relationship_kwargs={
            "lazy": "selectin",
            "foreign_keys": "[Task.workspace_id]",
        },
    )


class RunModeEnum(str, Enum):
    AUTONOMOUS = "autonomous"
    AGENT = "agent"


class TaskCategory(str, Enum):
    """
    Enumeration of possible task categories.
    """

    COST_OPTIMIZE = "COST_OPTIMIZE"
    OPERATIONAL = "OPERATIONAL"
    SCALABILITY = "SCALABILITY"
    SECURITY = "SECURITY"
    OTHER = "OTHER"  # Default category for uncategorized tasks


class TaskService(str, Enum):
    """
    Enumeration of possible task services.
    """

    COMPUTE = "COMPUTE"
    STORAGE = "STORAGE"
    SERVERLESS = "SERVERLESS"
    DATABASE = "DATABASE"
    NETWORK = "NETWORK"
    OTHER = "OTHER"  # Default service for uncategorized services


class TaskTemplate(SQLModel, table=True):
    id: UUID = Field(default_factory=uuid4, primary_key=True)
    workspace_id: UUID | None = Field(
        default=None,
        foreign_key="workspace.id",
        index=True,
        nullable=True,  # Null for default templates
        ondelete="CASCADE",
    )
    task: str = Field(description="Task name starting with action verb")
    category: TaskCategoryEnum = Field(
        default=TaskCategoryEnum.OTHER, description="Task category"
    )
    service: TaskServiceEnum = Field(
        default=TaskServiceEnum.OTHER, description="AWS service category"
    )
    service_name: str = Field(default="", description="Service name")
    cloud: TaskCouldEnum = Field(
        default=TaskCouldEnum.AWS, description="Cloud provider"
    )
    run_mode: RunModeEnum = Field(
        default=RunModeEnum.AUTONOMOUS,
        description="Either 'autonomous' or 'agent'",
        nullable=True,
    )
    schedule: str | None = Field(
        default=None, description="Cron syntax for autonomous tasks"
    )
    context: str = Field(description="Detailed task description")
    priority: TaskPriority = Field(
        default=TaskPriority.LOW, description="Priority of the task"
    )
    impact: TaskImpactEnum = Field(
        default=TaskImpactEnum.LOW, description="Impact of the task"
    )
    is_default: bool = Field(
        default=False, description="Whether this is a system default template"
    )
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )
    updated_at: datetime | None = Field(
        default=None,
        sa_column=Column(DateTime(timezone=True), nullable=True),
    )


class SeedVersion(SQLModel, table=True):
    """Tracks versions of seed data that have been applied to the database."""

    id: UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    seed_type: str = Field(
        description="Type of seed data (e.g., 'task_templates', 'settings')"
    )
    version: str = Field(description="Version identifier for the seed data")
    applied_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )
    description: str | None = Field(
        default=None, description="Description of what this seed version contains"
    )
    checksum: str = Field(description="Checksum of the seed data to detect changes")
    is_active: bool = Field(
        default=True, description="Whether this seed version is active"
    )
