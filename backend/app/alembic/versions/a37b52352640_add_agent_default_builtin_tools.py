"""add agent default builtin tools

Revision ID: a37b52352640
Revises: 97a2712e7994
Create Date: 2025-07-23 10:51:59.832933

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'a37b52352640'
down_revision = '97a2712e7994'
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    # Define default agent tools mapping based on DefaultAgents configuration
    agent_tools_mapping = {
        "Anna": [
            "planning",
            "recommendation",
            "create_chart",
            "push_alert",
            "search_internet",
            "report",
        ],
        "Kai": [
            "recommendation",
            "create_chart",
            "push_alert",
            "search_internet",
            "report",
        ],
        "Tony": [
            "recommendation",
            "create_chart",
            "push_alert",
            "search_internet",
            "report",
        ],
        "Alex": [
            "recommendation",
            "create_chart",
            "search_internet",
            "push_alert",
            "report",
            "use_console_read_only_permissions",
            "use_console_write_permissions",
        ],
        "Oliver": [
            "recommendation",
            "create_chart",
            "push_alert",
            "search_internet",
            "report",
        ],
    }

    # Get all existing agents (excluding autonomous agents like Cloud Thinker)
    agents = conn.execute(
        sa.text("""
            SELECT a.id, a.title, a.workspace_id, a.type
            FROM agent a
            WHERE a.type = 'CONVERSATION_AGENT'
        """)
    ).fetchall()

    for agent in agents:
        agent_id, agent_title, workspace_id, agent_type = agent

        # Skip if agent title is not in our default mapping
        if agent_title not in agent_tools_mapping:
            continue

        # Get workspace builtin tools for this workspace
        workspace_tools = conn.execute(
            sa.text("""
                SELECT wbt.id, bt.name
                FROM workspacebuiltintool wbt
                JOIN builtintool bt ON wbt.builtin_tool_id = bt.id
                WHERE wbt.workspace_id = :workspace_id
            """),
            {"workspace_id": workspace_id}
        ).fetchall()

        # Create mapping of tool name to workspace builtin tool ID
        tool_name_to_id = {tool[1]: tool[0] for tool in workspace_tools}

        # Get all workspace tool IDs for this workspace
        all_workspace_tool_ids = [tool[0] for tool in workspace_tools]

        # Get active tool IDs for this agent based on default configuration
        active_tool_names = agent_tools_mapping.get(agent_title, [])
        active_tool_ids = [
            tool_name_to_id[tool_name]
            for tool_name in active_tool_names
            if tool_name in tool_name_to_id
        ]

        # Insert agent builtin tool relationships for all workspace tools
        for workspace_tool_id in all_workspace_tool_ids:
            # Check if relationship already exists
            exists = conn.execute(
                sa.text("""
                    SELECT 1 FROM agent_builtin_tools
                    WHERE agent_id = :agent_id AND workspace_builtin_tool_id = :workspace_tool_id
                """),
                {"agent_id": agent_id, "workspace_tool_id": workspace_tool_id}
            ).fetchone()

            if not exists:
                # Determine if this tool should be active for this agent
                is_active = workspace_tool_id in active_tool_ids

                # Insert the relationship
                conn.execute(
                    sa.text("""
                        INSERT INTO agent_builtin_tools (agent_id, workspace_builtin_tool_id, is_active, created_at, updated_at)
                        VALUES (:agent_id, :workspace_tool_id, :is_active, NOW(), NOW())
                    """),
                    {
                        "agent_id": agent_id,
                        "workspace_tool_id": workspace_tool_id,
                        "is_active": is_active
                    }
                )


def downgrade():
    # Remove all agent builtin tool relationships
    conn = op.get_bind()
    conn.execute(sa.text("DELETE FROM agent_builtin_tools"))
