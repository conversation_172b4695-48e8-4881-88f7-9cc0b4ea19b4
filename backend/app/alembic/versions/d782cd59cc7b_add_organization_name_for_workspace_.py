"""add organization_name for workspace table

Revision ID: d782cd59cc7b
Revises: beb694cc99f7
Create Date: 2025-07-23 18:30:09.904654

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'd782cd59cc7b'
down_revision = 'beb694cc99f7'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('workspace', sa.Column('organization_name', sqlmodel.sql.sqltypes.AutoString(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('workspace', 'organization_name')
    # ### end Alembic commands ###
