"""Remove unused indexes from tasks tables to reclaim 100MB+ storage

Revision ID: cb7715fd8592
Revises: f8712b741bac
Create Date: 2025-08-12 12:12:50.540338

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'cb7715fd8592'
down_revision = 'f8712b741bac'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_seedversion_seed_type', table_name='seedversion')
    op.drop_index('ix_task_category', table_name='task')
    op.drop_index('ix_task_cloud', table_name='task')
    op.drop_index('ix_task_enable', table_name='task')
    op.drop_index('ix_task_execution_status', table_name='task')
    op.drop_index('ix_task_last_run', table_name='task')
    op.drop_index('ix_task_next_run', table_name='task')
    op.drop_index('ix_task_owner_id', table_name='task')
    op.drop_index('ix_task_priority', table_name='task')
    op.drop_index('ix_task_scheduled_status', table_name='task')
    op.drop_index('ix_task_service', table_name='task')
    op.drop_index('ix_task_service_name', table_name='task')
    op.drop_index('ix_task_title', table_name='task')
    op.drop_index('ix_task_workspace_id', table_name='task')
    op.drop_index('ix_taskhistory_status', table_name='taskhistory')
    op.drop_index('ix_taskhistory_task_id', table_name='taskhistory')
    op.drop_index('ix_tasktemplate_category', table_name='tasktemplate')
    op.drop_index('ix_tasktemplate_cloud', table_name='tasktemplate')
    op.drop_index('ix_tasktemplate_service', table_name='tasktemplate')
    op.drop_index('ix_tasktemplate_service_name', table_name='tasktemplate')
    op.drop_index('ix_tasktemplate_task', table_name='tasktemplate')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('ix_tasktemplate_task', 'tasktemplate', ['task'], unique=False)
    op.create_index('ix_tasktemplate_service_name', 'tasktemplate', ['service_name'], unique=False)
    op.create_index('ix_tasktemplate_service', 'tasktemplate', ['service'], unique=False)
    op.create_index('ix_tasktemplate_cloud', 'tasktemplate', ['cloud'], unique=False)
    op.create_index('ix_tasktemplate_category', 'tasktemplate', ['category'], unique=False)
    op.create_index('ix_taskhistory_task_id', 'taskhistory', ['task_id'], unique=False)
    op.create_index('ix_taskhistory_status', 'taskhistory', ['status'], unique=False)
    op.create_index('ix_task_workspace_id', 'task', ['workspace_id'], unique=False)
    op.create_index('ix_task_title', 'task', ['title'], unique=False)
    op.create_index('ix_task_service_name', 'task', ['service_name'], unique=False)
    op.create_index('ix_task_service', 'task', ['service'], unique=False)
    op.create_index('ix_task_scheduled_status', 'task', ['scheduled_status'], unique=False)
    op.create_index('ix_task_priority', 'task', ['priority'], unique=False)
    op.create_index('ix_task_owner_id', 'task', ['owner_id'], unique=False)
    op.create_index('ix_task_next_run', 'task', ['next_run'], unique=False)
    op.create_index('ix_task_last_run', 'task', ['last_run'], unique=False)
    op.create_index('ix_task_execution_status', 'task', ['execution_status'], unique=False)
    op.create_index('ix_task_enable', 'task', ['enable'], unique=False)
    op.create_index('ix_task_cloud', 'task', ['cloud'], unique=False)
    op.create_index('ix_task_category', 'task', ['category'], unique=False)
    op.create_index('ix_seedversion_seed_type', 'seedversion', ['seed_type'], unique=False)
    # ### end Alembic commands ###
