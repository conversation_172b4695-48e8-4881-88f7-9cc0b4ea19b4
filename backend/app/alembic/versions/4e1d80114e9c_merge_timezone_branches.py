"""merge timezone branches

Revision ID: 4e1d80114e9c
Revises: 9388758c8e45, 9a68287996e7
Create Date: 2025-08-01 18:38:26.916398

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '4e1d80114e9c'
down_revision = ('9388758c8e45', '9a68287996e7')
branch_labels = None
depends_on = None


def upgrade():
    pass


def downgrade():
    pass
