"""Add connection template model

Revision ID: afcec62ef56d
Revises: 5f95b1cfd638
Create Date: 2025-07-31 10:18:58.191959

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy.dialects import postgresql
from sqlalchemy_utils import EncryptedType, StringEncryptedType
from sqlalchemy_utils.types.encrypted.encrypted_type import FernetEngine
from app.core.config import settings

# revision identifiers, used by Alembic.
revision = 'afcec62ef56d'
down_revision = '5f95b1cfd638'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    sa.Enum('STREAMABLE_HTTP', 'SSE', name='mcptransporttype').create(op.get_bind())
    sa.Enum('CLOUD', 'DB', 'ORCHESTRATION', 'OBSERVABILITY', 'CLI', name='builtinconnectiontype').create(op.get_bind())
    op.create_table('connectiontemplate',
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('prefix', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('builtin_connection_type', postgresql.ENUM('CLOUD', 'DB', 'ORCHESTRATION', 'OBSERVABILITY', 'CLI', name='builtinconnectiontype', create_type=False), nullable=False),
    sa.Column('is_sandbox_supported', sa.Boolean(), nullable=False),
    sa.Column('config', sa.JSON(), nullable=True),
    sa.Column('env', EncryptedType(sa.String, settings.SECRET_KEY, FernetEngine), nullable=False),
    sa.Column('tools', sa.JSON(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.add_column('connection', sa.Column('builtin_connection_type', postgresql.ENUM('CLOUD', 'DB', 'ORCHESTRATION', 'OBSERVABILITY', 'CLI', name='builtinconnectiontype', create_type=False), nullable=True))
    op.add_column('connection', sa.Column('mcp_transport_type', postgresql.ENUM('STREAMABLE_HTTP', 'SSE', name='mcptransporttype', create_type=False), nullable=True))
    op.add_column('connection', sa.Column('connection_type', postgresql.ENUM('BUILTIN', 'MCP', 'CLOUD', 'CLI', name='connectiontype', create_type=False), nullable=False, server_default='MCP'))
    # Add env column as nullable first
    op.add_column('connection', sa.Column('env', EncryptedType(sa.String, settings.SECRET_KEY, FernetEngine), nullable=True))

    # Update existing records with empty JSON object as default
    from sqlalchemy import text
    op.execute(text("UPDATE connection SET env = '{}' WHERE env IS NULL"))

    # Now make the column NOT NULL
    op.alter_column('connection', 'env', nullable=False)
    op.add_column('connection', sa.Column('tools', sa.JSON(), nullable=False, server_default='[]'))
    op.drop_index('ix_connection_is_active', table_name='connection')
    op.drop_column('connection', 'type')
    op.drop_column('connection', 'is_active')
    op.drop_column('connection', 'transport_type')
    sa.Enum('STREAMABLE_HTTP', 'SSE', name='connectiontransport').drop(op.get_bind())
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    sa.Enum('STREAMABLE_HTTP', 'SSE', name='connectiontransport').create(op.get_bind())
    op.add_column('connection', sa.Column('transport_type', postgresql.ENUM('STREAMABLE_HTTP', 'SSE', name='connectiontransport', create_type=False), autoincrement=False, nullable=False, server_default='STREAMABLE_HTTP'))
    op.add_column('connection', sa.Column('is_active', sa.BOOLEAN(), autoincrement=False, nullable=False, server_default='true'))
    op.add_column('connection', sa.Column('type', postgresql.ENUM('BUILTIN', 'MCP', 'CLOUD', 'CLI', name='connectiontype', create_type=False), autoincrement=False, nullable=False, server_default='MCP'))
    op.create_index('ix_connection_is_active', 'connection', ['is_active'], unique=False)
    op.drop_column('connection', 'tools')
    op.drop_column('connection', 'env')
    op.drop_column('connection', 'mcp_transport_type')
    op.drop_column('connection', 'connection_type')
    op.drop_column('connection', 'builtin_connection_type')
    op.drop_table('connectiontemplate')
    sa.Enum('CLOUD', 'DB', 'ORCHESTRATION', 'OBSERVABILITY', 'CLI', name='builtinconnectiontype').drop(op.get_bind())
    sa.Enum('STREAMABLE_HTTP', 'SSE', name='mcptransporttype').drop(op.get_bind())
    # ### end Alembic commands ###
