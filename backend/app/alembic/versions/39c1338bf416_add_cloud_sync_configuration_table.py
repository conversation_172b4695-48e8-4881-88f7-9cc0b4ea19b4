"""Add cloud sync configuration table

Revision ID: 39c1338bf416
Revises: 77708718622a
Create Date: 2025-07-27 06:32:04.474040

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '39c1338bf416'
down_revision = '77708718622a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    sa.Enum('COMPUTE', 'DATABASE', 'STORAGE', 'NETWORKING', 'SERVERLESS', 'CONTAINER', 'MESSAGING', 'MONITORING', 'SECURITY', 'MANAGEMENT', 'ANALYTICS', 'AI_ML', 'OTHER', name='resourcecategory').create(op.get_bind())
    op.create_table('cloud_sync_config',
    sa.Column('include_stopped_resources', sa.<PERSON>(), nullable=False),
    sa.Column('refresh_interval', sa.Integer(), nullable=False),
    sa.Column('selected_resources', postgresql.ARRAY(sa.String()), nullable=True),
    sa.Column('is_enabled', sa.Boolean(), nullable=False),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('workspace_id', sa.Uuid(), nullable=False),
    sa.Column('connection_id', sa.Uuid(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['connection_id'], ['connection.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['workspace_id'], ['workspace.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('workspace_id', 'connection_id', name='uq_workspace_connection_sync_config')
    )
    op.create_index(op.f('ix_cloud_sync_config_connection_id'), 'cloud_sync_config', ['connection_id'], unique=False)
    op.create_index(op.f('ix_cloud_sync_config_workspace_id'), 'cloud_sync_config', ['workspace_id'], unique=False)
    op.add_column('resource', sa.Column('resource_id', sqlmodel.sql.sqltypes.AutoString(length=2048), nullable=False))
    op.add_column('resource', sa.Column('category', postgresql.ENUM('COMPUTE', 'DATABASE', 'STORAGE', 'NETWORKING', 'SERVERLESS', 'CONTAINER', 'MESSAGING', 'MONITORING', 'SECURITY', 'MANAGEMENT', 'ANALYTICS', 'AI_ML', 'OTHER', name='resourcecategory', create_type=False), nullable=False))
    op.alter_column('resource', 'type',
               existing_type=postgresql.ENUM('EC2', 'LAMBDA', 'ECS', 'EKS', 'BATCH', 'EC2_AUTO_SCALING', 'ELASTIC_BEANSTALK', 'APP_RUNNER', 'RDS', 'DYNAMODB', 'ELASTICACHE', 'NEPTUNE', 'DOCUMENTDB', 'OPENSEARCH', 'REDSHIFT', 'S3', 'EBS', 'EFS', 'BACKUP', 'VPC', 'ELB', 'CLOUDFORMATION', 'CLOUDWATCH', 'SQS', 'SNS', name='resourcetype'),
               type_=sqlmodel.sql.sqltypes.AutoString(),
               existing_nullable=False)
    op.alter_column('resource', 'provider',
               existing_type=postgresql.ENUM('AWS', 'GCP', 'AZURE', name='cloudprovider'),
               nullable=False)
    op.drop_index('ix_resource_arn', table_name='resource')
    op.create_index(op.f('ix_resource_category'), 'resource', ['category'], unique=False)
    op.create_index(op.f('ix_resource_provider'), 'resource', ['provider'], unique=False)
    op.create_index(op.f('ix_resource_resource_id'), 'resource', ['resource_id'], unique=False)
    op.drop_column('resource', 'arn')
    sa.Enum('EC2', 'LAMBDA', 'ECS', 'EKS', 'BATCH', 'EC2_AUTO_SCALING', 'ELASTIC_BEANSTALK', 'APP_RUNNER', 'RDS', 'DYNAMODB', 'ELASTICACHE', 'NEPTUNE', 'DOCUMENTDB', 'OPENSEARCH', 'REDSHIFT', 'S3', 'EBS', 'EFS', 'BACKUP', 'VPC', 'ELB', 'CLOUDFORMATION', 'CLOUDWATCH', 'SQS', 'SNS', name='resourcetype').drop(op.get_bind())
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    sa.Enum('EC2', 'LAMBDA', 'ECS', 'EKS', 'BATCH', 'EC2_AUTO_SCALING', 'ELASTIC_BEANSTALK', 'APP_RUNNER', 'RDS', 'DYNAMODB', 'ELASTICACHE', 'NEPTUNE', 'DOCUMENTDB', 'OPENSEARCH', 'REDSHIFT', 'S3', 'EBS', 'EFS', 'BACKUP', 'VPC', 'ELB', 'CLOUDFORMATION', 'CLOUDWATCH', 'SQS', 'SNS', name='resourcetype').create(op.get_bind())
    op.add_column('resource', sa.Column('arn', sa.VARCHAR(length=2048), autoincrement=False, nullable=False))
    op.drop_index(op.f('ix_resource_resource_id'), table_name='resource')
    op.drop_index(op.f('ix_resource_provider'), table_name='resource')
    op.drop_index(op.f('ix_resource_category'), table_name='resource')
    op.create_index('ix_resource_arn', 'resource', ['arn'], unique=False)
    op.alter_column('resource', 'provider',
               existing_type=postgresql.ENUM('AWS', 'GCP', 'AZURE', name='cloudprovider'),
               nullable=True)
    op.alter_column('resource', 'type',
               existing_type=sqlmodel.sql.sqltypes.AutoString(),
               type_=postgresql.ENUM('EC2', 'LAMBDA', 'ECS', 'EKS', 'BATCH', 'EC2_AUTO_SCALING', 'ELASTIC_BEANSTALK', 'APP_RUNNER', 'RDS', 'DYNAMODB', 'ELASTICACHE', 'NEPTUNE', 'DOCUMENTDB', 'OPENSEARCH', 'REDSHIFT', 'S3', 'EBS', 'EFS', 'BACKUP', 'VPC', 'ELB', 'CLOUDFORMATION', 'CLOUDWATCH', 'SQS', 'SNS', name='resourcetype'),
               existing_nullable=False)
    op.drop_column('resource', 'category')
    op.drop_column('resource', 'resource_id')
    op.drop_index(op.f('ix_cloud_sync_config_workspace_id'), table_name='cloud_sync_config')
    op.drop_index(op.f('ix_cloud_sync_config_connection_id'), table_name='cloud_sync_config')
    op.drop_table('cloud_sync_config')
    sa.Enum('COMPUTE', 'DATABASE', 'STORAGE', 'NETWORKING', 'SERVERLESS', 'CONTAINER', 'MESSAGING', 'MONITORING', 'SECURITY', 'MANAGEMENT', 'ANALYTICS', 'AI_ML', 'OTHER', name='resourcecategory').drop(op.get_bind())
    # ### end Alembic commands ###
