"""Add unique (conversation_id, workspace_id) to reports/dashboards

Revision ID: 3d684525cafc
Revises: 035d61bee1ce
Create Date: 2025-08-14 17:32:22.353085

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '3d684525cafc'
down_revision = '035d61bee1ce'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # Create constraints only if they don't already exist, but first clean up duplicates
    from sqlalchemy import text
    connection = op.get_bind()
    
    # Clean up duplicate reports before adding unique constraint
    # Keep only the most recent report for each conversation_id, workspace_id pair
    print("Cleaning up duplicate reports...")
    cleanup_reports_sql = text("""
        WITH duplicates AS (
            SELECT id, conversation_id, workspace_id,
                   ROW_NUMBER() OVER (
                       PARTITION BY conversation_id, workspace_id 
                       ORDER BY created_at DESC, id DESC
                   ) as rn
            FROM reports
        )
        DELETE FROM reports 
        WHERE id IN (
            SELECT id FROM duplicates WHERE rn > 1
        )
    """)
    
    result = connection.execute(cleanup_reports_sql)
    deleted_count = result.rowcount if hasattr(result, 'rowcount') else 0
    print(f"Deleted {deleted_count} duplicate report records")
    
    # Clean up duplicate dashboards before adding unique constraint  
    print("Cleaning up duplicate dashboards...")
    cleanup_dashboards_sql = text("""
        WITH duplicates AS (
            SELECT id, conversation_id, workspace_id,
                   ROW_NUMBER() OVER (
                       PARTITION BY conversation_id, workspace_id 
                       ORDER BY created_at DESC, id DESC
                   ) as rn
            FROM dashboards
        )
        DELETE FROM dashboards 
        WHERE id IN (
            SELECT id FROM duplicates WHERE rn > 1
        )
    """)
    
    result = connection.execute(cleanup_dashboards_sql)
    deleted_count = result.rowcount if hasattr(result, 'rowcount') else 0
    print(f"Deleted {deleted_count} duplicate dashboard records")
    
    # Check if dashboard constraint exists
    dashboard_constraint_exists = connection.execute(text(
        "SELECT COUNT(*) FROM pg_constraint WHERE conname = 'uq_dashboard_conversation_workspace'"
    )).scalar()
    
    if not dashboard_constraint_exists:
        print("Creating unique constraint for dashboards...")
        op.create_unique_constraint('uq_dashboard_conversation_workspace', 'dashboards', ['conversation_id', 'workspace_id'])
    
    # Check if report constraint exists
    report_constraint_exists = connection.execute(text(
        "SELECT COUNT(*) FROM pg_constraint WHERE conname = 'uq_report_conversation_workspace'"
    )).scalar()
    
    if not report_constraint_exists:
        print("Creating unique constraint for reports...")
        op.create_unique_constraint('uq_report_conversation_workspace', 'reports', ['conversation_id', 'workspace_id'])
    
    print("Migration completed successfully!")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # Drop constraints only if they exist
    from sqlalchemy import text
    connection = op.get_bind()
    
    print("Rolling back unique constraints...")
    
    # Check if report constraint exists before dropping
    report_constraint_exists = connection.execute(text(
        "SELECT COUNT(*) FROM pg_constraint WHERE conname = 'uq_report_conversation_workspace'"
    )).scalar()
    
    if report_constraint_exists:
        print("Dropping unique constraint for reports...")
        op.drop_constraint('uq_report_conversation_workspace', 'reports', type_='unique')
    
    # Check if dashboard constraint exists before dropping
    dashboard_constraint_exists = connection.execute(text(
        "SELECT COUNT(*) FROM pg_constraint WHERE conname = 'uq_dashboard_conversation_workspace'"
    )).scalar()
    
    if dashboard_constraint_exists:
        print("Dropping unique constraint for dashboards...")
        op.drop_constraint('uq_dashboard_conversation_workspace', 'dashboards', type_='unique')
    
    print("Rollback completed successfully!")
    # ### end Alembic commands ###
