"""add connection env type

Revision ID: 29fb80f0c601
Revises: 6a1b4f1a3606
Create Date: 2025-08-02 14:37:07.247143

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '29fb80f0c601'
down_revision = '6a1b4f1a3606'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    sa.Enum('SANDBOX', 'PRODUCTION', name='builtinconnectionenvtype').create(op.get_bind())
    op.add_column('connection', sa.Column('connection_env_type', postgresql.ENUM('SANDBOX', 'PRODUCTION', name='builtinconnectionenvtype', create_type=False), nullable=False, server_default='PRODUCTION'))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('connection', 'connection_env_type')
    sa.Enum('SANDBOX', 'PRODUCTION', name='builtinconnectionenvtype').drop(op.get_bind())
    # ### end Alembic commands ###
