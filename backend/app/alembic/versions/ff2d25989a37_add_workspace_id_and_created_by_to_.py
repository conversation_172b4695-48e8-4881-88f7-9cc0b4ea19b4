"""add workspace_id and created_by to recommendations

Revision ID: ff2d25989a37
Revises: cbf19f29e9e9
Create Date: 2025-08-10 12:59:10.067555

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'ff2d25989a37'
down_revision = 'cbf19f29e9e9'
branch_labels = None
depends_on = None


def upgrade():
    # Add columns as nullable first
    op.add_column('recommendation', sa.Column('workspace_id', sa.Uuid(), nullable=True))
    op.add_column('recommendation', sa.Column('created_by', sa.Uuid(), nullable=True))

    # Update existing recommendations to set workspace_id and created_by
    # For existing records, we'll use the workspace_id from the resource table
    # and set created_by to the first user
    op.execute("""
        UPDATE recommendation
        SET workspace_id = r.workspace_id,
            created_by = (SELECT u.id FROM "user" u LIMIT 1)
        FROM resource r
        WHERE recommendation.resource_id = r.id
    """)

    # Handle recommendations without resource_id (if any)
    op.execute("""
        UPDATE recommendation
        SET workspace_id = (SELECT w.id FROM workspace w LIMIT 1),
            created_by = (SELECT u.id FROM "user" u LIMIT 1)
        WHERE workspace_id IS NULL
    """)

    # Now make the columns NOT NULL
    op.alter_column('recommendation', 'workspace_id', nullable=False)
    op.alter_column('recommendation', 'created_by', nullable=False)

    # Create indexes and foreign keys
    op.create_index(op.f('ix_recommendation_created_by'), 'recommendation', ['created_by'], unique=False)
    op.create_index(op.f('ix_recommendation_workspace_id'), 'recommendation', ['workspace_id'], unique=False)
    op.create_foreign_key(None, 'recommendation', 'workspace', ['workspace_id'], ['id'], ondelete='CASCADE')
    op.create_foreign_key(None, 'recommendation', 'user', ['created_by'], ['id'], ondelete='CASCADE')


def downgrade():
    # Drop foreign keys and indexes
    op.drop_constraint(None, 'recommendation', type_='foreignkey')  # workspace_id FK
    op.drop_constraint(None, 'recommendation', type_='foreignkey')  # created_by FK
    op.drop_index(op.f('ix_recommendation_workspace_id'), table_name='recommendation')
    op.drop_index(op.f('ix_recommendation_created_by'), table_name='recommendation')

    # Drop columns
    op.drop_column('recommendation', 'created_by')
    op.drop_column('recommendation', 'workspace_id')
