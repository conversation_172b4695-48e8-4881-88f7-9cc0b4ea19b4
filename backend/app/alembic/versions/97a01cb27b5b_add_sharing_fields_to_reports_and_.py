"""Add sharing fields to reports and dashboards

Revision ID: 97a01cb27b5b
Revises: 87b193331952
Create Date: 2025-08-06 19:39:05.746055

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '97a01cb27b5b'
down_revision = '87b193331952'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('dashboards', sa.Column('share_id', sa.Uuid(), nullable=True))
    op.add_column('dashboards', sa.Column('is_shared', sa.Boolean(), nullable=False, server_default='false'))
    op.add_column('dashboards', sa.Column('shared_at', sa.DateTime(), nullable=True))
    op.add_column('dashboards', sa.Column('shared_by', sa.Uuid(), nullable=True))
    op.create_index(op.f('ix_dashboards_share_id'), 'dashboards', ['share_id'], unique=True)
    op.create_unique_constraint('uq_dashboard_share_id', 'dashboards', ['share_id'])
    op.create_foreign_key(None, 'dashboards', 'user', ['shared_by'], ['id'])
    op.add_column('reports', sa.Column('share_id', sa.Uuid(), nullable=True))
    op.add_column('reports', sa.Column('is_shared', sa.Boolean(), nullable=False, server_default='false'))
    op.add_column('reports', sa.Column('shared_at', sa.DateTime(), nullable=True))
    op.add_column('reports', sa.Column('shared_by', sa.Uuid(), nullable=True))
    op.create_index(op.f('ix_reports_share_id'), 'reports', ['share_id'], unique=True)
    op.create_unique_constraint('uq_report_share_id', 'reports', ['share_id'])
    op.create_foreign_key(None, 'reports', 'user', ['shared_by'], ['id'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'reports', type_='foreignkey')
    op.drop_constraint('uq_report_share_id', 'reports', type_='unique')
    op.drop_index(op.f('ix_reports_share_id'), table_name='reports')
    op.drop_column('reports', 'shared_by')
    op.drop_column('reports', 'shared_at')
    op.drop_column('reports', 'is_shared')
    op.drop_column('reports', 'share_id')
    op.drop_constraint(None, 'dashboards', type_='foreignkey')
    op.drop_constraint('uq_dashboard_share_id', 'dashboards', type_='unique')
    op.drop_index(op.f('ix_dashboards_share_id'), table_name='dashboards')
    op.drop_column('dashboards', 'shared_by')
    op.drop_column('dashboards', 'shared_at')
    op.drop_column('dashboards', 'is_shared')
    op.drop_column('dashboards', 'share_id')
    # ### end Alembic commands ###
