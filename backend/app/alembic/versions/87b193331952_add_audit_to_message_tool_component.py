"""add audit to message tool component

Revision ID: 87b193331952
Revises: 853f4e768aae
Create Date: 2025-08-05 18:42:32.523261

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '87b193331952'
down_revision = '853f4e768aae'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('messagetoolcomponent', sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now()))
    op.add_column('messagetoolcomponent', sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now(), onupdate=sa.func.now()))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('messagetoolcomponent', 'updated_at')
    op.drop_column('messagetoolcomponent', 'created_at')
    # ### end Alembic commands ###
