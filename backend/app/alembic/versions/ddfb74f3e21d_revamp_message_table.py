"""revamp message table

Revision ID: ddfb74f3e21d
Revises: 010c9667d9b0
Create Date: 2025-08-05 10:39:46.950219

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'ddfb74f3e21d'
down_revision = '010c9667d9b0'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    sa.Enum('DISPLAY', 'TOOL', 'THINKING', name='messagecomponenttype').create(op.get_bind())

    # Create messagecomponent table first (without foreign key constraints to child tables)
    op.create_table('messagecomponent',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('type', postgresql.ENUM('DISPLAY', 'TOOL', 'THINKING', name='messagecomponenttype', create_type=False), nullable=False),
    sa.Column('position', sa.Integer(), nullable=False),
    sa.Column('message_id', sa.Uuid(), nullable=False),
    sa.Column('tool_component_id', sa.Uuid(), nullable=True),
    sa.Column('display_component_id', sa.Uuid(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
    sa.ForeignKeyConstraint(['message_id'], ['message.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_messagecomponent_message_id'), 'messagecomponent', ['message_id'], unique=False)

    # Create messagetoolcomponent table
    op.create_table('messagetoolcomponent',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('message_component_id', sa.Uuid(), nullable=False),
    sa.Column('tool_name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('tool_input', sa.JSON(), nullable=True),
    sa.Column('tool_output', sa.Text(), nullable=True),
    sa.Column('tool_reasoning', sa.Text(), nullable=True),
    sa.Column('tool_runtime', sa.Float(), nullable=True),
    sa.ForeignKeyConstraint(['message_component_id'], ['messagecomponent.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_messagetoolcomponent_message_component_id'), 'messagetoolcomponent', ['message_component_id'], unique=False)

    # Add foreign key constraints to messagecomponent table after dependent tables exist
    op.create_foreign_key('fk_messagecomponent_tool_component', 'messagecomponent', 'messagetoolcomponent', ['tool_component_id'], ['id'], ondelete='CASCADE')
    op.create_index(op.f('ix_messagecomponent_tool_component_id'), 'messagecomponent', ['tool_component_id'], unique=False)

    # Drop and recreate messagedisplaycomponent table with new structure
    op.drop_table('messagedisplaycomponent')
    op.create_table('messagedisplaycomponent',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('message_component_id', sa.Uuid(), nullable=False),
    sa.Column('type', postgresql.ENUM('TABLE', 'CHART', name='messagedisplaycomponenttype', create_type=False), nullable=False),
    sa.Column('chart_type', postgresql.ENUM('LINE', 'BAR', 'PIE', 'DOUGHNUT', 'AREA', 'SCATTER', 'RADAR', 'STEP_AREA', 'SANKEY', name='charttype', create_type=False), nullable=True),
    sa.Column('title', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('data', sa.JSON(), nullable=False),
    sa.Column('config', sa.JSON(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
    sa.ForeignKeyConstraint(['message_component_id'], ['messagecomponent.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_messagedisplaycomponent_message_component_id'), 'messagedisplaycomponent', ['message_component_id'], unique=False)

    # Add display component foreign key constraint after messagedisplaycomponent is updated
    op.create_foreign_key('fk_messagecomponent_display_component', 'messagecomponent', 'messagedisplaycomponent', ['display_component_id'], ['id'], ondelete='CASCADE')
    op.create_index(op.f('ix_messagecomponent_display_component_id'), 'messagecomponent', ['display_component_id'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # Drop foreign key constraints first
    op.drop_constraint('fk_messagecomponent_display_component', 'messagecomponent', type_='foreignkey')
    op.drop_index(op.f('ix_messagecomponent_display_component_id'), table_name='messagecomponent')

    # Drop tool component foreign key constraint
    op.drop_constraint('fk_messagecomponent_tool_component', 'messagecomponent', type_='foreignkey')
    op.drop_index(op.f('ix_messagecomponent_tool_component_id'), table_name='messagecomponent')

    # Drop all tables first (to avoid enum dependency issues)
    op.drop_table('messagedisplaycomponent')
    op.drop_index(op.f('ix_messagetoolcomponent_message_component_id'), table_name='messagetoolcomponent')
    op.drop_table('messagetoolcomponent')
    op.drop_index(op.f('ix_messagecomponent_message_id'), table_name='messagecomponent')
    op.drop_table('messagecomponent')

    # Recreate messagedisplaycomponent with old structure
    op.create_table('messagedisplaycomponent',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('message_id', sa.Uuid(), nullable=False),
    sa.Column('type', postgresql.ENUM('TABLE', 'CHART', name='messagedisplaycomponenttype', create_type=False), nullable=False),
    sa.Column('chart_type', postgresql.ENUM('LINE', 'BAR', 'PIE', 'DOUGHNUT', 'AREA', 'SCATTER', 'RADAR', 'STEP_AREA', 'SANKEY', name='charttype', create_type=False), nullable=True),
    sa.Column('title', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('data', sa.JSON(), nullable=False),
    sa.Column('config', sa.JSON(), nullable=False),
    sa.Column('position', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
    sa.ForeignKeyConstraint(['message_id'], ['message.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )

    # Drop only the messagecomponenttype enum (the other enums were pre-existing)
    sa.Enum('DISPLAY', 'TOOL', 'THINKING', name='messagecomponenttype').drop(op.get_bind())
    # ### end Alembic commands ###
