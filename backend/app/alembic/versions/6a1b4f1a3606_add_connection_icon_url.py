"""add connection icon_url

Revision ID: 6a1b4f1a3606
Revises: abf0f377f2ec
Create Date: 2025-08-02 10:06:51.351704

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '6a1b4f1a3606'
down_revision = 'abf0f377f2ec'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('connection', sa.Column('icon_url', sqlmodel.sql.sqltypes.AutoString(length=2048), nullable=True))
    op.add_column('connection_template', sa.Column('icon_url', sqlmodel.sql.sqltypes.AutoString(length=2048), nullable=True))
    op.drop_column('connection_template', 'icon_storage_key')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('connection_template', sa.Column('icon_storage_key', sa.VARCHAR(length=512), autoincrement=False, nullable=True))
    op.drop_column('connection_template', 'icon_url')
    op.drop_column('connection', 'icon_url')
    # ### end Alembic commands ###
