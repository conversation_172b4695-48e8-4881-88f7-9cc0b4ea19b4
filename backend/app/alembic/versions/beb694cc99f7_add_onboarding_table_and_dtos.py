"""add onboarding table and dtos

Revision ID: beb694cc99f7
Revises: a37b52352640
Create Date: 2025-07-23 15:21:46.505578

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'beb694cc99f7'
down_revision = 'a37b52352640'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('user_onboarding',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('user_id', sa.Uuid(), nullable=False),
    sa.Column('current_step', sa.Integer(), nullable=False),
    sa.Column('is_completed', sa.<PERSON>(), nullable=False),
    sa.Column('completed_at', sa.DateTime(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_onboarding_user_id'), 'user_onboarding', ['user_id'], unique=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_user_onboarding_user_id'), table_name='user_onboarding')
    op.drop_table('user_onboarding')
    # ### end Alembic commands ###
