"""add last_sync_at field to cloud_sync_config table

Revision ID: a72f0f00660e
Revises: 9ea6d999d978
Create Date: 2025-07-27 13:49:46.958878

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'a72f0f00660e'
down_revision = '9ea6d999d978'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('cloud_sync_config', sa.Column('last_sync_at', sa.DateTime(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('cloud_sync_config', 'last_sync_at')
    # ### end Alembic commands ###
