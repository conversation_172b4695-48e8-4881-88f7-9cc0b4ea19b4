"""add token usage for token_usage table

Revision ID: 7a36d56f227a
Revises: 3f61fc759931
Create Date: 2025-08-13 20:05:29.743582

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '7a36d56f227a'
down_revision = '3f61fc759931'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # First add total_tokens as nullable
    op.add_column('token_usage', sa.Column('total_tokens', sa.Integer(), nullable=True))
    
    # Update existing rows to set total_tokens to 0
    op.execute("UPDATE token_usage SET total_tokens = 0 WHERE total_tokens IS NULL")
    
    # Now alter the column to be NOT NULL
    op.alter_column('token_usage', 'total_tokens', nullable=False)
    
    op.add_column('token_usage', sa.Column('input_token_details', sa.JSON(), nullable=True))
    op.add_column('token_usage', sa.Column('output_token_details', sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('token_usage', 'output_token_details')
    op.drop_column('token_usage', 'input_token_details')
    op.drop_column('token_usage', 'total_tokens')
    # ### end Alembic commands ###
