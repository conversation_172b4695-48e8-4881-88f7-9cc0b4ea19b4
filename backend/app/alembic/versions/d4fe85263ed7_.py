"""empty message

Revision ID: d4fe85263ed7
Revises: 6cdd4b251538, 715cf614eb82
Create Date: 2025-08-14 13:07:53.991527

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'd4fe85263ed7'
down_revision = ('6cdd4b251538', '715cf614eb82')
branch_labels = None
depends_on = None


def upgrade():
    pass


def downgrade():
    pass
