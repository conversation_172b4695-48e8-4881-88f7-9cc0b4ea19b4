"""make category nullable

Revision ID: 9ea6d999d978
Revises: 39c1338bf416
Create Date: 2025-07-27 09:28:56.068292

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '9ea6d999d978'
down_revision = '39c1338bf416'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('resource', 'category',
               existing_type=postgresql.ENUM('COMPUTE', 'DATABASE', 'STORAGE', 'NETWORKING', 'SERVERLESS', 'CONTAINER', 'MESSAGING', 'MONITORING', 'SECURITY', 'MANAGEMENT', 'ANALYTICS', 'AI_ML', 'OTHER', name='resourcecategory'),
               nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('resource', 'category',
               existing_type=postgresql.ENUM('COMPUTE', 'DATABASE', 'STORAGE', 'NETWORKING', 'SERVERLESS', 'CONTAINER', 'MESSAGING', 'MONITORING', 'SECURITY', 'MANAGEMENT', 'ANALYTICS', 'AI_ML', 'OTHER', name='resourcecategory'),
               nullable=False)
    # ### end Alembic commands ###
