"""change provider in workspace to optional

Revision ID: b45a99981bcc
Revises: d782cd59cc7b
Create Date: 2025-07-24 11:34:02.393820

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'b45a99981bcc'
down_revision = 'd782cd59cc7b'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('workspace', 'provider',
               existing_type=postgresql.ENUM('AWS', 'GCP', 'AZURE', name='cloudprovider'),
               nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('workspace', 'provider',
               existing_type=postgresql.ENUM('AWS', 'GCP', 'AZUR<PERSON>', name='cloudprovider'),
               nullable=False)
    # ### end Alembic commands ###
