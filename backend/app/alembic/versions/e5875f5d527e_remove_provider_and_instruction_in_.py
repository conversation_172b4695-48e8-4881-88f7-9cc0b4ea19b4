"""remove provider and instruction in conversation table

Revision ID: e5875f5d527e
Revises: b45a99981bcc
Create Date: 2025-07-26 17:33:45.923273

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'e5875f5d527e'
down_revision = 'b45a99981bcc'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('conversation', 'instructions')
    op.drop_column('conversation', 'model_provider')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('conversation', sa.Column('model_provider', sa.VARCHAR(length=255), autoincrement=False, nullable=False))
    op.add_column('conversation', sa.Column('instructions', sa.TEXT(), autoincrement=False, nullable=True))
    # ### end Alembic commands ###
