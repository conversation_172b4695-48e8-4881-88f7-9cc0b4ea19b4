"""add connection description

Revision ID: 71a8e34ac322
Revises: 5c337d380350
Create Date: 2025-08-01 16:32:05.653571

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '71a8e34ac322'
down_revision = '5c337d380350'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('connection_template', sa.Column('description', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('connection_template', 'description')
    # ### end Alembic commands ###
