"""Add performance indexes for message queries

Revision ID: 035d61bee1ce
Revises: 9253e50d232c
Create Date: 2025-08-14 16:00:53.465066

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '035d61bee1ce'
down_revision = '9253e50d232c'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('ix_message_conversation_deleted_created', 'message', ['conversation_id', 'is_deleted', 'created_at'], unique=False)
    op.create_index(op.f('ix_message_is_deleted'), 'message', ['is_deleted'], unique=False)
    op.create_index(op.f('ix_message_attachments_message_id'), 'message_attachments', ['message_id'], unique=False)
    op.create_index(op.f('ix_messagecomponent_message_id'), 'messagecomponent', ['message_id'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_messagecomponent_message_id'), table_name='messagecomponent')
    op.drop_index(op.f('ix_message_attachments_message_id'), table_name='message_attachments')
    op.drop_index(op.f('ix_message_is_deleted'), table_name='message')
    op.drop_index('ix_message_conversation_deleted_created', table_name='message')
    # ### end Alembic commands ###
