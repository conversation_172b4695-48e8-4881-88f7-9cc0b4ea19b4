"""add selected provider field

Revision ID: 41e47eb352df
Revises: a72f0f00660e
Create Date: 2025-07-28 18:12:20.486800

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '41e47eb352df'
down_revision = 'a72f0f00660e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_onboarding', sa.Column('selected_provider', postgresql.ENUM('AWS', 'GCP', 'AZURE', name='cloudprovider', create_type=False), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user_onboarding', 'selected_provider')
    # ### end Alembic commands ###
