"""fix ambiguous foreign key and add interupt reasoning

Revision ID: 853f4e768aae
Revises: ddfb74f3e21d
Create Date: 2025-08-05 17:18:18.888856

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '853f4e768aae'
down_revision = 'ddfb74f3e21d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_messageagentthought_message_id', table_name='messageagentthought')
    op.drop_table('messageagentthought')
    op.add_column('message', sa.Column('interrupt_reasoning', sqlmodel.sql.sqltypes.AutoString(), nullable=True))
    op.drop_index('ix_messagecomponent_display_component_id', table_name='messagecomponent')
    op.drop_index('ix_messagecomponent_tool_component_id', table_name='messagecomponent')
    op.drop_constraint('fk_messagecomponent_display_component', 'messagecomponent', type_='foreignkey')
    op.drop_constraint('fk_messagecomponent_tool_component', 'messagecomponent', type_='foreignkey')
    op.drop_column('messagecomponent', 'tool_component_id')
    op.drop_column('messagecomponent', 'display_component_id')
    op.alter_column('messagedisplaycomponent', 'data',
               existing_type=postgresql.JSON(astext_type=sa.Text()),
               nullable=True)
    op.drop_column('messagedisplaycomponent', 'config')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('messagedisplaycomponent', sa.Column('config', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=False))
    op.alter_column('messagedisplaycomponent', 'data',
               existing_type=postgresql.JSON(astext_type=sa.Text()),
               nullable=False)
    op.add_column('messagecomponent', sa.Column('display_component_id', sa.UUID(), autoincrement=False, nullable=True))
    op.add_column('messagecomponent', sa.Column('tool_component_id', sa.UUID(), autoincrement=False, nullable=True))
    op.create_foreign_key('fk_messagecomponent_tool_component', 'messagecomponent', 'messagetoolcomponent', ['tool_component_id'], ['id'], ondelete='CASCADE')
    op.create_foreign_key('fk_messagecomponent_display_component', 'messagecomponent', 'messagedisplaycomponent', ['display_component_id'], ['id'], ondelete='CASCADE')
    op.create_index('ix_messagecomponent_tool_component_id', 'messagecomponent', ['tool_component_id'], unique=False)
    op.create_index('ix_messagecomponent_display_component_id', 'messagecomponent', ['display_component_id'], unique=False)
    op.drop_column('message', 'interrupt_reasoning')
    op.create_table('messageagentthought',
    sa.Column('position', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('tool_name', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('tool_input', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('tool_output', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('content', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('message_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('tool_runtime', sa.DOUBLE_PRECISION(precision=53), server_default=sa.text("'0'::double precision"), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['message_id'], ['message.id'], name='messageagentthought_message_id_fkey', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name='messageagentthought_pkey')
    )
    op.create_index('ix_messageagentthought_message_id', 'messageagentthought', ['message_id'], unique=False)
    # ### end Alembic commands ###
