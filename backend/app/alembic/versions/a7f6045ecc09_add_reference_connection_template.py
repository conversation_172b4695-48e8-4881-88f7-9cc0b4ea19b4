"""add reference connection template

Revision ID: a7f6045ecc09
Revises: afcec62ef56d
Create Date: 2025-07-31 11:37:13.163522

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy.dialects import postgresql
from sqlalchemy_utils import EncryptedType, StringEncryptedType
from sqlalchemy_utils.types.encrypted.encrypted_type import FernetEngine
from app.core.config import settings


# revision identifiers, used by Alembic.
revision = 'a7f6045ecc09'
down_revision = 'afcec62ef56d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('connection_template',
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('prefix', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('builtin_connection_type', postgresql.ENUM('CLOUD', 'DB', 'ORCHESTRATION', 'OBSERVABILITY', 'CLI', name='builtinconnectiontype', create_type=False), nullable=False),
    sa.Column('is_sandbox_supported', sa.Boolean(), nullable=False),
    sa.Column('config', sa.JSON(), nullable=True),
    sa.Column('env', EncryptedType(sa.String, settings.SECRET_KEY, FernetEngine), nullable=False),
    sa.Column('tools', sa.JSON(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.drop_table('connectiontemplate')
    op.add_column('connection', sa.Column('connection_template_id', sa.Uuid(), nullable=True))
    op.create_index(op.f('ix_connection_connection_template_id'), 'connection', ['connection_template_id'], unique=False)
    op.create_foreign_key(None, 'connection', 'connection_template', ['connection_template_id'], ['id'], ondelete='CASCADE')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # Get the foreign key constraint name dynamically
    bind = op.get_bind()
    metadata = sa.MetaData()
    metadata.reflect(bind=bind)

    # Find and drop the foreign key constraint on connection_template_id
    conn_table = metadata.tables.get('connection')
    if conn_table is not None:
        for fk in conn_table.foreign_keys:
            if fk.column.table.name == 'connection_template':
                op.drop_constraint(fk.constraint.name, 'connection', type_='foreignkey')
                break

    op.drop_index(op.f('ix_connection_connection_template_id'), table_name='connection')
    op.drop_column('connection', 'connection_template_id')
    op.create_table('connectiontemplate',
    sa.Column('name', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('prefix', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('builtin_connection_type', postgresql.ENUM('CLOUD', 'DB', 'ORCHESTRATION', 'OBSERVABILITY', 'CLI', name='builtinconnectiontype', create_type=False), autoincrement=False, nullable=False),
    sa.Column('is_sandbox_supported', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('config', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('env', EncryptedType(sa.String, settings.SECRET_KEY, FernetEngine), autoincrement=False, nullable=False),
    sa.Column('tools', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('id', name='connectiontemplate_pkey')
    )
    op.drop_table('connection_template')
    # ### end Alembic commands ###
