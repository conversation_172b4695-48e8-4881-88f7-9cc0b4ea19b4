"""add resource_type for rcm table

Revision ID: b35456ad9c89
Revises: 715cf614eb82
Create Date: 2025-08-14 14:09:10.843306

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'b35456ad9c89'
down_revision = '715cf614eb82'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('recommendation', sa.Column('resource_type', sqlmodel.sql.sqltypes.AutoString(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('recommendation', 'resource_type')
    # ### end Alembic commands ###
