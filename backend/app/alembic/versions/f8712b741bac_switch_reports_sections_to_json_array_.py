"""Switch reports.sections to JSON array and safe defaults

Revision ID: f8712b741bac
Revises: ff2d25989a37
Create Date: 2025-08-10 16:59:28.834592

"""
from alembic import op


# revision identifiers, used by Alembic.
revision = 'f8712b741bac'
down_revision = 'ff2d25989a37'
branch_labels = None
depends_on = None


def upgrade():
    # Convert sections stored as JSON object -> JSON array
    # We sort by numeric key so section order is preserved.
    op.execute(
        """
        UPDATE reports r
        SET sections = sub.new_sections
        FROM (
          SELECT id,
                 to_json(
                   (
                     SELECT jsonb_agg(value ORDER BY (key)::int)
                     FROM jsonb_each(r.sections::jsonb)
                   )
                 ) AS new_sections
          FROM reports r
          WHERE jsonb_typeof(r.sections::jsonb) = 'object'
        ) AS sub
        WHERE r.id = sub.id;
        """
    )


def downgrade():
    # No schema change to revert; leave as no-op.
    pass
