"""Add description field to dashboards table

Revision ID: a240ca4998a1
Revises: 1e3fba494405
Create Date: 2025-08-01 11:45:07.602756

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'a240ca4998a1'
down_revision = '1e3fba494405'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('dashboards', sa.Column('description', sqlmodel.sql.sqltypes.AutoString(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('dashboards', 'description')
    # ### end Alembic commands ###
