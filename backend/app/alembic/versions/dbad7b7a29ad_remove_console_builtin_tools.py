"""remove console builtin tools

Revision ID: dbad7b7a29ad
Revises: 41e47eb352df
Create Date: 2025-07-30 07:28:05.182711

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'dbad7b7a29ad'
down_revision = '41e47eb352df'
branch_labels = None
depends_on = None


def upgrade():
    """Remove console builtin tools - cascades will handle related records"""
    connection = op.get_bind()

    # Remove console builtin tools from BuiltInTool table
    # This will cascade and remove all related records automatically
    connection.execute(
        sa.text("DELETE FROM builtintool WHERE name IN ('use_console_read_only_permissions', 'use_console_write_permissions')")
    )


def downgrade():
    """Re-add console builtin tools"""
    connection = op.get_bind()

    console_tools = [
        {
            "name": "use_console_read_only_permissions",
            "display_name": "Use Console Read Only Permissions",
            "description": "Built-in connector for using console with read only permissions",
            "default_required_permission": False,
        },
        {
            "name": "use_console_write_permissions",
            "display_name": "Use Console Write Permissions",
            "description": "Built-in connector for using console with write permissions",
            "default_required_permission": True,
        }
    ]

    for tool in console_tools:
        connection.execute(
            sa.text("""
                INSERT INTO builtintool (id, name, display_name, description, default_required_permission, created_at, updated_at)
                VALUES (gen_random_uuid(), :name, :display_name, :description, :default_required_permission, NOW(), NOW())
                ON CONFLICT (name) DO NOTHING
            """),
            tool
        )
