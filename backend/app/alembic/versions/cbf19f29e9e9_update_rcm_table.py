"""update rcm table

Revision ID: cbf19f29e9e9
Revises: 97a01cb27b5b
Create Date: 2025-08-10 12:58:19.073209

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'cbf19f29e9e9'
down_revision = '97a01cb27b5b'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_foreign_key(None, 'dashboards', 'workspace', ['workspace_id'], ['id'])
    op.create_foreign_key(None, 'dashboards', 'conversation', ['conversation_id'], ['id'])

    # First, we need to handle existing recommendations
    # Since we're adding NOT NULL columns, we need to ensure existing data has values
    # Skip adding columns to recommendation for now since it will require data migration

    op.alter_column('recommendation', 'resource_id',
               existing_type=sa.UUID(),
               nullable=True)

    op.create_foreign_key(None, 'reports', 'conversation', ['conversation_id'], ['id'])
    op.create_foreign_key(None, 'reports', 'workspace', ['workspace_id'], ['id'])

    # Update null full_name values before making it NOT NULL
    op.execute("UPDATE \"user\" SET full_name = email WHERE full_name IS NULL")
    op.alter_column('user', 'full_name',
               existing_type=sa.VARCHAR(length=255),
               nullable=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('user', 'full_name',
               existing_type=sa.VARCHAR(length=255),
               nullable=True)
    op.drop_constraint(None, 'reports', type_='foreignkey')
    op.drop_constraint(None, 'reports', type_='foreignkey')
    op.drop_constraint(None, 'recommendation', type_='foreignkey')
    op.drop_constraint(None, 'recommendation', type_='foreignkey')
    op.drop_index(op.f('ix_recommendation_workspace_id'), table_name='recommendation')
    op.drop_index(op.f('ix_recommendation_created_by'), table_name='recommendation')
    op.alter_column('recommendation', 'resource_id',
               existing_type=sa.UUID(),
               nullable=False)
    op.drop_column('recommendation', 'created_by')
    op.drop_column('recommendation', 'workspace_id')
    op.drop_constraint(None, 'dashboards', type_='foreignkey')
    op.drop_constraint(None, 'dashboards', type_='foreignkey')
    # ### end Alembic commands ###
