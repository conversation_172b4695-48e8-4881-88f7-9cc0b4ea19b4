"""update connectio

Revision ID: ac07b7671a36
Revises: a7f6045ecc09
Create Date: 2025-08-01 01:55:19.206832

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'ac07b7671a36'
down_revision = 'a7f6045ecc09'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('connection', 'tool_permissions')
    op.drop_column('connection', 'tool_enabled')
    op.drop_column('connection', 'tool_list')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('connection', sa.Column('tool_list', postgresql.ARRAY(sa.VARCHAR()), autoincrement=False, nullable=True))
    op.add_column('connection', sa.Column('tool_enabled', postgresql.ARRAY(sa.VARCHAR()), autoincrement=False, nullable=True))
    op.add_column('connection', sa.Column('tool_permissions', postgresql.ARRAY(sa.VARCHAR()), autoincrement=False, nullable=True))
    # ### end Alembic commands ###
