"""Remove additional unused indexes across recommendation, usage_quota, agent, conversation, message tables to reclaim 2.5MB+ storage

Revision ID: 3f61fc759931
Revises: cb7715fd8592
Create Date: 2025-08-12 12:22:27.533321

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy import text


# revision identifiers, used by Alembic.
revision = '3f61fc759931'
down_revision = 'cb7715fd8592'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###

    # Helper function to safely drop index
    def safe_drop_index(table_name, index_name):
        try:
            # Check if index exists before dropping
            connection = op.get_bind()
            result = connection.execute(text(f"""
                SELECT 1 FROM pg_indexes
                WHERE tablename = '{table_name}' AND indexname = '{index_name}'
            """))
            if result.fetchone():
                op.drop_index(index_name, table_name=table_name)
                print(f"Successfully dropped index {index_name} from {table_name}")
            else:
                print(f"Index {index_name} does not exist on {table_name}, skipping...")
        except Exception as e:
            print(f"Warning: Could not drop index {index_name} from {table_name}: {e}")
            # Continue with migration even if index drop fails

    # Drop indexes with graceful failure handling
    safe_drop_index('agent', 'ix_agent_is_active')
    safe_drop_index('agent', 'ix_agent_is_deleted')
    safe_drop_index('agent', 'ix_agent_title')
    safe_drop_index('agent', 'ix_agent_type')
    safe_drop_index('agent', 'ix_agent_workspace_id')
    safe_drop_index('conversation', 'ix_conversation_share_id')
    safe_drop_index('message', 'ix_message_is_deleted')
    safe_drop_index('message', 'ix_message_is_interrupt')
    safe_drop_index('message', 'ix_message_role')
    safe_drop_index('message_feedback', 'ix_message_feedback_message_id')
    safe_drop_index('message_feedback', 'ix_message_feedback_user_id')
    safe_drop_index('messagecomponent', 'ix_messagecomponent_message_id')
    safe_drop_index('messagedisplaycomponent', 'ix_messagedisplaycomponent_message_component_id')
    safe_drop_index('messagetoolcomponent', 'ix_messagetoolcomponent_message_component_id')
    safe_drop_index('recommendation', 'ix_recommendation_created_by')
    safe_drop_index('recommendation', 'ix_recommendation_status')
    safe_drop_index('recommendation', 'ix_recommendation_workspace_id')
    safe_drop_index('usage_quota', 'ix_usage_quota_user_id')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('ix_usage_quota_user_id', 'usage_quota', ['user_id'], unique=False)
    op.create_index('ix_recommendation_workspace_id', 'recommendation', ['workspace_id'], unique=False)
    op.create_index('ix_recommendation_status', 'recommendation', ['status'], unique=False)
    op.create_index('ix_recommendation_created_by', 'recommendation', ['created_by'], unique=False)
    op.create_index('ix_messagetoolcomponent_message_component_id', 'messagetoolcomponent', ['message_component_id'], unique=False)
    op.create_index('ix_messagedisplaycomponent_message_component_id', 'messagedisplaycomponent', ['message_component_id'], unique=False)
    op.create_index('ix_messagecomponent_message_id', 'messagecomponent', ['message_id'], unique=False)
    op.create_index('ix_message_feedback_user_id', 'message_feedback', ['user_id'], unique=False)
    op.create_index('ix_message_feedback_message_id', 'message_feedback', ['message_id'], unique=False)
    op.create_index('ix_message_role', 'message', ['role'], unique=False)
    op.create_index('ix_message_is_interrupt', 'message', ['is_interrupt'], unique=False)
    op.create_index('ix_message_is_deleted', 'message', ['is_deleted'], unique=False)
    op.create_index('ix_conversation_share_id', 'conversation', ['share_id'], unique=True)
    op.create_index('ix_agent_workspace_id', 'agent', ['workspace_id'], unique=False)
    op.create_index('ix_agent_type', 'agent', ['type'], unique=False)
    op.create_index('ix_agent_title', 'agent', ['title'], unique=False)
    op.create_index('ix_agent_is_deleted', 'agent', ['is_deleted'], unique=False)
    op.create_index('ix_agent_is_active', 'agent', ['is_active'], unique=False)
    # ### end Alembic commands ###
