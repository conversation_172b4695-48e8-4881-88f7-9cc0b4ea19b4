"""empty message

Revision ID: 485288791a83
Revises: 288e2d1ecc7e, e5875f5d527e
Create Date: 2025-07-26 21:40:00.959670

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '485288791a83'
down_revision = ('288e2d1ecc7e', 'e5875f5d527e')
branch_labels = None
depends_on = None


def upgrade():
    pass


def downgrade():
    pass
