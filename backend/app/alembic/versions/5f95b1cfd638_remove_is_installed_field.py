"""remove is installed field

Revision ID: 5f95b1cfd638
Revises: dbad7b7a29ad
Create Date: 2025-07-30 15:07:22.420519

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '5f95b1cfd638'
down_revision = 'dbad7b7a29ad'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('cloudprovidersetting')
    op.drop_index('ix_connection_is_installed', table_name='connection')
    op.drop_column('connection', 'is_installed')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('connection', sa.Column('is_installed', sa.BOOLEAN(), autoincrement=False, nullable=False))
    op.create_index('ix_connection_is_installed', 'connection', ['is_installed'], unique=False)
    op.create_table('cloudprovidersetting',
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('provider_name', postgresql.ENUM('AWS', 'GCP', 'AZURE', name='cloudprovider', create_type=False), autoincrement=False, nullable=False),
    sa.Column('regions', postgresql.ARRAY(sa.VARCHAR()), autoincrement=False, nullable=True),
    sa.Column('types', postgresql.ARRAY(sa.VARCHAR()), autoincrement=False, nullable=True),
    sa.Column('cron_patterns', postgresql.ARRAY(sa.VARCHAR()), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='cloudprovidersetting_pkey')
    )
    # ### end Alembic commands ###
