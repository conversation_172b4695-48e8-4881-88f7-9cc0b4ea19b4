"""add connection storage key and time expires token

Revision ID: 010c9667d9b0
Revises: 29fb80f0c601
Create Date: 2025-08-03 06:40:59.874555

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '010c9667d9b0'
down_revision = '29fb80f0c601'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('connection', sa.Column('icon_url_expires_at', sa.DateTime(), nullable=True))
    op.add_column('connection_template', sa.Column('icon_url_expires_at', sa.DateTime(), nullable=True))
    op.add_column('connection_template', sa.Column('icon_storage_key', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('connection_template', 'icon_storage_key')
    op.drop_column('connection_template', 'icon_url_expires_at')
    op.drop_column('connection', 'icon_url_expires_at')
    # ### end Alembic commands ###
