"""Remove icon fields from connection templates

Revision ID: 6cdd4b251538
Revises: 3f61fc759931
Create Date: 2025-08-14 11:31:03.100967

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '6cdd4b251538'
down_revision = '3f61fc759931'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('connection', 'icon_url')
    op.drop_column('connection', 'icon_url_expires_at')
    op.drop_column('connection_template', 'icon_storage_key')
    op.drop_column('connection_template', 'icon_url_expires_at')
    op.drop_column('connection_template', 'icon_url')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('connection_template', sa.Column('icon_url', sa.VARCHAR(length=2048), autoincrement=False, nullable=True))
    op.add_column('connection_template', sa.Column('icon_url_expires_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True))
    op.add_column('connection_template', sa.Column('icon_storage_key', sa.VARCHAR(length=255), autoincrement=False, nullable=True))
    op.add_column('connection', sa.Column('icon_url_expires_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True))
    op.add_column('connection', sa.Column('icon_url', sa.VARCHAR(length=2048), autoincrement=False, nullable=True))
    # ### end Alembic commands ###
