"""update connection connection_type

Revision ID: 1e3fba494405
Revises: ac07b7671a36
Create Date: 2025-08-01 06:06:09.772985

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from alembic_postgresql_enum import TableReference

# revision identifiers, used by Alembic.
revision = '1e3fba494405'
down_revision = 'ac07b7671a36'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.sync_enum_values(
        enum_schema='public',
        enum_name='connectiontype',
        new_values=['BUILTIN', 'MCP'],
        affected_columns=[TableReference(table_schema='public', table_name='connection', column_name='connection_type', existing_server_default="'MCP'::connectiontype")],
        enum_values_to_rename=[],
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.sync_enum_values(
        enum_schema='public',
        enum_name='connectiontype',
        new_values=['BUILTIN', 'MCP', 'CLOUD', 'CLI'],
        affected_columns=[TableReference(table_schema='public', table_name='connection', column_name='connection_type', existing_server_default="'MCP'::connectiontype")],
        enum_values_to_rename=[],
    )
    # ### end Alembic commands ###
