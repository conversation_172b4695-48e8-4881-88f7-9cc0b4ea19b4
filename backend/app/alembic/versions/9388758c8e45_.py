"""empty message

Revision ID: 9388758c8e45
Revises: 71a8e34ac322, a54367e38f7d
Create Date: 2025-08-01 16:56:57.152382

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '9388758c8e45'
down_revision = ('71a8e34ac322', 'a54367e38f7d')
branch_labels = None
depends_on = None


def upgrade():
    pass


def downgrade():
    pass
