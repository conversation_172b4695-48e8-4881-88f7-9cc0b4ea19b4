"""add connection icon_storage_key

Revision ID: abf0f377f2ec
Revises: 4e1d80114e9c
Create Date: 2025-08-01 19:57:31.971003

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'abf0f377f2ec'
down_revision = '4e1d80114e9c'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('connection_template', sa.Column('icon_storage_key', sqlmodel.sql.sqltypes.AutoString(length=512), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('connection_template', 'icon_storage_key')
    # ### end Alembic commands ###
