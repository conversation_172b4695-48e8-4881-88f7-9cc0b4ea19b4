"""add_index_usage_quota_user_id_actual

Revision ID: d5f4e132b259
Revises: 3d684525cafc
Create Date: 2025-08-14 21:43:29.883481

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'd5f4e132b259'
down_revision = '3d684525cafc'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(op.f('ix_usage_quota_user_id'), 'usage_quota', ['user_id'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_usage_quota_user_id'), table_name='usage_quota')
    # ### end Alembic commands ###
