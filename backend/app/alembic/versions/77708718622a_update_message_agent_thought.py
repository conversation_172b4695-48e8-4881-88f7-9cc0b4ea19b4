"""update message agent thought

Revision ID: 77708718622a
Revises: 485288791a83
Create Date: 2025-07-27 00:08:35.237027

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '77708718622a'
down_revision = '485288791a83'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # Rename existing columns instead of dropping them
    op.alter_column('messageagentthought', 'tool', new_column_name='tool_name')
    op.alter_column('messageagentthought', 'answer', new_column_name='content')
    op.alter_column('messageagentthought', 'observation', new_column_name='tool_output')

    # Add new columns
    op.add_column('messageagentthought', sa.Column('tool_runtime', sa.Float(), nullable=True, server_default='0'))
    op.add_column('messageagentthought', sa.Column('created_at', sa.DateTime(), nullable=False, server_default=sa.func.now()))
    op.add_column('messageagentthought', sa.Column('updated_at', sa.DateTime(), nullable=False, server_default=sa.func.now()))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # Drop new columns
    op.drop_column('messageagentthought', 'updated_at')
    op.drop_column('messageagentthought', 'created_at')
    op.drop_column('messageagentthought', 'tool_runtime')

    # Rename columns back to original names
    op.alter_column('messageagentthought', 'tool_name', new_column_name='tool')
    op.alter_column('messageagentthought', 'content', new_column_name='answer')
    op.alter_column('messageagentthought', 'tool_output', new_column_name='observation')
    # ### end Alembic commands ###
