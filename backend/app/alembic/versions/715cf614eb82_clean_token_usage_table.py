"""clean token_usage table

Revision ID: 715cf614eb82
Revises: 7a36d56f227a
Create Date: 2025-08-13 20:25:29.750407

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '715cf614eb82'
down_revision = '7a36d56f227a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('token_usage', 'model_provider')
    op.drop_column('token_usage', 'model_id')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('token_usage', sa.Column('model_id', sa.VARCHAR(), autoincrement=False, nullable=False))
    op.add_column('token_usage', sa.Column('model_provider', sa.VARCHAR(), autoincrement=False, nullable=False))
    # ### end Alembic commands ###
