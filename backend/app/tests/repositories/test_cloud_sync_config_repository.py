from uuid import uuid4

import pytest
from sqlmodel import Session

from app.models import (
    BuiltinConnectionType,
    CloudSyncConfigCreate,
    CloudSyncConfigUpdate,
    Connection,
    Workspace,
)
from app.repositories.cloud_sync_config import CloudSyncConfigRepository


class TestCloudSyncConfigRepository:
    """Test suite for CloudSyncConfigRepository"""

    @pytest.fixture
    def workspace(self, db: Session):
        """Create a test workspace"""
        from app import crud
        from app.models import UserCreate

        # Create a user first
        user_create = UserCreate(
            email=f"test-{uuid4()}@example.com", password="testpassword123"
        )
        user = crud.create_user(session=db, user_create=user_create)

        # Create workspace
        workspace = Workspace(
            id=uuid4(),
            name="Test Workspace",
            description="Test workspace for cloud sync config",
            owner_id=user.id,
        )
        db.add(workspace)
        db.commit()
        db.refresh(workspace)
        return workspace

    @pytest.fixture
    def connection(self, db: Session, workspace):
        """Create a test connection"""
        connection = Connection(
            id=uuid4(),
            workspace_id=workspace.id,
            name="Test AWS Connection",
            prefix="aws",
            builtin_connection_type=BuiltinConnectionType.CLOUD,
            config={
                "env": {
                    "AWS_ACCESS_KEY_ID": "test_key",
                    "AWS_SECRET_ACCESS_KEY": "test_secret",
                }
            },
            is_installed=True,
        )
        db.add(connection)
        db.commit()
        db.refresh(connection)
        return connection

    @pytest.fixture
    def repository(self, db: Session):
        """CloudSyncConfigRepository instance"""
        # Convert sync session to async-like for testing
        from unittest.mock import AsyncMock

        async_session = AsyncMock()
        async_session.exec = db.exec
        async_session.add = db.add
        async_session.commit = lambda: None  # Mock async commit
        async_session.refresh = db.refresh
        async_session.delete = db.delete
        async_session.rollback = lambda: None  # Mock async rollback

        return CloudSyncConfigRepository(async_session)

    @pytest.fixture
    def sample_config_create(self, connection):
        """Sample CloudSyncConfigCreate"""
        return CloudSyncConfigCreate(
            connection_id=connection.id,
            include_stopped_resources=True,
            refresh_interval=120,
            selected_resources=["EC2", "RDS", "S3"],
            is_enabled=True,
        )

    async def test_create_config_success(
        self, repository, workspace, sample_config_create
    ):
        """Test successful configuration creation"""
        config = await repository.create_config(workspace.id, sample_config_create)

        assert config.workspace_id == workspace.id
        assert config.connection_id == sample_config_create.connection_id
        assert (
            config.include_stopped_resources
            == sample_config_create.include_stopped_resources
        )
        assert config.refresh_interval == sample_config_create.refresh_interval
        assert config.selected_resources == sample_config_create.selected_resources
        assert config.is_enabled == sample_config_create.is_enabled
        assert config.id is not None
        assert config.created_at is not None
        assert config.updated_at is not None

    async def test_get_by_workspace_and_connection_success(
        self, repository, workspace, connection, sample_config_create
    ):
        """Test successful retrieval by workspace and connection"""
        # Create a config first
        created_config = await repository.create_config(
            workspace.id, sample_config_create
        )

        # Retrieve it
        retrieved_config = await repository.get_by_workspace_and_connection(
            workspace.id, connection.id
        )

        assert retrieved_config is not None
        assert retrieved_config.id == created_config.id
        assert retrieved_config.workspace_id == workspace.id
        assert retrieved_config.connection_id == connection.id

    async def test_get_by_workspace_and_connection_not_found(
        self, repository, workspace
    ):
        """Test retrieval when configuration doesn't exist"""
        non_existent_connection_id = uuid4()

        result = await repository.get_by_workspace_and_connection(
            workspace.id, non_existent_connection_id
        )

        assert result is None

    async def test_get_by_workspace_success(
        self, repository, workspace, sample_config_create
    ):
        """Test successful retrieval of all workspace configurations"""
        # Create multiple configs
        config1 = await repository.create_config(workspace.id, sample_config_create)

        # Create another connection and config
        from app.models import Connection

        connection2 = Connection(
            id=uuid4(),
            workspace_id=workspace.id,
            name="Test GCP Connection",
            prefix="gcp",
            builtin_connection_type=BuiltinConnectionType.CLOUD,
            config={"env": {"GOOGLE_APPLICATION_CREDENTIALS": "test"}},
            is_installed=True,
        )
        repository.session.add(connection2)

        config_create2 = CloudSyncConfigCreate(
            connection_id=connection2.id,
            include_stopped_resources=False,
            refresh_interval=60,
            selected_resources=["COMPUTE_ENGINE"],
            is_enabled=True,
        )
        config2 = await repository.create_config(workspace.id, config_create2)

        # Retrieve all configs for workspace
        configs = await repository.get_by_workspace(workspace.id)

        assert len(configs) == 2
        config_ids = [c.id for c in configs]
        assert config1.id in config_ids
        assert config2.id in config_ids

    async def test_get_by_workspace_empty(self, repository, workspace):
        """Test retrieval when no configurations exist"""
        configs = await repository.get_by_workspace(workspace.id)

        assert len(configs) == 0

    async def test_get_enabled_configs_success(
        self, repository, workspace, sample_config_create
    ):
        """Test retrieval of enabled configurations"""
        # Create enabled config
        enabled_config = await repository.create_config(
            workspace.id, sample_config_create
        )

        # Create disabled config
        disabled_config_create = CloudSyncConfigCreate(
            connection_id=sample_config_create.connection_id,
            include_stopped_resources=False,
            refresh_interval=60,
            selected_resources=["EC2"],
            is_enabled=False,
        )
        # We need another connection for the second config due to unique constraint
        from app.models import Connection

        connection2 = Connection(
            id=uuid4(),
            workspace_id=workspace.id,
            name="Test Disabled Connection",
            prefix="aws",
            builtin_connection_type=BuiltinConnectionType.CLOUD,
            config={"env": {"AWS_ACCESS_KEY_ID": "test"}},
            is_installed=True,
        )
        repository.session.add(connection2)
        disabled_config_create.connection_id = connection2.id

        disabled_config = await repository.create_config(
            workspace.id, disabled_config_create
        )

        # Get enabled configs
        enabled_configs = await repository.get_enabled_configs()

        # Should only return the enabled config
        enabled_ids = [c.id for c in enabled_configs]
        assert enabled_config.id in enabled_ids
        assert disabled_config.id not in enabled_ids

    async def test_update_config_success(
        self, repository, workspace, sample_config_create
    ):
        """Test successful configuration update"""
        # Create config
        config = await repository.create_config(workspace.id, sample_config_create)
        original_updated_at = config.updated_at

        # Update config
        update_data = CloudSyncConfigUpdate(
            refresh_interval=240,
            selected_resources=["EC2", "RDS"],
            is_enabled=False,
        )

        updated_config = await repository.update_config(config.id, update_data)

        assert updated_config is not None
        assert updated_config.id == config.id
        assert updated_config.refresh_interval == 240
        assert updated_config.selected_resources == ["EC2", "RDS"]
        assert updated_config.is_enabled == False
        assert (
            updated_config.include_stopped_resources == config.include_stopped_resources
        )  # Unchanged
        assert updated_config.updated_at > original_updated_at

    async def test_update_config_not_found(self, repository):
        """Test updating non-existent configuration"""
        non_existent_id = uuid4()
        update_data = CloudSyncConfigUpdate(refresh_interval=240)

        result = await repository.update_config(non_existent_id, update_data)

        assert result is None

    async def test_update_config_partial_update(
        self, repository, workspace, sample_config_create
    ):
        """Test partial configuration update"""
        # Create config
        config = await repository.create_config(workspace.id, sample_config_create)

        # Update only refresh_interval
        update_data = CloudSyncConfigUpdate(refresh_interval=300)

        updated_config = await repository.update_config(config.id, update_data)

        assert updated_config is not None
        assert updated_config.refresh_interval == 300
        # Other fields should remain unchanged
        assert (
            updated_config.include_stopped_resources == config.include_stopped_resources
        )
        assert updated_config.selected_resources == config.selected_resources
        assert updated_config.is_enabled == config.is_enabled

    async def test_update_or_create_create_new(
        self, repository, workspace, sample_config_create
    ):
        """Test update_or_create when creating new configuration"""
        config = await repository.update_or_create(
            workspace.id, sample_config_create.connection_id, sample_config_create
        )

        assert config.workspace_id == workspace.id
        assert config.connection_id == sample_config_create.connection_id
        assert config.refresh_interval == sample_config_create.refresh_interval

    async def test_update_or_create_update_existing(
        self, repository, workspace, sample_config_create
    ):
        """Test update_or_create when updating existing configuration"""
        # Create initial config
        initial_config = await repository.create_config(
            workspace.id, sample_config_create
        )

        # Update via update_or_create
        updated_create_data = CloudSyncConfigCreate(
            connection_id=sample_config_create.connection_id,
            include_stopped_resources=False,
            refresh_interval=300,
            selected_resources=["EC2"],
            is_enabled=False,
        )

        updated_config = await repository.update_or_create(
            workspace.id, sample_config_create.connection_id, updated_create_data
        )

        # Should be the same record, but updated
        assert updated_config.id == initial_config.id
        assert updated_config.refresh_interval == 300
        assert updated_config.include_stopped_resources == False
        assert updated_config.selected_resources == ["EC2"]
        assert updated_config.is_enabled == False

    async def test_delete_config_success(
        self, repository, workspace, sample_config_create
    ):
        """Test successful configuration deletion"""
        # Create config
        config = await repository.create_config(workspace.id, sample_config_create)

        # Delete config
        result = await repository.delete_config(config.id)

        assert result == True

        # Verify it's deleted
        deleted_config = await repository.get_config_by_id(config.id)
        assert deleted_config is None

    async def test_delete_config_not_found(self, repository):
        """Test deleting non-existent configuration"""
        non_existent_id = uuid4()

        result = await repository.delete_config(non_existent_id)

        assert result == False

    async def test_get_config_by_id_success(
        self, repository, workspace, sample_config_create
    ):
        """Test successful retrieval by ID"""
        # Create config
        config = await repository.create_config(workspace.id, sample_config_create)

        # Retrieve by ID
        retrieved_config = await repository.get_config_by_id(config.id)

        assert retrieved_config is not None
        assert retrieved_config.id == config.id
        assert retrieved_config.workspace_id == workspace.id

    async def test_get_config_by_id_not_found(self, repository):
        """Test retrieval by ID when configuration doesn't exist"""
        non_existent_id = uuid4()

        result = await repository.get_config_by_id(non_existent_id)

        assert result is None

    async def test_unique_constraint_workspace_connection(
        self, repository, workspace, sample_config_create
    ):
        """Test that unique constraint on workspace_id + connection_id is enforced"""
        # Create first config
        await repository.create_config(workspace.id, sample_config_create)

        # Try to create another config with same workspace and connection
        duplicate_config_create = CloudSyncConfigCreate(
            connection_id=sample_config_create.connection_id,
            include_stopped_resources=False,
            refresh_interval=60,
            selected_resources=["RDS"],
            is_enabled=True,
        )

        # This should raise an exception due to unique constraint
        with pytest.raises(Exception):
            await repository.create_config(workspace.id, duplicate_config_create)
