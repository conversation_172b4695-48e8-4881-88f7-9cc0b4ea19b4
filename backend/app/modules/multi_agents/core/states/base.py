"""Base state shared by all agents."""

from dataclasses import dataclass, field
from typing import Annotated

from langchain_core.messages import AnyMessage
from langgraph.graph import add_messages


@dataclass
class StateBase:
    """Base state shared by all agents."""

    # Messages
    messages: Annotated[list[AnyMessage], add_messages] = field(default_factory=list)

    # Summary
    summary: str = ""


@dataclass
class InputState:
    """Input state shared by all agents."""

    messages: str = ""
