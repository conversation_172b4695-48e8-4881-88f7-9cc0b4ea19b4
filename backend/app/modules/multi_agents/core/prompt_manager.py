from datetime import datetime

from langchain_core.runnables import RunnableConfig

from app.modules.multi_agents.config import AgentConfig
from app.modules.multi_agents.core.configuration import Configuration
from app.modules.multi_agents.core.states.global_state import GlobalState
from app.modules.multi_agents.prompts import (
    AGENT_ROLE_DESCRIPTION,
    CUSTOMER_ROLE_DESCRIPTION,
    REGION_PROMPT,
    ROLE_PLAY_PROMPT,
    ROLE_PLAYING_PROMPT,
    THINKING_PROMPT,
)
from app.modules.multi_agents.prompts.networking import OBJECTIVES_PROMPT


class PromptManager:
    async def prepare_agent_list(
        self, state: GlobalState, config: RunnableConfig
    ) -> tuple[list[str], list[str]]:
        """Prepare messages for the conversational agent."""
        # Get the configuration
        configuration = Configuration.from_runnable_config(config)
        available_agents_list: list[str] = []
        unavailable_agents_list: list[str] = []

        for agent_name, agent_config in configuration.agents_config.agents.items():
            if agent_name in configuration.agents_config.active_agents:
                available_agents_list.append(
                    AGENT_ROLE_DESCRIPTION.format(
                        role_name=agent_name,
                        role_description=agent_config.role,
                        role_goal=agent_config.goal,
                    )
                )
            else:
                unavailable_agents_list.append(
                    AGENT_ROLE_DESCRIPTION.format(
                        role_name=agent_name,
                        role_description=agent_config.role,
                        role_goal=agent_config.goal,
                    )
                )

        return available_agents_list, unavailable_agents_list

    async def get_conversation_agent_system_prompt(
        self,
        agent_config: AgentConfig,
        state: GlobalState,
        config: RunnableConfig,
    ) -> str:
        """Get the system prompt for the conversation agent."""
        system_prompt = (
            THINKING_PROMPT
            + "\n\n"
            + ROLE_PLAY_PROMPT.format(
                name=state.name,
                role=agent_config.role,
                goal=agent_config.goal,
                instructions=agent_config.instructions,
            )
        )
        if agent_config.region_constraints:
            system_prompt += "\n\n" + REGION_PROMPT.format(
                region_constraints=agent_config.region_constraints
            )
        return system_prompt

    async def get_coordination_agent_role_playing_prompt(
        self, state: GlobalState, config: RunnableConfig
    ) -> str:
        """Get the role playing prompt for the coordination agent."""
        configuration = Configuration.from_runnable_config(config)
        available_agents_list, _ = await self.prepare_agent_list(state, config)

        role_playing_prompt = ROLE_PLAYING_PROMPT.format(
            name=state.name,
            role_descriptions="\n".join(available_agents_list),
            customer_role_description=CUSTOMER_ROLE_DESCRIPTION,
            group_chat=state.group_chat.get_messages(),
            last_group_chat_message=f"{state.group_chat.messages[-1][0]}: {state.group_chat.messages[-1][1]}",
            last_message=state.group_chat.last_message,
            participants=", ".join(configuration.agents_config.active_agents),
        )

        return role_playing_prompt


    async def get_objectives_prompt(
        self,
        agent_config: AgentConfig,
        state: GlobalState,
        config: RunnableConfig,
    ) -> str:
        configuration = Configuration.from_runnable_config(config)
        available_agents_list, _ = await self.prepare_agent_list(state, config)

        # user_prompt = configuration.user_prompt
        # memory_service = MemoryService()
        # available_memories_prompt = await memory_service.search_memory(
        #     user_prompt, state.name, configuration.workspace_id
        # )

        objectives_prompt = OBJECTIVES_PROMPT.format(
            available_agents_list="\n".join(available_agents_list),
            group_chat=state.group_chat.get_messages(),
            # TODO: @duc.bui add memories prompt
            available_memories_prompt="",
            kb_prompt=configuration.kb_prompt,
            agent_context=agent_config.agent_context,
            resource_context=agent_config.resource_context,
            name=state.name,
            datetime=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            last_message=state.group_chat.last_message,
        )
        return objectives_prompt


prompt_manager = PromptManager()
