import json
import re
from typing import Any, <PERSON>tern

from langchain_core.runnables import RunnableConfig
from langchain_core.tools import BaseTool, StructuredTool
from pydantic import BaseModel

from app.logger import logger
from app.models import ConnectionBase
from app.modules.connectors.mcp_client import MC<PERSON>lientConnector
from app.modules.multi_agents.config import AgentConfig
from app.modules.multi_agents.core.states.global_state import GlobalState
from app.modules.multi_agents.tools import (
    PlanManager,
    PlanningTool,
    call_executor,
    create_chart,
    dashboard,
    fetch_url,
    group_chat,
    push_alert,
    report,
    schedule_task,
    search_internet,
    search_knowledge_base,
    use_console_read_only_permissions,
    use_console_write_permissions,
)
from app.modules.multi_agents.tools.recommendation.recommendation import recommendation

AVAILABLE_TOOLS: dict[str, BaseTool] = {
    "push_alert": push_alert,
    "create_chart": create_chart,
    "recommendation": recommendation,
    "search_knowledge_base": search_knowledge_base,
    "search_internet": search_internet,
    "planning": PlanningTool,
    "group_chat": group_chat,
    "schedule_task": schedule_task,
    "report": report,
    "dashboard": dashboard,
    "fetch_url": fetch_url,
    "use_console_read_only_permissions": use_console_read_only_permissions,
    "use_console_write_permissions": use_console_write_permissions,
}


class ToolManager:
    builtin_tools: dict[str, BaseTool] = AVAILABLE_TOOLS
    tool_policies: dict[str, str] = {}
    tool_tags: dict[str, str] = {}

    _tool_metadata_initialized = False
    _tool_metadata_lock = None

    def __init__(
        self,
        mcp_servers: list[ConnectionBase] | None = None,
        connections_tools: dict[str, list[BaseTool]] | None = None,
    ) -> None:
        self._extract_tool_metadata()
        self.mcp_client = MCPClientConnector(mcp_servers) if mcp_servers else None
        self.connections_tools: dict[str, list[BaseTool]] = connections_tools

    @classmethod
    def _extract_tool_metadata(cls) -> None:
        """Extract tool metadata once during class initialization."""
        if cls._tool_metadata_initialized:
            return

        policy_pattern: Pattern[str] = re.compile(
            r"Tool Tag:\s*#?(\w+).*?Tool Use Policy:\s*#?(\w+)",
            re.DOTALL | re.IGNORECASE,
        )

        for tool_name, tool_func in cls.builtin_tools.items():
            docstring: str | None = cls._get_tool_docstring(tool_func)
            cls._process_docstring_metadata(tool_name, docstring, policy_pattern)

        cls._tool_metadata_initialized = True

    @classmethod
    def _get_tool_docstring(cls, tool_func) -> str | None:
        docstring_extractors = [
            lambda t: getattr(t, "description", None)
            if hasattr(t, "description")
            else None,
            lambda t: getattr(t, "__doc__", None),
            lambda t: getattr(t.func, "__doc__", None) if hasattr(t, "func") else None,
            lambda t: getattr(t.__wrapped__, "__doc__", None)
            if hasattr(t, "__wrapped__")
            else None,
            lambda t: cls._extract_pydantic_description(t) if callable(t) else None,
        ]

        for extractor in docstring_extractors:
            try:
                docstring = extractor(tool_func)
                if docstring:
                    return docstring
            except (AttributeError, TypeError):
                continue
        return None

    @classmethod
    def _extract_pydantic_description(cls, tool_func) -> str | None:
        try:
            return tool_func.__pydantic_fields__.get("description", None).default
        except (AttributeError, TypeError):
            return None

    @classmethod
    def _process_docstring_metadata(
        cls, tool_name: str, docstring: str | None, pattern
    ) -> None:
        if not docstring:
            return

        match: re.Match[str] | None = pattern.search(docstring)
        if match:
            tag, policy = match.group(1), match.group(2)
            cls.tool_tags[tool_name] = tag
            cls.tool_policies[tool_name] = policy
            logger.info(f"Tool {tool_name}: Tag={tag}, Policy={policy}")

    def _create_builtin_tool_instance(self, name: str, state: GlobalState) -> BaseTool:
        tool_creators = {
            "planning": lambda: self._create_planning_tool(state),
        }

        if name in tool_creators:
            return tool_creators[name]()
        return self.builtin_tools[name]

    def _create_planning_tool(self, state: GlobalState) -> PlanningTool:
        return (
            PlanningTool(plan_manager=state.plan_manager[state.name])
            if state
            else PlanningTool(plan_manager=PlanManager())
        )

    async def get_console_tools(self, console_tools_name: list[str]) -> list[BaseTool]:
        """Get console tools from the tool manager. Given console tools name, return the tools."""
        if not console_tools_name:
            return []

        tools: list[BaseTool] = []
        for name in console_tools_name:
            if "aws" in name:
                description = "AWS CLI to execute the AWS command."
            elif "gcp" in name:
                description = "GCP CLI to execute the GCP command."
            elif "azure" in name:
                description = "Azure CLI to execute the Azure command."
            elif "k8s" in name:
                description = "K8S CLI to execute the Kubernetes command."
            else:
                raise ValueError(f"Console tool {name} not found")

            description = f"""
{description}

Tips:
- Consolidate multiple commands into single bash script when possible
- Read-only permissions only
- For AWS/GCP/Azure CLI, use output/format text for consistent parsing"""

            async def call_console_tool(
                config: RunnableConfig,
                **arguments: dict[str, Any],
            ) -> tuple[str, None]:
                try:
                    call_tool_result = await call_executor(
                        script=str(arguments.get("script", "")), config=config
                    )
                except Exception as e:
                    return str(e), None
                return json.dumps(call_tool_result), None

            class ConsoleToolArgs(BaseModel):
                script: str

            tool = StructuredTool(
                name=name,
                description=description,
                args_schema=ConsoleToolArgs,
                coroutine=call_console_tool,
                response_format="content_and_artifact",
            )
            tools.append(tool)
        return tools

    async def get_builtin_tools(
        self, builtin_tools_name: list[str], state: GlobalState
    ) -> list[BaseTool]:
        """Get builtin tools from the tool manager."""
        if not builtin_tools_name:
            return []

        tools: list[BaseTool] = []
        for name in builtin_tools_name:
            if name not in self.builtin_tools:
                continue
            tool: BaseTool = self._create_builtin_tool_instance(name, state)
            tools.append(tool)
        return tools

    async def get_mcp_tools(self, mcp_connections_name: list[str]) -> list[BaseTool]:
        if not mcp_connections_name or not self.mcp_client:
            return []

        tools: list[BaseTool] = await self.mcp_client.list_all_tools_by_server_names(
            mcp_connections_name
        )
        return tools

    async def get_connection_tools(self, connections_name: list[str]) -> list[BaseTool]:
        if not connections_name or not self.connections_tools:
            return []

        tools: list[BaseTool] = []
        for name in connections_name:
            if name not in self.connections_tools:
                continue
            tools.extend(self.connections_tools.get(name, []))
        return tools

    async def get_tools(
        self,
        agent_config: AgentConfig,
        state: GlobalState,
    ) -> list[BaseTool]:
        """Get tools from the tool manager."""

        builtin_tools_name: list[str] = agent_config.tool_config.builtin
        mcp_connections_name: list[str] = agent_config.mcp_connections or []
        builtin_connections_name: list[str] = agent_config.connections or []

        builtin_tools: list[BaseTool] = await self.get_builtin_tools(
            builtin_tools_name, state
        )
        mcp_tools: list[BaseTool] = await self.get_mcp_tools(mcp_connections_name)
        connection_tools: list[BaseTool] = await self.get_connection_tools(
            builtin_connections_name
        )
        return builtin_tools + mcp_tools + connection_tools
