"""Utility & helper functions."""

import os

from langchain.chat_models import init_chat_model
from langchain_core.language_models import BaseChatModel
from langchain_core.messages import AIMessage

from app.core.config import settings

# Anthropic beta features for enhanced model capabilities
# These are passed as anthropic_beta parameter (array of strings) for AWS Bedrock
ANTHROPIC_BETA_FEATURES = [
    "token-efficient-tools-2025-02-19",  # More efficient token usage for tool calls
    "interleaved-thinking-2025-05-14",  # Interleaved thinking for Claude 4 models
    # Note: Only using validated AWS Bedrock beta features
    # Prompt caching is enabled by default and doesn't require a beta flag
]


def load_chat_model(
    fully_specified_name: str, metadata: dict | None = None
) -> BaseChatModel:
    """Load a chat model from a fully specified name.

    Args:
        fully_specified_name (str): String in the format 'provider/model'.
    """
    provider, model = fully_specified_name.split("/", maxsplit=1)
    if provider == "openai":
        return init_chat_model(
            model,
            model_provider=provider,
            model_kwargs={"max_tokens": 8096},
            stream_usage=True,
        )
    elif provider == "bedrock":
        from botocore.config import Config
        from langchain_aws import ChatBedrockConverse

        return ChatBedrockConverse(
            model=model,
            max_tokens=8096,
            temperature=0,
            config=Config(
                read_timeout=1000,
            ),
            additional_model_request_fields={"anthropic_beta": ANTHROPIC_BETA_FEATURES},
        )
    elif provider == "bedrock_converse":
        return init_chat_model(
            model,
            model_provider=provider,
            temperature=0,
            max_tokens=8096,
            region_name=os.getenv("BEDROCK_REGION_NAME"),
            stream_usage=True,
            additional_model_request_fields={"anthropic_beta": ANTHROPIC_BETA_FEATURES},
        )
    elif provider == "anthropic":
        return init_chat_model(
            model,
            model_provider=provider,
            temperature=0,
            max_tokens=8096,
            stream_usage=True,
        )
    elif provider == "litellm":
        return init_chat_model(
            model=model,
            # Since we are using litellm to wrap the base model,
            # we need to set the model provider to openai.
            model_provider="openai",
            api_key="anything",
            streaming=True,
            base_url=settings.LITELLM_BASE_URL,
            extra_body={
                "metadata": metadata,
                # "stream_options": {"include_usage": True},
            },
            stream_usage=True,
        )

    else:
        raise ValueError(f"Unsupported provider: {provider}")


def has_tool_calls(message) -> bool:
    """Check if a message has valid tool calls.

    Args:
        message: The message to check

    Returns:
        bool: True if the message has valid tool calls, False otherwise
    """
    return (
        hasattr(message, "tool_calls")
        and isinstance(message.tool_calls, list)
        and len(message.tool_calls) > 0
    )


def is_valid_tool_calls_message(message) -> bool:
    """Check if a message is a valid AIMessage with tool calls.

    Args:
        message: The message to check

    Returns:
        bool: True if the message is a valid AIMessage with tool calls, False otherwise
    """
    return (
        isinstance(message, AIMessage)
        and hasattr(message, "tool_calls")
        and bool(message.tool_calls)  # Convert to bool to handle any truthy/falsy value
    )
