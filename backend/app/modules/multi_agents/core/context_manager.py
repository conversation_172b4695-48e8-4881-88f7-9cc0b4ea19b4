from typing import cast

from langchain_core.messages import (
    AIMessage,
    AnyMessage,
    HumanMessage,
    SystemMessage,
    ToolMessage,
)

from app.logger import logger
from app.modules.multi_agents.core.states.base import StateBase
from app.modules.multi_agents.core.utils import has_tool_calls


class ContextManager:
    """Main context manager for conversational agents."""

    def build_system_message(self, system_prompt: str) -> SystemMessage:
        """Build 1-hour cached system message."""
        payload = [
            {
                "type": "text",
                "text": system_prompt,
                "cache_control": {"type": "ephemeral", "ttl": "1h"},
            }
        ]
        return SystemMessage(content=payload)  # type: ignore

    def build_objectives_message(
        self, objectives_prompt: str, attachment_content: list | None = None
    ) -> HumanMessage:
        payload = [
            {
                "type": "text",
                "text": objectives_prompt,
                "cache_control": {"type": "ephemeral", "ttl": "5m"},
            }
        ]
        if attachment_content:
            payload.extend(attachment_content)

        return HumanMessage(content=payload)  # type: ignore

    def _fix_tool_call_messages(self, instance_state: StateBase) -> list[AnyMessage]:
        """Fix tool call messages by inserting missing tool messages for assistant responses."""
        # Get latest assistant message
        latest_assistant_message, latest_assistant_message_index = None, None
        for index, message in enumerate(reversed(instance_state.messages)):
            if isinstance(message, AIMessage):
                latest_assistant_message = message
                latest_assistant_message_index = (
                    len(instance_state.messages) - index - 1
                )
                break

        if latest_assistant_message:
            # Check if the latest assistant message has tool calls and if not have tool message append tool messages to base message
            has_tool_calls_response = has_tool_calls(latest_assistant_message)

            if has_tool_calls_response and latest_assistant_message_index is not None:
                # Check if there's a next message and if it's a ToolMessage
                next_message_index = latest_assistant_message_index + 1
                if next_message_index >= len(instance_state.messages) or not isinstance(
                    instance_state.messages[next_message_index], ToolMessage
                ):
                    # Insert a tool message with empty content and name
                    insert_tool_message = ToolMessage(
                        content="Unexpected error occurred",
                        tool_call_id=latest_assistant_message.tool_calls[0]["id"],
                        name=latest_assistant_message.tool_calls[0]["name"],
                    )
                    instance_state.messages.insert(
                        next_message_index, insert_tool_message
                    )
        return instance_state.messages

    def build_message_hierarchy(
        self,
        instance_state,
        system_prompt: str,
        objectives_prompt: str,
        attachment_content: list | None = None,
    ) -> tuple[list[AnyMessage], list[AnyMessage]]:
        """
        Build message hierarchy for the agent.

        Returns:
            tuple: (full_message_hierarchy, updated_instance_messages)
                - full_message_hierarchy: Complete message list with system and objectives
                - updated_instance_messages: Updated instance state messages for clarity
        """
        try:
            # First, fix any tool call message issues when user interrupt the agent when calling tool
            instance_state.messages = self._fix_tool_call_messages(instance_state)

            # Then, build the message hierarchy
            system_message = self.build_system_message(system_prompt)
            objectives_message = self.build_objectives_message(
                objectives_prompt, attachment_content
            )

            context = list(instance_state.messages)
            if instance_state.summary:
                # Clear existing context and start fresh with summary
                payload = [
                    {
                        "type": "text",
                        "text": f"Here is everything happened so far.\n{instance_state.summary}",
                        "cache_control": {"type": "ephemeral", "ttl": "5m"},
                    }
                ]
                context = [HumanMessage(content=payload)]  # type: ignore
                instance_state.messages = context

            messages = [
                system_message,
                *context,  # Flatten the context list instead of nesting it
                # NOTE: Objectives message is the last message in the hierarchy
                objectives_message,
            ]

            # Return both the complete hierarchy and the updated instance messages for clarity
            return cast(list[AnyMessage], messages), cast(
                list[AnyMessage], instance_state.messages
            )
        except Exception as e:
            logger.exception(
                f"Error building message hierarchy during agent switching. Instance state: {getattr(instance_state, 'name', 'unknown')}, \
                    Message count: {len(getattr(instance_state, 'messages', []))}, \
                    Error: {e}"
            )
            raise e
