import random
import re

from langchain_core.runnables import RunnableConfig
from langgraph.graph import END, StateGraph
from langgraph.types import Command

from app.core.config import settings
from app.modules.multi_agents.agents.base import BaseAgent
from app.modules.multi_agents.agents.conversation_agent import ConversationalAgent
from app.modules.multi_agents.common import (
    BaseAgentToolNode,
    BaseConversationalAgentNode,
)
from app.modules.multi_agents.core import (
    Configuration,
    GlobalState,
    InputState,
)
from app.modules.multi_agents.core.prompt_manager import prompt_manager
from app.modules.multi_agents.core.utils import load_chat_model
from app.modules.multi_agents.tools import RolePlayingOutput


class CoordinatorAgent(BaseAgent, BaseAgentToolNode, BaseConversationalAgentNode):
    """Coordinator agent."""

    # Declare attribute for linters; actual graph is initialized in build_graph
    graph: StateGraph

    def extract_mentioned_agent(
        self, message: str, available_agents: list[str]
    ) -> str | None:
        """Extract explicitly mentioned agent from user message.

        Args:
            message: The user message to parse
            available_agents: List of available/active agent names

        Returns:
            Agent name if found and valid, None otherwise
        """
        if not message or not available_agents:
            return None

        # Find all @mentions in the message using regex
        mentions = re.findall(r"@(\w+)", message)

        # Check if any mention matches available agents (case-insensitive)
        for mention in mentions:
            for agent in available_agents:
                if mention.lower() == agent.lower():
                    return agent

        return None

    async def set_up_state(self, state: GlobalState, config: RunnableConfig):
        """Set up initial state for the networking agent."""
        configuration = Configuration.from_runnable_config(config)

        # Initialize state if needed
        if not state.init_state:
            state = GlobalState.from_config(configuration.agents_config, state.messages)
            state.init_state = True
            state.target_group_chat_member = None
            state.name = None

        # Initialize group chat
        if state.messages:
            state.group_chat = state.group_chat.add_message(state.messages, "Customer")
        return Command(goto="coordinator_agent", update=state)


    async def handle_role_playing(
        self,
        state: GlobalState,
        config: RunnableConfig,
    ) -> str:
        """Handle role playing by selecting the next agent to respond."""
        role_playing_prompt = (
            await prompt_manager.get_coordination_agent_role_playing_prompt(
                state, config
            )
        )
        messages = [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": role_playing_prompt,
                        "cache_control": {"type": "ephemeral"},
                    }
                ],
            }
        ]

        model = load_chat_model(settings.NETWORKING_MODEL).with_structured_output(
            RolePlayingOutput, strict=True
        )
        config["run_name"] = "Coordinator"
        response: RolePlayingOutput = await model.ainvoke(messages, config)
        role = response.role.strip()
        if role in state.instance_states:
            state.name = role
            return role
        else:
            state.name = random.choice(list(state.instance_states.keys()))
            return state.name

    async def coordinator_agent(self, state: GlobalState, config: RunnableConfig):
        """Coordinating agent (refactored for clarity and maintainability)."""
        configuration = Configuration.from_runnable_config(config)
        active_agents = configuration.agents_config.active_agents

        # Handle direct target group chat member
        if state.target_group_chat_member:
            if state.target_group_chat_member == "Customer":
                state.target_group_chat_member = None
                state.name = None
                return Command(goto=END, update=state)

            # Target is another agent
            state.name = state.target_group_chat_member
            state.target_group_chat_member = None
            return Command(goto="conversational_agent", update=state)

        # NEW: Check for explicit agent mention in user message (bypass coordinator)
        if configuration.user_prompt:
            mentioned_agent = self.extract_mentioned_agent(
                configuration.user_prompt, active_agents
            )
            if mentioned_agent:
                state.name = mentioned_agent
                return Command(goto="conversational_agent", update=state)

        # Decide next agent to handle the message
        if len(active_agents) > 1:
            state.name = await self.handle_role_playing(state, config)
        else:
            state.name = active_agents[0]

        return Command(goto="conversational_agent", update=state)

    def build_graph(self) -> None:
        """Build the graph."""
        # TODO: Migrate to context_schema
        self.graph = StateGraph(
            GlobalState, input=InputState, config_schema=Configuration
        )

        # Add nodes
        self.graph.add_node("set_up_state", self.set_up_state)
        self.graph.add_node("coordinator_agent", self.coordinator_agent)
        self.graph.add_node("conversational_agent", ConversationalAgent().compile())

        # Add edges
        self.graph.add_edge("conversational_agent", "coordinator_agent")
        self.graph.add_edge("__start__", "set_up_state")

    def get_graph(self) -> StateGraph:
        """Get the graph."""
        return self.graph

    def compile(self, **kwargs):
        """Compile the graph."""
        return self.graph.compile(**kwargs)
