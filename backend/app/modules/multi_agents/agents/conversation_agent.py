from langchain_core.messages import AIMessage, HumanMessage
from langchain_core.runnables import RunnableConfig
from langgraph.graph import StateGraph
from langgraph.types import Command, StreamWriter

from app.modules.multi_agents.agents.base import BaseAgent
from app.modules.multi_agents.common import (
    BaseAgentToolNode,
    BaseConversationalAgentNode,
)
from app.modules.multi_agents.core import Configuration, GlobalState
from app.modules.multi_agents.core.context_manager import ContextManager
from app.modules.multi_agents.core.prompt_manager import prompt_manager
from app.modules.multi_agents.core.utils import has_tool_calls, load_chat_model


class ConversationalAgent(BaseAgent, BaseAgentToolNode, BaseConversationalAgentNode):
    """Graph builder for the conversational agent system."""

    # Declare attribute for linters; actual graph is initialized in build_graph
    graph: StateGraph

    def __init__(self):
        self.context_manager = ContextManager()
        self.build_graph()

    # ===== Helper methods to keep reasoning_agent concise =====
    async def _prepare_model_with_tools(
        self,
        configuration: Configuration,
        agent_config,
        state: GlobalState,
        config: RunnableConfig,
    ):
        tool_manager = configuration.tool_manager
        tools = await tool_manager.get_tools(agent_config=agent_config, state=state)
        model = load_chat_model(
            configuration.model_name, metadata=config.get("metadata", {})
        )
        model = model.bind_tools(
            tools,
            # strict=True,
            # parallel_tool_calls=True,
        )
        return model, tools

    def _handle_model_response(self, state: GlobalState, response: AIMessage):
        # Check for tool calls
        has_tool_calls_response = has_tool_calls(response)

        if has_tool_calls_response:
            # Keep only the first tool call if multiple exist
            response.tool_calls = [response.tool_calls[0]]

        # Update messages
        state.instance_states[state.name].messages.append(response)

        if not has_tool_calls_response:
            # Should end with a group chat tool call
            rem = list(state.instance_states[state.name].messages)
            rem.append(HumanMessage(content="PLEASE END WITH A GROUP CHAT TOOL CALL"))
            state.instance_states[state.name].messages = rem
            return Command(goto="reasoning_agent", update=state)

        # Route based on tool name
        if any(
            tool_call["name"] == "TaskBase" or tool_call["name"] == "create_chart"
            for tool_call in response.tool_calls
        ):
            return Command(goto="structured_tools", update=state)

        # All other tools including recommendations go through normal tool flow
        return Command(goto="check_permissions", update=state)

    async def reasoning_agent(
        self,
        state: GlobalState,
        config: RunnableConfig,
        writer: StreamWriter,
    ) -> dict[str, list[AIMessage]]:
        """Call the LLM powering our agent."""

        writer({"type": "on_agent_start", "content": state.name})

        # Get the instance state and configuration
        instance_state = state.instance_states[state.name]
        configuration = Configuration.from_runnable_config(config)
        agent_config = configuration.agents_config.agents[state.name]

        # Prepare model and tools
        model, _ = await self._prepare_model_with_tools(
            configuration, agent_config, state, config
        )

        system_prompt = await prompt_manager.get_conversation_agent_system_prompt(
            agent_config=agent_config, state=state, config=config
        )

        objectives_prompt = await prompt_manager.get_objectives_prompt(
            agent_config=agent_config, state=state, config=config
        )

        # Build optimized cache hierarchy using ContextManager
        messages, instance_state.messages = (
            self.context_manager.build_message_hierarchy(
                instance_state,
                system_prompt,
                objectives_prompt,
                configuration.attachment_content,
            )
        )

        config["run_name"] = f"{state.name.capitalize()}"
        config["metadata"] = {
            "observation_type": "agent",
        }
        # NOTE: @duc.bui found a weird bug:
        # When uncomment the callback, the response will not contain the detailed usage metadata
        # (e.g. input_tokens, output_tokens, etc.).
        # TODO: @duc.bui find the root cause and fix it
        # config["callbacks"] = []
        response: AIMessage = await model.ainvoke(
            messages,
            config,
        )

        return self._handle_model_response(state, response)

    def build_graph(self) -> None:
        """Build the agent system graph structure."""
        self.graph = StateGraph(GlobalState)
        self.graph.add_node("reasoning_agent", self.reasoning_agent)
        self.graph.add_node("tools", self.tool_node)
        self.graph.add_node("check_permissions", self.check_permissions)
        self.graph.add_node("summarize", self.summarize_conversation)
        self.graph.add_node("structured_tools", self.structured_tools)

        self.graph.add_edge("__start__", "summarize")
        self.graph.add_edge("summarize", "reasoning_agent")
        self.graph.add_edge("structured_tools", "reasoning_agent")

    def get_graph(self) -> StateGraph:
        """Return the final graph."""
        return self.graph

    def compile(self, **kwargs):
        return self.graph.compile(**kwargs)
