from enum import Enum
from typing import Literal

from pydantic import BaseModel, Field


class ChartType(str, Enum):
    BAR = "bar"
    PIE = "pie"
    LINE = "line"
    AREA = "area"
    STEP_AREA = "step_area"
    RADAR = "radar"
    SANKEY = "sankey"


class ChartDataset(BaseModel):
    data: list[float]
    label: str | None = None


class SankeyNode(BaseModel):
    id: str
    label: str
    color: str | None = None


class SankeyLink(BaseModel):
    source: str
    target: str
    value: float
    label: str | None = None


class ChartAxis(BaseModel):
    title: str | None = None
    type: str = "category"


class ChartStructuredOutput(BaseModel):
    """This schema is the same as the #chart tool but without description, \
        when create this schema, please ensure the correct structure output.
    """

    title: str
    description: str | None
    chart_type: ChartType
    categories: list[str]
    datasets: list[ChartDataset]
    sankey_nodes: list[SankeyNode] | None = None
    sankey_links: list[SankeyLink] | None = None
    x_axis: ChartAxis
    y_axis: ChartAxis
    show_legend: bool
    show_grid: bool
    currency_format: bool
    percentage_format: bool
    position: int | None = 0


class Trend(BaseModel):
    direction: Literal["up", "down", "neutral"]
    value: str = Field(description="The text value of the trend, e.g., '23%'.")
    description: str | None = Field(
        default=None, description="Context for the trend, e.g., 'vs last year'."
    )


class KPICard(BaseModel):
    """
    A key performance indicator card. Only use this schema when you want to display \
        important metrics or KPIs. You should use at least 2 cards.
    """

    title: str = Field(description="The title of the card.")
    value: str = Field(description="The main data point or value of the card.")
    description: str | None = Field(default=None)
    trend: Trend | None = Field(default=None)
    icon: str | None = Field(
        default=None, description="Name of a Lucide icon to display."
    )
    alert: bool = Field(default=False, description="Flag to indicate an alert state.")
