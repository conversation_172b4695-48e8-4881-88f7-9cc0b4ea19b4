from typing import Annotated, cast

from langchain_core.runnables.base import RunnableConfig
from langchain_core.tools import InjectedToolArg, tool
from openai import APIError, AsyncOpenAI
from openai.types.chat import ChatCompletionMessageParam

from app.core.config import settings
from app.logger import logger

SYSTEM_PROMPT = """You are an AI assistant specialized in providing concise answers based on real-time web searches.
Answer the user's query directly using the most relevant and up-to-date information found online.
Avoid introductory phrases or explanations about your process.
Format lists naturally if needed for the answer."""

# Pre-construct the system message
SYSTEM_MESSAGE = {
    "role": "system",
    "content": SYSTEM_PROMPT,
}


@tool
async def search_internet(
    query: str,
    *,
    config: Annotated[RunnableConfig, InjectedToolArg],
) -> str:
    """
    ###### TOOL USE POLICY #####
    Tool Tag: #search
    Tool Use Policy: #manual
    ##########################

    This tool performs web searches to retrieve current information from the internet, allowing access to:
        - Recent events and news
        - Up-to-date facts and information
        - Public data and statistics
        - Published content and reference materials
        - General knowledge beyond the agent's internal resources

     Args:
        query: The search query to submit to internet search engines
    Returns:
        Relevant search results from internet sources

    """
    try:
        client = AsyncOpenAI(
            api_key=settings.PERPLEXITY_API_KEY,
            base_url="https://api.perplexity.ai",
            timeout=60.0,
            max_retries=3,
        )

        messages = cast(
            list[ChatCompletionMessageParam],
            [
                SYSTEM_MESSAGE,
                {
                    "role": "user",
                    "content": query,
                },
            ],
        )
        response = await client.chat.completions.create(
            model=settings.PERPLEXITY_MODEL,
            messages=messages,
            temperature=0.2,
            max_completion_tokens=1024,
        )

        # Basic validation of response structure
        if (
            response.choices
            and response.choices[0].message
            and response.choices[0].message.content
        ):
            answer = response.choices[0].message.content.strip()
            if answer:
                logger.info("Perplexity search successful.")
                return answer
            else:
                logger.warning("Perplexity search returned an empty answer.")
                return "Perplexity returned an empty response."
        else:
            logger.warning(
                f"Received unexpected response structure from Perplexity: {response}"
            )
            return "Error: Received an unexpected response structure from Perplexity."

    except APIError:
        logger.exception("Perplexity API error during search.")
        return "Error: Perplexity API request failed. Please check API key, usage limits, or try again later."
    except TimeoutError:
        logger.error(
            f"Perplexity API request timed out for query: '{query}'",
            exc_info=True,
        )
        return "Error: The request to Perplexity timed out. Please try again later."
    except Exception as e:
        logger.exception(
            f"An unexpected error occurred during Perplexity search for query: '{query}'"
        )
        return f"Error: An unexpected error occurred during the search: {str(e)}"
