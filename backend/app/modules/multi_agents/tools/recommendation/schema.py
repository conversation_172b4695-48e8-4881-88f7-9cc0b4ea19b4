from datetime import datetime
from typing import Literal

from pydantic import BaseModel, Field

from app.models import RecommendationStatus


class Recommendation(BaseModel):
    """Individual recommendation model with detailed fields for cost optimization."""

    type: str = Field(
        default="",
        description=(
            "It categorizes the recommendation into specific areas, which helps in organizing and prioritizing recommendations. "
        ),
    )
    title: str = Field(
        default="",
        description=(
            "A concise and descriptive title for the recommendation. This title should provide a quick "
            "overview of the recommendation's focus, allowing stakeholders to quickly understand its essence."
            "explicity mention the resource type in the title such as 'Consider downsizing t3.xlarge to t3.large'"
            "Dont mention the resource name in the title"
            "This is bad: Consider downsizing t2.medium to t2.small for Sample-EC2-i-1de20d89952e48958"
            "This is good: Consider downsizing t2.medium to t2.small"
        ),
    )
    description: str = Field(
        default="",
        description=(
            "A short explanation of the recommendation, including the context, underlying issues or opportunities, "
            "and the anticipated benefits. This description should provide enough information for decision-makers and "
            "technical teams to understand the impact and scope of the recommendation."
            "Keep the description short and concise, at most 1 sentence."
        ),
    )
    effort: str = Field(
        default="",
        description=(
            "A qualitative assessment of the effort required to implement the recommendation. This should be expressed in "
            "terms such as low, medium, or high, and may take into account factors like technical complexity, required resources, "
            "and potential downtime or disruption."
            "If you cannot estimate the effort, return 'unknown'"
            "The value should be one of the following: low, medium, high"
        ),
    )
    risk: str = Field(
        default="",
        description=(
            "A qualitative evaluation of the risk involved in implementing the recommendation. This field should outline potential "
            "challenges, dependencies, or known issues that could affect the implementation, helping teams assess the risk-reward balance."
            "The value should be one of the following: low, medium, high"
        ),
    )
    potential_savings: float | None = Field(
        default=None,
        description=(
            "The exact MONTHLY cost savings in dollars (USD) if the recommendation is implemented. This field must be "
            "calculated based on precise resource costs and usage patterns. Only provide a value if you can calculate "
            "the exact dollar amount. This helps in accurately comparing and prioritizing recommendations based on their financial impact. "
            "If you cannot calculate the exact monthly savings amount, return None"
        ),
    )
    status: RecommendationStatus = Field(
        default=RecommendationStatus.PENDING,
        description=(
            "You don't need to set this field, it is set automatically by the system."
        ),
    )


class Recommendations(BaseModel):
    """
    Base model representing a list of recommendations. That is best practices way to advise user based on tasks like review, assess, analytics, etc.
    Only use this tool if you are sure that the recommendation is a best practice and actually actionable.
    """

    recommendations: list[Recommendation]


class RecommendationInput(BaseModel):
    """
    A tool for managing recommendations.

    ###### TOOL USE POLICY #####
    Tool Tag: #recommendation
    Tool Use Policy: #manual
    ##########################

    Commands:
    1. get_all:
       Required: None
       Example: Get all recommendations for the current resource
       NOTE: PLEASE ALWAYS USE THIS COMMAND FIRST TO GET THE RECOMMENDATIONS BEFORE DOING ANYTHING ELSE

    2. delete: Delete a recommendation
       Required: recommendation_id
       Example: Delete recommendation with id 123e4567-e89b-12d3-a456-426614174000
       NOTE: PLEASE ALWAYS USE THIS COMMAND ONLY IF YOU KNOW THE RECOMMENDATION ID

    3. create: Create a recommendation
       Required: title, description, effort, risk, potential_savings
       Example: Create a recommendation with title "Consider downsizing t2.medium to t2.small"

    4. get_instructions:
       Required: None
       NOTE: Returns usage instructions for the recommendation tool.
    """

    command: Literal["get_all", "delete", "create", "get_instructions"] = Field(
        description="Command to execute: get_all, delete, create, get_instructions"
    )
    recommendation_ids: list[str] | None = Field(
        default=None,
        description="Recommendation identifiers. Required for: delete. Example: ['123e4567-e89b-12d3-a456-426614174000']",
    )
    new_recommendations: Recommendations | None = Field(
        default=None,
        description="New recommendations to create. Required for: create",
    )

    class Config:
        """Pydantic model configuration."""

        extra = "ignore"
        validate_assignment = True
        arbitrary_types_allowed = True
        use_enum_values = True
        validate_default = True
        populate_by_name = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
        }
