"""Predefined instructions for the recommendation tool.

These instructions provide guidance on creating actionable, well-structured
cost optimization recommendations with proper categorization and impact assessment.
"""

RECOMMENDATION_TOOL_INSTRUCTIONS = """
You are using the Recommendation Tool (#recommendation).

Purpose:
- Create and manage cost optimization recommendations for cloud resources.
- Provide actionable insights based on resource analysis and best practices.

Available commands:
1) get_all: Retrieve all existing recommendations for the current resource.
2) delete: Remove specific recommendations by their IDs.
3) create: Generate new recommendations based on analysis findings.
4) get_instructions: Return these usage instructions.

Recommendation creation guidelines (must follow):

1) Quality Standards:
   - Only create recommendations that are actionable and based on concrete evidence.
   - Ensure each recommendation addresses a specific optimization opportunity.
   - Provide clear, measurable benefits when possible.

2) Title Requirements:
   - Be concise and descriptive (e.g., "Consider downsizing t3.xlarge to t3.large").
   - Explicitly mention resource types, not specific resource names.
   - Focus on the action and target state.

3) Description Guidelines:
   - Keep descriptions to 1-2 sentences maximum.
   - Include context, issue/opportunity, and expected benefits.
   - Avoid technical jargon; make it stakeholder-friendly.

4) Effort Assessment:
   - Use qualitative terms: "low", "medium", "high", or "unknown".
   - Consider technical complexity, required resources, and potential downtime.
   - Factor in dependencies and coordination requirements.

5) Risk Evaluation:
   - Assess implementation risks: "low", "medium", or "high".
   - Consider potential service disruption, rollback complexity, and business impact.
   - Account for dependencies and known issues.

6) Financial Impact:
   - Provide exact monthly savings in USD when calculable.
   - Only include values you can substantiate with data.
   - Set to None if exact calculations aren't possible.

7) Categorization:
   - Use clear categories like "rightsizing", "storage optimization", "reserved instances".
   - Help stakeholders organize and prioritize recommendations.

Best Practices:
- Always retrieve existing recommendations first using get_all.
- Avoid duplicate recommendations for the same optimization.
- Prioritize recommendations with high impact and low risk.
- Consider implementation order and dependencies.

Output constraints:
- All recommendations must be actionable and specific.
- Financial estimates must be based on actual resource costs and usage.
- Risk and effort assessments should be realistic and well-reasoned.
""".strip()
