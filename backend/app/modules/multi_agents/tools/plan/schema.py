import ast
import json
from datetime import datetime
from typing import Any, Literal

from pydantic import BaseModel, Field, field_validator


class Task(BaseModel):
    """A single task in a plan."""

    id: int = Field(
        ...,
        description="Unique identifier for the task. MUST be sequential (1, 2, 3, ...) to enforce execution order.",
    )
    content: str = Field(..., description="Brief description of the task.")
    status: Literal["pending", "in_progress", "completed", "blocked"] = Field(
        default="pending", description="Status of the task. Default: pending"
    )
    priority: Literal["low", "medium", "high"] = Field(
        default="medium", description="Priority of the task. Default: medium"
    )
    notes: str | None = Field(None, description="Notes for the task. Optional.")


class PlanInput(BaseModel):
    tasks: list[Task] = Field(description="Sequential tasks to complete.")

    # Accept both proper lists and stringified JSON/Python lists for tasks
    @field_validator("tasks", mode="before")
    @classmethod
    def parse_tasks_value(cls, value: Any) -> Any:
        # Already a list (of dicts or Task), let Pydantic handle nested parsing
        if isinstance(value, list):
            return value
        # Received a string; try JSO<PERSON> first, then safe Python literal eval
        if isinstance(value, str):
            value_str = value.strip()
            try:
                parsed = json.loads(value_str)
                if not isinstance(parsed, list):
                    raise ValueError("'tasks' JSON must decode to a list")
                return parsed
            except json.JSONDecodeError:
                try:
                    parsed = ast.literal_eval(value_str)
                except Exception as exc:  # noqa: BLE001 - surface clear error downstream
                    raise ValueError(
                        "Invalid 'tasks' format. Provide a list or a JSON stringified list."
                    ) from exc
                if not isinstance(parsed, list):
                    raise ValueError("'tasks' string must represent a list")
                return parsed
        # Anything else is invalid
        raise ValueError("'tasks' must be a list or a JSON string representing a list")

    class Config:
        """Pydantic model configuration."""

        extra = "ignore"
        validate_assignment = True
        arbitrary_types_allowed = True
        use_enum_values = True
        validate_default = True
        populate_by_name = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
        }
