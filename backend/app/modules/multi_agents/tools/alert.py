from typing import Annotated, Any, Literal

from langchain_core.runnables.base import RunnableConfig
from langchain_core.tools import InjectedToolArg, tool

from app.models import AlertSeverity
from app.tasks.alert_task import create_alert

# Define valid severity levels based on AlertSeverity enum
SeverityLevel = Literal["critical", "high", "medium", "low", "info"]

# Mapping from tool severity levels to AlertSeverity enum
SEVERITY_MAP = {
    "critical": AlertSeverity.CRITICAL,
    "high": AlertSeverity.HIGH,
    "medium": AlertSeverity.MEDIUM,
    "low": AlertSeverity.LOW,
    "info": AlertSeverity.INFO,
}


@tool
def push_alert(
    title: str,
    description: str,
    level: SeverityLevel = "info",
    *,
    config: Annotated[RunnableConfig, InjectedToolArg],
    reasoning: str,
) -> dict[str, Any]:
    """
    ###### TOOL USE POLICY #####
    Tool Tag: #alert
    Tool Use Policy: #manual
    ##########################

    The Push Alert Notification Tool enables AI agents to send high-priority, contextual alerts to users based on important events, errors, or situations requiring immediate human attention.

    Args:
        title: Title of the alert
        description: Clear description of what happened
        level: Alert severity level (critical/high/medium/low/info)
    """
    workspace_id = config.get("configurable", {}).get("workspace_id", None)
    user_id = config.get("configurable", {}).get("user_id", None)

    # Map severity level
    severity = SEVERITY_MAP[level]

    # Submit alert creation task
    create_alert.delay(
        # Create brief title from description
        title=title,
        description=description,
        severity=severity,
        workspace_id=workspace_id,
        source="ai_agent",
        user_id=user_id,
        tags=["ai_generated", level],
    )

    alert_data = {"status": "submitted"}

    return alert_data
