from typing import Annotated

from crawl4ai import AsyncWebCrawler, BrowserConfig
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import InjectedToolArg, tool

from app.logger import logger


def _extract_markdown(obj: object) -> str | None:
    md = getattr(obj, "markdown", None)
    if md is None:
        return None
    text = getattr(md, "fit_markdown", None) or getattr(md, "raw_markdown", None)
    if isinstance(text, str):
        return text
    if isinstance(md, str):
        return md
    return None


@tool
async def fetch_url(
    urls: list[str],
    *,
    config: Annotated[RunnableConfig, InjectedToolArg],
) -> str:
    """
    Crawl a list of URLs concurrently and return their markdown content with basic metadata.
    Only call this tool when the URL is in the user prompt.

    Args:
        urls: List of URLs to fetch

    Returns:
        A list of result objects: [{"url", "title", "description", "markdown"}]
    """

    if not urls:
        return ""

    # Basic headless chromium config similar to KB reader
    browser_config = BrowserConfig(
        headless=True,
        browser_type="chromium",
        channel="chrome",
        viewport_width=1920,
        viewport_height=1080,
        user_agent=(
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) "
            "AppleWebKit/537.36 (KHTML, like Gecko) "
            "Chrome/********* Safari/537.36"
        ),
    )

    crawler = AsyncWebCrawler(config=browser_config)
    await crawler.start()
    try:
        results = await crawler.arun_many(urls=urls)

        output: list[str] = []

        def handle_sync_iter(iterable) -> None:
            for r in iterable:
                if not r:
                    continue
                try:
                    md = _extract_markdown(r) or ""
                    # Access CrawlResult object attributes instead of dictionary keys
                    url = getattr(r, "url", "Unknown URL")
                    title = getattr(r, "title", "No Title")
                    description = getattr(r, "description", "No Description")
                    content = f"{url}\n{title}\n{description}\n{md}"
                    output.append(content)
                except Exception:
                    logger.exception("Error processing crawl result")
                    continue

        handle_sync_iter(results)

        return "\n---\n".join(output)
    except Exception as e:
        logger.exception(f"fetch_url failed: {e}")
        return f"Error: {str(e)}"
    finally:
        await crawler.close()
    return "Internal error"  # Ensure explicit return in all code paths
