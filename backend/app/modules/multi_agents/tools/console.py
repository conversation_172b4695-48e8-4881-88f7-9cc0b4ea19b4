from typing import Annotated, Any

from langchain_core.runnables import RunnableConfig
from langchain_core.tools import InjectedToolArg, tool

from .executor import call_executor


@tool
async def use_console_read_only_permissions(
    script: str, *, config: Annotated[RunnableConfig, InjectedToolArg], reasoning: str
) -> dict[str, Any]:
    """CLI Script Execution Tool with read-only permissions.
    - OS: ubuntu:22.04

    Tips:
    - Consolidate multiple commands into single bash script when possible
    - Read-only permissions only
    - For AWS/GCP/Azure CLI, use output/format text for consistent parsing

    Best practices for clean terminal output:
    - Use echo statements to explain what you're doing
    - Add comments with # for clarity
    - Combine related commands into single script
    - Use proper error handling with || for fallbacks

    Supported:
    - Cloud: AWS, GCP, Azure CLIs (aws-cli, gcloud, az)
    - Version Control: git
    - Data Processing: jq, yq, xmlstarlet, csvkit
    - Text Processing: grep, sed, awk, ripgrep, fzf
    - File Operations: find, rsync, tar, tree, fd
    - Network Tools: curl, wget, nc, nmap, dig/nslookup, ping/traceroute
    - Modern CLI: bat, exa/eza, delta, zoxide, tldr
    - Logs & Monitoring: tail/head, less/more, journalctl, logrotate, multitail
    - Compression: gzip, zip, 7z

    Args:
        script: string - Bash script to execute CLI tools
        reasoning: string - Justification for permission request
    """
    result = await call_executor(script, config)
    return result


@tool
async def use_console_write_permissions(
    script: str, *, config: Annotated[RunnableConfig, InjectedToolArg], reasoning: str
) -> dict[str, Any]:
    """CLI Script Execution Tool with write permissions.
    - OS: ubuntu:22.04

    Tips:
    - Consolidate multiple commands into single bash script when possible
    - Write permissions only
    - For AWS/GCP/Azure CLI, use output/format text for consistent parsing

    Best practices for clean terminal output:
    - Use echo statements to explain what you're doing
    - Add comments with # for clarity
    - Combine related commands into single script
    - Use proper error handling with || for fallbacks

    Supported:
    - Cloud: AWS, GCP, Azure CLIs (aws-cli, gcloud, az)
    - Version Control: git
    - Data Processing: jq, yq, xmlstarlet, csvkit
    - Text Processing: grep, sed, awk, ripgrep, fzf
    - File Operations: find, rsync, tar, tree, fd
    - Network Tools: curl, wget, nc, nmap, dig/nslookup, ping/traceroute
    - Modern CLI: bat, exa/eza, delta, zoxide, tldr
    - Logs & Monitoring: tail/head, less/more, journalctl, logrotate, multitail
    - Compression: gzip, zip, 7z

    Args:
        script: string - Bash script to execute CLI tools
        reasoning: string - Justification for permission request
    """
    result = await call_executor(script, config)
    return result
