from dataclasses import field

from pydantic import BaseModel, Field


class GroupChat(BaseModel):
    """Group chat between agents."""

    messages: list[tuple[str, str]] = field(default_factory=list)
    last_message: str = Field(description="Last message from the customer", default="")

    def get_messages(self) -> str:
        """Get the messages as a string."""
        return "\n\n".join([f"{msg[0]}:\n{msg[1]}" for msg in self.messages])

    def add_message(self, message: str, role: str):
        """Add a message to the group chat."""
        return GroupChat(
            messages=self.messages + [(role, message)],
            last_message=message,
        )
