"""Predefined instructions for the report tool.

These instructions enforce narrative structure, avoid back-to-back visuals,
and require interpretation paragraphs for every visual.
"""

REPORT_TOOL_INSTRUCTIONS = """
You are using the Report Tool (#report).

Purpose:
- Create and manage a structured, stakeholder-ready report during a conversation.

Available commands:
1) create_outline: Provide title, description, and initial sections (headers only).
2) update_sections: Update or add sections (by index). Only include fields you change.
3) remove_sections: Remove sections by index.
4) create_or_update_executive_summary: Provide the executive summary when ready.
5) get_report: Retrieve the current full report object.
6) get_instructions: Return these usage instructions.

Report generation rules (must follow):

1) Structure:
   - Executive summary → (optional) context/method → findings sections → recommendations → appendix.

2) Section pattern (strict):
   - Begin with a short paragraph that states the claim of the section (1–2 sentences).
   - Then include at most one visual (chart/table/card).
   - Immediately follow with a paragraph that interprets the visual and explains business impact (what, so‑what, now‑what).
   - Do NOT place two visuals back-to-back. Always insert a paragraph between non-paragraph content types.
   - Only place visuals side-by-side if explicitly comparing; add a comparison paragraph before and an interpretation paragraph after.

3) Visual requirements:
   - Use assertion-style titles that express the takeaway (e.g., "Storage spiked in EU after backup policy change").
   - Include units, timeframe, and labeled axes. Prefer direct labels; keep color/scale consistent across comparable visuals.
   - Add reference lines/bands for targets or thresholds where relevant and annotate anomalies.

4) KPI cards:
   - Limit to 3–5 critical metrics. Each card requires a nearby paragraph explaining implications.

5) Recommendations:
   - Prioritize and tie each recommendation to a specific finding; quantify expected impact when possible.

6) Output constraints for this tool’s schema:
   - The `sections[i].content` list must not contain two consecutive items where `type` ∈ {chart, table, card}. Insert a `paragraph` between them.
   - Every chart/table must be preceded by a `paragraph` and followed by a `paragraph` summarizing the insight and impact.
   - Keep paragraph text plain (no markdown) as required by the schema.

Output shape:
- The get_instructions command returns JSON with {"instructions": string} in data.
""".strip()
