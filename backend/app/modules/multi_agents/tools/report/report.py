import json
from typing import Annotated, Literal, cast
from uuid import UUID

from langchain_core.runnables.config import RunnableConfig
from langchain_core.tools import InjectedToolArg, tool

from app.logger import logger
from app.services.report_service import ReportService

from .prompt import REPORT_TOOL_INSTRUCTIONS
from .schema import ExecutiveSummary, ReportSection


@tool
async def report(
    command: Literal[
        "create_outline",
        "update_sections",
        "remove_sections",
        "create_or_update_executive_summary",
        "get_report",
        "get_instructions",
    ]
    | str,
    title: str | None = None,
    description: str | None = None,
    sections: list[ReportSection] | None = None,
    executive_summary: ExecutiveSummary | None = None,
    *,
    config: Annotated[RunnableConfig, InjectedToolArg],
) -> str:
    """
    A tool for managing report in a conversation.

    ###### TOOL USE POLICY #####
    Tool Tag: #report
    Tool Use Policy: #manual
    ##########################

    Commands:
    - create_outline: Initialize or overwrite the outline with `title`, `description`, and `sections`.
    - update_sections: Merge provided `sections` into existing ones by index; add if not present.
    - remove_sections: Remove sections using `sections` indices; accepts dicts or typed sections.
    - create_or_update_executive_summary: Set the executive summary for the report.
    - get_report: Fetch the whole report object.
    - get_instructions: Return usage instructions for this tool.
    """
    try:
        workspace_id = UUID(config.get("configurable", {}).get("workspace_id"))
        conversation_id = UUID(config.get("configurable", {}).get("conversation_id"))

        # Cast to precise schema types when required
        sections = cast(list[ReportSection], sections)
        executive_summary = cast(ExecutiveSummary, executive_summary)

        # Define async helper functions
        async def _create_outline():
            return await ReportService.create_outline(
                workspace_id, conversation_id, title, description, sections
            )

        async def _update_sections():
            return await ReportService.update_sections(
                workspace_id, conversation_id, sections
            )

        async def _remove_sections():
            return await ReportService.remove_sections_by_objects(
                workspace_id,
                conversation_id,
                sections,
            )

        async def _create_or_update_executive_summary():
            return await ReportService.update_executive_summary(
                workspace_id, conversation_id, executive_summary
            )

        async def _get_report():
            return await ReportService.get_or_create_report(
                workspace_id, conversation_id
            )

        async def _get_instructions():
            return json.dumps(
                {
                    "status": "success",
                    "message": "Report tool instructions",
                    "data": {"instructions": REPORT_TOOL_INSTRUCTIONS},
                }
            )

        # Command dispatch using unified async approach
        command_handlers = {
            "create_outline": _create_outline,
            "update_sections": _update_sections,
            "remove_sections": _remove_sections,
            "create_or_update_executive_summary": _create_or_update_executive_summary,
            "get_report": _get_report,
            "get_instructions": _get_instructions,
        }

        handler = command_handlers.get(command)
        if not handler:
            return json.dumps(
                {
                    "status": "error",
                    "message": "Invalid command",
                }
            )

        return await handler()

    except Exception as e:
        logger.error(f"Report tool error for command '{command}': {e}")
        return json.dumps(
            {
                "status": "error",
                "message": f"Failed to execute {command}: {str(e)}",
            }
        )
