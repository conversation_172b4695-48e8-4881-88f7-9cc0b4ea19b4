from datetime import datetime
from typing import Literal

from pydantic import BaseModel, Field

from ..shared_schema import (
    ChartStructuredOutput,
    KPICard,
)


class TableColumnConfig(BaseModel):
    """Configuration for a table column"""

    header: str = Field(description="The header text")


class TableStructuredOutput(BaseModel):
    title: str = Field(description="The title of the table")
    description: str | None = Field(
        default=None, description="The description of the table"
    )
    columns: list[TableColumnConfig] | list[dict] = Field(
        description="The columns of the table"
    )
    rows: list[list[str]] = Field(
        description="The rows of the table, each row is a list of strings and seperated by comma"
    )


class Content(BaseModel):
    index: int = Field(description="The index of the content")
    type: Literal["paragraph", "chart", "table", "card"] = Field(
        description="The type of the content"
    )
    content: (
        str | ChartStructuredOutput | TableStructuredOutput | KPICard | dict | None
    ) = Field(
        default=None,
        description="""
            The data of the content, please use plain text for the paragraph (no markdown).
        """,
    )


class ExecutiveSummary(BaseModel):
    """
    Enhanced executive summary with visual elements and key metrics.

    Use this for high-level overviews and stakeholder communication.
    Include key metrics, risk indicators, and actionable recommendations.
    """

    key_findings: list[str] = Field(description="The key findings of the report")
    business_impact: list[str] = Field(description="The business impact of the report")
    key_metrics: list[KPICard] | None = Field(
        default=None,
        description="Key performance indicators and metrics cards for quick overview, max 3 cards.",
    )
    recommendations: list[str] | None = Field(
        default=None, description="Prioritized recommendations and next steps"
    )


class ReportSection(BaseModel):
    """
    NOTE: DO NOT PUT Executive Summary INTO THIS REPORT SECTION, WE ALREADY HAVE AN EXECUTIVE SUMMARY FIELD.
    """

    index: int = Field(description="The index of the section")
    header: str | None = Field(
        default=None,
        description="""
            The header of the section, example: "Monthly Cost Trends Analysis", "Service-Level Cost Analysis", "Resource Utilization Analysis, "Detailed Optimization Recommendations", "Security Operations Metrics", "Threat Landscape & Attack Patterns", "Detailed Security Findings & Risk Analysis", "Compliance Framework Assessment".
        """,
    )
    content: list[Content] | None = Field(
        default=None,
        description="The content of the section, each content has an index",
    )


class ReportInput(BaseModel):
    """
    A tool for managing report in a conversation.

    ###### TOOL USE POLICY #####

    Tool Tag: #report
    Tool Use Policy: #manual
    ##########################

    Commands:
    1. create_outline:
       Required: title, description, sections
       NOTE: YOU DONT NEED TO FILL THE CONTENT, YOU CAN FILL THEM LATER.

    2. update_sections:
       Required: sections
       NOTE: WHEN YOU UPDATE THE SECTIONS, YOU DONT NEED TO FILL ALL FIELDS WITHIN THE SECTION, IF A FIELD IS ALREADY FILLED, KEEP IT NONE. IF TITLE OR THE DESCRIPTION IS ALREADY FILLED, KEEP IT NONE. YOU CAN ALSO ADD NEW SECTIONS TO THE REPORT.

    3. remove_sections:
       Required: sections
       NOTE: ONLY USE THE INDEX OF THE SECTION TO REMOVE.

    4. create_or_update_executive_summary:
       Required: executive_summary
       NOTE: ONLY CALL THIS TOOL IF YOU ARE SURE THAT THE EXECUTIVE SUMMARY IS COMPLETE AND YOU HAVE ALL THE INFORMATION YOU NEED.

    5. get_report:
       Required: None
       NOTE: ONLY CALL THIS TOOL WHEN YOU WANT TO GET THE WHOLE REPORT.

    6. get_instructions:
       Required: None
       NOTE: Returns usage instructions for the report tool.
    """

    command: Literal[
        "create_outline",
        "update_sections",
        "remove_sections",
        "create_or_update_executive_summary",
        "get_report",
        "get_instructions",
    ]
    title: str | None = Field(default=None, description="The title of the whole report")
    description: str | None = Field(
        default=None, description="The description of the whole report"
    )
    sections: list[ReportSection] | None = Field(
        default=None,
        description="""
            The sections within the report. Don't add the Executive Summary in this field, you should add it in the `executive_summary` field.
        """,
    )
    executive_summary: ExecutiveSummary | dict | None = Field(
        default=None,
        description="""
            The executive summary of the report, ONLY CALL THIS TOOL WHEN YOU COMPLETE THE REPORT AND YOU ARE SURE THAT THE EXECUTIVE SUMMARY IS COMPLETE AND YOU HAVE ALL THE INFORMATION YOU NEED.
        """,
    )

    class Config:
        extra = "ignore"
        validate_assignment = True
        arbitrary_types_allowed = False
        use_enum_values = True
        validate_default = True
        populate_by_name = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
        }
