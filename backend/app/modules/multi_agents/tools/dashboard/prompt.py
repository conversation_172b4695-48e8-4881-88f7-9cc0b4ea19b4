"""Predefined instructions for the dashboard tool.

These instructions emphasize grid hygiene, widget clarity, and cross-widget consistency.
"""

DASHBOARD_TOOL_INSTRUCTIONS = """
You are using the Dashboard Tool (#dashboard).

Widget rules:
- KPI cards: keep to 3–6 key metrics; use consistent units and short labels.
- Charts: use appropriate types (trend=line/area, compare=bar, composition=stacked). Titles should be descriptive but neutral.
- Tables: concise columns; avoid wrapping text; ensure headers are clear.

Layout and consistency:
- Do not overlap widgets. Every widget must specify layout {x,y,w,h} and fit within the grid.
- Keep consistent colors/scales for the same metric across widgets.
- Reserve full-row width for crowded bar charts (w equals total columns when categories > 5).
- Prefer even vertical rhythm; avoid narrow, tall widgets for dense tables.

Output constraints for this tool’s schema:
- All widgets MUST include a layout object with integer x, y, w, h.
- Ensure JSON-serializable content for every widget.

Output shape:
- The get_instructions command returns JSON with {"instructions": string} in data.
""".strip()
