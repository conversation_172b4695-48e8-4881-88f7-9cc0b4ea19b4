import json
from typing import Annotated, Literal, cast
from uuid import UUID

from langchain_core.runnables.config import RunnableConfig
from langchain_core.tools import InjectedToolArg, tool

from app.logger import logger
from app.services.dashboard_service import DashboardService

from .prompt import DASHBOARD_TOOL_INSTRUCTIONS
from .schema import DashboardWidget, GridConfig, LayoutConfig


@tool
async def dashboard(
    command: Literal[
        "create_dashboard",
        "add_widgets",
        "update_widgets",
        "remove_widgets",
        "update_grid",
        "get_dashboard",
        "get_instructions",
    ]
    | str,
    title: str | None = None,
    description: str | None = None,
    grid_config: GridConfig | None = None,
    widgets: list[DashboardWidget] | None = None,
    widget_positions: list[LayoutConfig] | None = None,
    *,
    config: Annotated[RunnableConfig, InjectedToolArg],
) -> str:
    """
    A tool for managing dashboards in a conversation.

    ###### TOOL USE POLICY #####
    Tool Tag: #dashboard
    Tool Use Policy: #manual
    ##########################

    Commands:
    - create_dashboard: Initialize or overwrite the dashboard with `title`, `description`, `grid_config`, and `widgets`.
    - add_widgets: Add new `widgets` to the existing dashboard.
    - update_widgets: Update existing `widgets` in the dashboard.
    - remove_widgets: Remove widgets using `widget_positions`; accepts layout configs.
    - update_grid: Update the dashboard `grid_config`.
    - get_dashboard: Fetch the whole dashboard object.
    - get_instructions: Return usage instructions for this tool.
    """
    try:
        workspace_id = UUID(config.get("configurable", {}).get("workspace_id"))
        conversation_id = UUID(config.get("configurable", {}).get("conversation_id"))

        # Cast to precise schema types when required
        widgets = cast(list[DashboardWidget], widgets)
        grid_config = cast(GridConfig, grid_config)

        # Define async helper functions
        async def _create_dashboard():
            return await DashboardService.create_dashboard(
                workspace_id,
                conversation_id,
                title,
                description,
                grid_config,
                widgets,
            )

        async def _add_widgets():
            return await DashboardService.add_widgets(
                workspace_id, conversation_id, widgets
            )

        async def _update_widgets():
            return await DashboardService.update_widgets(
                workspace_id, conversation_id, widgets
            )

        async def _remove_widgets():
            return await DashboardService.remove_widgets_by_positions(
                workspace_id, conversation_id, widget_positions
            )

        async def _update_grid():
            return await DashboardService.update_grid(
                workspace_id, conversation_id, grid_config
            )

        async def _get_dashboard():
            return await DashboardService.get_or_create_dashboard(
                workspace_id, conversation_id
            )

        async def _get_instructions():
            return json.dumps(
                {
                    "status": "success",
                    "message": "Dashboard tool instructions",
                    "data": {"instructions": DASHBOARD_TOOL_INSTRUCTIONS},
                }
            )

        # Command dispatch using unified async approach
        command_handlers = {
            "create_dashboard": _create_dashboard,
            "add_widgets": _add_widgets,
            "update_widgets": _update_widgets,
            "remove_widgets": _remove_widgets,
            "update_grid": _update_grid,
            "get_dashboard": _get_dashboard,
            "get_instructions": _get_instructions,
        }

        handler = command_handlers.get(command)
        if not handler:
            return json.dumps(
                {
                    "status": "error",
                    "message": "Invalid command",
                }
            )

        return await handler()

    except Exception as e:
        logger.error(f"Dashboard tool error for command '{command}': {e}")
        return json.dumps(
            {
                "status": "error",
                "message": f"Failed to execute {command}: {str(e)}",
            }
        )
