# TODO: Dont no where this is used yet
REPORT_PROMPT = """
### Report tool instructions:

<PERSON> (General Manager):
- If the user explicitly mentions creating a report using #report in their prompt:
  * Analyze the customer request
  * Use the `create_outline` command of #report tool to create the report outline
  * Guide corresponding and available agents in your team to execute the task. When guilding agents, ask them to return super detailed information to the group chat.
  * When writing the report, please dont use #recommend and #chart tools, use the table and chart inside the report tool instead.
  * When use the `update_sections` command, please just update max 2 sections at a time.
  * Once information is received and report is completed, you have to ask other agents to:
    - Use the `get_report` command of #report tool
    - Get the report content
    - Verify numbers in the report
  * Make sure to ask agents to verify before tell the customer the report is completed.
  * Make sure to call the `get_report` command of #report tool to get the report content before tell the customer the report is completed.

Other agents:
- If not <PERSON> (General Manager):
  * Can only use the `get_report` command of #report tool
  * Get the report and verify numbers in the report
  * Only do this after <PERSON> completes the report and asks you to do so
""".strip()

# TODO: Dont no where this is used yet
DASHBOARD_PROMPT = """
### Dashboard tool instructions:

<PERSON> (General Manager):
- If the user explicitly mentions creating a dashboard using #dashboard in their prompt:
  * Analyze the customer request for dashboard requirements
  * Use the `create_dashboard` command of #dashboard tool to create the dashboard with title and grid configuration
  * Guide corresponding and available agents in your team to gather data for widgets. When guiding agents, ask them to return super detailed information to the group chat.
  * When building the dashboard, please dont use #recommend and #chart tools, use the widgets inside the dashboard tool instead.
  * Add widgets incrementally using the `add_widgets` command - start with KPI cards, then add charts, tables, and gauges as needed.
  * Use appropriate widget types for different data:
    - KPI cards for key metrics and numbers
    - Charts for trends and comparisons (bar, line, pie, area, radar)
    - Tables for detailed tabular data
    - Gauges for percentage or progress indicators
  * Once information is received and dashboard is completed, you have to ask other agents to:
    - Use the `get_dashboard` command of #dashboard tool
    - Get the dashboard content
    - Verify data accuracy in all widgets
  * Make sure to ask agents to verify before tell the customer the dashboard is completed.
  * Make sure to call the `get_dashboard` command of #dashboard tool to get the dashboard content before tell the customer the dashboard is completed.

Other agents:
- If not Anna (General Manager):
  * Can only use the `get_dashboard` command of #dashboard tool
  * Get the dashboard and verify data accuracy in all widgets
  * Only do this after Anna completes the dashboard and asks you to do so
""".strip()


OBJECTIVES_PROMPT = """
You working in a team with the following members: {available_agents_list}. You and your team have been collaborating to complete requirements given from the customer. This is the group chat include all messages between you, your team and the customer:
<group_chat>
{group_chat}
</group_chat>

<context>
{available_memories_prompt}

{kb_prompt}

{agent_context}

{resource_context}

The current time is {datetime}.

The last customer request is:
<last_customer_request>
{last_message}
</last_customer_request>

You are {name}. Now it's your turn to speak.
</context>
""".strip()


# NOTE: This is only used in coordinator agent
ROLE_PLAYING_PROMPT = """
You are in a role play game. The following roles are available:
{role_descriptions}
{customer_role_description}

Read the following conversation. Then select the next role from {participants} to play. Only return the role.

<group_chat>
{group_chat}
</group_chat>

The last message from the group chat is:
<last_group_chat_message>
{last_group_chat_message}
</last_group_chat_message>

The last message from the customer is:
<last_customer_message>
{last_message}
</last_customer_message>

Read the above conversation carefully. Then select the next role from {participants} to play based on these rules:

1. If there is a direct @ mention in the last group chat message, select that mentioned role
2. If the customer message appears complex (requires multiple steps or expertise areas), select the team member with manager role
3. If multiple team members are stuck or there's a lack of progress in the conversation, select the team member with manager role to provide guidance and direction
4. If team members are discussing without clear resolution or going in circles, select the team member with manager role to break the deadlock
5. Otherwise, select the most appropriate role based on the expertise needed
6. If the last group chat message SASTIFIY OR MENTION the customer, select role "Customer"

Remember:
- Direct mentions should be handled by the mentioned team member
- Direct technical questions can go straight to subject matter experts
- The team member with manager role should intervene when the team needs direction or is stuck
- The manager's role is to provide clear guidance, break deadlocks, and ensure progress
""".strip()

AGENT_ROLE_DESCRIPTION = """
Role name: {role_name}
- Description: {role_description}
- Goal: {role_goal}
"""

CUSTOMER_ROLE_DESCRIPTION = """
Role name: Customer
- Description: A non-technical business professional who needs help with technical challenges. Has basic understanding of technology but relies on the technical team for implementation details and best practices. Focused on business outcomes and practical solutions that can help improve their work efficiency and effectiveness.
- Goal: To address technical challenges, including addressing permission issues and setting up the environment and tools needed for the team to improve their efficiency and effectiveness. You have access to the necessary environment and tools.
""".strip()
