"""Common utilities and base classes for multi-agent system."""

import copy
import json
from uuid import UUID

from langchain_core.messages import AIMessage, HumanMessage, ToolMessage
from langchain_core.runnables import RunnableConfig
from langgraph.graph import END
from langgraph.types import Command, StreamWriter

from app.core.config import settings
from app.logger import logger
from app.modules.multi_agents.core import Configuration, GlobalState
from app.modules.multi_agents.core.utils import (
    is_valid_tool_calls_message,
)
from app.modules.multi_agents.prompts import (
    INVALID_TOOL_NAME_ERROR_TEMPLATE,
    SUMMARY_PROMPT,
    TOOL_CALL_ERROR_TEMPLATE,
    TOOL_PERMISSION_DENIED,
)
from app.modules.multi_agents.tools.plan.model import PlanManager
from app.repositories.agent_context import AgentContextRepository
from app.services.attachment_service import AttachmentService

from .permission import count_conversation_rounds, get_user_permission


class BaseAgentToolNode:
    """Base class containing shared node functionality for agents."""

    async def tool_node(
        self, state: GlobalState, config: <PERSON><PERSON><PERSON><PERSON><PERSON>fi<PERSON>, writer: StreamWriter
    ):
        """Execute tool calls and handle errors."""

        # Get the configuration
        configuration = Configuration.from_runnable_config(config)
        tool_manager = configuration.tool_manager
        agent_config = configuration.agents_config.agents[state.name]

        # Get the tools
        tools = await tool_manager.get_tools(agent_config=agent_config, state=state)

        tools_by_name = {tool.name: tool for tool in tools}

        # Get the last message and check if it has tool calls
        last_message = state.instance_states[state.name].messages[-1]
        if not is_valid_tool_calls_message(last_message):
            logger.error("No tool calls found in message")
            return Command(goto="reasoning_agent", update=state)

        # At this point, we know last_message is an AIMessage with tool_calls
        assert isinstance(last_message, AIMessage) and last_message.tool_calls

        config["metadata"] = {
            "observation_type": "tools",
        }

        # Process each tool call
        for tool_call in last_message.tool_calls:
            try:
                # Check if tool exists
                if tool_call["name"] not in tools_by_name:
                    error_msg = INVALID_TOOL_NAME_ERROR_TEMPLATE.format(
                        requested_tool=tool_call["name"]
                    )
                    state.instance_states[state.name].messages.append(
                        ToolMessage(
                            content=error_msg,
                            tool_call_id=tool_call["id"],
                            name=tool_call["name"],
                            status="error",
                        )
                    )
                    continue

                tool = tools_by_name[tool_call["name"]]
                tool_args = copy.deepcopy(tool_call["args"])

                try:
                    response = await tool.ainvoke(tool_args, config)
                    tool_content = response
                    if tool_call["name"] == "group_chat":
                        if response["status"] == "error":
                            state.instance_states[state.name].messages.append(
                                ToolMessage(
                                    content=response,
                                    tool_call_id=tool_call["id"],
                                    name=tool_call["name"],
                                    status="error",
                                )
                            )
                            return Command(goto="reasoning_agent", update=state)
                        else:
                            state.target_group_chat_member = tool_args["target_member"]
                            state.group_chat.messages.append(
                                (state.name, tool_args["message"])
                            )
                            state.instance_states[state.name].messages.append(
                                ToolMessage(
                                    content=response,
                                    tool_call_id=tool_call["id"],
                                    name=tool_call["name"],
                                )
                            )
                            return Command(goto=END, update=state)
                    elif tool_call["name"] == "planning":
                        agent_ids = list(state.instance_states.keys())
                        all_plans = PlanManager.get_all_agents_plans(
                            state.plan_manager, agent_ids
                        )
                        writer(
                            {
                                "type": "planning",
                                "content": all_plans,
                            }
                        )
                    elif tool_call["name"] == "report":
                        response = json.loads(response)
                        tool_content = {
                            "status": response["status"],
                            "is_completed": response["is_completed"]
                            if "is_completed" in response
                            else False,
                            "message": response["message"],
                        }
                        report_data = response.get("data", {})
                        writer(
                            {
                                "type": "on_report_generation_response",
                                "content": report_data,
                            }
                        )
                    elif tool_call["name"] == "dashboard":
                        command = tool_args["command"]
                        response = json.loads(response)
                        tool_content = {
                            "status": response["status"],
                            "message": response["message"],
                        }
                        if command == "get_dashboard":
                            tool_content = response
                        dashboard_data = response.get("data", {})
                        writer(
                            {
                                "type": "on_dashboard_generation_response",
                                "content": dashboard_data,
                            }
                        )
                    elif tool_call["name"] == "recommendation":
                        tool_content = response
                        command = tool_args["command"]
                        if command == "create":
                            tool_call_result = tool_call["args"]["new_recommendations"][
                                "recommendations"
                            ]
                            resource_id = config["metadata"].get("resource_id")
                            # Fix resource type
                            for i, rcm in enumerate(tool_call_result):
                                resource_type = rcm.get("resource_type")
                                if isinstance(resource_type, str):
                                    resource_type = resource_type.replace(
                                        "_", " "
                                    ).capitalize()
                                tool_call_result[i]["resource_type"] = resource_type
                            writer(
                                {
                                    "type": "on_recommendation_generation_response",
                                    "content": tool_call_result,
                                    "metadata": {
                                        "resource_id": str(resource_id)
                                        if resource_id is not None
                                        else None,
                                        "type": "recommendation",
                                    },
                                }
                            )

                    state.instance_states[state.name].messages.append(
                        ToolMessage(
                            content=str(tool_content)
                            if isinstance(tool_content, dict)
                            else tool_content,
                            tool_call_id=tool_call["id"],
                            name=tool_call["name"],
                        )
                    )
                except Exception as e:
                    error_msg = TOOL_CALL_ERROR_TEMPLATE.format(error=str(e))
                    state.instance_states[state.name].messages.append(
                        ToolMessage(
                            content=error_msg,
                            tool_call_id=tool_call["id"],
                            name=tool_call["name"],
                            status="error",
                        )
                    )
            except Exception as e:
                # Catch any other unexpected errors
                state.instance_states[state.name].messages.append(
                    ToolMessage(
                        content=f"Unexpected error: {str(e)}",
                        tool_call_id=tool_call["id"],
                        name=tool_call.get("name", "unknown"),
                        status="error",
                    )
                )

            # Auto-save context for specific MCP tools
            if any(
                tool_substring in tool_call["name"]
                for tool_substring in settings.MCP_AUTO_CONTEXT_TOOLS
            ):
                try:
                    logger.info(f"Auto-saving context for {tool_call['name']}")
                    uuid_agent_id = UUID(agent_config.agent_id)
                    title = f"Database Overview - {tool_call['name']}"
                    context = str(tool_content)

                    # Save to Redis with 30 minutes TTL
                    agent_context_repository = AgentContextRepository()
                    success = agent_context_repository.set_context(
                        agent_id=uuid_agent_id,
                        title=title,
                        context=context,
                        ttl_seconds=1800,  # 30 minutes
                    )
                    if success:
                        logger.info(
                            f"Successfully saved context to Redis for agent {uuid_agent_id}"
                        )
                    else:
                        logger.error(
                            f"Failed to save context to Redis for agent {uuid_agent_id}"
                        )
                except Exception:
                    logger.exception("Failed to auto-save context")

        return Command(goto="reasoning_agent", update=state)


class BaseConversationalAgentNode:
    """Base class containing shared node functionality for agents."""

    def get_reasoning_agent_name(self) -> str:
        """Get the name of the reasoning agent node.

        This method can be overridden by subclasses to provide a different reasoning agent name.
        """
        return "reasoning_agent"

    async def check_permissions(self, state: GlobalState, config: RunnableConfig):
        """Check if user has required permissions for operations."""

        # Get the instance state
        instance_state = state.instance_states[state.name]

        # Get the configuration
        configuration = Configuration.from_runnable_config(config)
        agent_configuration = configuration.agents_config.agents[state.name]

        # Get the last message
        last_message = instance_state.messages[-1]

        # Check if the last message has tool calls
        if not is_valid_tool_calls_message(last_message):
            return Command(goto="tools", update=state)

        # At this point, we know last_message is an AIMessage with tool_calls
        assert isinstance(last_message, AIMessage) and last_message.tool_calls

        # Get the permission tool
        permission_tool = next(
            (
                tool_call
                for tool_call in last_message.tool_calls
                if tool_call["name"] in agent_configuration.tool_config.tool_permissions
            ),
            None,
        )
        if permission_tool:
            permission_granted, approve_message = await get_user_permission(
                permission_tool["name"], permission_tool.get("args", {})
            )
            if permission_granted:
                return Command(goto="tools")
            else:
                instance_state.messages.append(
                    ToolMessage(
                        content=TOOL_PERMISSION_DENIED.format(
                            tool_name=permission_tool["name"],
                            approve_message=approve_message,
                        ),
                        tool_call_id=permission_tool["id"],
                    )
                )
                return Command(goto=self.get_reasoning_agent_name(), update=state)
        else:
            return Command(goto="tools", update=state)

    async def summarize_conversation(self, state: GlobalState, config: RunnableConfig):
        """Summarize the conversation and maintain a running summary."""

        # Get the instance state
        instance_state = state.instance_states[state.name]

        # Get the configuration
        configuration = Configuration.from_runnable_config(config)
        model = configuration.model

        conversation_rounds = count_conversation_rounds(instance_state.messages)

        if conversation_rounds >= configuration.max_conversation_rounds:
            # The latest message is the user message
            # The second latest message is the assistant message
            # If the second latest message is a tool message, we need to keep the assistant message
            # Must check instance_state.messages has at least 2 messages
            if len(instance_state.messages) >= 2:
                if isinstance(instance_state.messages[-2], ToolMessage):
                    messages_to_keep = instance_state.messages[-3:]
                else:
                    messages_to_keep = instance_state.messages[-2:]
            else:
                return Command(goto=self.get_reasoning_agent_name(), update=state)

            messages_to_summarize = instance_state.messages
            message_history = "\n".join(
                f"{'User' if isinstance(m, HumanMessage) else 'Assistant'}: {AttachmentService.format_message_content_for_summary(m.content)}"
                for m in messages_to_summarize
            )

            config["run_name"] = "Summarizer"
            response = await model.ainvoke(
                [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": SUMMARY_PROMPT,
                                "cache_control": {"type": "ephemeral"},
                            },
                            {
                                "type": "text",
                                "text": message_history,
                            },
                        ],
                    }
                ],
                config,
            )

            instance_state.summary = response.content
            instance_state.messages = messages_to_keep

        return Command(goto=self.get_reasoning_agent_name(), update=state)

    async def structured_tools(
        self,
        state: GlobalState,
        config: RunnableConfig,
        writer: StreamWriter,
    ) -> GlobalState:
        # Get the instance state
        instance_state = state.instance_states[state.name]

        last_message = instance_state.messages[-1]

        # Check if the last message has tool calls
        if not is_valid_tool_calls_message(last_message):
            logger.error("No tool calls found in message")
            return state

        # At this point, we know last_message is an AIMessage with tool_calls
        assert isinstance(last_message, AIMessage) and last_message.tool_calls

        tool_call = last_message.tool_calls[0]

        # Validate that we have tool calls and they're properly structured
        if not last_message.tool_calls:
            logger.error("No tool calls found in message")
            return state

        tool_message = ToolMessage(
            content="Chart component created successfully",
            tool_call_id=tool_call.get("id", "unknown"),
            name=tool_call["name"],
        )

        if tool_call["name"] == "create_chart":
            writer(
                {"type": "on_chart_generation_response", "content": tool_call["args"]}
            )

        # Add the tool message to state
        instance_state.messages.append(tool_message)
        return state
