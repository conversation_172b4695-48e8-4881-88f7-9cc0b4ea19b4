import asyncio
from contextlib import asynccontextmanager

from psycopg_pool import As<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from app.logger import logger


class DatabaseOptimizer:
    """Database optimization utility for removing duplicate indexes, unused indexes, and handling bloat."""

    def __init__(self, pool: AsyncConnectionPool, schema_name: str = "public"):
        self.pool = pool
        self.schema_name = schema_name
        self._optimization_lock = asyncio.Lock()

    @asynccontextmanager
    async def _get_connection_with_advisory_lock(self, lock_key: int):
        """Get a connection with an advisory lock to prevent concurrent operations."""
        async with self.pool.connection() as conn:
            # Acquire advisory lock to prevent concurrent optimization operations
            await conn.execute("SELECT pg_advisory_lock(%s);", (lock_key,))
            try:
                yield conn
            finally:
                # Release advisory lock
                await conn.execute("SELECT pg_advisory_unlock(%s);", (lock_key,))

    async def optimize_database(self) -> None:
        """Main optimization method that handles all database optimization tasks."""
        # Use a lock to prevent multiple optimization processes from running simultaneously
        async with self._optimization_lock:
            try:
                # Remove duplicate indexes that duplicate primary key functionality
                await self._remove_duplicate_indexes()

                # Remove unused indexes (0 scans)
                await self._remove_unused_indexes()

                # Handle index bloat through VACUUM operations
                await self._handle_index_bloat()

            except Exception as e:
                logger.error(f"Database optimization failed: {e}")
                raise

    async def _remove_duplicate_indexes(self) -> None:
        """Remove duplicate indexes that duplicate primary key functionality."""
        # Query to find duplicate indexes that duplicate primary keys
        duplicate_indexes_query = """
        SELECT 
            i.indexname as index_name,
            t.tablename as table_name,
            c.conname as constraint_name
        FROM pg_indexes i
        JOIN pg_tables t ON i.tablename = t.tablename
        JOIN pg_constraint c ON c.conrelid = t.tablename::regclass
        WHERE i.schemaname = %s
        AND c.contype = 'p'  -- Primary key constraints
        AND i.indexname != c.conname  -- Index is not the constraint itself
        AND i.indexname IN (
            'checkpoint_blobs_thread_id_idx',
            'checkpoint_writes_thread_id_idx',
            'checkpoints_thread_id_idx'
        );
        """

        async with self._get_connection_with_advisory_lock(12345) as conn:
            result = await conn.execute(duplicate_indexes_query, (self.schema_name,))
            rows = await result.fetchall()

            if not rows:
                return

            for row in rows:
                index_name, table_name, constraint_name = row

                try:
                    # Use regular DROP INDEX instead of CONCURRENTLY to avoid lock conflicts
                    drop_query = (
                        f"DROP INDEX IF EXISTS {self.schema_name}.{index_name};"
                    )
                    await conn.execute(drop_query)
                except Exception as e:
                    logger.warning(
                        f"Failed to remove duplicate index {index_name}: {e}"
                    )

    async def _remove_unused_indexes(self) -> None:
        """Remove unused indexes (0 scans) to reclaim storage space."""
        # Query to find unused indexes (0 scans)
        unused_indexes_query = """
        SELECT 
            si.schemaname,
            si.relname as tablename,
            si.indexrelname as indexname,
            pg_size_pretty(pg_relation_size(si.indexrelid)) as index_size
        FROM pg_stat_user_indexes si
        WHERE si.schemaname = %s
        AND si.idx_scan = 0  -- 0 scans = unused
        AND si.indexrelname NOT LIKE '%%_pkey'  -- Don't remove primary keys
        AND si.indexrelname NOT LIKE '%%_key'   -- Don't remove unique keys
        AND si.indexrelname NOT LIKE '%%_idx'   -- Don't remove foreign key indexes
        ORDER BY pg_relation_size(si.indexrelid) DESC;
        """

        async with self._get_connection_with_advisory_lock(12346) as conn:
            result = await conn.execute(unused_indexes_query, (self.schema_name,))
            rows = await result.fetchall()

            if not rows:
                return

            for row in rows:
                schema, table, index_name, size = row

                try:
                    # Use regular DROP INDEX instead of CONCURRENTLY to avoid lock conflicts
                    drop_query = f"DROP INDEX IF EXISTS {schema}.{index_name};"
                    await conn.execute(drop_query)
                except Exception as e:
                    logger.warning(f"Failed to remove unused index {index_name}: {e}")

    async def _handle_index_bloat(self) -> None:
        """Handle index bloat through VACUUM operations."""
        # Query to find bloated indexes
        bloated_indexes_query = """
        SELECT 
            si.schemaname,
            si.relname as tablename,
            si.indexrelname as indexname,
            pg_size_pretty(pg_relation_size(si.indexrelid)) as index_size,
            '0 bytes' as bloat_size
        FROM pg_stat_user_indexes si
        WHERE si.schemaname = %s
        AND pg_relation_size(si.indexrelid) > 1024 * 1024  -- Only indexes > 1MB
        ORDER BY pg_relation_size(si.indexrelid) DESC;
        """

        async with self._get_connection_with_advisory_lock(12347) as conn:
            result = await conn.execute(bloated_indexes_query, (self.schema_name,))
            rows = await result.fetchall()

            if not rows:
                return

            for row in rows:
                schema, table, index_name, size, bloat_size = row

                try:
                    # Perform VACUUM ANALYZE on the table to clean up bloat
                    vacuum_query = f"VACUUM ANALYZE {schema}.{table};"
                    await conn.execute(vacuum_query)

                    # Reindex if the index is still large after VACUUM
                    await self._reindex_if_needed(conn, schema, table, index_name)

                except Exception as e:
                    logger.warning(
                        f"Failed to handle bloat for index {index_name}: {e}"
                    )

    async def _reindex_if_needed(
        self, conn, schema: str, table: str, index_name: str
    ) -> None:
        """Reindex an index if it's still significantly large after VACUUM."""
        try:
            # Check if index is still large after VACUUM
            size_query = f"SELECT pg_size_pretty(pg_relation_size('{schema}.{index_name}'::regclass)) as current_size;"
            result = await conn.execute(size_query)
            current_size = await result.fetchone()

            if current_size and current_size[0]:
                # If index is still very large (>100MB), consider reindexing
                size_bytes = await self._get_index_size_bytes(conn, schema, index_name)
                if size_bytes > 100 * 1024 * 1024:  # 100MB
                    # Use regular REINDEX instead of CONCURRENTLY to avoid lock conflicts
                    # This will take an exclusive lock but prevents deadlocks
                    reindex_query = f"REINDEX INDEX {schema}.{index_name};"
                    await conn.execute(reindex_query)

        except Exception as e:
            logger.warning(f"Failed to reindex {index_name}: {e}")

    async def _get_index_size_bytes(self, conn, schema: str, index_name: str) -> int:
        """Get the size of an index in bytes."""
        try:
            size_query = f"SELECT pg_relation_size('{schema}.{index_name}'::regclass);"
            result = await conn.execute(size_query)
            size_row = await result.fetchone()
            return size_row[0] if size_row else 0
        except Exception:
            return 0

    async def get_optimization_summary(self) -> dict[str, str | int | None]:
        """Get a summary of the current database state for optimization."""
        summary: dict[str, str | int | None] = {
            "duplicate_indexes": [],
            "unused_indexes": [],
            "bloated_indexes": [],
            "total_storage_potential": "0 MB",
        }

        try:
            async with self.pool.connection() as conn:
                # Count duplicate indexes
                duplicate_count_query = """
                SELECT COUNT(*) FROM pg_indexes i
                JOIN pg_tables t ON i.tablename = t.tablename
                JOIN pg_constraint c ON c.conrelid = t.tablename::regclass
                WHERE i.schemaname = %s AND c.contype = 'p'
                AND i.indexname != c.conname;
                """
                result = await conn.execute(duplicate_count_query, (self.schema_name,))
                duplicate_count = await result.fetchone()
                summary["duplicate_indexes_count"] = (
                    duplicate_count[0] if duplicate_count else 0
                )

                # Count unused indexes
                unused_count_query = """
                SELECT COUNT(*) FROM pg_stat_user_indexes si
                JOIN pg_indexes i ON si.indexrelname = i.indexname
                WHERE si.schemaname = %s AND si.idx_scan = 0;
                """
                result = await conn.execute(unused_count_query, (self.schema_name,))
                unused_count = await result.fetchone()
                summary["unused_indexes_count"] = unused_count[0] if unused_count else 0

                # Calculate potential storage savings
                storage_query = """
                SELECT pg_size_pretty(SUM(pg_relation_size(indexname::regclass))) as total_size
                FROM pg_stat_user_indexes si
                JOIN pg_indexes i ON si.indexrelname = i.indexname
                WHERE si.schemaname = %s AND si.idx_scan = 0;
                """
                result = await conn.execute(storage_query, (self.schema_name,))
                storage_row = await result.fetchone()
                summary["total_storage_potential"] = (
                    storage_row[0] if storage_row else "0 MB"
                )

        except Exception as e:
            logger.error(f"Failed to generate optimization summary: {e}")

        return summary


async def optimize_langgraph_database(
    pool: AsyncConnectionPool, schema_name: str = "public"
) -> dict[str, str | int | None]:
    """Convenience function to optimize LangGraph database tables."""
    optimizer = DatabaseOptimizer(pool, schema_name)
    await optimizer.optimize_database()
    return await optimizer.get_optimization_summary()
