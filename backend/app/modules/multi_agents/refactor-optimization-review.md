## Multi-Agents Module: Architecture Review and Optimization Plan

This document provides a detailed architectural overview and a prioritized set of refactors and optimizations for `backend/app/modules/multi_agents`.

### TL;DR — Highest-Impact Fixes First

- Fix prompt construction so conversation context is always provided to the LLM, even when cached context is below threshold.
- Add missing `update=state` on permission-approved path in `check_permissions` to prevent state loss.
- Fix `executor.py` variable scope bug (`workspace` referenced even when `config` is None) and improve credentials handling.
- Unify chart schemas and enums; remove duplicated/incorrect `ChartType` enum in `create_chart.py` and reuse `tools/shared_schema.py`.
- Standardize tool message payloads to JSON (avoid `str(dict)`) and normalize tool responses/streaming events.
- Harden DB optimizer safety: correct schema joins, avoid name-based index drops unless verified, add dry-run/metrics.

---

## 1) Architectural Overview

### 1.1 Components

- Agents
  - `CoordinatorAgent` orchestrates role selection and message routing
  - `ConversationalAgent` performs LLM calls and tool routing
  - `AgentFactory` bootstraps graphs, Postgres checkpointer, and registration
- Core
  - `Configuration` (dataclass) creates run-time config including `ToolManager`
  - `ContextManager` builds composable message hierarchy with caching
  - `ToolManager` resolves built-in, MCP, and connection tools
  - `GlobalState`/`StateBase` manage per-agent conversation state, plan, and group chat
- Common
  - `BaseAgentToolNode`, `BaseConversationalAgentNode`, permission prompts, and DB optimizer helper
- Tools
  - Internal tools: planning, dashboard, report, create_chart, recommendation, console, kb, search, fetch_url, alert, schedule_task

### 1.2 Runtime Flow (simplified)

```mermaid
flowchart TD
  A[Customer Prompt] --> B[CoordinatorAgent.set_up_state]
  B --> C[CoordinatorAgent.role selection]
  C --> D[Append group_chat prompt to target agent's messages]
  D --> E[ConversationalAgent.summarize]
  E --> F[ConversationalAgent.reasoning_agent]
  F -->|tool calls?| G{Tool name}
  G -->|planning/report/dashboard/create_chart| H[structured_tools]
  G -->|other tools| I[check_permissions]
  I -->|approved| J[tools]
  I -->|denied| F
  H --> F
  J --> F
  F -->|group_chat target=Customer| K[END]
  F -->|else| C
```

---

## 2) Key Findings and Recommendations

### 2.1 Prompt Construction and Context

- Issue: `ConversationalAgent.reasoning_agent` uses `ContextManager.build_message_hierarchy` which returns only system + optional cached context. When cached context is below threshold, no conversation content is provided at all — the LLM may be missing the actual group chat prompt just added to `instance_state.messages`.
- Impact: Tool routing and correctness degrade for short prompts or the first few turns; the agent may fail to act.
- Recommendation:
  - Always include the most recent message(s) in the LLM call, even if cached context is not used yet.
  - Suggestion: include the last N messages (e.g., 4–8) as non-cached user/assistant messages below the system/cached blocks.
  - Consider a fallback: if cached context is absent, include the entire current prompt or group chat message once (bounded by token limit).

### 2.2 Permission Node State Update

- Issue: In `BaseConversationalAgentNode.check_permissions`, the permission-approved branch returns `Command(goto="tools")` but omits `update=state`.
- Impact: Risk of state not being persisted or downstream nodes not receiving updated state consistently.
- Recommendation: Return `Command(goto="tools", update=state)`.

```12:33:backend/app/modules/multi_agents/common/nodes.py
        if permission_tool:
            permission_granted, approve_message = await get_user_permission(
                permission_tool["name"], permission_tool.get("args", {})
            )
            if permission_granted:
                return Command(goto="tools")
            else:
                instance_state.messages.append(
                    ToolMessage(
                        content=TOOL_PERMISSION_DENIED.format(
                            tool_name=permission_tool["name"],
                            approve_message=approve_message,
                        ),
                        tool_call_id=permission_tool["id"],
                    )
                )
                return Command(goto=self.get_reasoning_agent_name(), update=state)
```

Proposed change: add `update=state` to the approved path.

### 2.3 Executor Workspace Variable Bug

- Issue: `workspace` is referenced outside the `if config:` scope in `tools/executor.py`.
- Impact: Raises `NameError` when `config` is None; brittle handling of optional credentials.
- Recommendation: Initialize `workspace = None` before the `if config:` block; guard all `workspace` uses; ensure kubeconfig handling resides inside the guarded block.

```107:115:backend/app/modules/multi_agents/tools/executor.py
    else:
        raise ValueError(f"Unsupported provider: {provider}")

    if workspace.kubeconfig:
        body["k8s_credentials"] = {"kubeconfig": str(workspace.kubeconfig)}
```

Proposed change: guard with `if workspace and workspace.kubeconfig:` and move under the `if config:` branch.

### 2.4 Chart Schema and Enum Duplication

- Issue: `create_chart.py` defines a second `ChartType` enum using `Field(...)` for enum members. This is incorrect for Python `Enum` and duplicates `tools/shared_schema.py`.
- Impact: Type confusion, validation fragility, maintenance overhead.
- Recommendation:
  - Delete the local `ChartType` in `create_chart.py`; import and reuse `ChartType` and `ChartStructuredOutput` from `tools/shared_schema.py`.
  - Ensure the tool’s args schema matches a single source-of-truth.

### 2.5 Tool Message Consistency and JSON Serialization

- Issue: Tool node often stringifies dicts (`str(tool_content)`) and mixes plain strings/JSON.
- Impact: Harder parsing/consumption in clients; inconsistent downstream behavior; breaks UI expectations.
- Recommendation:
  - Normalize all tool outputs to JSON strings via `json.dumps`.
  - Standardize streamed writer events (`on_*_response`) to consistent shapes and include `metadata` where appropriate.

### 2.6 Database Optimizer Safety

- Issues:
  - Duplicate index query joins `pg_tables` only by `tablename` (not `schemaname`), risking cross-schema collisions.
  - Hard-coded index names for drops; may be brittle across versions.
  - Using regular `DROP INDEX` could lock; may be acceptable during maintenance, but consider scheduling.
- Recommendations:
  - Join on `(schemaname, tablename)` in all queries.
  - Detect duplicates by index expressions/columns, not by name alone; keep a dry-run mode and log candidates before action.
  - Make the optimizer opt-in via `settings` and add metrics/summary reporting more prominently.

### 2.7 Streaming and Usage Metadata

- Issue: Comments indicate callback usage hides detailed usage metadata for some providers.
- Recommendation: Centralize provider/model-specific callback configuration in `load_chat_model` with a capability matrix; ensure consistent `stream_usage` and usage collection. Consider explicit `include_usage` flags when supported.

### 2.8 fetch_url Tool Robustness

- Issues:
  - No concurrency/timeout caps per-URL; returns a large string rather than structured objects.
  - Ignores `robots.txt`/site politeness.
- Recommendations:
  - Add per-request timeouts and concurrency limits; return JSON array of `{ url, title, description, markdown }` objects.
  - Consider a `max_pages` and `max_bytes` guard.

### 2.9 Planning Tool Semantics

- Good: Enforces sequential blocking and single `in_progress` semantics via `PlanManager`.
- Recommendation: Add a small helper in `ToolManager` to always inject a `PlanManager` if missing to avoid misuse; ensure writer events also reflect plan updates for UI.

### 2.10 Recommendation Tool Write Path

- Issue: `_create` currently returns a stub message and does not persist to DB.
- Recommendation: Either fully implement the persistence path (commented), or explicitly mark the tool as read-only (get/delete only) in description to avoid confusion.

### 2.11 Minor Consistency Items

- Add consistent `config["run_name"]` values for tracing.
- Prefer `dict[str, T]` over plain `dict` for type clarity.
- Replace remaining `Any` where feasible with precise types (aligns with project preferences).

---

## 3) Suggested Refactors (Prioritized)

### P0 (Immediate)

1. Always include recent messages in LLM input

   - In `ContextManager.build_message_hierarchy`, append a bounded window of recent `instance_state.messages` when `cached_context` is None.
   - In `ConversationalAgent.reasoning_agent`, consider allowing message lists (`BaseMessage`) directly to the model, not only dicts.

2. Fix `check_permissions` update path

   - Add `update=state` when permission is granted.

3. Fix `executor.py` workspace scoping

   - Initialize `workspace = None`; wrap kubeconfig handling with a guard; keep everything under `if config:`.

4. Unify chart schemas

   - Remove duplicated enum and reuse `tools/shared_schema.py` in `create_chart.py`.

5. Normalize tool outputs
   - JSON-serialize all tool outputs; align writer event payloads across `report`, `dashboard`, `recommendation`.

### P1 (Near-term)

6. DB optimizer hardening

   - Correct joins, add dry-run, metrics, and safer drop logic.
   - Make optimization schedule configurable.

7. fetch_url improvements

   - Concurrency, timeouts, and structured JSON output; optional robots.txt compliance.

8. Observability
   - Standardize log levels and add trace IDs (`conversation_id`, `workspace_id`, `agent_name`) to logs and tool outputs.

### P2 (Mid-term)

9. Testing

   - Unit tests for: `ContextManager` caching logic; tool node routing; `DatabaseOptimizer` planners (dry-run); tool arg validation; `PlanManager` sequencing.

10. Configuration

- Centralize model/provider capabilities; expose `max_conversation_rounds`, caching TTLs, and planning defaults via env or settings.

---

## 4) Concrete Edit Suggestions

Below are targeted code changes to address critical issues. These are minimal and backward compatible.

### 4.1 check_permissions update

Change approved branch to include state update:

```12:18:backend/app/modules/multi_agents/common/nodes.py
            if permission_granted:
                return Command(goto="tools", update=state)
```

### 4.2 executor workspace scoping

Guard `workspace` usage and move kubeconfig into the `if config:` block:

```104:116:backend/app/modules/multi_agents/tools/executor.py
    if config:
        from app.modules.multi_agents.core import Configuration
        configuration = Configuration.from_runnable_config(config)
        workspace = configuration.workspace_config
        ...
        if workspace.kubeconfig:
            body["k8s_credentials"] = {"kubeconfig": str(workspace.kubeconfig)}
```

### 4.3 Include recent messages in LLM input

Extend `ContextManager.build_message_hierarchy` to include a small window of recent messages when `cached_context` is absent:

```132:144:backend/app/modules/multi_agents/core/context_manager.py
    def build_message_hierarchy(self, instance_state, system_prompt: str) -> list[dict]:
        system_cache_message = self.build_system_cache_message(system_prompt)
        cached_context = self.build_cached_context(instance_state)

        messages = [system_cache_message]
        if cached_context:
            messages.append(cached_context)
        else:
            # Fallback: include up to last 4 plain-text messages
            recent = []
            for msg in instance_state.messages[-4:]:
                text = format_for_context([msg])
                if text:
                    recent.append({"role": "user", "content": [{"type": "text", "text": text}]})
            messages.extend(recent)
        return messages
```

### 4.4 Unify chart enums/schemas

- In `create_chart.py`, remove the local `ChartType` and import from `tools/shared_schema.py`:

```1:18:backend/app/modules/multi_agents/tools/create_chart.py
from .shared_schema import ChartStructuredOutput, ChartAxis, ChartDataset, ChartType, SankeyLink, SankeyNode
```

---

## 5) Performance and Cost

- Caching
  - Keep 1h system cache TTL; consider 30m for long sessions.
  - Conversation cached context at 15m can be adaptive based on activity.
- Token usage
  - Limit fallback recent messages to 4–8; increase only when needed.
- Tool parallelism
  - Consider safe parallel tool calls for read-only tools (e.g., KB + fetch_url) if provider supports it.

---

## 6) Security & Permissions

- Console tools
  - Enforce `get_user_permission` before any write-permission commands; ensure the permission tool is part of `tool_permissions`.
- External requests
  - Limit `fetch_url` scope and add clear timeouts. Ensure user intent is explicit for external fetching.
- Executor
  - Log and audit scripts with `workspace_id` and hash; consider server-side allowlist for commands.

---

## 7) Testing & Quality Gates

- Add unit tests for:
  - `ContextManager.build_cached_context` and `build_message_hierarchy` fallback path
  - `check_permissions` branches
  - `create_chart` inputs and schema validation using `shared_schema`
  - `DatabaseOptimizer` (dry-run) to validate SQL generation and filtering by `schemaname`
  - `ToolManager.get_tools` composition precedence (builtin vs MCP vs connections)
- Add integration tests for Coordinator→Conversational→Tools happy paths.

---

## 8) Migration Notes

- After unifying chart schemas, adjust any callers expecting the local enum.
- When normalizing ToolMessage payloads to JSON, update any consumer deserializers/UI handlers accordingly.
- DB optimizer: roll out with dry-run first and log the plan before enabling delete operations.

---

## 9) Optional Enhancements

- Add trace context to `writer` events to simplify frontend correlation.
- Add a `capabilities` registry to `load_chat_model` to encapsulate provider-specific flags.
- Provide a `Tool IO contract` doc for all tool inputs/outputs.
