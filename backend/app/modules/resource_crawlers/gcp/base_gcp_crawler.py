# backend/app/modules/resource_crawlers/gcp/base_gcp_crawler.py
import json
import time
import uuid
from abc import abstractmethod
from functools import wraps
from typing import Callable, TypeVar

from google.api_core.exceptions import GoogleAPIError
from google.oauth2 import service_account
from sqlmodel import Session

from app.logger import logger
from app.models import Resource

from ..base_cloud_crawler import BaseCloudCrawler

T = TypeVar("T")


def gcp_retry(
    max_retries: int = 3,
    retry_delay: int = 5,
    retryable_errors: tuple[str, ...] = (
        "RESOURCE_EXHAUSTED",
        "UNAVAILABLE",
        "DEADLINE_EXCEEDED",
        "INTERNAL",
        "ABORTED",
    ),
) -> Callable[[Callable[..., T]], Callable[..., T]]:
    """
    Decorator for GCP API calls with exponential backoff retry logic.

    Args:
        max_retries: Maximum number of retry attempts (default: 3)
        retry_delay: Base delay between retries in seconds (default: 5)
        retryable_errors: Tuple of GCP error codes that should trigger a retry

    Returns:
        Decorated function with retry logic
    """

    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        @wraps(func)
        def wrapper(*args, **kwargs) -> T:  # type: ignore
            last_exception = None

            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except GoogleAPIError as e:
                    last_exception = e

                    # Check if this is a retryable error
                    if e.code in retryable_errors:
                        if attempt < max_retries - 1:  # Don't sleep on the last attempt
                            # Exponential backoff with jitter
                            delay = retry_delay * (2**attempt)
                            logger.warning(
                                f"GCP API call failed with {e.code}, retrying in {delay}s "
                                f"(attempt {attempt + 1}/{max_retries})"
                            )
                            time.sleep(delay)
                            continue

                    # Non-retryable error or max retries reached
                    logger.error(
                        f"GCP API call failed after {attempt + 1} attempts: {e.code} - {str(e)}"
                    )
                    raise
                except Exception as e:
                    # Non-GoogleAPIError exceptions are not retried
                    logger.error(f"Non-retryable error in GCP API call: {str(e)}")
                    raise

            # This should never be reached, but just in case
            if last_exception:
                raise last_exception

        return wrapper

    return decorator


class BaseGCPCrawler(BaseCloudCrawler):
    """GCP-specific base crawler implementation"""

    def __init__(
        self,
        workspace_id: uuid.UUID,
        project_id: str,
        service_account_key: str | None = None,
        region: str | None = None,
        max_retries: int = 3,
        retry_delay: int = 5,
    ):
        super().__init__(workspace_id, region, max_retries, retry_delay)

        self.project_id = project_id
        self.service_account_key = service_account_key
        self.include_stopped_resources = True  # Default to True for GCP
        self.credentials = None  # Initialize credentials attribute

        # Initialize GCP credentials and clients
        self._init_clients()

    def _init_clients(self):
        """Initialize GCP service clients with credentials"""
        try:
            if self.service_account_key:
                # Parse service account key JSON
                service_account_info = json.loads(self.service_account_key)
                credentials = service_account.Credentials.from_service_account_info(
                    service_account_info
                )
            else:
                # Use default credentials (Application Default Credentials)
                credentials = None
                logger.warning(
                    "No service account key provided, using default credentials"
                )

            # Store credentials for use in subclasses
            self.credentials = credentials

            logger.info(f"GCP clients initialized for project: {self.project_id}")

        except Exception as e:
            logger.error(f"Failed to initialize GCP clients: {str(e)}")
            raise

    def get_provider_name(self) -> str:
        """Return GCP as the provider name"""
        return "gcp"

    def validate_credentials(self) -> bool:
        """Validate GCP credentials by testing API access"""
        try:
            # Import here to avoid circular imports
            from google.cloud import compute_v1

            # Test with a simple API call to list zones
            client = compute_v1.ZonesClient(credentials=self.credentials)
            request = compute_v1.ListZonesRequest(project=self.project_id)

            # Try to list zones (this will fail if credentials are invalid)
            zones = client.list(request=request)
            # Just iterate once to trigger the API call
            list(zones)[:1]  # Get first item to trigger API call

            logger.info(
                f"GCP credentials validated successfully for project: {self.project_id}"
            )
            return True

        except Exception as e:
            logger.error(f"GCP credential validation failed: {str(e)}")
            return False

    @abstractmethod
    def crawl_resources_and_metrics(self, db: Session) -> list[Resource]:
        pass
