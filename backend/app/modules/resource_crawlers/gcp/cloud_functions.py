from google.cloud import functions_v2
from rich import print
from sqlmodel import Session

from app.logger import logger
from app.models import (
    CloudProvider,
    GCPResourceType,
    Resource,
    ResourceCategory,
    ResourceStatus,
)
from app.repositories.resources import ResourceRepository

from .base_gcp_crawler import BaseGCPCrawler


class CloudFunctionsCrawler(BaseGCPCrawler):
    """Crawler for GCP Cloud Functions"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def _crawl_cloud_functions(self) -> list[dict]:
        """Crawl Cloud Functions across all regions"""
        functions = []

        try:
            # Get all regions (using a default list for now)
            # TODO: Get regions from project or use a comprehensive list
            regions = [
                "us-central1",
                "us-east1",
                "us-west1",
                "europe-west1",
                "asia-southeast1",
                "asia-northeast1",
            ]

            for region in regions:
                try:
                    # List functions in each region
                    client = functions_v2.FunctionServiceClient(
                        credentials=self.credentials
                    )

                    # Format the parent string for the request
                    parent = f"projects/{self.project_id}/locations/{region}"

                    request = functions_v2.ListFunctionsRequest(parent=parent)

                    # Get the page iterator
                    page_result = client.list_functions(request=request)

                    for function in page_result:
                        # Debug: Print available fields for first function
                        if len(functions) == 0:
                            logger.info(f"Sample function fields: {dir(function)}")
                            print(f"Raw function: {function}")

                        # Convert to dict for now (we'll create Pydantic models later)
                        function_data = {
                            "name": function.name,
                            "description": function.description,
                            "state": function.state.name if function.state else None,
                            "environment": function.environment.name
                            if function.environment
                            else None,
                            "runtime": function.runtime,
                            "entry_point": function.entry_point,
                            "available_memory_mb": function.available_memory_mb,
                            "timeout_seconds": function.timeout_seconds,
                            "service_account_email": function.service_account_email,
                            "build_config": {
                                "runtime": function.build_config.runtime
                                if function.build_config
                                else None,
                                "entry_point": function.build_config.entry_point
                                if function.build_config
                                else None,
                                "source": {
                                    "storage_source": {
                                        "bucket": function.build_config.source.storage_source.bucket
                                        if function.build_config
                                        and function.build_config.source
                                        and function.build_config.source.storage_source
                                        else None,
                                        "object_": function.build_config.source.storage_source.object_
                                        if function.build_config
                                        and function.build_config.source
                                        and function.build_config.source.storage_source
                                        else None,
                                    }
                                    if function.build_config
                                    and function.build_config.source
                                    and function.build_config.source.storage_source
                                    else None,
                                }
                                if function.build_config
                                and function.build_config.source
                                else None,
                            }
                            if function.build_config
                            else None,
                            "service_config": {
                                "max_instance_count": function.service_config.max_instance_count
                                if function.service_config
                                else None,
                                "available_memory": function.service_config.available_memory
                                if function.service_config
                                else None,
                                "timeout_seconds": function.service_config.timeout_seconds
                                if function.service_config
                                else None,
                                "environment_variables": dict(
                                    function.service_config.environment_variables
                                )
                                if function.service_config
                                and function.service_config.environment_variables
                                else {},
                                "ingress_settings": function.service_config.ingress_settings.name
                                if function.service_config
                                and function.service_config.ingress_settings
                                else None,
                                "all_traffic_on_latest_revision": function.service_config.all_traffic_on_latest_revision
                                if function.service_config
                                else None,
                            }
                            if function.service_config
                            else None,
                            "labels": dict(function.labels) if function.labels else {},
                            "create_time": function.create_time.isoformat()
                            if function.create_time
                            else None,
                            "update_time": function.update_time.isoformat()
                            if function.update_time
                            else None,
                            "region": region,
                        }

                        functions.append(function_data)

                except Exception as e:
                    logger.error(
                        f"Error crawling functions in region {region}: {str(e)}"
                    )
                    continue

        except Exception as e:
            logger.error(
                f"Error crawling Cloud Functions for project {self.project_id}: {str(e)}"
            )
            raise

        return functions

    def _map_function_to_resource(self, function_data: dict) -> Resource:
        """Map Cloud Function data to Resource model"""

        # Extract labels as tags
        tags = function_data.get("labels", {})

        # Build configurations
        configurations = {
            "function_name": function_data.get("name", "").split("/")[-1],
            "description": function_data.get("description"),
            "state": function_data.get("state"),
            "environment": function_data.get("environment"),
            "runtime": function_data.get("runtime"),
            "entry_point": function_data.get("entry_point"),
            "available_memory_mb": function_data.get("available_memory_mb"),
            "timeout_seconds": function_data.get("timeout_seconds"),
            "service_account_email": function_data.get("service_account_email"),
            "build_config": function_data.get("build_config"),
            "service_config": function_data.get("service_config"),
            "create_time": function_data.get("create_time"),
            "update_time": function_data.get("update_time"),
        }

        # Build resource ID (GCP format)
        resource_id = function_data.get("name", "")

        return Resource(
            workspace_id=self.workspace_id,
            name=function_data.get("name", "").split("/")[-1],
            region=function_data.get("region"),
            configurations=configurations,
            type=GCPResourceType.CLOUD_FUNCTIONS,
            resource_id=resource_id,
            tags=tags,
            description=function_data.get("description")
            or f"Cloud Function with runtime {function_data.get('runtime')}",
            status=ResourceStatus.RUNNING
            if function_data.get("state") == "ACTIVE"
            else ResourceStatus.STOPPED,
            provider=CloudProvider.GCP,
            category=ResourceCategory.COMPUTE,
        )

    def crawl_resources_and_metrics(self, db: Session) -> list[Resource]:
        """Crawl all Cloud Functions resources"""
        all_resources = []

        logger.info(f"Starting Cloud Functions crawl for project {self.project_id}")

        functions = self._crawl_cloud_functions()

        for function_data in functions:
            try:
                resource = self._map_function_to_resource(function_data)
                db_resource = ResourceRepository.create_or_update(db, resource)
                all_resources.append(db_resource)
            except Exception as e:
                logger.error(
                    f"Error processing function {function_data.get('name', 'unknown')}: {str(e)}"
                )
                continue

        db.commit()
        logger.info(f"Crawled {len(all_resources)} Cloud Functions resources")

        return all_resources
