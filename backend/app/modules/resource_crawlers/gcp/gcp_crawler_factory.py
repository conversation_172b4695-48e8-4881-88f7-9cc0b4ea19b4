import uuid
from typing import Any

from app.models import GCPResourceType

from .base_gcp_crawler import BaseGCPCrawler
from .cloud_functions import CloudFunctionsCrawler
from .cloud_storage import CloudStorageCrawler
from .compute_engine import ComputeEngineCrawler


class GCPCrawlerFactory:
    """Factory for creating GCP resource crawlers"""

    @staticmethod
    def get_crawler(
        workspace_id: uuid.UUID,
        scan_type: GCPResourceType,
        project_id: str,
        service_account_key: str | None = None,
        region: str | None = None,
        max_retries: int = 3,
        retry_delay: int = 5,
    ) -> BaseGCPCrawler:
        """Create a GCP resource crawler based on the scan type"""

        common_args: dict[str, Any] = {
            "workspace_id": workspace_id,
            "project_id": project_id,
            "service_account_key": service_account_key,
            "region": region,
            "max_retries": max_retries,
            "retry_delay": retry_delay,
        }

        if scan_type == GCPResourceType.COMPUTE_ENGINE:
            return ComputeEngineCrawler(**common_args)
        elif scan_type == GCPResourceType.CLOUD_STORAGE:
            return CloudStorageCrawler(**common_args)
        elif scan_type == GCPResourceType.CLOUD_FUNCTIONS:
            return CloudFunctionsCrawler(**common_args)
        else:
            # TODO: Implement other GCP crawlers
            raise NotImplementedError(
                f"GCP crawler for {scan_type} not yet implemented"
            )
