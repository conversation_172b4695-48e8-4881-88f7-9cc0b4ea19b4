import uuid
from abc import abstractmethod

from sqlmodel import Session

from app.logger import logger
from app.models import Resource

from ..base_cloud_crawler import BaseCloudCrawler


class BaseAzureCrawler(BaseCloudCrawler):
    """Azure-specific base crawler implementation"""

    def __init__(
        self,
        workspace_id: uuid.UUID,
        subscription_id: str,
        tenant_id: str,
        client_id: str | None = None,
        client_secret: str | None = None,
        region: str | None = None,
        max_retries: int = 3,
        retry_delay: int = 5,
    ):
        super().__init__(workspace_id, region, max_retries, retry_delay)
        self.subscription_id = subscription_id
        self.tenant_id = tenant_id
        self.client_id = client_id
        self.client_secret = client_secret

    def get_provider_name(self) -> str:
        """Return Azure as the provider name"""
        return "azure"

    def validate_credentials(self) -> bool:
        """Validate Azure credentials - placeholder implementation"""
        # TODO: Implement Azure credential validation
        logger.warning("Azure credential validation not yet implemented")
        return True

    @abstractmethod
    def crawl_resources_and_metrics(self, db: Session) -> list[Resource]:
        pass
