import uuid

from app.models import AzureResourceType

from .base_azure_crawler import BaseAzureCrawler


class AzureCrawlerFactory:
    """Factory for creating Azure resource crawlers"""

    @staticmethod
    def get_crawler(
        workspace_id: uuid.UUID,
        scan_type: AzureResourceType,
        subscription_id: str,
        tenant_id: str,
        client_id: str | None = None,
        client_secret: str | None = None,
        region: str | None = None,
        max_retries: int = 3,
        retry_delay: int = 5,
    ) -> BaseAzureCrawler:
        """Create an Azure resource crawler based on the scan type"""
        # TODO: Implement specific Azure crawlers
        raise NotImplementedError(f"Azure crawler for {scan_type} not yet implemented")
