import uuid
from abc import ABC, abstractmethod

from sqlmodel import Session

from app.models import Resource


class BaseCloudCrawler(ABC):
    """Abstract base class for all cloud resource crawlers"""

    def __init__(
        self,
        workspace_id: uuid.UUID,
        region: str | None = None,
        max_retries: int = 3,
        retry_delay: int = 5,
    ):
        self.workspace_id = workspace_id
        self.region = region
        self.max_retries = max_retries
        self.retry_delay = retry_delay

    @abstractmethod
    def crawl_resources_and_metrics(self, db: Session) -> list[Resource]:
        """Crawl resources and metrics for this cloud provider"""
        pass

    @abstractmethod
    def get_provider_name(self) -> str:
        """Return the cloud provider name (aws, gcp, azure)"""
        pass

    @abstractmethod
    def validate_credentials(self) -> bool:
        """Validate the cloud provider credentials"""
        pass
