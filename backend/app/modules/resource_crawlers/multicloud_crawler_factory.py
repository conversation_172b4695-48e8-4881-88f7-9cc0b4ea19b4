import uuid

from app.models import (
    AWSResourceType,
    AzureResourceType,
    CloudProvider,
    GCPResourceType,
)

from .aws.crawler_factory import AWSCrawlerFactory
from .azure.azure_crawler_factory import AzureCrawlerFactory
from .base_cloud_crawler import BaseCloudCrawler
from .gcp.gcp_crawler_factory import GCPCrawlerFactory


class MultiCloudCrawlerFactory:
    """Main factory router for creating cloud-specific resource crawlers"""

    @staticmethod
    def get_aws_crawler(
        workspace_id: uuid.UUID,
        scan_type: AWSResourceType,
        **kwargs,
    ) -> BaseCloudCrawler:
        """Create an AWS resource crawler"""
        return AWSCrawlerFactory.get_crawler(
            workspace_id=workspace_id,
            scan_type=scan_type,
            **kwargs,
        )

    @staticmethod
    def get_gcp_crawler(
        workspace_id: uuid.UUID,
        scan_type: GCPResourceType,
        project_id: str,
        service_account_key: str | None = None,
        region: str | None = None,
        max_retries: int = 3,
        retry_delay: int = 5,
    ) -> BaseCloudCrawler:
        """Create a GCP resource crawler"""
        return GCPCrawlerFactory.get_crawler(
            workspace_id=workspace_id,
            scan_type=scan_type,
            project_id=project_id,
            service_account_key=service_account_key,
            region=region,
            max_retries=max_retries,
            retry_delay=retry_delay,
        )

    @staticmethod
    def get_azure_crawler(
        workspace_id: uuid.UUID,
        scan_type: AzureResourceType,
        subscription_id: str,
        tenant_id: str,
        client_id: str | None = None,
        client_secret: str | None = None,
        region: str | None = None,
        max_retries: int = 3,
        retry_delay: int = 5,
    ) -> BaseCloudCrawler:
        """Create an Azure resource crawler"""
        return AzureCrawlerFactory.get_crawler(
            workspace_id=workspace_id,
            scan_type=scan_type,
            subscription_id=subscription_id,
            tenant_id=tenant_id,
            client_id=client_id,
            client_secret=client_secret,
            region=region,
            max_retries=max_retries,
            retry_delay=retry_delay,
        )

    @staticmethod
    def get_crawler_for_provider(
        provider: CloudProvider, workspace_id: uuid.UUID, resource_type: str, **kwargs
    ) -> BaseCloudCrawler:
        """Factory method to get a crawler based on cloud provider and resource type"""
        if provider == CloudProvider.AWS:
            try:
                aws_type = AWSResourceType(resource_type)
                return MultiCloudCrawlerFactory.get_aws_crawler(
                    workspace_id=workspace_id, scan_type=aws_type, **kwargs
                )
            except ValueError:
                raise ValueError(f"Unsupported AWS resource type: {resource_type}")

        elif provider == CloudProvider.GCP:
            try:
                gcp_type = GCPResourceType(resource_type)
                return MultiCloudCrawlerFactory.get_gcp_crawler(
                    workspace_id=workspace_id, scan_type=gcp_type, **kwargs
                )
            except ValueError:
                raise ValueError(f"Unsupported GCP resource type: {resource_type}")

        elif provider == CloudProvider.AZURE:
            try:
                azure_type = AzureResourceType(resource_type)
                return MultiCloudCrawlerFactory.get_azure_crawler(
                    workspace_id=workspace_id, scan_type=azure_type, **kwargs
                )
            except ValueError:
                raise ValueError(f"Unsupported Azure resource type: {resource_type}")

        else:
            raise ValueError(f"Unsupported cloud provider: {provider}")

    @staticmethod
    def get_crawler_for_resource_type_string(
        provider: CloudProvider,
        workspace_id: uuid.UUID,
        resource_type_string: str,
        **kwargs,
    ) -> BaseCloudCrawler:
        """Helper method that accepts string resource types and converts them to appropriate enums"""
        if provider == CloudProvider.AWS:
            aws_type = AWSResourceType(resource_type_string)
            return MultiCloudCrawlerFactory.get_aws_crawler(
                workspace_id=workspace_id, scan_type=aws_type, **kwargs
            )
        elif provider == CloudProvider.GCP:
            gcp_type = GCPResourceType(resource_type_string)
            return MultiCloudCrawlerFactory.get_gcp_crawler(
                workspace_id=workspace_id, scan_type=gcp_type, **kwargs
            )
        elif provider == CloudProvider.AZURE:
            azure_type = AzureResourceType(resource_type_string)
            return MultiCloudCrawlerFactory.get_azure_crawler(
                workspace_id=workspace_id, scan_type=azure_type, **kwargs
            )
        else:
            raise ValueError(f"Unsupported cloud provider: {provider}")
