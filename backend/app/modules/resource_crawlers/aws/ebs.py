from typing import Any

from botocore.exceptions import ClientError
from sqlmodel import Session

from app.logger import logger
from app.models import (
    RESOURCE_TYPE_MAPPINGS,
    AWSResourceType,
    CloudProvider,
    Resource,
    ResourceStatus,
)
from app.repositories.resources import ResourceRepository

from .base_crawler import (
    BaseCrawler,
    ResourceStateMapper,
    aws_retry,
)


class EBSStateMapper(ResourceStateMapper):
    """Maps EBS volume states to ResourceStatus"""

    _STATE_MAPPING = {
        "creating": ResourceStatus.STARTING,
        "available": ResourceStatus.RUNNING,
        "in-use": ResourceStatus.RUNNING,
        "deleting": ResourceStatus.DELETED,
        "deleted": ResourceStatus.DELETED,
        "error": ResourceStatus.DELETED,
    }


class EBSResourceCrawler(BaseCrawler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.state_mapper = EBSStateMapper()
        self.ec2_client = self.session.client("ec2", region_name=self.region)

    @aws_retry()
    def _crawl_ebs_volumes(self) -> list[dict[str, Any]]:
        """Crawl EBS volumes in the specified region."""
        volumes = []

        paginator = self.ec2_client.get_paginator("describe_volumes")
        for page in paginator.paginate():
            for volume in page["Volumes"]:
                try:
                    # Get volume tags
                    tags = {tag["Key"]: tag["Value"] for tag in volume.get("Tags", [])}

                    # Get volume status
                    status_response = self.ec2_client.describe_volume_status(
                        VolumeIds=[volume["VolumeId"]]
                    )
                    status = (
                        status_response["VolumeStatuses"][0]
                        if status_response["VolumeStatuses"]
                        else {}
                    )

                    # Filter volumes based on include_stopped_resources setting
                    if not self.include_stopped_resources:
                        # Skip volumes in error state
                        if volume.get("State", "").lower() == "error":
                            continue

                    volume_data = {
                        "VolumeId": volume["VolumeId"],
                        "Size": volume["Size"],
                        "VolumeType": volume["VolumeType"],
                        "State": volume["State"],
                        "AvailabilityZone": volume["AvailabilityZone"],
                        "CreateTime": volume["CreateTime"],
                        "Encrypted": volume["Encrypted"],
                        "Iops": volume.get("Iops"),
                        "Throughput": volume.get("Throughput"),
                        "MultiAttachEnabled": volume.get("MultiAttachEnabled", False),
                        "Tags": tags,
                        "Status": status,
                    }
                    volumes.append(volume_data)
                except ClientError as e:
                    logger.exception(
                        f"Error getting details for volume {volume['VolumeId']}: {e}"
                    )
                    raise

        return volumes

    def _map_ebs_to_resource(self, volume: dict[str, Any]) -> Resource:
        """Map EBS volume data to a Resource object."""
        config = {
            "Size": volume["Size"],
            "VolumeType": volume["VolumeType"],
            "State": volume["State"],
            "AvailabilityZone": volume["AvailabilityZone"],
            "CreateTime": volume["CreateTime"].isoformat(),
            "Encrypted": volume["Encrypted"],
            "Iops": volume.get("Iops"),
            "Throughput": volume.get("Throughput"),
            "MultiAttachEnabled": volume["MultiAttachEnabled"],
            "Status": volume["Status"],
        }

        resource = Resource(
            workspace_id=self.workspace_id,
            name=volume["VolumeId"],
            region=self.region if self.region else "",
            type=AWSResourceType.EBS,
            resource_id=f"arn:aws:ec2:{self.region}:{self.aws_access_account_id}:volume/{volume['VolumeId']}",
            tags=volume["Tags"],
            description=f"{volume['VolumeType']} volume of {volume['Size']} GiB",
            configurations=config,
            status=self.state_mapper.map_state(volume["State"]),
            provider=CloudProvider.AWS,
            category=RESOURCE_TYPE_MAPPINGS[CloudProvider.AWS][
                AWSResourceType.EBS
            ].category,
        )
        return resource

    @aws_retry()
    def crawl_resources_and_metrics(self, session: Session) -> list[Resource]:
        """Crawl all EBS resources."""
        all_resources = []

        if not self.region:
            logger.error("No region specified for EBS crawler")
            return all_resources

        volumes = self._crawl_ebs_volumes()
        if volumes:
            resources = [self._map_ebs_to_resource(volume) for volume in volumes]
            for resource in resources:
                db_resource = ResourceRepository.create_or_update(session, resource)
                all_resources.append(db_resource)

        session.commit()
        logger.info(f"Crawled {len(all_resources)} EBS resources")
        return all_resources
