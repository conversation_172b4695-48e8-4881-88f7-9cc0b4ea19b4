import time
from dataclasses import dataclass
from datetime import datetime, timed<PERSON>ta
from enum import Enum
from typing import Any

from boto3.exceptions import Boto3<PERSON>rror
from botocore.exceptions import ClientError

from app.logger import logger


@dataclass
class MetricDefinition:
    name: str
    unit: str
    namespace: str = "AWS/EC2"
    period: int = 86400


class MetricStatistic(Enum):
    AVERAGE = "Average"
    SUM = "Sum"
    MINIMUM = "Minimum"
    MAXIMUM = "Maximum"
    SAMPLE_COUNT = "SampleCount"


class CloudWatchMetricsFetcher:
    CHUNK_SIZE = 100
    MAX_RETRIES = 3

    def __init__(self, session):
        self.session = session
        self.metrics_config = self._load_metrics_config()

    def _load_metrics_config(self) -> list[MetricDefinition]:
        """Load EC2 CloudWatch metric definitions from configuration.

        Returns:
            List of MetricDefinition objects containing metric name and unit.
        """
        return [
            # CPU Metrics
            MetricDefinition("CPUUtilization", "Percent"),
            MetricDefinition("CPUCreditUsage", "Count"),
            MetricDefinition("CPUCreditBalance", "Count"),
            MetricDefinition("CPUSurplusCreditBalance", "Count"),
            MetricDefinition("CPUSurplusCreditsCharged", "Count"),
            # Disk Metrics
            MetricDefinition("DiskReadOps", "Count"),
            MetricDefinition("DiskWriteOps", "Count"),
            MetricDefinition("DiskReadBytes", "Bytes"),
            MetricDefinition("DiskWriteBytes", "Bytes"),
            # Network Metrics
            MetricDefinition("NetworkIn", "Bytes"),
            MetricDefinition("NetworkOut", "Bytes"),
            MetricDefinition("NetworkPacketsIn", "Count"),
            MetricDefinition("NetworkPacketsOut", "Count"),
            # Status Check Metrics
            MetricDefinition("StatusCheckFailed", "Count"),
            MetricDefinition("StatusCheckFailed_Instance", "Count"),
            MetricDefinition("StatusCheckFailed_System", "Count"),
            # EBS Metrics
            MetricDefinition("EBSReadOps", "Count"),
            MetricDefinition("EBSWriteOps", "Count"),
            MetricDefinition("EBSReadBytes", "Bytes"),
            MetricDefinition("EBSWriteBytes", "Bytes"),
            MetricDefinition("EBSIOBalance%", "Percent"),
            MetricDefinition("EBSByteBalance%", "Percent"),
            # Memory Metrics (Requires CloudWatch agent)
            MetricDefinition("MemoryUtilization", "Percent"),
            MetricDefinition("MemoryUsed", "Bytes"),
            MetricDefinition("MemoryAvailable", "Bytes"),
            # Instance Metrics
            MetricDefinition("MetadataNoToken", "Count"),
        ]

    def _create_metric_query(
        self, metric: MetricDefinition, instance_id: str, query_id: int
    ) -> dict[str, Any]:
        """Create a single metric query."""
        return {
            "Id": f"metric_{query_id}",
            "MetricStat": {
                "Metric": {
                    "Namespace": metric.namespace,
                    "MetricName": metric.name,
                    "Dimensions": [{"Name": "InstanceId", "Value": instance_id}],
                },
                "Period": metric.period,
                "Stat": MetricStatistic.AVERAGE.value,
            },
            "ReturnData": True,
        }

    def _handle_cloudwatch_error(self, error: ClientError, retry_count: int) -> None:
        """Handle CloudWatch API errors with specific error handling logic."""
        if error.response["Error"]["Code"] == "Throttling":
            if retry_count < self.MAX_RETRIES:
                time.sleep(2**retry_count)  # Exponential backoff
                return
        elif error.response["Error"]["Code"] == "InvalidParameterValue":
            raise Exception(f"Invalid parameter: {error}")
        raise Exception(f"CloudWatch API error: {error}")

    def _process_metric_response(
        self,
        response: dict[str, Any],
        instance_ids: list[str],
        metric: MetricDefinition,
    ) -> list[dict[str, Any]]:
        """Process and validate CloudWatch API response."""
        processed_metrics = []

        for i, instance_id in enumerate(instance_ids):
            try:
                metric_data = response["MetricDataResults"][i]
                if not metric_data.get("Values"):
                    continue

                processed_metrics.extend(
                    [
                        {
                            "InstanceId": instance_id,
                            "MetricName": metric.name,
                            "Value": float(value),
                            "Unit": metric.unit,
                            "Timestamp": timestamp,
                        }
                        for value, timestamp in zip(
                            metric_data["Values"],
                            metric_data["Timestamps"],
                            strict=False,
                        )
                        if value is not None
                    ]
                )
            except (KeyError, IndexError, ValueError) as e:
                logger.error(
                    f"Error processing metric data for instance {instance_id}: {e}"
                )
                continue

        return processed_metrics

    def fetch_metrics(
        self, region: str, instance_ids: list[str]
    ) -> list[dict[str, Any]]:
        """Main method to fetch CloudWatch metrics for given EC2 instances."""
        cloudwatch = self.session.client("cloudwatch", region_name=region)
        end_time = datetime.utcnow()
        start_time = end_time - timedelta(minutes=30)
        all_metrics = []

        # Process instances in chunks
        for instance_chunk in self._chunk_list(instance_ids, self.CHUNK_SIZE):
            all_metrics.extend(
                self._fetch_chunk_metrics(
                    cloudwatch, instance_chunk, start_time, end_time
                )
            )

        logger.info(f"Successfully collected {len(all_metrics)} metrics from {region}")
        return all_metrics

    def _chunk_list(self, items: list[Any], chunk_size: int) -> list[list[Any]]:
        """Split a list into chunks of specified size."""
        return [items[i : i + chunk_size] for i in range(0, len(items), chunk_size)]

    def _fetch_chunk_metrics(
        self,
        cloudwatch,
        instance_chunk: list[str],
        start_time: datetime,
        end_time: datetime,
    ) -> list[dict[str, Any]]:
        """Fetch metrics for a chunk of instances."""
        chunk_metrics = []

        for metric in self.metrics_config:
            metric_queries = [
                self._create_metric_query(metric, instance_id, i)
                for i, instance_id in enumerate(instance_chunk)
            ]

            try:
                response = self._make_cloudwatch_request(
                    cloudwatch, metric_queries, start_time, end_time
                )
                chunk_metrics.extend(
                    self._process_metric_response(response, instance_chunk, metric)
                )
            except Exception as e:
                logger.error(f"Failed to fetch metric {metric.name}: {e}")
                continue

        return chunk_metrics

    def _make_cloudwatch_request(
        self,
        cloudwatch,
        metric_queries: list[dict[str, Any]],
        start_time: datetime,
        end_time: datetime,
    ) -> dict[str, Any]:
        """Make CloudWatch API request with retries."""
        for retry_count in range(self.MAX_RETRIES):
            try:
                return cloudwatch.get_metric_data(
                    MetricDataQueries=metric_queries,
                    StartTime=start_time,
                    EndTime=end_time,
                )
            except ClientError as e:
                self._handle_cloudwatch_error(e, retry_count)
            except Boto3Error as e:
                logger.error(f"Boto3 error: {e}")
                raise Exception(f"Boto3 error: {e}")

        raise Exception("Max retries exceeded")
