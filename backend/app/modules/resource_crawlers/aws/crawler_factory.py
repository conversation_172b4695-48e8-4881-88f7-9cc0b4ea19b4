import uuid

from app.models import AWSResourceType

from .app_runner import AppRunnerResourceCrawler
from .backup import BackupResourceCrawler
from .base_crawler import BaseCrawler
from .batch import BatchResourceCrawler
from .cloudformation import CloudFormationCrawler
from .documentdb import DocumentDBResourceCrawler
from .dynamodb import DynamoDBResourceCrawler
from .ebs import EBSResourceCrawler
from .ec2 import EC2ResourceCrawler
from .ec2_auto_scaling import EC2AutoScalingResourceCrawler
from .ecs import ECSResourceCrawler
from .efs import EFSResourceCrawler
from .eks import EKSResourceCrawler
from .elastic_beanstalk import ElasticBeanstalkResourceCrawler
from .elasticache import ElastiCacheResourceCrawler
from .elb import ELBResourceCrawler
from .lambda_ import LambdaResourceCrawler
from .neptune import NeptuneResourceCrawler
from .opensearch import OpenSearchResourceCrawler
from .rds import RDSResourceCrawler
from .redshift import RedshiftResourceCrawler
from .s3 import S3ResourceCrawler
from .vpc import VPCCrawler


class AWSCrawlerFactory:
    @staticmethod
    def get_crawler(
        workspace_id: uuid.UUID,
        scan_type: AWSResourceType,
        **kwargs,
    ) -> BaseCrawler:
        common_args = {
            "workspace_id": workspace_id,
            **kwargs,
        }

        if scan_type == AWSResourceType.RDS:
            return RDSResourceCrawler(**common_args)
        elif scan_type == AWSResourceType.EC2:
            return EC2ResourceCrawler(**common_args)
        elif scan_type == AWSResourceType.VPC:
            return VPCCrawler(**common_args)
        elif scan_type == AWSResourceType.CLOUDFORMATION:
            return CloudFormationCrawler(**common_args)
        elif scan_type == AWSResourceType.ECS:
            return ECSResourceCrawler(**common_args)
        elif scan_type == AWSResourceType.EKS:
            return EKSResourceCrawler(**common_args)
        elif scan_type == AWSResourceType.REDSHIFT:
            return RedshiftResourceCrawler(**common_args)
        elif scan_type == AWSResourceType.LAMBDA:
            return LambdaResourceCrawler(**common_args)
        elif scan_type == AWSResourceType.S3:
            return S3ResourceCrawler(**common_args)
        elif scan_type == AWSResourceType.DYNAMODB:
            return DynamoDBResourceCrawler(**common_args)
        elif scan_type == AWSResourceType.ELB:
            return ELBResourceCrawler(**common_args)
        elif scan_type == AWSResourceType.EBS:
            return EBSResourceCrawler(**common_args)
        elif scan_type == AWSResourceType.EFS:
            return EFSResourceCrawler(**common_args)
        elif scan_type == AWSResourceType.BATCH:
            return BatchResourceCrawler(**common_args)
        elif scan_type == AWSResourceType.ELASTIC_BEANSTALK:
            return ElasticBeanstalkResourceCrawler(**common_args)
        elif scan_type == AWSResourceType.APP_RUNNER:
            return AppRunnerResourceCrawler(**common_args)
        elif scan_type == AWSResourceType.ELASTICACHE:
            return ElastiCacheResourceCrawler(**common_args)
        elif scan_type == AWSResourceType.EC2_AUTO_SCALING:
            return EC2AutoScalingResourceCrawler(**common_args)
        elif scan_type == AWSResourceType.NEPTUNE:
            return NeptuneResourceCrawler(**common_args)
        elif scan_type == AWSResourceType.DOCUMENTDB:
            return DocumentDBResourceCrawler(**common_args)
        elif scan_type == AWSResourceType.OPENSEARCH:
            return OpenSearchResourceCrawler(**common_args)
        elif scan_type == AWSResourceType.BACKUP:
            return BackupResourceCrawler(**common_args)
        else:
            raise ValueError(f"Unsupported scan type: {scan_type}")
