from typing import Any

from botocore.exceptions import ClientError
from sqlmodel import Session

from app.logger import logger
from app.models import (
    RESOURCE_TYPE_MAPPINGS,
    AWSResourceType,
    CloudProvider,
    Resource,
    ResourceStatus,
)
from app.repositories.resources import ResourceRepository

from .base_crawler import (
    BaseCrawler,
    ResourceStateMapper,
    aws_retry,
)


class RedshiftStateMapper(ResourceStateMapper):
    """Maps Redshift cluster states to ResourceStatus"""

    _STATE_MAPPING = {
        "creating": ResourceStatus.STARTING,
        "available": ResourceStatus.RUNNING,
        "deleting": ResourceStatus.DELETED,
        "deleted": ResourceStatus.DELETED,
        "final-snapshot": ResourceStatus.STOPPED,
        "hardware-failure": ResourceStatus.STOPPED,
        "incompatible-hsm": ResourceStatus.STOPPED,
        "incompatible-network": ResourceStatus.STOPPED,
        "incompatible-parameters": ResourceStatus.STOPPED,
        "incompatible-restore": ResourceStatus.STOPPED,
        "modifying": ResourceStatus.STARTING,
        "rebooting": ResourceStatus.STARTING,
        "renaming": ResourceStatus.STARTING,
        "resizing": ResourceStatus.STARTING,
        "rotating-keys": ResourceStatus.STARTING,
        "storage-full": ResourceStatus.STOPPED,
        "updating-hsm": ResourceStatus.STARTING,
    }


class RedshiftResourceCrawler(BaseCrawler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.state_mapper = RedshiftStateMapper()

    def _crawl_redshift_clusters(self, region: str) -> list[dict[str, Any]]:
        """Crawl Redshift clusters in a specific region."""
        redshift_client = self.session.client("redshift", region_name=region)
        clusters = []
        try:
            paginator = redshift_client.get_paginator("describe_clusters")
            for page in paginator.paginate():
                page_clusters = page["Clusters"]

                # Filter clusters based on include_stopped_resources setting
                if not self.include_stopped_resources:
                    # Only include available clusters, exclude paused/incompatible states
                    page_clusters = [
                        cluster
                        for cluster in page_clusters
                        if cluster.get("ClusterStatus", "").lower()
                        not in [
                            "paused",
                            "pausing",
                            "incompatible-parameters",
                            "incompatible-network",
                            "hardware-failure",
                            "storage-full",
                        ]
                    ]

                clusters.extend(page_clusters)
        except ClientError as e:
            logger.exception(f"Error crawling Redshift clusters in {region}: {e}")
        return clusters

    def _map_redshift_to_resource(
        self, cluster: dict[str, Any], region: str
    ) -> Resource:
        """Map Redshift cluster data to a Resource object."""
        tags = {tag["Key"]: tag["Value"] for tag in cluster.get("Tags", [])}
        config = {
            "cluster_identifier": cluster["ClusterIdentifier"],
            "node_type": cluster["NodeType"],
            "number_of_nodes": cluster["NumberOfNodes"],
            "cluster_status": cluster["ClusterStatus"],
            "master_username": cluster["MasterUsername"],
            "db_name": cluster.get("DBName"),
            "endpoint": cluster.get("Endpoint", {}),
            "cluster_create_time": cluster["ClusterCreateTime"].isoformat(),
            "automated_snapshot_retention_period": cluster.get(
                "AutomatedSnapshotRetentionPeriod"
            ),
            "manual_snapshot_retention_period": cluster.get(
                "ManualSnapshotRetentionPeriod"
            ),
            "cluster_security_groups": cluster.get("ClusterSecurityGroups", []),
            "vpc_security_groups": cluster.get("VpcSecurityGroups", []),
            "cluster_parameter_groups": cluster.get("ClusterParameterGroups", []),
            "cluster_subnet_group_name": cluster.get("ClusterSubnetGroupName"),
            "availability_zone": cluster.get("AvailabilityZone"),
            "preferred_maintenance_window": cluster.get("PreferredMaintenanceWindow"),
            "pending_modified_values": cluster.get("PendingModifiedValues", {}),
            "cluster_version": cluster.get("ClusterVersion"),
            "allow_version_upgrade": cluster.get("AllowVersionUpgrade", False),
            "encrypted": cluster.get("Encrypted", False),
            "restore_status": cluster.get("RestoreStatus", {}),
            "data_transfer_progress": cluster.get("DataTransferProgress", {}),
            "hsm_status": cluster.get("HsmStatus", {}),
            "cluster_snapshot_copy_status": cluster.get(
                "ClusterSnapshotCopyStatus", {}
            ),
            "cluster_public_key": cluster.get("ClusterPublicKey"),
            "cluster_nodes": cluster.get("ClusterNodes", []),
            "elastic_ip_status": cluster.get("ElasticIpStatus", {}),
            "cluster_revision_number": cluster.get("ClusterRevisionNumber"),
            "tags": tags,
        }

        resource = Resource(
            workspace_id=self.workspace_id,
            name=cluster["ClusterIdentifier"],
            region=region if region else "",
            type=AWSResourceType.REDSHIFT,
            resource_id=f"arn:aws:redshift:{region}:{self.aws_access_account_id}:cluster:{cluster['ClusterIdentifier']}",
            tags=tags,
            description=(
                f"Redshift cluster with {cluster['NumberOfNodes']} nodes of type {cluster['NodeType']}"
            ),
            configurations=config,
            status=self.state_mapper.map_state(cluster["ClusterStatus"]),
            provider=CloudProvider.AWS,
            category=RESOURCE_TYPE_MAPPINGS[CloudProvider.AWS][
                AWSResourceType.REDSHIFT
            ].category,
        )
        return resource

    @aws_retry()
    def crawl_resources_and_metrics(self, session: Session) -> list[Resource]:
        """Crawl all Redshift resources."""
        all_resources = []

        if not self.region:
            logger.error("No region specified for Redshift crawler")
            return all_resources

        clusters = self._crawl_redshift_clusters(self.region)
        if clusters:
            resources = [
                self._map_redshift_to_resource(cluster, self.region)
                for cluster in clusters
            ]

            for resource in resources:
                db_resource = ResourceRepository.create_or_update(session, resource)
                all_resources.append(db_resource)

        session.commit()

        logger.info(f"Crawled {len(all_resources)} Redshift clusters")

        return all_resources
