from typing import Any

import boto3
from botocore.exceptions import ClientError
from sqlmodel import Session

from app.logger import logger
from app.models import (
    RESOURCE_TYPE_MAPPINGS,
    AWSResourceType,
    CloudProvider,
    Resource,
    ResourceStatus,
)
from app.repositories.resources import ResourceRepository

from .base_crawler import (
    BaseCrawler,
    ResourceStateMapper,
    aws_retry,
)


class OpenSearchStateMapper(ResourceStateMapper):
    """Maps OpenSearch domain states to ResourceStatus"""

    _STATE_MAPPING = {
        "Active": ResourceStatus.RUNNING,
        "Processing": ResourceStatus.STARTING,
        "NotAvailable": ResourceStatus.STOPPED,
        "Deleted": ResourceStatus.DELETED,
        "Failed": ResourceStatus.STOPPED,
        "UpgradeProcessing": ResourceStatus.STARTING,
        "UpgradeFailed": ResourceStatus.STOPPED,
        "UpgradeSucceeded": ResourceStatus.RUNNING,
        "UpgradeRollbackSucceeded": ResourceStatus.RUNNING,
        "UpgradeRollbackFailed": ResourceStatus.STOPPED,
        "UpgradeRollbackProcessing": ResourceStatus.STARTING,
    }


class OpenSearchResourceCrawler(BaseCrawler):
    """Crawler for OpenSearch resources"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.state_mapper = OpenSearchStateMapper()
        self.client = boto3.client(
            "opensearch",
            region_name=self.region,
            aws_access_key_id=self.aws_access_key_id,
            aws_secret_access_key=self.aws_secret_access_key,
        )

    @aws_retry()
    def crawl_resources_and_metrics(self, session: Session) -> list[Resource]:
        """Crawl OpenSearch resources"""
        all_resources = []

        if not self.region:
            logger.error("No region specified for OpenSearch crawler")
            return all_resources

        try:
            # Get all domains
            response = self.client.list_domain_names()
            domains = response["DomainNames"]

            for domain in domains:
                try:
                    # Get domain details
                    domain_details = self.client.describe_domain(
                        DomainName=domain["DomainName"]
                    )["DomainStatus"]

                    # Filter domains based on include_stopped_resources setting
                    if not self.include_stopped_resources:
                        # Skip domains that are not available or have failed
                        if (
                            domain_details.get("Processing", False)
                            or domain_details.get("Deleted", False)
                            or not domain_details.get("Created", True)
                        ):
                            continue

                    # Get domain tags
                    tags = self.client.list_tags(ARN=domain_details["ARN"])["TagList"]

                    resource = self._map_domain_to_resource(domain_details, tags)
                    db_resource = ResourceRepository.create_or_update(session, resource)
                    all_resources.append(db_resource)
                except ClientError as e:
                    logger.exception(
                        f"Error getting details for domain {domain['DomainName']}: {e}"
                    )
                    continue

            session.commit()
            logger.info(f"Crawled {len(all_resources)} OpenSearch resources")
            return all_resources

        except ClientError as e:
            logger.exception(f"Error crawling OpenSearch resources: {e}")
            raise

    def _map_domain_to_resource(
        self, domain: dict[str, Any], tags: list[dict[str, str]]
    ) -> Resource:
        """Map OpenSearch domain data to a Resource object."""
        config = {
            "domain_name": domain["DomainName"],
            "domain_id": domain["DomainId"],
            "arn": domain["ARN"],
            "created": domain.get("Created", False),
            "deleted": domain.get("Deleted", False),
            "endpoint": domain.get("Endpoints", {}),
            "processing": domain.get("Processing", False),
            "upgrade_processing": domain.get("UpgradeProcessing", False),
            "engine_version": domain.get("EngineVersion", {}),
            "cluster_config": domain.get("ClusterConfig", {}),
            "ebs_options": domain.get("EBSOptions", {}),
            "access_policies": domain.get("AccessPolicies"),
            "snapshot_options": domain.get("SnapshotOptions", {}),
            "vpc_options": domain.get("VPCOptions", {}),
            "cognito_options": domain.get("CognitoOptions", {}),
            "encryption_at_rest_options": domain.get("EncryptionAtRestOptions", {}),
            "node_to_node_encryption_options": domain.get(
                "NodeToNodeEncryptionOptions", {}
            ),
            "advanced_options": domain.get("AdvancedOptions", {}),
            "log_publishing_options": domain.get("LogPublishingOptions", {}),
            "service_software_options": domain.get("ServiceSoftwareOptions", {}),
            "domain_endpoint_options": domain.get("DomainEndpointOptions", {}),
            "advanced_security_options": domain.get("AdvancedSecurityOptions", {}),
            "auto_tune_options": domain.get("AutoTuneOptions", {}),
            "change_progress_details": domain.get("ChangeProgressDetails", {}),
        }

        return Resource(
            workspace_id=self.workspace_id,
            name=domain["DomainName"],
            region=self.region if self.region else "",
            type=AWSResourceType.OPENSEARCH,
            resource_id=domain["ARN"],
            tags={tag["Key"]: tag["Value"] for tag in tags},
            description=f"OpenSearch domain running {domain.get('EngineVersion', {}).get('OpenSearchVersion', 'unknown')}",
            configurations=config,
            status=self.state_mapper.map_state(domain.get("Processing", "Active")),
            provider=CloudProvider.AWS,
            category=RESOURCE_TYPE_MAPPINGS[CloudProvider.AWS][
                AWSResourceType.OPENSEARCH
            ].category,
        )
