from typing import Any

import boto3
from botocore.exceptions import ClientError
from sqlmodel import Session

from app.logger import logger
from app.models import (
    RESOURCE_TYPE_MAPPINGS,
    AWSResourceType,
    CloudProvider,
    Resource,
    ResourceStatus,
)
from app.repositories.resources import ResourceRepository

from .base_crawler import (
    BaseCrawler,
    ResourceStateMapper,
    aws_retry,
)


class S3StateMapper(ResourceStateMapper):
    """Maps S3 bucket states to ResourceStatus"""

    _STATE_MAPPING = {
        "enabled": ResourceStatus.RUNNING,
        "disabled": ResourceStatus.STOPPED,
        "suspended": ResourceStatus.STOPPED,
        "deleting": ResourceStatus.DELETED,
    }


class S3ResourceCrawler(BaseCrawler):
    """Crawler for S3 resources"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.state_mapper = S3StateMapper()
        self.client = boto3.client(
            "s3",
            region_name=self.region,
            aws_access_key_id=self.aws_access_key_id,
            aws_secret_access_key=self.aws_secret_access_key,
        )

    @aws_retry()
    def crawl_resources_and_metrics(self, session: Session) -> list[Resource]:
        """Crawl S3 resources"""
        all_resources = []

        if not self.region:
            logger.error("No region specified for S3 crawler")
            return all_resources

        try:
            # Get all buckets
            response = self.client.list_buckets()
            for bucket in response["Buckets"]:
                try:
                    # Get bucket details
                    location = self.client.get_bucket_location(Bucket=bucket["Name"])[
                        "LocationConstraint"
                    ]
                    if location is None:
                        location = "us-east-1"

                    # Get bucket tags
                    try:
                        tags = self.client.get_bucket_tagging(Bucket=bucket["Name"])[
                            "TagSet"
                        ]
                    except ClientError:
                        tags = []

                    # Get bucket versioning
                    try:
                        versioning = self.client.get_bucket_versioning(
                            Bucket=bucket["Name"]
                        )
                    except ClientError:
                        versioning = {}

                    # Get bucket encryption
                    try:
                        encryption = self.client.get_bucket_encryption(
                            Bucket=bucket["Name"]
                        )
                    except ClientError:
                        encryption = {}

                    # Get bucket lifecycle
                    try:
                        lifecycle = self.client.get_bucket_lifecycle_configuration(
                            Bucket=bucket["Name"]
                        )
                    except ClientError:
                        lifecycle = {}

                    # Get bucket policy
                    try:
                        policy = self.client.get_bucket_policy(Bucket=bucket["Name"])
                    except ClientError:
                        policy = {}

                    # Filter buckets based on include_stopped_resources setting
                    bucket_status = versioning.get("Status", "Disabled").lower()
                    if not self.include_stopped_resources:
                        # Skip buckets that are disabled or suspended based on _STATE_MAPPING
                        if bucket_status in ["disabled", "suspended"]:
                            continue

                    resource = self._map_bucket_to_resource(
                        bucket,
                        location,
                        tags,
                        versioning,
                        encryption,
                        lifecycle,
                        policy,
                    )
                    db_resource = ResourceRepository.create_or_update(session, resource)
                    all_resources.append(db_resource)
                except ClientError as e:
                    logger.error(
                        f"Error getting details for bucket {bucket['Name']}: {str(e)}"
                    )
                    continue

            session.commit()
            logger.info(f"Crawled {len(all_resources)} S3 resources")
            return all_resources

        except ClientError as e:
            logger.error(f"Error crawling S3 resources: {str(e)}")
            raise

    def _map_bucket_to_resource(
        self,
        bucket: dict[str, Any],
        location: str,
        tags: list[dict[str, str]],
        versioning: dict[str, Any],
        encryption: dict[str, Any],
        lifecycle: dict[str, Any],
        policy: dict[str, Any],
    ) -> Resource:
        """Map S3 bucket data to a Resource object."""
        config = {
            "bucket_name": bucket["Name"],
            "creation_date": bucket["CreationDate"].isoformat(),
            "location": location,
            "versioning": versioning.get("Status", "Disabled"),
            "encryption": encryption.get("ServerSideEncryptionConfiguration", {}),
            "lifecycle_rules": lifecycle.get("Rules", []),
            "policy": policy.get("Policy", {}),
        }

        return Resource(
            workspace_id=self.workspace_id,
            name=bucket["Name"],
            region=location,
            type=AWSResourceType.S3,
            resource_id=f"arn:aws:s3:::{bucket['Name']}",
            tags={tag["Key"]: tag["Value"] for tag in tags},
            description=f"S3 bucket created on {bucket['CreationDate'].strftime('%Y-%m-%d')}",
            configurations=config,
            status=self.state_mapper.map_state(versioning.get("Status", "Disabled")),
            provider=CloudProvider.AWS,
            category=RESOURCE_TYPE_MAPPINGS[CloudProvider.AWS][
                AWSResourceType.S3
            ].category,
        )
