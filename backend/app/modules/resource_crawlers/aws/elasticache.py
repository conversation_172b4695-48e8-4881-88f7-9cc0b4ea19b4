from typing import Any

import boto3
from botocore.exceptions import ClientError
from sqlmodel import Session

from app.logger import logger
from app.models import (
    RESOURCE_TYPE_MAPPINGS,
    AWSResourceType,
    CloudProvider,
    Resource,
    ResourceStatus,
)
from app.repositories.resources import ResourceRepository

from .base_crawler import (
    BaseCrawler,
    ResourceStateMapper,
    aws_retry,
)


class ElastiCacheStateMapper(ResourceStateMapper):
    """Maps ElastiCache cluster states to ResourceStatus"""

    _STATE_MAPPING = {
        "creating": ResourceStatus.STARTING,
        "available": ResourceStatus.RUNNING,
        "modifying": ResourceStatus.STARTING,
        "deleting": ResourceStatus.DELETED,
        "deleted": ResourceStatus.DELETED,
        "incompatible-network": ResourceStatus.STOPPED,
        "incompatible-parameters": ResourceStatus.STOPPED,
        "incompatible-restore": ResourceStatus.STOPPED,
        "restore-failed": ResourceStatus.STOPPED,
        "backup-restoring": ResourceStatus.STARTING,
        "snapshotting": ResourceStatus.RUNNING,
        "rebooting": ResourceStatus.STARTING,
        "maintenance": ResourceStatus.STARTING,
        "failed": ResourceStatus.STOPPED,
    }


class ElastiCacheResourceCrawler(BaseCrawler):
    """Crawler for ElastiCache resources"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.state_mapper = ElastiCacheStateMapper()
        self.client = boto3.client(
            "elasticache",
            region_name=self.region,
            aws_access_key_id=self.aws_access_key_id,
            aws_secret_access_key=self.aws_secret_access_key,
        )

    @aws_retry()
    def crawl_resources_and_metrics(self, session: Session) -> list[Resource]:
        """Crawl ElastiCache resources"""
        all_resources = []

        if not self.region:
            logger.error("No region specified for ElastiCache crawler")
            return all_resources

        try:
            # Get all clusters
            paginator = self.client.get_paginator("describe_cache_clusters")
            for page in paginator.paginate():
                clusters = page["CacheClusters"]

                # Filter clusters based on include_stopped_resources setting
                if not self.include_stopped_resources:
                    # Only include available clusters, exclude failed/incompatible states
                    clusters = [
                        cluster
                        for cluster in clusters
                        if cluster.get("CacheClusterStatus", "").lower()
                        not in [
                            "failed",
                            "incompatible-network",
                            "incompatible-parameters",
                            "incompatible-restore",
                            "restore-failed",
                        ]
                    ]

                for cluster in clusters:
                    try:
                        # Get cluster tags
                        tags = self.client.list_tags_for_resource(
                            ResourceName=cluster["ARN"]
                        )["TagList"]

                        resource = self._map_cluster_to_resource(cluster, tags)
                        db_resource = ResourceRepository.create_or_update(
                            session, resource
                        )
                        all_resources.append(db_resource)
                    except ClientError as e:
                        logger.exception(
                            f"Error getting details for cluster {cluster['CacheClusterId']}: {e}"
                        )
                        continue

            session.commit()
            logger.info(f"Crawled {len(all_resources)} ElastiCache resources")
            return all_resources

        except ClientError as e:
            logger.exception(f"Error crawling ElastiCache resources: {e}")
            raise

    def _map_cluster_to_resource(
        self, cluster: dict[str, Any], tags: list[dict[str, str]]
    ) -> Resource:
        """Map ElastiCache cluster data to a Resource object."""
        config = {
            "cache_cluster_id": cluster["CacheClusterId"],
            "engine": cluster["Engine"],
            "engine_version": cluster["EngineVersion"],
            "cache_node_type": cluster["CacheNodeType"],
            "num_cache_nodes": cluster["NumCacheNodes"],
            "status": cluster["CacheClusterStatus"],
            "port": cluster.get("Port"),
            "preferred_availability_zone": cluster.get("PreferredAvailabilityZone"),
            "preferred_maintenance_window": cluster.get("PreferredMaintenanceWindow"),
            "replication_group_id": cluster.get("ReplicationGroupId"),
            "snapshot_retention_limit": cluster.get("SnapshotRetentionLimit"),
            "snapshot_window": cluster.get("SnapshotWindow"),
            "auth_token_enabled": cluster.get("AuthTokenEnabled", False),
            "transit_encryption_enabled": cluster.get(
                "TransitEncryptionEnabled", False
            ),
            "at_rest_encryption_enabled": cluster.get("AtRestEncryptionEnabled", False),
            "cache_nodes": cluster.get("CacheNodes", []),
            "cache_parameter_group": cluster.get("CacheParameterGroup", {}),
            "cache_subnet_group": cluster.get("CacheSubnetGroup", {}),
            "cache_security_groups": cluster.get("CacheSecurityGroups", []),
            "notification_configuration": cluster.get("NotificationConfiguration", {}),
        }

        return Resource(
            workspace_id=self.workspace_id,
            name=cluster["CacheClusterId"],
            region=self.region if self.region else "",
            type=AWSResourceType.ELASTICACHE,
            resource_id=cluster["ARN"],
            tags={tag["Key"]: tag["Value"] for tag in tags},
            description=f"ElastiCache cluster running {cluster['Engine']} {cluster['EngineVersion']}",
            configurations=config,
            status=self.state_mapper.map_state(cluster["CacheClusterStatus"]),
            provider=CloudProvider.AWS,
            category=RESOURCE_TYPE_MAPPINGS[CloudProvider.AWS][
                AWSResourceType.ELASTICACHE
            ].category,
        )
