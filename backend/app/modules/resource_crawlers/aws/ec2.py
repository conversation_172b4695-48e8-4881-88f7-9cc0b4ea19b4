from typing import Any

from botocore.exceptions import ClientError
from sqlmodel import Session

from app.logger import logger
from app.models import (
    RESOURCE_TYPE_MAPPINGS,
    AWSResourceType,
    CloudProvider,
    Resource,
    ResourceStatus,
)
from app.repositories.resources import ResourceRepository

from .base_crawler import (
    BaseCrawler,
    ResourceStateMapper,
    aws_retry,
)


class EC2StateMapper(ResourceStateMapper):
    """Maps EC2 instance states to ResourceStatus"""

    _STATE_MAPPING = {
        "pending": ResourceStatus.STARTING,
        "running": ResourceStatus.RUNNING,
        "stopping": ResourceStatus.STOPPED,
        "stopped": ResourceStatus.STOPPED,
        "shutting-down": ResourceStatus.DELETED,
        "terminated": ResourceStatus.DELETED,
    }


class EC2ResourceCrawler(BaseCrawler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.state_mapper = EC2StateMapper()

    def _crawl_ec2_instances(self, region: str) -> list[dict[str, Any]]:
        """Crawl EC2 instances in a specific region."""
        ec2_client = self.session.client("ec2", region_name=region)
        instances = []

        try:
            # Prepare filters based on include_stopped_resources setting
            filters = []
            if not self.include_stopped_resources:
                # Only include running and pending instances, exclude stopped/terminated
                filters.append(
                    {
                        "Name": "instance-state-name",
                        "Values": ["running", "pending", "rebooting", "stopping"],
                    }
                )

            paginator = ec2_client.get_paginator("describe_instances")

            # Apply filters if any are defined
            paginate_kwargs = {}
            if filters:
                paginate_kwargs["Filters"] = filters

            for page in paginator.paginate(**paginate_kwargs):
                for reservation in page["Reservations"]:
                    instances.extend(reservation["Instances"])
        except ClientError as e:
            logger.exception(f"Error crawling EC2 instances in {region}: {e}")
            raise
        return instances

    def _map_ec2_to_resource(self, instance: dict[str, Any], region: str) -> Resource:
        """Map EC2 instance data to a Resource object."""
        tags = {tag["Key"]: tag["Value"] for tag in instance.get("Tags", [])}

        # Extract relevant configuration details
        configurations = {
            "instance_id": instance["InstanceId"],
            "instance_type": instance["InstanceType"],
            "architecture": instance.get("Architecture"),
            "availability_zone": instance.get("Placement", {}).get("AvailabilityZone"),
            "vpc_id": instance.get("VpcId"),
            "subnet_id": instance.get("SubnetId"),
            "private_ip": instance.get("PrivateIpAddress"),
            "public_ip": instance.get("PublicIpAddress"),
            "public_dns": instance.get("PublicDnsName"),
            "private_dns": instance.get("PrivateDnsName"),
            "launch_time": launch_time.isoformat()
            if (launch_time := instance.get("LaunchTime")) is not None
            else None,
            "image_id": instance.get("ImageId"),
            "ebs_optimized": instance.get("EbsOptimized"),
            "monitoring": instance.get("Monitoring", {}).get("State"),
            "security_groups": [
                {"id": sg.get("GroupId"), "name": sg.get("GroupName")}
                for sg in instance.get("SecurityGroups", [])
            ],
            "block_devices": [
                {
                    "device_name": bd.get("DeviceName"),
                    "volume_id": bd.get("Ebs", {}).get("VolumeId")
                    if "Ebs" in bd
                    else None,
                    "status": bd.get("Ebs", {}).get("Status") if "Ebs" in bd else None,
                    "delete_on_termination": bd.get("Ebs", {}).get(
                        "DeleteOnTermination"
                    )
                    if "Ebs" in bd
                    else None,
                }
                for bd in instance.get("BlockDeviceMappings", [])
            ],
            "cpu_options": {
                "core_count": instance.get("CpuOptions", {}).get("CoreCount"),
                "threads_per_core": instance.get("CpuOptions", {}).get(
                    "ThreadsPerCore"
                ),
            }
            if "CpuOptions" in instance
            else {},
            "platform_details": instance.get("PlatformDetails"),
            "virtualization_type": instance.get("VirtualizationType"),
            "hypervisor": instance.get("Hypervisor"),
        }

        resource = Resource(
            workspace_id=self.workspace_id,
            name=tags.get("Name", instance["InstanceId"]),
            region=region,
            configurations=configurations,
            type=AWSResourceType.EC2,
            resource_id=f"arn:aws:ec2:{region}:{self.aws_access_account_id}:instance/{instance['InstanceId']}",
            tags=tags,
            description=f"EC2 instance of type {instance['InstanceType']}",
            status=self.state_mapper.map_state(instance["State"]["Name"]),
            provider=CloudProvider.AWS,
            category=RESOURCE_TYPE_MAPPINGS[CloudProvider.AWS][
                AWSResourceType.EC2
            ].category,
        )
        return resource

    @aws_retry()
    def crawl_resources_and_metrics(self, session: Session) -> list[Resource]:
        """Crawl all EC2 resources."""
        all_resources = []

        # Validate region is available
        if not self.region:
            logger.error("No region specified for EC2 crawler")
            return all_resources

        instances = self._crawl_ec2_instances(self.region)
        if instances:
            resources = [
                self._map_ec2_to_resource(instance, self.region)
                for instance in instances
            ]

            for resource in resources:
                db_resource = ResourceRepository.create_or_update(session, resource)
                all_resources.append(db_resource)

        session.commit()

        logger.info(f"Crawled {len(all_resources)} EC2 resources")

        return all_resources
