from typing import Any

import boto3
from botocore.exceptions import ClientError
from sqlmodel import Session

from app.logger import logger
from app.models import (
    RESOURCE_TYPE_MAPPINGS,
    AWSResourceType,
    CloudProvider,
    Resource,
    ResourceStatus,
)
from app.repositories.resources import ResourceRepository

from .base_crawler import (
    BaseCrawler,
    ResourceStateMapper,
    aws_retry,
)


class AppRunnerStateMapper(ResourceStateMapper):
    """Maps App Runner service states to ResourceStatus"""

    _STATE_MAPPING = {
        "create_failed": ResourceStatus.STOPPED,
        "running": ResourceStatus.RUNNING,
        "deleted": ResourceStatus.DELETED,
        "delete_failed": ResourceStatus.STOPPED,
        "paused": ResourceStatus.STOPPED,
        "operation_in_progress": ResourceStatus.STARTING,
        "create_in_progress": ResourceStatus.STARTING,
        "delete_in_progress": ResourceStatus.DELETED,
        "update_in_progress": ResourceStatus.STARTING,
        "update_failed": ResourceStatus.STOPPED,
        "pause_failed": ResourceStatus.STOPPED,
        "resume_failed": ResourceStatus.STOPPED,
    }


class AppRunnerResourceCrawler(BaseCrawler):
    """Crawler for AWS App Runner resources"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.state_mapper = AppRunnerStateMapper()
        self.client = boto3.client(
            "apprunner",
            region_name=self.region,
            aws_access_key_id=self.aws_access_key_id,
            aws_secret_access_key=self.aws_secret_access_key,
        )

    @aws_retry()
    def crawl_resources_and_metrics(self, session: Session) -> list[Resource]:
        """Crawl AWS App Runner resources"""
        all_resources = []

        try:
            # Get all services - list_services does not support pagination
            response = self.client.list_services()
            services = response["ServiceSummaryList"]

            # Filter services based on include_stopped_resources setting
            if not self.include_stopped_resources:
                # Only include running services, exclude paused/failed states
                services = [
                    service
                    for service in services
                    if service.get("Status", "").lower()
                    not in [
                        "paused",
                        "create_failed",
                        "delete_failed",
                        "update_failed",
                        "pause_failed",
                        "resume_failed",
                    ]
                ]

            for service in services:
                try:
                    # Get service details
                    service_details = self.client.describe_service(
                        ServiceArn=service["ServiceArn"]
                    )["Service"]

                    resource = self._map_service_to_resource(service_details)
                    db_resource = ResourceRepository.create_or_update(session, resource)
                    all_resources.append(db_resource)
                except ClientError as e:
                    logger.exception(
                        f"Error getting details for service {service['ServiceName']}: {str(e)}"
                    )
                    continue

            session.commit()
            logger.info(f"Crawled {len(all_resources)} AWS App Runner resources")
            return all_resources

        except ClientError as e:
            logger.exception(f"Error crawling AWS App Runner resources: {str(e)}")
            raise

    def _map_service_to_resource(self, service: dict[str, Any]) -> Resource:
        """Map App Runner service data to a Resource object."""
        config = {
            "service_name": service["ServiceName"],
            "service_id": service["ServiceId"],
            "service_arn": service["ServiceArn"],
            "service_url": service["ServiceUrl"],
            "status": service["Status"],
            "source_configuration": service.get("SourceConfiguration", {}),
            "instance_configuration": service.get("InstanceConfiguration", {}),
            "encryption_configuration": service.get("EncryptionConfiguration", {}),
            "health_check_configuration": service.get("HealthCheckConfiguration", {}),
            "auto_scaling_configuration": service.get(
                "AutoScalingConfigurationSummary", {}
            ),
            "network_configuration": service.get("NetworkConfiguration", {}),
            "observability_configuration": service.get(
                "ObservabilityConfiguration", {}
            ),
        }

        return Resource(
            workspace_id=self.workspace_id,
            name=service["ServiceName"],
            region=self.region if self.region else "",
            type=AWSResourceType.APP_RUNNER,
            resource_id=service["ServiceArn"],
            tags=service.get("Tags", {}),
            description=f"App Runner service in {service['Status']} status",
            configurations=config,
            status=self.state_mapper.map_state(service["Status"]),
            provider=CloudProvider.AWS,
            category=RESOURCE_TYPE_MAPPINGS[CloudProvider.AWS][
                AWSResourceType.APP_RUNNER
            ].category,
        )
