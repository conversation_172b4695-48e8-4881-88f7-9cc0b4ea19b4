from uuid import UUID

import httpx
from fastapi import Request

from app.core.config import settings
from app.modules.payment.exceptions import (
    PaymentAuthError,
    PaymentConfigError,
    PaymentServiceError,
)
from app.modules.payment.schema import (
    CheckoutSessionResponse,
    CustomerQuotaResponse,
    CustomerResponse,
    EnterpriseEnquiryRequest,
    EnterpriseEnquiryResponse,
    InvoiceResponse,
    PaymentMethodResponse,
    PlanChangeRequestCreate,
    PlanChangeRequestResponse,
    ProductResponse,
    SubscriptionStatus,
)


class PaymentClient:
    def __init__(self):
        if not settings.PAYMENT_ENABLED:
            return

        if not settings.PAYMENT_HOST:
            raise PaymentConfigError("Payment service configuration is incomplete")

        self.base_url = settings.PAYMENT_HOST.rstrip("/")
        self.headers = {"Content-Type": "application/json"}

    async def _make_request(
        self, method: str, path: str, headers: dict[str, str] = {}, **kwargs
    ) -> dict:
        """Make HTTP request to payment service"""
        if not settings.PAYMENT_ENABLED:
            raise PaymentServiceError("Payment service is not enabled")

        url = f"{self.base_url}/api/v1{path}"

        try:
            timeout = httpx.Timeout(30.0)  # 30 second timeout to prevent hanging requests
            async with httpx.AsyncClient(follow_redirects=True, timeout=timeout) as client:
                response = await client.request(
                    method=method,
                    url=url,
                    headers={**self.headers, **headers},
                    **kwargs,
                )

                if response.status_code == 401:
                    raise PaymentAuthError()

                response.raise_for_status()
                return response.json()

        except httpx.HTTPError as e:
            if isinstance(e, httpx.HTTPStatusError):
                error_detail = e.response.json().get("detail", str(e))
                raise PaymentServiceError(error_detail)
            raise PaymentServiceError(f"Payment service error: {str(e)}")

    async def create_customer(
        self,
        user_id: UUID,
        email: str,
    ) -> CustomerResponse:
        """Create a customer in the payment service"""
        data = {"user_id": str(user_id), "email": email}
        response = await self._make_request("POST", "/customers", json=data)
        return CustomerResponse.model_validate(response)

    async def get_customer_and_quota(self, customer_id: UUID) -> CustomerQuotaResponse:
        """Get customer's details and quota"""
        response = await self._make_request("GET", f"/customers/{customer_id}")
        return CustomerQuotaResponse.model_validate(response)

    async def get_customer_list(
        self, user_id: UUID | None = None, active: bool | None = None
    ) -> list[CustomerResponse]:
        """Get customer list with optional filters"""
        params = {}
        if user_id is not None:
            params["user_id"] = str(user_id)
        if active is not None:
            params["active"] = str(active).lower()

        response = await self._make_request("GET", "/customers", params=params)
        return [CustomerResponse.model_validate(customer) for customer in response]

    async def get_subscription_status(
        self, customer_id: UUID
    ) -> list[SubscriptionStatus]:
        """Get customer's subscription status"""
        response = await self._make_request(
            "GET", f"/customers/{customer_id}/subscriptions"
        )
        return [SubscriptionStatus.model_validate(sub) for sub in response]

    async def create_checkout_session(
        self,
        customer_id: UUID,
        price_id: UUID,
        success_url: str,
        cancel_url: str,
    ) -> CheckoutSessionResponse:
        """Create a checkout session"""
        data = {
            "customer_id": str(customer_id),
            "price_id": str(price_id),
            "success_url": success_url,
            "cancel_url": cancel_url,
        }
        response = await self._make_request(
            "POST", "/subscriptions/checkout", json=data
        )
        return CheckoutSessionResponse.model_validate(response)

    async def get_available_plans(
        self, active: bool | None = None
    ) -> list[ProductResponse]:
        """Get available subscription plans with prices"""
        params = {"include_prices": "true", "include_quotas": "true"}
        if active is not None:
            params["active"] = str(active).lower()

        response = await self._make_request("GET", "/products", params=params)
        return [ProductResponse.model_validate(product) for product in response]

    async def get_customer_payment_methods(
        self, customer_id: UUID, payment_type: str = "card"
    ) -> list[PaymentMethodResponse]:
        """Get customer's payment methods from payment service"""
        path = f"/customers/{customer_id}/payment-methods"
        params = {"type": payment_type}
        response = await self._make_request("GET", path, params=params)
        return [PaymentMethodResponse.model_validate(method) for method in response]

    async def get_customer_invoices(
        self, customer_id: UUID, limit: int = 10, status: str | None = None
    ) -> list[InvoiceResponse]:
        """Get customer's invoices from payment service"""
        path = f"/customers/{customer_id}/invoices"
        params = {"limit": str(limit)}
        if status:
            params["status"] = status
        response = await self._make_request("GET", path, params=params)
        return [InvoiceResponse.model_validate(invoice) for invoice in response]

    async def get_plan_by_id(self, plan_id: UUID) -> ProductResponse:
        """Get a specific plan by ID from the payment service"""
        response = await self._make_request("GET", f"/products/{plan_id}")
        return ProductResponse.model_validate(response)

    async def submit_enterprise_enquiry(
        self,
        enquiry: EnterpriseEnquiryRequest,
        customer_id: UUID,
    ) -> EnterpriseEnquiryResponse:
        """Submit an enterprise plan enquiry to the payment service"""
        payload = {
            "first_name": enquiry.first_name,
            "last_name": enquiry.last_name,
            "work_email": enquiry.work_email,
            "work_title": enquiry.work_title,
            "company_name": enquiry.company_name,
            "estimated_monthly_cost": enquiry.estimated_monthly_cost,
            "message": enquiry.message,
            "customer_id": str(customer_id),
            "product_id": str(enquiry.product_id),
        }

        response = await self._make_request(
            "POST", "/enterprise/enquiries", json=payload
        )
        return EnterpriseEnquiryResponse(**response)

    async def get_customer_enterprise_enquiries(
        self,
        customer_id: UUID,
        status: str | None = None,
        product_id: UUID | None = None,
    ) -> list[EnterpriseEnquiryResponse]:
        """Get enterprise enquiries for a customer from the payment service"""
        params = {}
        if status:
            params["status"] = status
        if product_id:
            params["product_id"] = str(product_id)

        response = await self._make_request(
            method="GET",
            path=f"/customers/{customer_id}/enterprise-enquiries",
            params=params,
        )

        return [EnterpriseEnquiryResponse(**item) for item in response]

    async def submit_plan_change_request(
        self,
        request: PlanChangeRequestCreate,
        customer_id: UUID,
        requested_product_id: UUID,
    ) -> PlanChangeRequestResponse:
        """Submit a plan change request to the payment service"""
        payload = {
            "first_name": request.first_name,
            "last_name": request.last_name,
            "work_email": request.work_email,
            "work_title": request.work_title,
            "company_name": request.company_name,
            "reason": request.reason,
            "customer_id": str(customer_id),
            "current_product_id": str(request.current_product_id),
            "requested_product_id": str(requested_product_id),
        }

        response = await self._make_request(
            "POST", "/plan-change/requests", json=payload
        )
        return PlanChangeRequestResponse(**response)

    async def get_customer_plan_change_requests(
        self,
        customer_id: UUID,
        status: str | None = None,
    ) -> list[PlanChangeRequestResponse]:
        """Get plan change requests for a customer from the payment service"""
        params = {}
        if status:
            params["status"] = status

        if customer_id:
            params["customer_id"] = str(customer_id)

        response = await self._make_request(
            "GET", "/plan-change/requests", params=params
        )
        return [PlanChangeRequestResponse(**request) for request in response]

    async def forward_webhook(self, request: Request) -> dict:
        """Forward webhook request to payment service"""
        body = await request.body()
        headers = dict(request.headers)
        response = await self._make_request(
            method="POST", path="/webhooks/stripe", content=body, headers=headers
        )
        return response

    async def cancel_subscription(self, customer_id: UUID) -> dict:
        """Cancel subscription for a customer"""
        response = await self._make_request(
            "POST", "/subscriptions/cancel", json={"customer_id": str(customer_id)}
        )
        return response
