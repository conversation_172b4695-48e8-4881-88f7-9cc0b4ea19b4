class QuotaExceededException(Exception):
    """Raised when token usage would exceed quota."""

    pass


class TokenTrackingError(Exception):
    """Raised when token tracking operations fail."""

    pass


class ScanningError(Exception):
    """Raised when the scanning tasks error."""

    pass


class ValidationError(Exception):
    """Raised when the validation error."""

    pass


class OptimizerError(Exception):
    """Base exception for optimizer errors."""

    pass


class ResourceNotFoundError(OptimizerError):
    """Raised when a resource cannot be found."""

    pass


class MetricsFetchError(OptimizerError):
    """Raised when there is an error fetching metrics."""

    pass


class PricingError(OptimizerError):
    """Raised when there is an error getting pricing information."""

    pass


class OptimizationError(OptimizerError):
    """Raised when there is an error during optimization."""

    pass


class TaskNotFoundError(Exception):
    """Raised when there is an error during optimization."""

    pass


class TemplateParsingError(Exception):
    """Custom exception for template parsing errors"""

    pass


class UserQuotaNotFoundError(Exception):
    """Raised when user quota is not found."""

    pass


class MCPClientError(Exception):
    """Raised when there is an error with the MCP client."""

    pass


class BaseAppException(Exception):
    """Base exception for all app exceptions."""

    def __init__(self, message: str, status_code: int):
        self.message = message
        self.status_code = status_code
        super().__init__(self.message)


class ServiceError(BaseAppException):
    """Raised when there is an error with the chat service."""

    pass


class RepositoryError(BaseAppException):
    """Raised when there is an error with the repository."""

    pass


class TaskOperationError(BaseAppException):
    """Raised when there is an error with the task operation."""

    pass


# Memory Service Exceptions
class MemoryServiceError(BaseAppException):
    """Base exception for memory service errors."""

    def __init__(self, message: str, status_code: int = 500):
        super().__init__(message, status_code)


class MemoryExtractionError(MemoryServiceError):
    """Raised when memory extraction fails."""

    def __init__(self, message: str = "Failed to extract memory from conversation"):
        super().__init__(message, 422)


class MemoryNodeProcessingError(MemoryServiceError):
    """Raised when memory node processing fails."""

    def __init__(self, message: str = "Failed to process memory node"):
        super().__init__(message, 422)


class VectorStoreOperationError(MemoryServiceError):
    """Raised when vector store operations fail."""

    def __init__(self, message: str = "Vector store operation failed"):
        super().__init__(message, 503)


class CheckpointRetrievalError(MemoryServiceError):
    """Raised when checkpoint data retrieval fails."""

    def __init__(self, message: str = "Failed to retrieve checkpoint data"):
        super().__init__(message, 404)


class MemoryEvolutionError(MemoryServiceError):
    """Raised when memory evolution analysis fails."""

    def __init__(self, message: str = "Memory evolution analysis failed"):
        super().__init__(message, 422)


class InvalidMemoryInputError(MemoryServiceError):
    """Raised when memory service receives invalid input."""

    def __init__(self, message: str = "Invalid input provided to memory service"):
        super().__init__(message, 400)
