from fastapi import HTTPException, status


class ConversationRepositoryError(HTTPException):
    def __init__(
        self,
        detail: str = "Conversation repository error",
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
    ):
        super().__init__(status_code=status_code, detail=detail)


class ConversationNotFound(ConversationRepositoryError):
    def __init__(self, conversation_id: str):
        detail = f"Conversation not found: {conversation_id}"
        super().__init__(detail=detail, status_code=status.HTTP_404_NOT_FOUND)


class ConversationAccessDenied(ConversationRepositoryError):
    def __init__(self, conversation_id: str, user_id: str):
        detail = (
            f"Access denied to conversation '{conversation_id}' for user '{user_id}'"
        )
        super().__init__(detail=detail, status_code=status.HTTP_403_FORBIDDEN)


class ConversationShareNotFound(ConversationRepositoryError):
    def __init__(self, share_id: str):
        detail = f"Conversation share not found: {share_id}"
        super().__init__(detail=detail, status_code=status.HTTP_404_NOT_FOUND)
