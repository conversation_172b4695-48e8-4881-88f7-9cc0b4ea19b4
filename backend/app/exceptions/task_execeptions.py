from fastapi import HTTPException, status


class TaskServiceError(HTTPException):
    def __init__(
        self,
        detail: str = "Task service error",
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
    ):
        super().__init__(status_code=status_code, detail=detail)


class TaskRepositoryError(HTTPException):
    def __init__(
        self,
        detail: str = "Task repository error",
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
    ):
        super().__init__(status_code=status_code, detail=detail)
