from fastapi import HTTPException, status


class ConnectionServiceError(HTTPException):
    def __init__(
        self,
        detail: str = "Connection service error",
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
    ):
        super().__init__(status_code=status_code, detail=detail)


class ConnectionNotFound(ConnectionServiceError):
    def __init__(self, conn_id: str):
        detail = f"Connection not found: {conn_id}"
        super().__init__(detail=detail, status_code=status.HTTP_404_NOT_FOUND)


class ConnectionRepositoryError(HTTPException):
    def __init__(
        self,
        detail: str = "Connection repository error",
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
    ):
        super().__init__(status_code=status_code, detail=detail)


class BuiltinConnectionTestError(ConnectionServiceError):
    def __init__(
        self,
        detail: str = "Builtin connection test error",
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
    ):
        super().__init__(detail=detail, status_code=status_code)
