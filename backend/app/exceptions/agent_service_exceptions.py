from fastapi import HTTPException, status


class AgentServiceError(HTTPException):
    def __init__(
        self,
        detail: str = "Agent service error",
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
    ):
        super().__init__(status_code=status_code, detail=detail)


class AgentServiceQuotaExceededError(AgentServiceError):
    def __init__(
        self,
        detail: str = "Agent service quota exceeded",
        status_code: int = status.HTTP_403_FORBIDDEN,
    ):
        super().__init__(status_code=status_code, detail=detail)
