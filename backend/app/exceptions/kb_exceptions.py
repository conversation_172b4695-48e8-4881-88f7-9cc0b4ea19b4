from fastapi import HTTPException, status


class KBServiceError(HTTPException):
    def __init__(
        self,
        detail: str = "Knowledge base service error",
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
    ):
        super().__init__(status_code=status_code, detail=detail)


class KBNotFound(KBServiceError):
    def __init__(self, kb_id: str):
        detail = f"Knowledge base not found: {kb_id}"
        super().__init__(detail=detail, status_code=status.HTTP_404_NOT_FOUND)


class KBAccessDenied(KBServiceError):
    def __init__(self, kb_id: str, user_id: str):
        detail = f"Access denied to knowledge base '{kb_id}' for user '{user_id}'"
        super().__init__(detail=detail, status_code=status.HTTP_403_FORBIDDEN)


class KBOwnershipRequired(KBServiceError):
    def __init__(self, kb_id: str):
        detail = f"You must be the owner of knowledge base '{kb_id}' to perform this operation"
        super().__init__(detail=detail, status_code=status.HTTP_403_FORBIDDEN)


class KBAlreadyExists(KBServiceError):
    def __init__(self, name: str):
        detail = f"Knowledge base with name '{name}' already exists"
        super().__init__(detail=detail, status_code=status.HTTP_409_CONFLICT)


class InvalidKBOperation(KBServiceError):
    def __init__(self, detail: str):
        super().__init__(detail=detail, status_code=status.HTTP_400_BAD_REQUEST)


class DocumentNotFound(KBServiceError):
    def __init__(self, document_id: str):
        detail = f"Document not found: {document_id}"
        super().__init__(detail=detail, status_code=status.HTTP_404_NOT_FOUND)


class WorkspaceNotFound(KBServiceError):
    def __init__(self, workspace_id: str):
        detail = f"Workspace not found: {workspace_id}"
        super().__init__(detail=detail, status_code=status.HTTP_404_NOT_FOUND)


class VectorStoreError(KBServiceError):
    def __init__(self, detail: str = "Vector store operation failed"):
        super().__init__(
            detail=detail, status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


class EmbeddingGenerationError(VectorStoreError):
    def __init__(self, detail: str = "Failed to generate embeddings"):
        super().__init__(detail=detail)


class CollectionCreationError(VectorStoreError):
    def __init__(self, collection_name: str):
        detail = f"Failed to create collection: {collection_name}"
        super().__init__(detail=detail)


class CollectionAccessError(VectorStoreError):
    def __init__(self, collection_name: str):
        detail = f"Failed to access collection: {collection_name}"
        super().__init__(detail=detail)


class DocumentDeletionError(VectorStoreError):
    def __init__(self, detail: str = "Failed to delete documents from vector store"):
        super().__init__(detail=detail)


class SearchServiceError(VectorStoreError):
    def __init__(self, detail: str = "Search service temporarily unavailable"):
        super().__init__(detail=detail)


class SynthesisError(VectorStoreError):
    def __init__(self, detail: str = "Failed to synthesize response"):
        super().__init__(detail=detail)


class ImageProcessingError(VectorStoreError):
    def __init__(self, detail: str = "Failed to process image content"):
        super().__init__(detail=detail)


class PointLimitExceeded(HTTPException):
    def __init__(self, current_points: int, limit: int):
        detail = f"Point limit reached: {current_points}/{limit}"
        super().__init__(status_code=status.HTTP_429_TOO_MANY_REQUESTS, detail=detail)
