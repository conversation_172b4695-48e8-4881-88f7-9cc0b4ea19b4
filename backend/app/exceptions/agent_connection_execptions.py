from fastapi import HTTPException, status


class AgentConnectionRepositoryError(HTTPException):
    def __init__(
        self,
        detail: str = "Agent connection repository error",
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
    ):
        super().__init__(status_code=status_code, detail=detail)


class AgentConnectionServiceError(HTTPException):
    def __init__(
        self,
        detail: str = "Agent connection service error",
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
    ):
        super().__init__(status_code=status_code, detail=detail)
