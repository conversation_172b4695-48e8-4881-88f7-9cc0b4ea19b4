import uuid
from datetime import UTC, datetime
from uuid import UUI<PERSON>

from sqlmodel import select
from sqlmodel.ext.asyncio.session import AsyncSession

from app.exceptions.legacy import RepositoryError
from app.logger import logger
from app.models import (
    Report,
)
from app.models.reports import ReportShare


class ReportRepository:
    def __init__(self, session: AsyncSession):
        self.session = session

    async def get_by_conversation_id(
        self, conversation_id: UUID, workspace_id: UUID
    ) -> Report:
        try:
            statement = select(Report).where(
                Report.conversation_id == conversation_id,
                Report.workspace_id == workspace_id,
            )
            result = await self.session.exec(statement)
            report = result.first()
            if report is None:
                raise RepositoryError(
                    "Report not found for the given conversation", 404
                )
            return report
        except RepositoryError:
            raise
        except Exception as e:
            await self.session.rollback()
            logger.error(
                f"Error getting report for conversation {conversation_id}: {str(e)}",
                exc_info=True,
            )
            raise RepositoryError(f"Failed to retrieve report: {str(e)}", 500)

    async def create_or_update(
        self,
        report_in: dict,
        workspace_id: UUID,
        conversation_id: UUID,
    ) -> Report:
        try:
            # Try to get existing report
            try:
                report = await self.get_by_conversation_id(
                    conversation_id, workspace_id
                )
                # Update existing report
                for key, value in report_in.items():
                    if key in report.__dict__:
                        setattr(report, key, value)
                self.session.add(report)
            except RepositoryError as e:
                if e.status_code == 404:
                    # Create new report if not found
                    report_data = {
                        "conversation_id": conversation_id,
                        "workspace_id": workspace_id,
                        **report_in,
                    }
                    report = Report(**report_data)
                    self.session.add(report)
                else:
                    raise

            await self.session.commit()
            await self.session.refresh(report)
            return report

        except RepositoryError:
            raise
        except Exception as e:
            await self.session.rollback()
            logger.error(f"Error in create_or_update report: {str(e)}", exc_info=True)
            raise RepositoryError(f"Failed to create or update report: {str(e)}", 500)

    async def get_by_id(self, report_id: UUID, workspace_id: UUID) -> Report:
        try:
            statement = select(Report).where(
                Report.id == report_id,
                Report.workspace_id == workspace_id,
            )
            result = await self.session.exec(statement)
            report = result.first()
            if report is None:
                raise RepositoryError("Report not found", 404)
            return report
        except RepositoryError:
            raise
        except Exception as e:
            await self.session.rollback()
            logger.error(f"Error getting report {report_id}: {str(e)}", exc_info=True)
            raise RepositoryError(f"Failed to retrieve report: {str(e)}", 500)

    async def get_by_share_id(self, share_id: UUID) -> ReportShare:
        try:
            statement = select(Report).where(
                Report.share_id == share_id,
                Report.is_shared == True,
            )
            result = await self.session.exec(statement)
            report = result.first()
            if report is None:
                raise RepositoryError("Shared report not found", 404)
            return ReportShare(**report.model_dump())
        except RepositoryError:
            raise
        except Exception as e:
            await self.session.rollback()
            logger.error(
                f"Error getting shared report {share_id}: {str(e)}", exc_info=True
            )
            raise RepositoryError(f"Failed to retrieve shared report: {str(e)}", 500)

    async def create_share(
        self, report_id: UUID, workspace_id: UUID, user_id: UUID
    ) -> Report:
        try:
            report = await self.get_by_id(report_id, workspace_id)

            # Generate new share_id if not already shared
            if not report.is_shared:
                report.share_id = uuid.uuid4()
                report.is_shared = True
                report.shared_at = datetime.now(UTC)
                report.shared_by = user_id

                self.session.add(report)
                await self.session.commit()
                await self.session.refresh(report)

            return report
        except RepositoryError:
            raise
        except Exception as e:
            await self.session.rollback()
            logger.error(
                f"Error creating share for report {report_id}: {str(e)}", exc_info=True
            )
            raise RepositoryError(f"Failed to create share: {str(e)}", 500)

    async def revoke_share(self, report_id: UUID, workspace_id: UUID) -> Report:
        try:
            report = await self.get_by_id(report_id, workspace_id)

            report.share_id = None
            report.is_shared = False
            report.shared_at = None
            report.shared_by = None

            self.session.add(report)
            await self.session.commit()
            await self.session.refresh(report)

            return report
        except RepositoryError:
            raise
        except Exception as e:
            await self.session.rollback()
            logger.error(
                f"Error revoking share for report {report_id}: {str(e)}", exc_info=True
            )
            raise RepositoryError(f"Failed to revoke share: {str(e)}", 500)
