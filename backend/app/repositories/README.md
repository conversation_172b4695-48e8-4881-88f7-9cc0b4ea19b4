# Repository Caching System

This document explains how to use the new caching capabilities in the `BaseRepository` class.

## Overview

The `BaseRepository` now includes optional Redis caching that can be easily enabled/disabled at both class and instance levels. The system automatically handles cache invalidation on create/update/delete operations.

## Cache Interface

The caching system is built around the `ICacheManager` interface, which provides:

- `get(key)`: Retrieve cached data
- `set(key, value, ttl)`: Store data in cache with optional TTL
- `delete(key)`: Remove specific cache key
- `delete_pattern(pattern)`: Remove keys matching a pattern
- `exists(key)`: Check if key exists
- `is_connected()`: Check cache connection status

## Implementation Classes

- **`RedisCacheManager`**: Uses Redis for actual caching
- **`NoOpCacheManager`**: No-operation implementation when caching is disabled

## Usage Examples

### 1. Enable Caching at Class Level

```python
class MyRepository(BaseRepository[MyModel, MyCreate, MyUpdate]):
    # Enable caching for all instances of this repository
    CACHE_ENABLED = True
    CACHE_TTL = 600  # 10 minutes

    def __init__(self, async_session: AsyncSession):
        super().__init__(
            model=MyModel,
            async_session=async_session,
            # cache_enabled will default to CACHE_ENABLED = True
        )
```

### 2. Disable Caching at Class Level

```python
class NoCacheRepository(BaseRepository[MyModel, MyCreate, MyUpdate]):
    # Disable caching for all instances of this repository
    CACHE_ENABLED = False

    def __init__(self, async_session: AsyncSession):
        super().__init__(
            model=MyModel,
            async_session=async_session,
            # cache_enabled will default to CACHE_ENABLED = False
        )
```

### 3. Override Cache Settings at Instance Level

```python
# Repository with caching enabled by default
repo = MyRepository(session)

# Disable caching for this specific instance
repo_no_cache = MyRepository(session, cache_enabled=False)

# Custom TTL for this instance
repo_custom_ttl = MyRepository(session, cache_ttl=1200)  # 20 minutes
```

### 4. Use Built-in Caching Methods

```python
class CustomRepository(BaseRepository[MyModel, MyCreate, MyUpdate]):
    CACHE_ENABLED = True

    async def get_by_custom_field(self, field_value: str):
        # Generate cache key
        cache_key = self._generate_cache_key(
            "get_by_custom_field",
            field_value=field_value
        )

        # Try cache first
        cached_result = self._get_from_cache(cache_key)
        if cached_result:
            return cached_result

        # Cache miss - query database
        result = await self._query_database(field_value)

        # Cache the result
        self._set_cache(cache_key, result)

        return result

    async def invalidate_custom_cache(self, pattern: str):
        """Custom cache invalidation"""
        return self._delete_cache_pattern(pattern)
```

## Automatic Cache Management

The base repository automatically handles:

1. **Cache Lookup**: Before database queries in `get_by_id` and `get_all`
2. **Cache Storage**: After successful database queries
3. **Cache Invalidation**: On create/update/delete operations
4. **Pattern-based Cleanup**: Removes related cache entries

## Cache Key Generation

Cache keys are automatically generated using:

- Model name (lowercase)
- Method name
- Sorted parameter values

Example: `resource:get_all:page=1:take=100:search=test`

## Environment Variables

You can control caching globally using:

- `REPOSITORY_CACHE_ENABLED=false` to disable all repository caching

## Benefits

1. **Easy Configuration**: Simple class-level flags
2. **Flexible Control**: Override at instance level when needed
3. **Automatic Management**: No manual cache invalidation needed
4. **Performance**: Redis-based caching with circuit breaker protection
5. **Fallback**: Graceful degradation when Redis is unavailable

## Migration from Existing Code

Existing repositories can easily add caching:

```python
# Before
class OldRepository:
    def __init__(self, session):
        self.session = session

# After - just inherit from BaseRepository
class NewRepository(BaseRepository[MyModel, MyCreate, MyUpdate]):
    CACHE_ENABLED = True  # Enable caching

    def __init__(self, session):
        super().__init__(
            model=MyModel,
            async_session=session
        )
```

All existing methods will automatically get caching support!
