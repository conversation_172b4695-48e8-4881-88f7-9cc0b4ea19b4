from datetime import UTC, datetime
from uuid import UUID

from sqlmodel import select
from sqlmodel.ext.asyncio.session import AsyncSession

from app.logger import logger
from app.models import (
    CloudSyncConfig,
    CloudSyncConfigCreate,
    CloudSyncConfigUpdate,
)


class CloudSyncConfigRepository:
    def __init__(self, session: AsyncSession):
        self.session = session

    async def get_by_workspace_and_connection(
        self, workspace_id: UUID, connection_id: UUID
    ) -> CloudSyncConfig | None:
        """Get cloud sync configuration by workspace and connection IDs"""
        try:
            statement = select(CloudSyncConfig).where(
                CloudSyncConfig.workspace_id == workspace_id,
                CloudSyncConfig.connection_id == connection_id,
            )
            result = await self.session.exec(statement)
            return result.first()
        except Exception as e:
            logger.exception(
                f"Error getting cloud sync config by workspace and connection: {e}"
            )
            raise e

    async def get_by_workspace(self, workspace_id: UUID) -> list[CloudSyncConfig]:
        """Get all cloud sync configurations for a workspace"""
        try:
            statement = (
                select(CloudSyncConfig)
                .where(CloudSyncConfig.workspace_id == workspace_id)
                .order_by(CloudSyncConfig.created_at.desc())
            )
            result = await self.session.exec(statement)
            return list(result.all())
        except Exception as e:
            logger.exception(f"Error getting cloud sync configs by workspace: {e}")
            raise e

    async def get_enabled_configs(self) -> list[CloudSyncConfig]:
        """Get all enabled cloud sync configurations across all workspaces"""
        try:
            statement = (
                select(CloudSyncConfig)
                .where(CloudSyncConfig.is_enabled == True)
                .order_by(
                    CloudSyncConfig.workspace_id, CloudSyncConfig.created_at.desc()
                )
            )
            result = await self.session.exec(statement)
            return list(result.all())
        except Exception as e:
            logger.exception(f"Error getting enabled cloud sync configs: {e}")
            raise e

    async def create_config(
        self, workspace_id: UUID, data: CloudSyncConfigCreate
    ) -> CloudSyncConfig:
        """Create a new cloud sync configuration"""
        try:
            db_config = CloudSyncConfig(
                **data.model_dump(),
                workspace_id=workspace_id,
            )
            self.session.add(db_config)
            await self.session.commit()
            await self.session.refresh(db_config)
            return db_config
        except Exception as e:
            await self.session.rollback()
            logger.exception(f"Error creating cloud sync config: {e}")
            raise e

    async def update_config(
        self, config_id: UUID, data: CloudSyncConfigUpdate
    ) -> CloudSyncConfig | None:
        """Update an existing cloud sync configuration"""
        try:
            statement = select(CloudSyncConfig).where(CloudSyncConfig.id == config_id)
            result = await self.session.exec(statement)
            db_config = result.first()

            if not db_config:
                return None

            # Update fields that are provided
            for key, value in data.model_dump(exclude_unset=True).items():
                if value is not None and hasattr(db_config, key):
                    setattr(db_config, key, value)

            db_config.updated_at = datetime.now(UTC)
            self.session.add(db_config)
            await self.session.commit()
            await self.session.refresh(db_config)
            return db_config
        except Exception as e:
            await self.session.rollback()
            logger.exception(f"Error updating cloud sync config: {e}")
            raise e

    async def update_or_create(
        self, workspace_id: UUID, connection_id: UUID, data: CloudSyncConfigCreate
    ) -> CloudSyncConfig:
        """Update existing configuration or create new one if it doesn't exist"""
        try:
            # Try to find existing configuration
            existing_config = await self.get_by_workspace_and_connection(
                workspace_id, connection_id
            )

            if existing_config:
                # Update existing configuration
                update_data = CloudSyncConfigUpdate(
                    include_stopped_resources=data.include_stopped_resources,
                    refresh_interval=data.refresh_interval,
                    selected_resources=data.selected_resources,
                    is_enabled=data.is_enabled,
                )
                return await self.update_config(existing_config.id, update_data)
            else:
                # Create new configuration
                return await self.create_config(workspace_id, data)
        except Exception as e:
            logger.exception(f"Error in update_or_create cloud sync config: {e}")
            raise e

    async def delete_config(self, config_id: UUID) -> bool:
        """Delete a cloud sync configuration"""
        try:
            statement = select(CloudSyncConfig).where(CloudSyncConfig.id == config_id)
            result = await self.session.exec(statement)
            db_config = result.first()

            if not db_config:
                return False

            await self.session.delete(db_config)
            await self.session.commit()
            return True
        except Exception as e:
            await self.session.rollback()
            logger.exception(f"Error deleting cloud sync config: {e}")
            raise e

    async def get_config_by_id(self, config_id: UUID) -> CloudSyncConfig | None:
        """Get cloud sync configuration by ID"""
        try:
            statement = select(CloudSyncConfig).where(CloudSyncConfig.id == config_id)
            result = await self.session.exec(statement)
            return result.first()
        except Exception as e:
            logger.exception(f"Error getting cloud sync config by ID: {e}")
            raise e

    async def update_last_sync_time(self, config_id: UUID, sync_time: datetime) -> bool:
        """Update the last sync timestamp for a configuration"""
        try:
            statement = select(CloudSyncConfig).where(CloudSyncConfig.id == config_id)
            result = await self.session.exec(statement)
            db_config = result.first()

            if not db_config:
                return False

            db_config.last_sync_at = sync_time
            db_config.updated_at = datetime.now(UTC)
            self.session.add(db_config)
            await self.session.commit()
            return True
        except Exception as e:
            await self.session.rollback()
            logger.exception(
                f"Error updating last sync time for config {config_id}: {e}"
            )
            raise e
