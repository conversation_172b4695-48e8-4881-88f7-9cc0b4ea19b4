from uuid import UUID

from sqlmodel import col, select
from sqlmodel.ext.asyncio.session import AsyncSession

from app.exceptions.connection_template_exceptions import (
    ConnectionTemplateRepositoryError,
)
from app.logger import logger
from app.models.connections import BuiltinConnectionType, ConnectionTemplate


class ConnectionTemplateRepository:
    def __init__(self, async_session: AsyncSession):
        self.async_session = async_session

    async def get_connection_template(
        self, connection_template_id: UUID
    ) -> ConnectionTemplate | None:
        return await self.async_session.get(ConnectionTemplate, connection_template_id)

    async def get_connection_templates(
        self, builtin_connection_types: list[BuiltinConnectionType] | None = None
    ) -> list[ConnectionTemplate]:
        try:
            statement = select(ConnectionTemplate)
            if builtin_connection_types:
                statement = statement.where(
                    col(ConnectionTemplate.builtin_connection_type).in_(
                        builtin_connection_types
                    )
                )
            result = await self.async_session.exec(statement)
            templates = result.all()
            if not templates:
                return []
            return list(templates)
        except Exception as e:
            logger.exception(f"Error getting connection templates: {e}")
            raise ConnectionTemplateRepositoryError(
                "Error getting connection templates.", 500
            )

    async def update_connection_template(
        self,
        connection_template: ConnectionTemplate,
    ) -> ConnectionTemplate:
        """Update a connection template with new values."""
        try:
            self.async_session.add(connection_template)
            await self.async_session.commit()
            await self.async_session.refresh(connection_template)

            return connection_template
        except Exception as e:
            await self.async_session.rollback()
            logger.exception(f"Error updating connection template: {e}")
            raise ConnectionTemplateRepositoryError(
                "Error updating connection template.", 500
            )
