from abc import ABC, abstractmethod
from typing import Any


class ICacheManager(ABC):
    """Abstract interface for cache operations."""

    @abstractmethod
    def get(self, key: str) -> Any | None:
        """Get value from cache by key."""
        pass

    @abstractmethod
    def set(self, key: str, value: Any, ttl: int | None = None) -> bool:
        """Set value in cache with optional TTL."""
        pass

    @abstractmethod
    def delete(self, key: str) -> bool:
        """Delete a specific key from cache."""
        pass

    @abstractmethod
    def delete_pattern(self, pattern: str) -> int:
        """Delete keys matching a pattern. Returns number of deleted keys."""
        pass

    @abstractmethod
    def exists(self, key: str) -> bool:
        """Check if a key exists in cache."""
        pass

    @abstractmethod
    def is_connected(self) -> bool:
        """Check if cache connection is available."""
        pass


class RedisCacheManager(ICacheManager):
    """Redis implementation of the cache interface using RedisManager."""

    def __init__(self, redis_manager):
        self.redis_manager = redis_manager

    def get(self, key: str) -> Any | None:
        """Get value from Redis cache."""
        return self.redis_manager.get(key)

    def set(self, key: str, value: Any, ttl: int | None = None) -> bool:
        """Set value in Redis cache with optional TTL."""
        return self.redis_manager.set(key, value, ttl=ttl)

    def delete(self, key: str) -> bool:
        """Delete a specific key from Redis cache."""
        return self.redis_manager.delete(key)

    def delete_pattern(self, pattern: str) -> int:
        """Delete keys matching a pattern from Redis cache."""
        return self.redis_manager.delete_pattern(pattern)

    def exists(self, key: str) -> bool:
        """Check if a key exists in Redis cache."""
        return self.redis_manager.exists(key)

    def is_connected(self) -> bool:
        """Check if Redis connection is available."""
        return self.redis_manager.is_connected()


class NoOpCacheManager(ICacheManager):
    """No-operation cache manager for when caching is disabled."""

    def get(self, key: str) -> Any | None:
        """Always return None (cache miss)."""
        return None

    def set(self, key: str, value: Any, ttl: int | None = None) -> bool:
        """Do nothing, return True to indicate success."""
        return True

    def delete(self, key: str) -> bool:
        """Do nothing, return True to indicate success."""
        return True

    def delete_pattern(self, pattern: str) -> int:
        """Do nothing, return 0 deleted keys."""
        return 0

    def exists(self, key: str) -> bool:
        """Always return False."""
        return False

    def is_connected(self) -> bool:
        """Always return False."""
        return False
