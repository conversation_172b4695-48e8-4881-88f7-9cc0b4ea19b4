import uuid
from typing import cast
from uuid import UUID

from sqlalchemy import or_
from sqlalchemy.orm import selectinload
from sqlalchemy.sql.elements import ColumnElement
from sqlmodel import Session, col, desc, not_, select
from sqlmodel.ext.asyncio.session import AsyncSession

from app.core.db import engine
from app.models import (
    AWSAccount,
    AWSAccountCredentials,
    GCPAccount,
    User,
    UserWorkspace,
    Workspace,
)
from app.repositories.base import BaseRepository


class WorkspaceRepository(BaseRepository):
    CACHE_ENABLED = True
    CACHE_TTL = 300

    def __init__(self, async_session: AsyncSession):
        super().__init__(Workspace)
        self.async_session = async_session
        # Use a context manager approach for sync session instead of keeping it open
        self._engine = engine

    def can_create_workspaces(self, user_id: UUID, default_workspace: bool) -> bool:
        """
        Check if a user can manage (create/update/delete) workspaces.
        Only superusers and users with own workspaces can manage workspaces.
        """
        cache_key = self._generate_cache_key(
            "can_create_workspaces",
            user_id=str(user_id),
            default_workspace=bool(default_workspace),
        )
        cached = self._get_from_cache(cache_key)
        if isinstance(cached, bool):
            return cached

        session = Session(self._engine)
        try:
            user = session.get(User, user_id)
            result = (
                user.is_superuser
                or len(user.own_workspaces or []) > 0
                or default_workspace
                if user
                else False
            )
        finally:
            session.close()
        self._set_cache(cache_key, result)
        return result

    def can_manage_workspace(self, user_id: UUID, workspace_id: UUID) -> bool:
        """
        Check if a user can manage a workspace.
        Only the workspace owner and superusers can manage workspaces.
        """
        cache_key = self._generate_cache_key(
            "can_manage_workspace", user_id=str(user_id), workspace_id=str(workspace_id)
        )
        cached = self._get_from_cache(cache_key)
        if isinstance(cached, bool):
            return cached

        session = Session(self._engine)
        try:
            user = session.get(User, user_id)
            workspace = session.get(Workspace, workspace_id)
            if not workspace:
                self._set_cache(cache_key, False)
                return False
            result = (
                user.is_superuser or user.id == workspace.owner_id if user else False
            )
        finally:
            session.close()
        self._set_cache(cache_key, result)
        return result

    def can_view_workspace(self, user_id: UUID, workspace_id: UUID) -> bool:
        """
        Check if a user can view a workspace.
        A user can view a workspace if they are:
        1. A superuser
        2. The workspace owner
        3. An invited member of the workspace
        """
        cache_key = self._generate_cache_key(
            "can_view_workspace", user_id=str(user_id), workspace_id=str(workspace_id)
        )
        cached = self._get_from_cache(cache_key)
        if isinstance(cached, bool):
            return cached

        session = Session(self._engine)
        try:
            user = session.get(User, user_id)
            if not user:
                self._set_cache(cache_key, False)
                return False

            if user.is_superuser:
                self._set_cache(cache_key, True)
                return True

            workspace = session.get(Workspace, workspace_id)
            if not workspace:
                self._set_cache(cache_key, False)
                return False

            # Check if user is owner
            if workspace.owner_id == user_id:
                self._set_cache(cache_key, True)
                return True

            # Check if user is invited member
            statement = select(UserWorkspace).where(
                UserWorkspace.workspace_id == workspace_id,
                UserWorkspace.user_id == user_id,
            )
            result = session.exec(statement)
            can_view = result.first() is not None
        finally:
            session.close()
        self._set_cache(cache_key, can_view)
        return can_view

    async def get_workspaces(
        self, user_id: UUID, skip: int = 0, limit: int = 10
    ) -> list[Workspace]:
        """
        Retrieve workspaces - both owned and invited (non-deleted only).
        """
        cache_key = self._generate_cache_key(
            "get_workspaces", user_id=str(user_id), skip=skip, limit=limit
        )
        cached = self._get_from_cache(cache_key)
        if cached:
            try:
                return [Workspace.model_validate(ws) for ws in cached]
            except Exception:
                pass

        statement = (
            select(Workspace)
            .distinct()
            .where(
                or_(
                    col(Workspace.owner_id) == user_id,  # Owned workspaces
                    cast("ColumnElement[UUID]", Workspace.id).in_(  # Invited workspaces
                        select(UserWorkspace.workspace_id).where(
                            UserWorkspace.user_id == user_id
                        )
                    ),
                ),
                not_(Workspace.is_deleted),
            )
            .offset(skip)
            .limit(limit)
            .order_by(desc(Workspace.created_at))
        )

        result = await self.async_session.exec(statement)
        workspaces = list(result.all())
        self._set_cache(cache_key, [w.model_dump() for w in workspaces])
        return workspaces

    async def get_workspace(self, workspace_id: uuid.UUID) -> Workspace | None:
        cache_key = self._generate_cache_key(
            "get_workspace", workspace_id=str(workspace_id)
        )
        cached = self._get_from_cache(cache_key)
        if cached:
            try:
                return Workspace.model_validate(cached)
            except Exception:
                pass

        statement = (
            select(Workspace)
            .where(Workspace.id == workspace_id)
            .options(
                selectinload(Workspace.aws_account).selectinload(AWSAccount.credential),  # type: ignore[arg-type]  # type: ignore[arg-type]
                selectinload(Workspace.gcp_account).selectinload(GCPAccount.credential),  # type: ignore[arg-type]  # type: ignore[arg-type]
                selectinload(Workspace.settings),  # type: ignore[arg-type]
            )
        )
        result = await self.async_session.exec(statement)
        workspace = result.first()

        if workspace:
            self._set_cache(cache_key, workspace.model_dump())

        return workspace

    async def create_workspace(self, workspace: Workspace) -> Workspace:
        created = await self._save_workspace(workspace)
        # Invalidate all workspace caches and warm get_workspace
        self._invalidate_model_cache(created.id)
        by_id_key = self._generate_cache_key(
            "get_workspace", workspace_id=str(created.id)
        )
        self._set_cache(by_id_key, created.model_dump())
        return created

    async def update_workspace(self, workspace: Workspace) -> Workspace:
        updated = await self._save_workspace(workspace)
        self._invalidate_model_cache(updated.id)
        by_id_key = self._generate_cache_key(
            "get_workspace", workspace_id=str(updated.id)
        )
        self._set_cache(by_id_key, updated.model_dump())
        return updated

    async def _save_workspace(self, workspace: Workspace) -> Workspace:
        """
        Private helper method to save (create or update) a workspace.
        """
        try:
            self.async_session.add(workspace)
            await self.async_session.commit()
            await self.async_session.refresh(workspace)
            return workspace
        except Exception as e:
            await self.async_session.rollback()
            raise e

    async def delete_workspace(self, workspace: Workspace) -> None:
        """
        Delete a workspace.
        """
        try:
            await self.async_session.delete(workspace)
            await self.async_session.commit()
            # Invalidate all workspace caches after deletion
            self._invalidate_model_cache(workspace.id)
        except Exception as e:
            await self.async_session.rollback()
            raise e

    def get_all_workspaces(
        self, workspace_ids: list[UUID] | None = None
    ) -> list[Workspace]:
        """
        Get all workspaces, optionally filtered by workspace IDs.

        Args:
            workspace_ids: Optional list of workspace IDs to filter by

        Returns:
            List of Workspace objects matching the criteria
        """
        cache_key = self._generate_cache_key(
            "get_all_workspaces",
            workspace_ids=",".join(sorted([str(wid) for wid in workspace_ids]))
            if workspace_ids
            else "",
        )
        cached = self._get_from_cache(cache_key)
        if cached:
            try:
                return [Workspace.model_validate(ws) for ws in cached]
            except Exception:
                pass

        session = Session(self._engine)
        try:
            statement = select(Workspace)
            if workspace_ids:
                statement = statement.where(
                    cast("ColumnElement[UUID]", Workspace.id).in_(workspace_ids)
                )
            result = session.exec(statement)
            workspaces = list(result.all())
        finally:
            session.close()
        self._set_cache(cache_key, [w.model_dump() for w in workspaces])
        return workspaces

    def get_aws_credentials(
        self, aws_account_id: uuid.UUID
    ) -> AWSAccountCredentials | None:
        cache_key = self._generate_cache_key(
            "get_aws_credentials", aws_account_id=str(aws_account_id)
        )
        cached = self._get_from_cache(cache_key)
        if cached:
            try:
                return AWSAccountCredentials.model_validate(cached)
            except Exception:
                pass

        session = Session(self._engine)
        try:
            statement = select(AWSAccountCredentials).where(
                AWSAccountCredentials.aws_account_id == aws_account_id
            )
            result = session.exec(statement)
            cred = result.first()
        finally:
            session.close()
        if cred:
            self._set_cache(cache_key, cred.model_dump())
        return cred
