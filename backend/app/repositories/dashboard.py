import uuid
from datetime import UTC, datetime
from uuid import UUI<PERSON>

from sqlmodel import select
from sqlmodel.ext.asyncio.session import AsyncSession

from app.exceptions.legacy import RepositoryError
from app.logger import logger
from app.models import (
    Dashboard,
    DashboardShare,
)


class DashboardRepository:
    def __init__(self, session: AsyncSession):
        self.session = session

    async def get_by_conversation_id(
        self, conversation_id: UUID, workspace_id: UUID
    ) -> Dashboard:
        try:
            statement = select(Dashboard).where(
                Dashboard.conversation_id == conversation_id,
                Dashboard.workspace_id == workspace_id,
            )
            result = await self.session.exec(statement)
            dashboard = result.first()
            if dashboard is None:
                raise RepositoryError(
                    "Dashboard not found for the given conversation", 404
                )
            return dashboard
        except RepositoryError:
            raise
        except Exception as e:
            await self.session.rollback()
            logger.error(
                f"Error getting dashboard for conversation {conversation_id}: {str(e)}",
                exc_info=True,
            )
            raise RepositoryError(f"Failed to retrieve dashboard: {str(e)}", 500)

    async def create_or_update(
        self,
        dashboard_in: dict,
        workspace_id: UUID,
        conversation_id: UUID,
    ) -> Dashboard:
        try:
            dashboard = None

            # First, try to find existing dashboard by ID if provided
            if "id" in dashboard_in:
                try:
                    dashboard = await self.get_by_id(dashboard_in["id"], workspace_id)
                except RepositoryError as e:
                    if e.status_code != 404:
                        raise

            # If not found by ID, try to find by conversation_id
            if dashboard is None:
                try:
                    dashboard = await self.get_by_conversation_id(
                        conversation_id, workspace_id
                    )
                except RepositoryError as e:
                    if e.status_code != 404:
                        raise
            if dashboard is not None:
                # Update existing dashboard
                for key, value in dashboard_in.items():
                    if key in dashboard.__dict__:
                        setattr(dashboard, key, value)
                dashboard.updated_at = datetime.now(UTC)
                self.session.add(dashboard)
            else:
                # Create new dashboard if not found
                dashboard_data = {
                    "conversation_id": conversation_id,
                    "workspace_id": workspace_id,
                    **dashboard_in,
                }
                dashboard = Dashboard(**dashboard_data)
                self.session.add(dashboard)

            await self.session.commit()
            await self.session.refresh(dashboard)
            return dashboard

        except RepositoryError:
            raise
        except Exception as e:
            await self.session.rollback()
            logger.error(
                f"Error in create_or_update dashboard: {str(e)}", exc_info=True
            )
            raise RepositoryError(
                f"Failed to create or update dashboard: {str(e)}", 500
            )

    async def get_by_id(self, dashboard_id: UUID, workspace_id: UUID) -> Dashboard:
        try:
            statement = select(Dashboard).where(
                Dashboard.id == dashboard_id,
                Dashboard.workspace_id == workspace_id,
            )
            result = await self.session.exec(statement)
            dashboard = result.first()
            if dashboard is None:
                raise RepositoryError("Dashboard not found", 404)
            return dashboard
        except RepositoryError:
            raise
        except Exception as e:
            await self.session.rollback()
            logger.error(
                f"Error getting dashboard {dashboard_id}: {str(e)}", exc_info=True
            )
            raise RepositoryError(f"Failed to retrieve dashboard: {str(e)}", 500)

    async def get_by_share_id(self, share_id: UUID) -> DashboardShare:
        try:
            statement = select(Dashboard).where(
                Dashboard.share_id == share_id,
                Dashboard.is_shared == True,
            )
            result = await self.session.exec(statement)
            dashboard = result.first()
            if dashboard is None:
                raise RepositoryError("Shared dashboard not found", 404)
            return DashboardShare(**dashboard.model_dump(mode="json"))
        except RepositoryError:
            raise
        except Exception as e:
            await self.session.rollback()
            logger.error(
                f"Error getting shared dashboard {share_id}: {str(e)}", exc_info=True
            )
            raise RepositoryError(f"Failed to retrieve shared dashboard: {str(e)}", 500)

    async def create_share(
        self, dashboard_id: UUID, workspace_id: UUID, user_id: UUID
    ) -> Dashboard:
        try:
            dashboard = await self.get_by_id(dashboard_id, workspace_id)

            # Generate new share_id if not already shared
            if not dashboard.is_shared:
                dashboard.share_id = uuid.uuid4()
                dashboard.is_shared = True
                dashboard.shared_at = datetime.now(UTC)
                dashboard.shared_by = user_id

                self.session.add(dashboard)
                await self.session.commit()
                await self.session.refresh(dashboard)

            return dashboard
        except RepositoryError:
            raise
        except Exception as e:
            await self.session.rollback()
            logger.error(
                f"Error creating share for dashboard {dashboard_id}: {str(e)}",
                exc_info=True,
            )
            raise RepositoryError(f"Failed to create share: {str(e)}", 500)

    async def revoke_share(self, dashboard_id: UUID, workspace_id: UUID) -> Dashboard:
        try:
            dashboard = await self.get_by_id(dashboard_id, workspace_id)

            dashboard.share_id = None
            dashboard.is_shared = False
            dashboard.shared_at = None
            dashboard.shared_by = None

            self.session.add(dashboard)
            await self.session.commit()
            await self.session.refresh(dashboard)

            return dashboard
        except RepositoryError:
            raise
        except Exception as e:
            await self.session.rollback()
            logger.error(
                f"Error revoking share for dashboard {dashboard_id}: {str(e)}",
                exc_info=True,
            )
            raise RepositoryError(f"Failed to revoke share: {str(e)}", 500)
