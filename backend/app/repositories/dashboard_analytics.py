from sqlmodel import and_, desc, func, select
from sqlmodel.ext.asyncio.session import AsyncSession

from app.logger import logger
from app.models.recommendations import Recommendation, RecommendationStatus
from app.models.resources import Resource, ResourceCategory
from app.schemas.dashboard_analytics import (
    DashboardAnalyticsQuery,
    HighConsumingServicesResponse,
    ServiceConsumptionData,
    TopRecommendationData,
    TopRecommendationsResponse,
)


class DashboardAnalyticsRepository:
    """Repository for dashboard analytics data"""

    def __init__(self, session: AsyncSession):
        self.session = session

    async def get_high_consuming_services(
        self, query_params: DashboardAnalyticsQuery
    ) -> HighConsumingServicesResponse:
        """Get high-consuming services with monthly savings opportunities"""
        try:
            # Base query for service consumption data
            base_query = (
                select(
                    Resource.type.label("service_type"),
                    Resource.category.label("service_category"),
                    Resource.provider.label("cloud_provider"),
                    func.count(Resource.id).label("resource_count"),
                    func.count(Recommendation.id).label("optimization_opportunities"),
                    func.coalesce(func.sum(Recommendation.potential_savings), 0).label(
                        "potential_monthly_savings"
                    ),
                    # Estimated current monthly cost (you may need to adjust this based on your cost data)
                    func.coalesce(func.sum(Recommendation.potential_savings), 0).label(
                        "estimated_monthly_cost"
                    ),
                )
                .outerjoin(
                    Recommendation,
                    and_(
                        Resource.id == Recommendation.resource_id,
                        Recommendation.status == RecommendationStatus.PENDING,
                    ),
                )
                .where(Resource.workspace_id == query_params.workspace_id)
                .group_by(Resource.type, Resource.category, Resource.provider)
            )

            # Apply filters
            if query_params.cloud_provider:
                base_query = base_query.where(
                    Resource.provider == query_params.cloud_provider
                )

            if query_params.service_category:
                base_query = base_query.where(
                    Resource.category == query_params.service_category
                )

            if query_params.min_savings_amount:
                base_query = base_query.having(
                    func.coalesce(func.sum(Recommendation.potential_savings), 0)
                    >= query_params.min_savings_amount
                )

            # Filter out services with 0 potential savings and order by potential savings (descending)
            final_query = (
                base_query.having(
                    func.coalesce(func.sum(Recommendation.potential_savings), 0) > 0
                )
                .order_by(
                    desc(func.coalesce(func.sum(Recommendation.potential_savings), 0))
                )
                .limit(query_params.top_services_limit)
            )

            # Execute query
            result = await self.session.exec(final_query)
            service_data = result.all()

            # Process results
            services = []
            total_monthly_cost = 0.0
            total_potential_savings = 0.0
            total_resources = 0

            for row in service_data:
                # Calculate savings percentage (assuming current cost = savings + remaining cost)
                # This is a simplified calculation - you may need to adjust based on actual cost data
                potential_savings = float(row.potential_monthly_savings or 0)
                estimated_cost = potential_savings * 2  # Rough estimate
                savings_percentage = (
                    (potential_savings / estimated_cost * 100)
                    if estimated_cost > 0
                    else 0.0
                )

                service = ServiceConsumptionData(
                    service_type=row.service_type,
                    service_category=row.service_category or ResourceCategory.OTHER,
                    cloud_provider=row.cloud_provider,
                    total_monthly_cost=estimated_cost,
                    potential_monthly_savings=potential_savings,
                    resource_count=row.resource_count,
                    optimization_opportunities=row.optimization_opportunities or 0,
                    savings_percentage=round(savings_percentage, 2),
                )
                services.append(service)

                total_monthly_cost += estimated_cost
                total_potential_savings += potential_savings
                total_resources += row.resource_count

            return HighConsumingServicesResponse(
                services=services,
                total_monthly_cost=round(total_monthly_cost, 2),
                total_potential_savings=round(total_potential_savings, 2),
                total_resources=total_resources,
            )

        except Exception as e:
            logger.exception("Error getting high consuming services: %s", e)
            raise

    async def get_top_recommendations(
        self, query_params: DashboardAnalyticsQuery
    ) -> TopRecommendationsResponse:
        """Get top 5 potential monthly savings recommendations with effort and risk assessment"""
        try:
            # Query for top recommendations with resource details
            base_query = (
                select(
                    Recommendation.id,
                    Recommendation.resource_id,
                    Recommendation.title,
                    Recommendation.description,
                    Recommendation.potential_savings,
                    Recommendation.effort,
                    Recommendation.risk,
                    Recommendation.status,
                    Resource.name.label("resource_name"),
                    Resource.type.label("resource_type"),
                    Resource.category.label("service_category"),
                    Resource.provider.label("cloud_provider"),
                    Resource.region,
                )
                .join(Resource, Resource.id == Recommendation.resource_id)
                .where(
                    and_(
                        Resource.workspace_id == query_params.workspace_id,
                        Recommendation.status == RecommendationStatus.PENDING,
                        Recommendation.potential_savings > 0,
                    )
                )
            )

            # Apply filters
            if query_params.cloud_provider:
                base_query = base_query.where(
                    Resource.provider == query_params.cloud_provider
                )

            if query_params.service_category:
                base_query = base_query.where(
                    Resource.category == query_params.service_category
                )

            if query_params.min_savings_amount:
                base_query = base_query.where(
                    Recommendation.potential_savings >= query_params.min_savings_amount
                )

            # Execute query to get all eligible recommendations
            result = await self.session.exec(base_query)
            recommendations_data = result.all()

            # Calculate priority scores and sort
            recommendations_with_scores = []
            for rec in recommendations_data:
                # Priority scoring algorithm
                # Higher savings = higher score (max weight)
                savings_score = min(rec.potential_savings / 1000, 10)  # Cap at 10

                # Lower effort = higher score
                effort_multiplier = {"low": 1.5, "medium": 1.0, "high": 0.5}.get(
                    rec.effort.lower(), 1.0
                )

                # Lower risk = higher score
                risk_multiplier = {"low": 1.5, "medium": 1.0, "high": 0.3}.get(
                    rec.risk.lower(), 1.0
                )

                # Calculate priority score
                priority_score = savings_score * effort_multiplier * risk_multiplier

                # Estimate implementation time
                implementation_time = self._estimate_implementation_time(
                    rec.effort, rec.risk, rec.resource_type
                )

                recommendation = TopRecommendationData(
                    id=rec.id,
                    resource_id=rec.resource_id,
                    resource_name=rec.resource_name,
                    resource_type=rec.resource_type,
                    service_category=rec.service_category or ResourceCategory.OTHER,
                    cloud_provider=rec.cloud_provider,
                    title=rec.title,
                    description=rec.description,
                    potential_monthly_savings=float(rec.potential_savings),
                    effort=rec.effort,
                    risk=rec.risk,
                    status=rec.status,
                    priority_score=round(priority_score, 2),
                    region=rec.region,
                    estimated_implementation_time=implementation_time,
                )
                recommendations_with_scores.append(recommendation)

            # Sort by priority score (highest first) and take top 5
            top_recommendations = sorted(
                recommendations_with_scores,
                key=lambda x: x.priority_score,
                reverse=True,
            )[:5]

            # Calculate summary statistics
            total_potential_savings = sum(
                rec.potential_monthly_savings for rec in top_recommendations
            )

            quick_wins_count = sum(
                1
                for rec in top_recommendations
                if rec.effort.lower() == "low" and rec.risk.lower() == "low"
            )

            high_impact_count = sum(
                1 for rec in top_recommendations if rec.potential_monthly_savings >= 500
            )

            return TopRecommendationsResponse(
                recommendations=top_recommendations,
                total_potential_savings=round(total_potential_savings, 2),
                quick_wins_count=quick_wins_count,
                high_impact_count=high_impact_count,
            )

        except Exception as e:
            logger.exception("Error getting top recommendations: %s", e)
            raise

    def _estimate_implementation_time(
        self, effort: str, risk: str, resource_type: str
    ) -> str:
        """Estimate implementation time based on effort, risk, and resource type"""
        base_times = {
            "low": {"low": "1-2 hours", "medium": "2-4 hours", "high": "4-8 hours"},
            "medium": {"low": "4-8 hours", "medium": "1-2 days", "high": "2-5 days"},
            "high": {"low": "1-2 days", "medium": "3-7 days", "high": "1-2 weeks"},
        }

        # Adjust for complex resource types
        complex_types = ["EKS", "RDS", "REDSHIFT", "GKE", "AKS"]
        if any(ct in resource_type.upper() for ct in complex_types):
            return (
                base_times.get(effort.lower(), {})
                .get(risk.lower(), "Time varies")
                .replace("hours", "hours+")
                .replace("days", "days+")
            )

        return base_times.get(effort.lower(), {}).get(risk.lower(), "Time varies")
