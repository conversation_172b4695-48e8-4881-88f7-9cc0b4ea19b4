import uuid
from datetime import UTC, datetime
from typing import cast

from sqlalchemy.sql.elements import ColumnElement
from sqlmodel import and_, delete, func, or_, select
from sqlmodel.ext.asyncio.session import AsyncSession

from app.logger import logger
from app.models import (
    AuthorizedUser,
    PaginationMeta,
    Recommendation,
    RecommendationCreate,
    RecommendationOveralPublic,
    RecommendationPublic,
    RecommendationsPublic,
    RecommendationStatus,
    RecommendationUpdate,
    Resource,
)
from app.repositories.base import BaseRepository


class RecommendationRepository(BaseRepository):
    CACHE_ENABLED = True
    CACHE_TTL = 300

    def __init__(
        self,
        async_session: AsyncSession,
        cache_enabled: bool | None = None,
    ):
        super().__init__(Recommendation, cache_enabled=cache_enabled)
        self.async_session = async_session

    async def get_all_recommendations(
        self, resource_id: uuid.UUID | None
    ) -> RecommendationsPublic:
        if not self.async_session:
            raise ValueError("Async session not provided")

        query = select(Recommendation).where(Recommendation.resource_id == resource_id)
        result = await self.async_session.exec(query)
        rcms = result.all()

        # Create pagination metadata
        total_items = len(rcms)
        pagination_meta = PaginationMeta(
            page=1,
            take=total_items,
            total_items=total_items,
            total_pages=1,
            has_previous=False,
            has_next=False,
            start_index=1 if total_items > 0 else 0,
            end_index=total_items,
        )

        recommendation_publics = [
            RecommendationPublic.model_validate(rcm) for rcm in rcms
        ]
        return RecommendationsPublic(data=recommendation_publics, meta=pagination_meta)

    async def delete_recommendations(
        self,
        ids: list[uuid.UUID],
        resource_id: uuid.UUID | None,
    ) -> bool:
        if not self.async_session:
            raise ValueError("Async session not provided")

        query = delete(Recommendation).where(
            cast(ColumnElement[uuid.UUID], Recommendation.id).in_(ids),  # type: ignore[attr-defined]
            Recommendation.resource_id == resource_id,
        )
        await self.async_session.exec(query)  # type: ignore[arg-type]
        await self.async_session.commit()
        # Invalidate caches after bulk deletion
        self._invalidate_model_cache()

        return True

    async def get_recommendation_overal_async(
        self, workspace_id: uuid.UUID
    ) -> RecommendationOveralPublic:
        """Get overall recommendation statistics async."""
        if not self.async_session:
            raise ValueError("Async session not provided")

        # Query for total_resource_scanned
        total_resource_query = select(
            func.count(Resource.id).label("total_resource_scanned")
        ).where(Resource.workspace_id == workspace_id)

        # Query for total_well_optimized
        well_optimized_query = (
            select(func.count(func.distinct(Resource.id)).label("total_well_optimized"))
            .select_from(Resource)
            .outerjoin(Recommendation, Resource.id == Recommendation.resource_id)
            .where(Resource.workspace_id == workspace_id)
            .group_by(Resource.id)
            .having(
                func.bool_and(
                    Recommendation.status == RecommendationStatus.IMPLEMENTED
                )  # All recommendations implemented
            )
        )

        # Query for recommendation-based stats
        recommendation_query = (
            select(
                func.count(Recommendation.id).label("total_optimization_opportunities"),
                func.sum(
                    cast("ColumnElement[float]", Recommendation.potential_savings)
                ).label("total_estimated_saving_amount"),
            )
            .join(Resource)
            .where(
                and_(
                    Resource.workspace_id == workspace_id,
                    Recommendation.status == RecommendationStatus.PENDING,
                )
            )
        )

        total_resource_result = await self.async_session.exec(total_resource_query)
        total_resource_row = total_resource_result.first()

        well_optimized_result = await self.async_session.exec(well_optimized_query)
        well_optimized_row = well_optimized_result.first()

        recommendation_result = await self.async_session.exec(recommendation_query)
        recommendation_row = recommendation_result.first()

        return RecommendationOveralPublic(
            total_resource_scanned=total_resource_row or 0,
            total_well_optimized=well_optimized_row or 0,
            total_optimization_opportunities=recommendation_row[0]
            if recommendation_row
            else 0,
            total_estimated_saving_amount=round(float(recommendation_row[1] or 0), 2)
            if recommendation_row
            else 0.0,
        )

    async def get_recommendations_with_pagination_async(
        self,
        workspace_id: uuid.UUID,
        skip: int = 0,
        limit: int = 10,
        search: str | None = None,
        resource_id: list[str] | None = None,
        resource_type: list[str] | None = None,
        status: list[str] | None = None,
        start_date: datetime | None = None,
        end_date: datetime | None = None,
        order_by: str | None = None,
        order_direction: str = "desc",
    ) -> RecommendationsPublic:
        """Get recommendations with pagination and filtering async."""
        if not self.async_session:
            raise ValueError("Async session not provided")

        try:
            # Try to get from cache first
            cache_key = self._generate_cache_key(
                "get_recommendations_with_pagination_async",
                workspace_id=str(workspace_id),
                skip=skip,
                limit=limit,
                search=search,
                # Preserve None distinctly from empty list for cache semantics
                resource_id=resource_id,
                resource_type=resource_type or [],
                status=status or [],
                start_date=start_date.isoformat() if start_date else None,
                end_date=end_date.isoformat() if end_date else None,
                order_by=order_by,
                order_direction=order_direction,
            )
            cached_result = self._get_from_cache(cache_key)
            if cached_result:
                logger.info(f"Cache hit for recommendations query: {cache_key}")
                return RecommendationsPublic(**cached_result)

            # Cache miss, query database
            logger.info(f"Cache miss for recommendations query: {cache_key}")

            # Use LEFT JOIN to include recommendations with NULL resource_id
            query = (
                select(Recommendation, Resource.name)
                .outerjoin(Resource, Resource.id == Recommendation.resource_id)
                .where(
                    or_(
                        Resource.workspace_id == workspace_id,
                        Recommendation.workspace_id == workspace_id,
                    )
                )
            )

            # resource_id filter semantics:
            # - None  -> filter for records where resource_id IS NULL
            # - []    -> no filter (all records)
            # - [ids] -> filter by provided ids
            if resource_id is None:
                logger.info("Filtering for recommendations with NULL resource_id")
                query = query.where(
                    cast(ColumnElement[uuid.UUID], Recommendation.resource_id).is_(None)
                )
            elif resource_id:
                logger.info(
                    f"Filtering for recommendations with resource_id: {resource_id}"
                )
                query = query.where(
                    cast(ColumnElement[uuid.UUID], Recommendation.resource_id).in_(
                        resource_id
                    )
                )  # type: ignore[attr-defined]

            if search:
                query = query.where(
                    or_(
                        cast(ColumnElement[str], Recommendation.title).ilike(
                            f"%{search}%"
                        ),  # type: ignore[attr-defined]
                        cast(ColumnElement[str], Recommendation.description).ilike(
                            f"%{search}%"
                        ),  # type: ignore[attr-defined]
                    )
                )

            if resource_type:
                query = query.where(
                    cast(ColumnElement[str], Resource.type).in_(resource_type)
                )  # type: ignore[attr-defined]

            if status:
                status_enums = [RecommendationStatus[rt.upper()] for rt in status]
                query = query.where(
                    cast(
                        "ColumnElement[RecommendationStatus]", Recommendation.status
                    ).in_(status_enums)
                )  # type: ignore[attr-defined]

            if start_date:
                query = query.where(Recommendation.created_at >= start_date)

            if end_date:
                query = query.where(Recommendation.created_at <= end_date)

            # Handle ordering
            if order_by:
                order_column = getattr(Recommendation, order_by)
                if order_direction.lower() == "desc":
                    query = query.order_by(order_column.desc())
                else:
                    query = query.order_by(order_column.asc())

            # Get total count
            count_query = select(func.count()).select_from(query.subquery())
            count_result = await self.async_session.exec(count_query)
            total_items = count_result.one()

            # Apply pagination
            paginated_query = query.offset(skip).limit(limit)
            recommendations_result = await self.async_session.exec(paginated_query)
            recommendations = recommendations_result.all()

            # Create pagination metadata
            page = (skip // limit) + 1 if limit > 0 else 1
            total_pages = (total_items + limit - 1) // limit if limit > 0 else 1

            pagination_meta = PaginationMeta(
                page=page,
                take=limit,
                total_items=total_items,
                total_pages=total_pages,
                has_previous=page > 1,
                has_next=page < total_pages,
                start_index=skip + 1 if total_items > 0 else 0,
                end_index=min(skip + limit, total_items),
            )

            recommendation_publics = []
            for rec, resource_name in recommendations:
                rec_dict = rec.model_dump()
                rec_dict["resource_name"] = resource_name or None
                recommendation_publics.append(
                    RecommendationPublic.model_validate(rec_dict)
                )
            result = RecommendationsPublic(
                data=recommendation_publics, meta=pagination_meta
            )

            # Cache the result
            self._set_cache(cache_key, result.model_dump())

            return result

        except Exception as e:
            logger.exception(
                f"Error getting recommendations with pagination async: {e}"
            )
            raise

    async def get_recommendation_by_id(
        self, recommendation_id: uuid.UUID, user: AuthorizedUser
    ) -> Recommendation | None:
        """Get a recommendation by ID with access control"""
        if not self.async_session:
            raise ValueError("Async session not provided")

        recommendation = await self.async_session.get(Recommendation, recommendation_id)
        if not recommendation:
            return None

        # Users can view recommendations in their workspace
        if (
            not user.is_superuser
            and recommendation.workspace_id != user.current_workspace_id
        ):
            return None

        return recommendation

    async def create_recommendation_with_validation(
        self, recommendation_in: RecommendationCreate, user: AuthorizedUser
    ) -> Recommendation | None:
        """Create a new recommendation with validation"""
        if not self.async_session:
            raise ValueError("Async session not provided")

        # If resource_id is provided, validate it belongs to the same workspace
        resource = None
        if recommendation_in.resource_id:
            resource = await self.async_session.get(
                Resource, recommendation_in.resource_id
            )
            if not resource:
                return None

            if resource.workspace_id != recommendation_in.workspace_id:
                return None

        # Create recommendation data with created_by field
        recommendation_data = recommendation_in.model_dump()
        recommendation_data["created_by"] = user.id
        recommendation_data["resource_type"] = resource.type if resource else "General"

        recommendation = Recommendation.model_validate(recommendation_data)
        self.async_session.add(recommendation)
        await self.async_session.commit()
        await self.async_session.refresh(recommendation)
        # Invalidate caches related to recommendations to avoid stale reads
        self._invalidate_model_cache(recommendation.id)
        return recommendation

    async def update_recommendation_by_id(
        self,
        recommendation_id: uuid.UUID,
        recommendation_in: RecommendationUpdate,
        user: AuthorizedUser,
    ) -> Recommendation | None:
        """Update a recommendation by ID with validation"""
        if not self.async_session:
            raise ValueError("Async session not provided")

        recommendation = await self.async_session.get(Recommendation, recommendation_id)
        if not recommendation:
            return None

        # Only the creator or superuser can update recommendations
        if not user.is_superuser and recommendation.created_by != user.id:
            return None

        update_dict = recommendation_in.model_dump(exclude_unset=True)
        recommendation.sqlmodel_update(update_dict)
        self.async_session.add(recommendation)
        await self.async_session.commit()
        await self.async_session.refresh(recommendation)
        # Invalidate caches related to this model to ensure fresh pagination/results
        self._invalidate_model_cache(recommendation.id)
        return recommendation

    async def delete_recommendation_by_id(
        self, recommendation_id: uuid.UUID, user: AuthorizedUser
    ) -> bool:
        """Delete a recommendation by ID with validation"""
        if not self.async_session:
            raise ValueError("Async session not provided")

        recommendation = await self.async_session.get(Recommendation, recommendation_id)
        if not recommendation:
            return False

        # Only the creator or superuser can delete recommendations
        if not user.is_superuser and recommendation.created_by != user.id:
            return False

        await self.async_session.delete(recommendation)
        await self.async_session.commit()
        # Invalidate caches after deletion
        self._invalidate_model_cache(recommendation_id)
        return True

    async def update_recommendation_status_by_id(
        self,
        recommendation_id: uuid.UUID,
        status: RecommendationStatus,
        user: AuthorizedUser,
    ) -> Recommendation | None:
        """Update recommendation status by ID with validation"""
        if not self.async_session:
            raise ValueError("Async session not provided")

        recommendation = await self.async_session.get(Recommendation, recommendation_id)
        if not recommendation:
            return None

        # Only the creator or superuser can update status
        if not user.is_superuser and recommendation.created_by != user.id:
            return None

        recommendation.status = status
        if status == RecommendationStatus.IMPLEMENTED:
            recommendation.implemented_at = datetime.now(UTC)
            recommendation.implemented_by = user.id

        self.async_session.add(recommendation)
        await self.async_session.commit()
        await self.async_session.refresh(recommendation)
        # Invalidate caches after status change
        self._invalidate_model_cache(recommendation.id)
        return recommendation

    def _format_recommendations_response(
        self, recommendations: RecommendationsPublic
    ) -> str:
        """Format recommendations for display."""
        try:
            content = f"Currently there are {recommendations.meta.total_items} recommendations for this resource.\n"

            for recommendation in recommendations.data:
                content += f"\n- ID: {recommendation.id}"
                content += f"\n- Title: {recommendation.title}"
                content += f"\n- Type: {recommendation.type}"
                content += f"\n- Description: {recommendation.description}"
                content += f"\n- Potential Savings: {recommendation.potential_savings}"
                content += f"\n- Effort: {recommendation.effort}"
                content += f"\n- Risk: {recommendation.risk}"
                content += f"\n- Status: {recommendation.status}"
                content += (
                    f"\n- Potential Savings: {recommendation.potential_savings}\n"
                )

            return content
        except Exception as e:
            logger.error(f"Error formatting recommendations response: {e}")
            import json

            return json.dumps(
                {
                    "status": "error",
                    "message": f"Failed to format recommendations: {str(e)}",
                }
            )
