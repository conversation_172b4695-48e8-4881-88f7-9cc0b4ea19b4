#,Task,Description,Prompt,Primary Agent,Category,Connection,Service,Service Name,Priority,Impact
1,EC2 Right-Sizing Analysis,Identify underutilized EC2 instances with <20% CPU usage over 14 days to reduce compute costs,@alex please analyze EC2 instances with <20% CPU utilization over 2 weeks. #recommend right-sizing options and #alert with potential monthly savings.,@alex,Cost Optimization,AWS,Compute,EC2,★★★,★★★
2,Unattached EBS Volume Cleanup,Find and remove orphaned EBS volumes that are incurring unnecessary storage costs,@alex please identify unattached EBS volumes older than 7 days. #recommend deletion candidates and #alert with cost savings potential.,@alex,Cost Optimization,AWS,Storage,EBS,★★★,★★★
3,Reserved Instance Optimization,Analyze EC2 usage patterns to recommend optimal Reserved Instance purchases,@alex please analyze EC2 usage patterns over 90 days. #recommend Reserved Instance strategy and #report potential cost savings.,@alex,Cost Optimization,AWS,Compute,EC2,★★★,★★★
4,S3 Storage Class Analysis,Review S3 objects for lifecycle policy optimization and storage class transitions,@alex please analyze S3 storage patterns and access frequency. #recommend lifecycle policies and #visual storage cost breakdown.,@alex,Cost Optimization,AWS,Storage,S3,★★★,★★★
4,Savings Plans Analysis,"Analyze compute usage patterns to recommend optimal Savings Plans purchases for EC2, Fargate, and Lambda","@alex please analyze compute spend across EC2, Fargate, and Lambda over 12 months. #recommend Savings Plans strategy with hourly commitments and #report potential savings up to 72%.",@alex,Cost Optimization,AWS,Compute,Savings Plans,★★★,★★★
,Idle Load Balancer Detection,Find Application Load Balancers with no active targets to eliminate waste,@alex please identify ALBs with zero active targets over 7 days. #alert with monthly costs and #recommend termination candidates.,@alex,Cost Optimization,AWS,Network,ALB,★★★,★★
6,RDS Instance Right-Sizing,Analyze RDS performance metrics to identify oversized database instances,@alex please review RDS CPU and memory utilization over 30 days. #recommend instance type optimizations and #report cost impact.,@alex,Cost Optimization,AWS,Database,RDS,★★★,★★★
7,CloudWatch Log Retention,Optimize CloudWatch Logs retention policies to reduce storage costs,@alex please audit CloudWatch log groups with retention >90 days. #recommend retention policies and #alert with storage savings.,@alex,Cost Optimization,AWS,Serverless,CloudWatch,★★,★★
8,NAT Gateway Cost Analysis,Review NAT Gateway usage and recommend cost-effective alternatives,@alex please analyze NAT Gateway data transfer costs over 30 days. #recommend NAT Instance alternatives and #visual cost comparison.,@alex,Cost Optimization,AWS,Network,NAT Gateway,★★★,★★
9,Auto Scaling Group Health,Monitor ASG instances for proper scaling behavior and cost efficiency,@alex please review ASG scaling metrics and instance health over 14 days. #recommend scaling policy adjustments and #alert on inefficiencies.,@alex,Scalability,AWS,Compute,Auto Scaling,★★★,★★
10,Snapshot Cleanup Strategy,Identify old EBS snapshots that can be safely deleted to reduce costs,@alex please find EBS snapshots older than 90 days not linked to AMIs. #recommend deletion strategy and #report storage savings.,@alex,Cost Optimization,AWS,Storage,EBS,★★,★★
11,Spot Instance Opportunities,Analyze workloads suitable for Spot Instance cost savings,@alex please identify stateless workloads suitable for Spot Instances. #recommend Spot Fleet configurations and #visual potential savings.,@alex,Cost Optimization,AWS,Compute,EC2,★★★,★★
12,CloudFront Distribution Analysis,Review CloudFront distributions for caching efficiency and cost optimization,@alex please analyze CloudFront cache hit ratios and origin requests. #recommend caching improvements and #report performance gains.,@alex,Operational Efficiency,AWS,Network,CloudFront,★★,★★
13,Unused Elastic IPs,Identify and release unassociated Elastic IP addresses,@alex please find unassociated Elastic IPs older than 24 hours. #alert with monthly charges and #recommend release candidates.,@alex,Cost Optimization,AWS,Network,Elastic IP,★★,★
14,Lambda Function Optimization,Analyze Lambda function memory and timeout settings for cost efficiency,@alex please review Lambda functions with >50% unused memory allocation. #recommend memory optimizations and #report cost impact.,@alex,Cost Optimization,AWS,Serverless,Lambda,★★,★★
15,ECS Task Right-Sizing,Optimize ECS task definitions for proper resource allocation,@alex please analyze ECS task CPU and memory utilization over 14 days. #recommend task definition adjustments and #visual resource usage.,@alex,Cost Optimization,AWS,Compute,ECS,★★,★★
16,VPC Endpoint Cost Analysis,Evaluate VPC endpoints for cost-effectiveness vs NAT Gateway usage,@alex please compare VPC endpoint costs vs NAT Gateway for S3/DynamoDB traffic. #recommend optimal configuration and #report savings.,@alex,Cost Optimization,AWS,Network,VPC,★★,★★
17,AMI Cleanup Process,Identify and deregister unused AMIs to reduce storage costs,@alex please find AMIs not used by any instances/launch templates in 60 days. #recommend cleanup strategy and #alert with storage costs.,@alex,Cost Optimization,AWS,Compute,AMI,★★,★
18,RDS Multi-AZ Analysis,Review RDS instances for unnecessary Multi-AZ deployments,@alex please identify dev/test RDS instances with Multi-AZ enabled. #recommend single-AZ candidates and #report cost savings.,@alex,Cost Optimization,AWS,Database,RDS,★★,★★
19,CloudTrail Log Analysis,Optimize CloudTrail logging configuration for cost and security,@alex please review CloudTrail data events and S3 storage costs. #recommend log optimization and #visual cost breakdown.,@alex,Cost Optimization,AWS,Serverless,CloudTrail,★★,★
20,Route53 Health Check Review,Analyze Route53 health checks for necessity and cost optimization,@alex please audit Route53 health checks for unused or redundant checks. #recommend consolidation and #alert with monthly savings.,@alex,Cost Optimization,AWS,Network,Route53,★,★
21,DynamoDB Capacity Planning,Optimize DynamoDB tables for proper read/write capacity allocation,@alex please analyze DynamoDB throttling and unused capacity over 30 days. #recommend capacity adjustments and #report cost optimization.,@alex,Cost Optimization,AWS,Database,DynamoDB,★★,★★
22,EKS Node Group Optimization,Right-size EKS worker nodes based on pod resource requests,@alex please analyze EKS node utilization and pod placement efficiency. #recommend node group adjustments and #visual resource allocation.,@alex,Cost Optimization,AWS,Compute,EKS,★★,★★
23,Data Transfer Cost Analysis,Identify high data transfer costs between AWS services and regions,@alex please analyze inter-region and inter-AZ data transfer costs over 30 days. #recommend architecture optimizations and #report savings opportunities.,@alex,Cost Optimization,AWS,Network,Data Transfer,★★★,★★
24,S3 Transfer Acceleration Review,Evaluate S3 Transfer Acceleration usage for cost-effectiveness,@alex please analyze S3 Transfer Acceleration costs vs benefits. #recommend usage optimization and #visual cost comparison.,@alex,Cost Optimization,AWS,Storage,S3,★,★
25,EFS Storage Optimization,Analyze EFS file systems for storage class optimization,@alex please review EFS access patterns and storage class distribution. #recommend lifecycle policies and #report cost optimization.,@alex,Cost Optimization,AWS,Storage,EFS,★★,★★
26,Redshift Cluster Analysis,Optimize Redshift cluster size and utilization,@alex please analyze Redshift CPU utilization and storage usage over 30 days. #recommend cluster adjustments and #visual performance metrics.,@alex,Cost Optimization,AWS,Database,Redshift,★★,★★
27,ElastiCache Instance Review,Right-size ElastiCache clusters based on memory utilization,@alex please analyze ElastiCache memory usage and connection patterns. #recommend instance type optimization and #report cost impact.,@alex,Cost Optimization,AWS,Database,ElastiCache,★★,★★
28,SQS Queue Cost Analysis,Review SQS queue usage patterns for cost optimization,@alex please analyze SQS message volumes and retention policies. #recommend queue optimization and #alert with cost savings.,@alex,Cost Optimization,AWS,Serverless,SQS,★,★
29,SNS Topic Optimization,Analyze SNS topics for unused subscriptions and cost efficiency,@alex please review SNS topics with low message volumes or failed deliveries. #recommend cleanup and #report optimization opportunities.,@alex,Cost Optimization,AWS,Serverless,SNS,★,★
30,API Gateway Usage Review,Optimize API Gateway configurations for cost and performance,@alex please analyze API Gateway request patterns and caching efficiency. #recommend optimization strategies and #visual usage metrics.,@alex,Cost Optimization,AWS,Serverless,API Gateway,★★,★★
31,EC2 Instance Scheduling,Implement instance scheduling for non-production environments,@alex please identify dev/test instances running 24/7 without usage. #recommend scheduling automation and #report potential savings.,@alex,Cost Optimization,AWS,Compute,EC2,★★★,★★
32,Storage Gateway Cost Review,Analyze AWS Storage Gateway usage for cost optimization,@alex please review Storage Gateway volume and bandwidth usage. #recommend configuration adjustments and #visual cost breakdown.,@alex,Cost Optimization,AWS,Storage,Storage Gateway,★,★
33,Direct Connect Utilization,Monitor Direct Connect usage and cost-effectiveness,@alex please analyze Direct Connect bandwidth utilization over 30 days. #recommend capacity adjustments and #report cost optimization.,@alex,Cost Optimization,AWS,Network,Direct Connect,★★,★★
34,Fargate Task Optimization,Right-size Fargate tasks for optimal resource allocation,@alex please analyze Fargate task CPU and memory utilization. #recommend task size adjustments and #visual resource efficiency.,@alex,Cost Optimization,AWS,Compute,Fargate,★★,★★
35,GuardDuty Finding Review,Analyze GuardDuty findings for operational efficiency,@alex please review GuardDuty findings and false positive patterns. #recommend suppression rules and #alert on critical findings.,@alex,Security,AWS,Serverless,GuardDuty,★★,★★
36,Config Rule Optimization,Review AWS Config rules for cost and compliance efficiency,@alex please analyze AWS Config rule evaluations and costs. #recommend rule optimization and #report compliance coverage.,@alex,Cost Optimization,AWS,Serverless,Config,★★,★
37,CloudFormation Stack Drift,Monitor CloudFormation stacks for configuration drift,@alex please detect CloudFormation stack drift in production resources. #alert on critical drift and #recommend remediation steps.,@alex,Operational Efficiency,AWS,Serverless,CloudFormation,★★,★★
38,Trusted Advisor Insights,Leverage Trusted Advisor recommendations for optimization,@alex please review Trusted Advisor cost optimization recommendations. #recommend implementation priorities and #dashboard with savings potential.,@alex,Cost Optimization,AWS,Serverless,Trusted Advisor,★★★,★★
39,ECS Service Auto Scaling,Optimize ECS service auto scaling policies,@alex please analyze ECS service scaling patterns and target tracking. #recommend scaling policy improvements and #visual performance metrics.,@alex,Scalability,AWS,Compute,ECS,★★,★★
40,Lambda Cold Start Analysis,Identify Lambda functions with high cold start impact,@alex please analyze Lambda cold start frequency and duration. #recommend optimization strategies and #report performance improvements.,@alex,Operational Efficiency,AWS,Serverless,Lambda,★★,★★
41,RDS Read Replica Review,Optimize RDS read replica configuration for cost and performance,@alex please analyze read replica utilization and cross-AZ costs. #recommend replica optimization and #visual read/write distribution.,@alex,Cost Optimization,AWS,Database,RDS,★★,★★
42,S3 Intelligent Tiering,Implement S3 Intelligent Tiering for automated cost optimization,@alex please analyze S3 access patterns suitable for Intelligent Tiering. #recommend implementation strategy and #report projected savings.,@alex,Cost Optimization,AWS,Storage,S3,★★,★★
43,CloudWatch Metric Filters,Optimize CloudWatch custom metrics for cost efficiency,@alex please review custom CloudWatch metrics and filter usage. #recommend metric optimization and #alert with cost reduction opportunities.,@alex,Cost Optimization,AWS,Serverless,CloudWatch,★,★
44,ELB Connection Draining,Review load balancer connection draining settings,@alex please analyze ELB deregistration delays and connection draining. #recommend timeout optimizations and #visual request handling patterns.,@alex,Operational Efficiency,AWS,Network,ELB,★,★
45,Auto Scaling Policies,Optimize EC2 Auto Scaling policies for efficiency,@alex please review ASG scaling policies and cooldown periods. #recommend policy improvements and #dashboard with scaling metrics.,@alex,Scalability,AWS,Compute,Auto Scaling,★★,★★
46,S3 Cross-Region Replication,Analyze S3 CRR for necessity and cost optimization,@alex please review S3 Cross-Region Replication configurations and costs. #recommend optimization strategies and #visual replication metrics.,@alex,Cost Optimization,AWS,Storage,S3,★★,★
47,EC2 Instance Metadata,Review EC2 instances for proper metadata configuration,@alex please audit EC2 instances for IMDSv2 enforcement and metadata access. #alert on security gaps and #recommend configuration updates.,@alex,Security,AWS,Compute,EC2,★★,★★
48,Route53 Resolver Rules,Optimize Route53 Resolver rules for DNS efficiency,@alex please analyze Route53 Resolver query patterns and rule effectiveness. #recommend rule optimization and #report query cost analysis.,@alex,Cost Optimization,AWS,Network,Route53,★,★
49,Systems Manager Patch,Monitor Systems Manager patch compliance across instances,@alex please review SSM patch compliance status and missing updates. #alert on critical patches and #dashboard with compliance metrics.,@alex,Security,AWS,Serverless,Systems Manager,★★,★★
50,Cost Anomaly Detection,Implement AWS Cost Anomaly Detection for spend monitoring,@alex please configure Cost Anomaly Detection thresholds and alerts. #recommend detection parameters and #dashboard with spend anomalies.,@alex,Cost Optimization,AWS,Serverless,Cost Explorer,★★★,★★
51,EBS GP3 Migration,Migrate EBS GP2 volumes to GP3 for cost savings,@alex please identify GP2 volumes suitable for GP3 migration. #recommend migration schedule and #report cost savings potential.,@alex,Cost Optimization,AWS,Storage,EBS,★★★,★★
52,RDS Storage Autoscaling,Configure RDS storage autoscaling to optimize costs,@alex please review RDS instances without storage autoscaling enabled. #recommend autoscaling configuration and #visual storage growth patterns.,@alex,Cost Optimization,AWS,Database,RDS,★★,★★
53,Lambda Memory Profiling,Profile Lambda function memory usage for right-sizing,@alex please analyze Lambda memory utilization reports and billing data. #recommend memory adjustments and #report performance vs cost optimization.,@alex,Cost Optimization,AWS,Serverless,Lambda,★★,★★
54,CloudWatch Dashboard Optimization,Consolidate and optimize CloudWatch dashboards,@alex please review CloudWatch dashboard widget counts and costs. #recommend dashboard consolidation and #visual monitoring efficiency.,@alex,Cost Optimization,AWS,Serverless,CloudWatch,★,★
55,EC2 Image Builder Optimization,Optimize EC2 Image Builder pipelines for cost efficiency,@alex please analyze Image Builder pipeline execution frequency and costs. #recommend schedule optimization and #alert on unused pipelines.,@alex,Cost Optimization,AWS,Compute,Image Builder,★,★
56,AWS Backup Cost Review,Analyze AWS Backup storage costs and retention policies,@alex please review AWS Backup vault costs and recovery point retention. #recommend retention optimization and #report storage savings.,@alex,Cost Optimization,AWS,Storage,AWS Backup,★★,★★
57,Step Functions Workflow,Optimize Step Functions workflows for cost and performance,@alex please analyze Step Functions execution patterns and state transitions. #recommend workflow optimization and #visual execution metrics.,@alex,Cost Optimization,AWS,Serverless,Step Functions,★,★
58,KMS Key Usage Analysis,Review KMS key usage patterns for cost optimization,@alex please analyze KMS key request volumes and unused keys. #recommend key consolidation and #alert with cost reduction opportunities.,@alex,Cost Optimization,AWS,Serverless,KMS,★,★
59,Glue Job Optimization,Right-size AWS Glue jobs for optimal resource usage,@alex please analyze Glue job execution times and DPU allocation. #recommend job optimization and #report cost efficiency improvements.,@alex,Cost Optimization,AWS,Serverless,Glue,★★,★
60,Transit Gateway Cost,Analyze Transit Gateway attachment costs and utilization,@alex please review Transit Gateway data processing charges over 30 days. #recommend attachment optimization and #visual traffic patterns.,@alex,Cost Optimization,AWS,Network,Transit Gateway,★★,★★
61,WorkSpaces Usage Review,Monitor WorkSpaces utilization for cost optimization,@alex please analyze WorkSpaces login patterns and AutoStop configurations. #recommend usage optimization and #report potential savings.,@alex,Cost Optimization,AWS,Compute,WorkSpaces,★,★
62,CodeBuild Project Costs,Optimize CodeBuild projects for build efficiency,@alex please analyze CodeBuild compute time and build frequency patterns. #recommend instance type optimization and #visual build metrics.,@alex,Cost Optimization,AWS,Serverless,CodeBuild,★,★
63,ECS Capacity Providers,Optimize ECS capacity providers for cost efficiency,@alex please analyze ECS capacity provider scaling and Spot usage. #recommend provider optimization and #dashboard with cost metrics.,@alex,Cost Optimization,AWS,Compute,ECS,★★,★★
64,CloudFront Cache Behavior,Optimize CloudFront cache behaviors for better efficiency,@alex please analyze CloudFront cache hit ratios by behavior pattern. #recommend caching improvements and #report origin load reduction.,@alex,Operational Efficiency,AWS,Network,CloudFront,★★,★★
65,EMR Cluster Sizing,Right-size EMR clusters based on job requirements,@alex please analyze EMR cluster utilization and job execution patterns. #recommend cluster optimization and #visual resource efficiency.,@alex,Cost Optimization,AWS,Compute,EMR,★★,★
66,Athena Query Optimization,Optimize Athena queries for cost and performance,@alex please analyze Athena query costs and data scanned patterns. #recommend query optimization and #report cost per query reduction.,@alex,Cost Optimization,AWS,Database,Athena,★,★
67,SageMaker Endpoint Costs,Optimize SageMaker endpoints for inference cost efficiency,@alex please analyze SageMaker endpoint utilization and auto-scaling. #recommend endpoint optimization and #visual inference patterns.,@alex,Cost Optimization,AWS,Compute,SageMaker,★,★
68,Inspector Assessment,Review Inspector findings for security optimization,@alex please analyze Inspector assessment findings and remediation status. #alert on critical vulnerabilities and #dashboard with security metrics.,@alex,Security,AWS,Serverless,Inspector,★★,★★
69,Macie Data Discovery,Optimize Macie data discovery jobs for cost efficiency,@alex please review Macie job frequencies and data processing volumes. #recommend job optimization and #report sensitive data insights.,@alex,Cost Optimization,AWS,Serverless,Macie,★,★
70,DocumentDB Cluster Review,Analyze DocumentDB clusters for right-sizing opportunities,@alex please review DocumentDB CPU and memory utilization patterns. #recommend cluster optimization and #visual performance metrics.,@alex,Cost Optimization,AWS,Database,DocumentDB,★,★
71,Neptune Database Sizing,Optimize Neptune database instances for graph workloads,@alex please analyze Neptune query performance and instance utilization. #recommend instance optimization and #report cost efficiency.,@alex,Cost Optimization,AWS,Database,Neptune,★,★
72,AppSync API Costs,Review AppSync API usage patterns for cost optimization,@alex please analyze AppSync request volumes and resolver execution costs. #recommend API optimization and #visual usage patterns.,@alex,Cost Optimization,AWS,Serverless,AppSync,★,★
73,Kinesis Stream Sharding,Optimize Kinesis streams for proper shard allocation,@alex please analyze Kinesis stream throughput and shard utilization. #recommend shard optimization and #dashboard with streaming metrics.,@alex,Cost Optimization,AWS,Serverless,Kinesis,★★,★
74,MSK Cluster Efficiency,Review Managed Kafka clusters for cost optimization,@alex please analyze MSK cluster utilization and broker sizing. #recommend cluster optimization and #visual throughput metrics.,@alex,Cost Optimization,AWS,Serverless,MSK,★,★
75,Batch Job Queue Costs,Optimize AWS Batch job queues for efficient resource usage,@alex please analyze Batch job execution patterns and compute environment costs. #recommend queue optimization and #report resource efficiency.,@alex,Cost Optimization,AWS,Compute,Batch,★,★
76,Lightsail Instance Review,Monitor Lightsail instances for optimization opportunities,@alex please analyze Lightsail resource utilization and transfer allowances. #recommend instance optimization and #alert on overage costs.,@alex,Cost Optimization,AWS,Compute,Lightsail,★,★
77,WAF Rule Efficiency,Optimize WAF rules for cost and security effectiveness,@alex please analyze WAF rule evaluation counts and blocked requests. #recommend rule optimization and #visual security metrics.,@alex,Cost Optimization,AWS,Network,WAF,★★,★
78,Shield Advanced Costs,Review Shield Advanced usage for DDoS protection ROI,@alex please analyze Shield Advanced costs vs DDoS mitigation value. #recommend protection optimization and #report security coverage.,@alex,Cost Optimization,AWS,Network,Shield,★,★
79,Connect Contact Center,Optimize Amazon Connect usage for call center efficiency,@alex please analyze Connect usage patterns and agent productivity metrics. #recommend configuration optimization and #dashboard with call metrics.,@alex,Operational Efficiency,AWS,Serverless,Connect,★,★
80,Pinpoint Campaign Costs,Review Pinpoint campaigns for marketing efficiency,@alex please analyze Pinpoint message volumes and campaign performance. #recommend campaign optimization and #visual engagement metrics.,@alex,Cost Optimization,AWS,Serverless,Pinpoint,★,★
81,MediaLive Channel Costs,Optimize MediaLive channels for streaming efficiency,@alex please analyze MediaLive encoding costs and channel utilization. #recommend encoding optimization and #report streaming efficiency.,@alex,Cost Optimization,AWS,Serverless,MediaLive,★,★
82,Rekognition API Usage,Monitor Rekognition API costs for image analysis efficiency,@alex please analyze Rekognition request patterns and accuracy metrics. #recommend usage optimization and #visual analysis results.,@alex,Cost Optimization,AWS,Serverless,Rekognition,★,★
83,Textract Document Processing,Optimize Textract usage for document analysis cost efficiency,@alex please analyze Textract page processing volumes and accuracy rates. #recommend processing optimization and #report cost per document.,@alex,Cost Optimization,AWS,Serverless,Textract,★,★
84,Comprehend Text Analysis,Review Comprehend usage patterns for NLP cost optimization,@alex please analyze Comprehend inference costs and batch job efficiency. #recommend processing optimization and #visual analysis metrics.,@alex,Cost Optimization,AWS,Serverless,Comprehend,★,★
85,Polly Text-to-Speech,Optimize Polly usage for voice synthesis cost efficiency,@alex please analyze Polly character processing costs and voice usage patterns. #recommend synthesis optimization and #report audio generation metrics.,@alex,Cost Optimization,AWS,Serverless,Polly,★,★
86,Translate Service Costs,Review Translate service usage for language processing efficiency,@alex please analyze Translate character volumes and language pair costs. #recommend translation optimization and #visual usage patterns.,@alex,Cost Optimization,AWS,Serverless,Translate,★,★
87,GameLift Fleet Costs,Optimize GameLift fleets for gaming workload efficiency,@alex please analyze GameLift fleet utilization and player session patterns. #recommend fleet optimization and #dashboard with gaming metrics.,@alex,Cost Optimization,AWS,Compute,GameLift,★,★
88,IoT Core Message Costs,Review IoT Core message patterns for device communication efficiency,@alex please analyze IoT message volumes and device connectivity costs. #recommend messaging optimization and #visual device metrics.,@alex,Cost Optimization,AWS,Serverless,IoT Core,★,★
89,Timestream Database Costs,Optimize Timestream database usage for time-series efficiency,@alex please analyze Timestream ingestion rates and query patterns. #recommend database optimization and #report time-series performance.,@alex,Cost Optimization,AWS,Database,Timestream,★,★
90,QLDB Ledger Optimization,Review QLDB ledger usage for blockchain efficiency,@alex please analyze QLDB transaction patterns and storage costs. #recommend ledger optimization and #visual transaction metrics.,@alex,Cost Optimization,AWS,Database,QLDB,★,★
91,X-Ray Tracing Costs,Optimize X-Ray tracing for application monitoring efficiency,@alex please analyze X-Ray trace volumes and sampling rates. #recommend tracing optimization and #dashboard with performance insights.,@alex,Cost Optimization,AWS,Serverless,X-Ray,★,★
92,CloudSearch Domain Costs,Review CloudSearch domains for search efficiency,@alex please analyze CloudSearch request patterns and index sizes. #recommend domain optimization and #visual search metrics.,@alex,Cost Optimization,AWS,Serverless,CloudSearch,★,★
93,Elasticsearch Service Sizing,Optimize Elasticsearch clusters for search workload efficiency,@alex please analyze Elasticsearch cluster utilization and query performance. #recommend cluster optimization and #report search efficiency.,@alex,Cost Optimization,AWS,Database,Elasticsearch,★★,★
94,OpenSearch Cluster Review,Review OpenSearch clusters for cost and performance optimization,@alex please analyze OpenSearch node utilization and index management. #recommend cluster optimization and #visual search performance.,@alex,Cost Optimization,AWS,Database,OpenSearch,★★,★
95,MemoryDB Redis Costs,Optimize MemoryDB for Redis clusters for caching efficiency,@alex please analyze MemoryDB throughput patterns and node utilization. #recommend cluster optimization and #dashboard with cache metrics.,@alex,Cost Optimization,AWS,Database,MemoryDB,★,★
96,AppFlow Data Transfer,Review AppFlow data transfer costs for integration efficiency,@alex please analyze AppFlow execution patterns and data volume costs. #recommend flow optimization and #report integration efficiency.,@alex,Cost Optimization,AWS,Serverless,AppFlow,★,★
97,DataSync Transfer Jobs,Optimize DataSync jobs for data migration efficiency,@alex please analyze DataSync job execution costs and transfer patterns. #recommend job optimization and #visual transfer metrics.,@alex,Cost Optimization,AWS,Storage,DataSync,★,★
98,Snow Family Usage,Review Snow device usage for large-scale data transfer efficiency,@alex please analyze Snow device utilization and transfer completion rates. #recommend usage optimization and #report transfer efficiency.,@alex,Cost Optimization,AWS,Storage,Snow Family,★,★
99,Outposts Rack Utilization,Monitor Outposts rack utilization for hybrid cloud efficiency,@alex please analyze Outposts compute and storage utilization patterns. #recommend capacity optimization and #dashboard with hybrid metrics.,@alex,Cost Optimization,AWS,Compute,Outposts,★,★
100,Local Zones Performance,Optimize Local Zones usage for low-latency applications,@alex please analyze Local Zones resource utilization and latency metrics. #recommend deployment optimization and #visual performance comparison.,@alex,Operational Efficiency,AWS,Compute,Local Zones,★,★
1,Compute Engine Cost Analysis,Analyze underutilized VMs consuming budget without delivering proportional value to optimize spending.,@alex please identify Compute Engine instances with <20% CPU utilization over 14 days. #recommend right-sizing options and #alert with potential monthly savings.,@alex,Cost Optimization,GCP,Compute,Compute Engine,★★★,★★★
2,Cloud Storage Lifecycle Management,Review storage buckets for objects that can be moved to cheaper storage classes or deleted entirely.,@alex please audit Cloud Storage buckets for objects >90 days in Standard class. #recommend lifecycle policies for Nearline/Coldline transitions.,@alex,Cost Optimization,GCP,Storage,Cloud Storage,★★★,★★★
3,Persistent Disk Optimization,Identify oversized or unused persistent disks that are increasing storage costs unnecessarily.,@alex please find persistent disks with <50% utilization or unattached disks >7 days. #alert team and #recommend cleanup actions.,@alex,Cost Optimization,GCP,Storage,Persistent Disk,★★★,★★★
4,BigQuery Cost Control,Monitor BigQuery usage patterns to prevent unexpected charges from inefficient queries or large datasets.,@alex please analyze BigQuery jobs exceeding $100 daily spend. #recommend query optimization and #alert on cost anomalies.,@alex,Cost Optimization,GCP,Database,BigQuery,★★★,★★★
5,Load Balancer Efficiency Check,Ensure load balancers are properly configured and not over-provisioned for actual traffic patterns.,@alex please review Load Balancers with <30% utilization over 30 days. #recommend rightsizing and #visual traffic distribution charts.,@alex,Cost Optimization,GCP,Network,Load Balancing,★★★,★★★
6,VM Preemptible Migration,Identify suitable workloads that can be migrated to preemptible instances for significant cost savings.,@alex please find non-critical Compute Engine instances suitable for preemptible migration. #recommend candidates and estimated savings.,@alex,Cost Optimization,GCP,Compute,Compute Engine,★★★,★★★
7,Cloud SQL Right-sizing,Optimize Cloud SQL instances that are over-provisioned relative to their actual usage patterns.,@alex please analyze Cloud SQL instances with <40% CPU/memory usage over 2 weeks. #recommend machine type adjustments.,@alex,Cost Optimization,GCP,Database,Cloud SQL,★★★,★★
8,Network Egress Cost Analysis,Review network egress charges to identify opportunities for cost reduction through architecture changes.,@alex please identify top 10 resources generating highest egress costs. #recommend CDN implementation or regional optimization.,@alex,Cost Optimization,GCP,Network,VPC,★★★,★★★
9,Compute Engine Auto-scaling Setup,Implement auto-scaling for VM instances to handle traffic variations efficiently without over-provisioning.,@alex please configure auto-scaling for production Compute Engine groups. #recommend scaling policies based on metrics.,@alex,Scalability,GCP,Compute,Compute Engine,★★★,★★★
10,Cloud Functions Cold Start Optimization,Reduce Cloud Functions cold start times and optimize memory allocation for better performance.,@alex please analyze Cloud Functions with >3 second cold starts or memory waste >50%. #recommend optimization strategies.,@alex,Operational Efficiency,GCP,Serverless,Cloud Functions,★★★,★★
11,Orphaned Resource Cleanup,Identify and clean up resources no longer associated with active projects or applications.,"@alex please find orphaned static IPs, snapshots, and images >30 days old. #alert team before cleanup and estimate savings.",@alex,Cost Optimization,GCP,Compute,Multiple,★★★,★★
12,Cloud Storage Regional Optimization,Ensure data is stored in optimal regions to minimize latency and costs for primary user base.,@alex please review multi-regional storage buckets for regional optimization opportunities. #recommend relocations for 80% cost reduction.,@alex,Cost Optimization,GCP,Storage,Cloud Storage,★★★,★★
13,VM Boot Disk Optimization,Right-size boot disks and convert to more cost-effective disk types where appropriate.,@alex please identify oversized boot disks (>50GB unused space) and HDD upgrade candidates. #recommend optimizations.,@alex,Cost Optimization,GCP,Storage,Persistent Disk,★★,★★
14,App Engine Scaling Analysis,Optimize App Engine scaling settings to balance performance and cost effectiveness.,@alex please review App Engine services with idle instances >4 hours daily. #recommend scaling configuration adjustments.,@alex,Cost Optimization,GCP,Serverless,App Engine,★★,★★
15,Cloud CDN Performance Review,Analyze CDN hit rates and identify opportunities to improve cache performance and reduce origin load.,@alex please review Cloud CDN cache hit rates <80% and high origin traffic. #recommend caching policy improvements.,@alex,Operational Efficiency,GCP,Network,Cloud CDN,★★★,★★
16,Compute Engine Startup Script Optimization,Review and optimize VM startup scripts for faster boot times and reduced provisioning costs.,@alex please analyze VMs with startup times >5 minutes. #recommend script optimizations to reduce provisioning time.,@alex,Operational Efficiency,GCP,Compute,Compute Engine,★★,★★
17,BigQuery Slot Utilization,Monitor BigQuery slot usage to optimize query performance and avoid unnecessary on-demand charges.,@alex please analyze BigQuery jobs with slot utilization <30% and queue times >2 minutes. #recommend slot optimization.,@alex,Cost Optimization,GCP,Database,BigQuery,★★★,★★
18,Cloud Storage Access Pattern Analysis,Analyze access patterns to optimize storage classes and reduce retrieval costs.,@alex please review storage objects accessed <5 times in 90 days in Standard class. #recommend storage class transitions.,@alex,Cost Optimization,GCP,Storage,Cloud Storage,★★,★★
19,VM Instance Group Health Monitoring,Ensure managed instance groups have proper health checks and auto-healing configured.,@alex please check instance groups without health checks or auto-healing. #recommend monitoring setup and #alert on issues.,@alex,Operational Efficiency,GCP,Compute,Compute Engine,★★★,★★
20,Cloud SQL Backup Cost Optimization,Review backup retention policies to balance data protection needs with storage costs.,@alex please analyze Cloud SQL backups >365 days old and backup storage >200GB. #recommend retention policy updates.,@alex,Cost Optimization,GCP,Database,Cloud SQL,★★,★★
21,Network Load Balancer Configuration,Optimize load balancer configurations for better traffic distribution and reduced latency.,@alex please review load balancers with uneven traffic distribution (>70% to single backend). #recommend rebalancing strategies.,@alex,Operational Efficiency,GCP,Network,Load Balancing,★★,★★
22,Persistent Disk Performance Analysis,Analyze disk performance metrics to identify bottlenecks and upgrade opportunities.,@alex please find persistent disks with IOPS utilization >90% or consistent latency >20ms. #recommend SSD upgrades.,@alex,Operational Efficiency,GCP,Storage,Persistent Disk,★★★,★★
23,Cloud Functions Memory Optimization,Right-size Cloud Functions memory allocation based on actual usage patterns.,@alex please analyze Cloud Functions with memory utilization <40% or >90%. #recommend memory adjustments for cost/performance.,@alex,Cost Optimization,GCP,Serverless,Cloud Functions,★★,★★
24,Compute Engine Image Management,Clean up old custom images and optimize image creation processes.,@alex please identify custom images >180 days old not used by active instances. #alert team and #recommend cleanup schedule.,@alex,Cost Optimization,GCP,Compute,Compute Engine,★★,★★
25,VPC Network Optimization,Review VPC configurations for optimal routing and reduced data transfer costs.,@alex please analyze VPC networks with cross-region traffic >100GB/month. #recommend regional optimization strategies.,@alex,Cost Optimization,GCP,Network,VPC,★★★,★★
26,Cloud Storage Transfer Acceleration,Implement and optimize transfer acceleration for large data uploads to Cloud Storage.,@alex please identify storage uploads >1GB taking >30 minutes. #recommend transfer acceleration and parallel upload strategies.,@alex,Operational Efficiency,GCP,Storage,Cloud Storage,★★,★★
27,BigQuery Materialized Views,Implement materialized views for frequently queried data to reduce compute costs and improve performance.,@alex please identify BigQuery queries running >10 times daily with >5 minute execution. #recommend materialized view candidates.,@alex,Cost Optimization,GCP,Database,BigQuery,★★★,★★
28,Compute Engine GPU Utilization,Monitor GPU-enabled instances for efficient utilization and cost optimization.,@alex please analyze GPU instances with <60% utilization over 7 days. #recommend rightsizing or scheduling optimization.,@alex,Cost Optimization,GCP,Compute,Compute Engine,★★★,★★
29,Cloud SQL Connection Pooling,Implement connection pooling to optimize database connections and reduce resource consumption.,@alex please review Cloud SQL instances with >100 connections and identify pooling opportunities. #recommend connection pooler setup.,@alex,Operational Efficiency,GCP,Database,Cloud SQL,★★,★★
30,App Engine Traffic Splitting,Optimize traffic splitting configurations for A/B testing and gradual rollouts.,@alex please review App Engine services with unoptimized traffic splits >30 days old. #recommend splitting strategy updates.,@alex,Operational Efficiency,GCP,Serverless,App Engine,★★,★★
31,Snapshot Schedule Optimization,Optimize snapshot schedules to balance data protection with storage costs.,@alex please review snapshot policies with frequency >daily for non-critical data. #recommend schedule optimization for cost reduction.,@alex,Cost Optimization,GCP,Storage,Persistent Disk,★★,★★
32,Cloud CDN Cache Optimization,Fine-tune CDN caching policies to improve performance and reduce origin server load.,@alex please analyze CDN cache policies with TTL <1 hour for static content. #recommend caching improvements.,@alex,Operational Efficiency,GCP,Network,Cloud CDN,★★,★★
33,Compute Engine Sole-tenant Analysis,Evaluate sole-tenant node usage for licensing compliance and cost optimization.,@alex please review sole-tenant nodes with <70% VM utilization. #recommend consolidation or regular instance migration.,@alex,Cost Optimization,GCP,Compute,Compute Engine,★★,★★
34,BigQuery Clustering Optimization,Implement table clustering for large datasets to improve query performance and reduce costs.,@alex please identify BigQuery tables >10GB without clustering that are frequently filtered. #recommend clustering strategies.,@alex,Operational Efficiency,GCP,Database,BigQuery,★★★,★★
35,Cloud Functions Timeout Optimization,Optimize function timeouts to prevent unnecessary charges from hanging executions.,@alex please find Cloud Functions with timeout >300 seconds and avg execution <60 seconds. #recommend timeout adjustments.,@alex,Cost Optimization,GCP,Serverless,Cloud Functions,★★,★★
36,Persistent Disk Encryption Review,Ensure appropriate encryption settings for persistent disks based on compliance requirements.,@alex please audit persistent disks without customer-managed encryption in sensitive projects. #recommend encryption upgrades.,@alex,Security,GCP,Storage,Persistent Disk,★★,★★
37,Load Balancer SSL Configuration,Optimize SSL/TLS configurations for load balancers to improve security and performance.,@alex please review load balancers with SSL policies using outdated TLS versions. #recommend security policy updates.,@alex,Security,GCP,Network,Load Balancing,★★,★★
38,Cloud Storage Bucket Versioning,Review bucket versioning settings to optimize storage costs while maintaining data integrity.,@alex please analyze versioned buckets with >50 versions per object. #recommend lifecycle policies for version cleanup.,@alex,Cost Optimization,GCP,Storage,Cloud Storage,★★,★★
39,VM Instance Labeling Strategy,Implement consistent labeling for better resource management and cost allocation.,@alex please identify unlabeled resources >30 days old. #recommend labeling standards and automated tagging.,@alex,Operational Efficiency,GCP,Compute,Compute Engine,★★,★★
40,BigQuery Partition Management,Optimize table partitioning strategies for improved query performance and cost reduction.,@alex please review large tables (>1TB) without partitioning or suboptimal partition schemes. #recommend partitioning strategies.,@alex,Cost Optimization,GCP,Database,BigQuery,★★★,★★
41,Cloud SQL Read Replicas,Implement read replicas to distribute read traffic and improve application performance.,@alex please identify Cloud SQL instances with read-heavy workloads >70% read queries. #recommend read replica deployment.,@alex,Scalability,GCP,Database,Cloud SQL,★★,★★
42,Compute Engine Commitment Analysis,Analyze usage patterns to identify opportunities for committed use discounts.,@alex please review stable workloads running >6 months for commitment discount eligibility. #recommend commitment strategies.,@alex,Cost Optimization,GCP,Compute,Compute Engine,★★★,★★
43,Cloud Functions Concurrency Tuning,Optimize function concurrency settings for better performance and cost efficiency.,@alex please analyze functions with default concurrency settings and high invocation rates. #recommend concurrency optimization.,@alex,Operational Efficiency,GCP,Serverless,Cloud Functions,★★,★★
44,Network Firewall Rule Optimization,Review and optimize firewall rules for security and performance improvements.,@alex please identify firewall rules not used in >90 days or overly permissive rules. #recommend rule cleanup and tightening.,@alex,Security,GCP,Network,VPC,★★,★★
45,Cloud Storage Multi-region Strategy,Evaluate multi-regional storage needs versus costs for optimal data placement.,@alex please review multi-regional buckets with access patterns concentrated in single region. #recommend regional optimization.,@alex,Cost Optimization,GCP,Storage,Cloud Storage,★★,★★
46,App Engine Instance Class Optimization,Right-size App Engine instance classes based on actual resource utilization.,@alex please analyze App Engine instances with consistent resource utilization <50%. #recommend instance class downsizing.,@alex,Cost Optimization,GCP,Serverless,App Engine,★★,★★
47,Persistent Disk Regional Migration,Migrate persistent disks to optimal regions to reduce latency and costs.,@alex please identify disks in suboptimal regions causing high network charges. #recommend regional migration strategy.,@alex,Cost Optimization,GCP,Storage,Persistent Disk,★★,★★
48,BigQuery Streaming Insert Optimization,Optimize streaming inserts to reduce costs while maintaining data freshness requirements.,@alex please review streaming inserts with high costs >$500/month. #recommend batching and optimization strategies.,@alex,Cost Optimization,GCP,Database,BigQuery,★★★,★★
49,Cloud CDN Geographic Distribution,Optimize CDN edge locations based on user geography and traffic patterns.,@alex please analyze CDN traffic patterns and identify underutilized edge locations. #recommend geographic optimization.,@alex,Operational Efficiency,GCP,Network,Cloud CDN,★★,★★
50,Compute Engine Preemption Handling,Implement robust preemption handling strategies for preemptible instances.,@alex please review preemptible instances without graceful shutdown scripts. #recommend preemption handling improvements.,@alex,Operational Efficiency,GCP,Compute,Compute Engine,★★,★★
51,Cloud SQL Backup Verification,Verify backup integrity and recovery procedures for Cloud SQL databases.,@alex please test restore procedures for Cloud SQL backups >30 days old. #report backup health and recovery time metrics.,@alex,Operational Efficiency,GCP,Database,Cloud SQL,★★★,★★
52,Load Balancer Backend Health,Monitor and optimize backend health check configurations for improved reliability.,@alex please review backends failing health checks >5% of time. #recommend health check tuning and backend optimization.,@alex,Operational Efficiency,GCP,Network,Load Balancing,★★,★★
53,Cloud Storage Request Optimization,Analyze storage request patterns to reduce API costs and improve performance.,@alex please identify storage buckets with high request rates >10k/hour. #recommend request optimization and caching strategies.,@alex,Cost Optimization,GCP,Storage,Cloud Storage,★★,★★
54,VM Custom Machine Type Analysis,Evaluate custom machine types for potential standard instance optimization.,@alex please review custom machine types for standard instance alternatives within 10% performance. #recommend optimizations.,@alex,Cost Optimization,GCP,Compute,Compute Engine,★★,★★
55,BigQuery Data Retention Policy,Implement data retention policies to manage storage costs while meeting compliance requirements.,@alex please review BigQuery datasets without retention policies and data >2 years old. #recommend retention strategies.,@alex,Cost Optimization,GCP,Database,BigQuery,★★,★★
56,Cloud Functions VPC Connector Usage,Optimize VPC connector usage for Cloud Functions to reduce latency and costs.,@alex please analyze functions using VPC connectors with minimal private resource access. #recommend connector optimization.,@alex,Cost Optimization,GCP,Serverless,Cloud Functions,★★,★★
57,Network Address Translation,Review NAT gateway configurations for cost optimization and performance.,@alex please analyze NAT gateways with low utilization <30% and high costs. #recommend rightsizing or shared NAT strategies.,@alex,Cost Optimization,GCP,Network,VPC,★★,★★
58,Persistent Disk Snapshot Cleanup,Implement automated cleanup of old snapshots to reduce storage costs.,@alex please identify snapshot chains >10 snapshots and implement automated cleanup. #recommend retention policies.,@alex,Cost Optimization,GCP,Storage,Persistent Disk,★★,★★
59,App Engine Version Management,Clean up old App Engine versions to reduce storage costs and improve management.,@alex please find App Engine versions >6 months old not serving traffic. #recommend version cleanup schedule.,@alex,Cost Optimization,GCP,Serverless,App Engine,★★,★★
60,Cloud CDN Compression Settings,Optimize compression settings to improve performance and reduce bandwidth costs.,@alex please review CDN responses without compression for compressible content >1KB. #recommend compression policies.,@alex,Operational Efficiency,GCP,Network,Cloud CDN,★★,★★
61,Compute Engine Metadata Optimization,Optimize instance metadata usage for improved security and performance.,@alex please review instances with metadata server v1 enabled or excessive metadata queries. #recommend security updates.,@alex,Security,GCP,Compute,Compute Engine,★★,★★
62,BigQuery External Table Optimization,Optimize external table configurations for better query performance and cost control.,@alex please analyze external tables with slow query performance >2x internal table speed. #recommend optimization strategies.,@alex,Operational Efficiency,GCP,Database,BigQuery,★★,★★
63,Cloud Storage CORS Configuration,Review and optimize CORS configurations for better security and performance.,@alex please audit storage buckets with overly permissive CORS policies or missing CORS. #recommend security improvements.,@alex,Security,GCP,Storage,Cloud Storage,★★,★★
64,VM Instance Template Updates,Update instance templates with latest configurations and security patches.,@alex please review instance templates >90 days old using outdated images. #recommend template refresh schedule.,@alex,Security,GCP,Compute,Compute Engine,★★,★★
65,Cloud SQL Maintenance Window,Optimize maintenance windows to minimize impact on application availability.,@alex please review Cloud SQL instances with maintenance windows during peak hours. #recommend optimal maintenance scheduling.,@alex,Operational Efficiency,GCP,Database,Cloud SQL,★★,★★
66,Load Balancer Session Affinity,Configure session affinity appropriately for application requirements and performance.,@alex please review load balancers with inappropriate session affinity causing uneven distribution. #recommend configuration updates.,@alex,Operational Efficiency,GCP,Network,Load Balancing,★★,★★
67,Cloud Functions Error Rate Analysis,Monitor and reduce Cloud Functions error rates to improve reliability and reduce costs.,@alex please identify functions with error rates >5% and analyze failure patterns. #recommend reliability improvements.,@alex,Operational Efficiency,GCP,Serverless,Cloud Functions,★★,★★
68,Persistent Disk Type Migration,Migrate persistent disks to appropriate types based on performance requirements.,@alex please identify standard disks that could benefit from SSD performance or SSD disks with low IOPS usage. #recommend migrations.,@alex,Cost Optimization,GCP,Storage,Persistent Disk,★★,★★
69,BigQuery Query Optimization,Identify and optimize expensive BigQuery queries to reduce compute costs.,@alex please find queries consuming >10GB data with potential optimization opportunities. #recommend query improvements.,@alex,Cost Optimization,GCP,Database,BigQuery,★★★,★★
70,Network Peering Optimization,Review VPC peering configurations for optimal connectivity and cost management.,@alex please analyze VPC peering with low traffic <1GB/month and high setup overhead. #recommend peering optimization.,@alex,Cost Optimization,GCP,Network,VPC,★★,★★
71,Cloud Storage Bucket Location,Ensure storage buckets are in optimal locations for access patterns and compliance.,@alex please review buckets with primary access from different regions than bucket location. #recommend relocations.,@alex,Operational Efficiency,GCP,Storage,Cloud Storage,★★,★★
72,App Engine Scaling Configuration,Fine-tune App Engine automatic scaling settings for optimal performance and cost.,@alex please review services with frequent scaling events >50/hour. #recommend scaling parameter adjustments.,@alex,Cost Optimization,GCP,Serverless,App Engine,★★,★★
73,Compute Engine Live Migration,Configure live migration settings appropriately for workload requirements.,@alex please review instances with live migration disabled unnecessarily or causing performance issues. #recommend migration policies.,@alex,Operational Efficiency,GCP,Compute,Compute Engine,★★,★★
74,Cloud CDN Invalidation Costs,Monitor and optimize CDN cache invalidation to reduce operational costs.,@alex please analyze CDN invalidation patterns >1000 requests/month. #recommend caching strategy improvements.,@alex,Cost Optimization,GCP,Network,Cloud CDN,★★,★★
75,BigQuery Reservations Usage,Optimize BigQuery reservations for predictable workloads and cost savings.,@alex please analyze BigQuery usage patterns suitable for reservation pricing. #recommend reservation strategies for >20% savings.,@alex,Cost Optimization,GCP,Database,BigQuery,★★★,★★
76,Cloud Functions Deployment Size,Optimize function deployment packages to reduce cold start times and storage costs.,@alex please identify functions with deployment sizes >50MB. #recommend code optimization and dependency reduction.,@alex,Operational Efficiency,GCP,Serverless,Cloud Functions,★★,★★
77,VM Disk Attachment Optimization,Review disk attachment configurations for optimal performance and cost.,@alex please find VMs with multiple disks that could be consolidated or detached unused disks. #recommend optimizations.,@alex,Cost Optimization,GCP,Storage,Persistent Disk,★★,★★
78,Cloud SQL Point-in-time Recovery,Configure and test point-in-time recovery settings for optimal data protection.,@alex please review Cloud SQL instances without point-in-time recovery or with excessive retention. #recommend PITR optimization.,@alex,Operational Efficiency,GCP,Database,Cloud SQL,★★,★★
79,Load Balancer URL Map Optimization,Optimize URL maps and routing rules for improved performance and maintainability.,@alex please review complex URL maps with >20 rules or inefficient routing patterns. #recommend simplification strategies.,@alex,Operational Efficiency,GCP,Network,Load Balancing,★★,★★
80,Cloud Storage Signed URL Usage,Monitor signed URL usage patterns for security and cost optimization.,@alex please analyze signed URL generation >1000/day for potential caching opportunities. #recommend access pattern optimization.,@alex,Cost Optimization,GCP,Storage,Cloud Storage,★★,★★
81,Compute Engine Network Performance,Analyze network performance metrics to identify bottlenecks and optimization opportunities.,@alex please review instances with network utilization >80% or packet loss >0.1%. #recommend network optimization.,@alex,Operational Efficiency,GCP,Compute,Compute Engine,★★,★★
82,BigQuery Approximate Aggregation,Implement approximate aggregation functions to reduce query costs for large datasets.,@alex please identify exact COUNT DISTINCT queries on large tables suitable for HLL approximation. #recommend optimization.,@alex,Cost Optimization,GCP,Database,BigQuery,★★,★★
83,App Engine Traffic Migration,Plan and execute traffic migration strategies for App Engine deployments.,@alex please review services with manual traffic allocation >30 days old. #recommend automated migration strategies.,@alex,Operational Efficiency,GCP,Serverless,App Engine,★★,★★
84,Persistent Disk Multi-attach,Evaluate multi-attach persistent disk usage for optimization opportunities.,@alex please review multi-attach disks with single active attachment >14 days. #recommend single-attach migration.,@alex,Cost Optimization,GCP,Storage,Persistent Disk,★★,★★
85,Cloud CDN Origin Selection,Optimize origin server selection and failover configurations for CDN.,@alex please review CDN configurations with single origin servers lacking redundancy. #recommend multi-origin setup.,@alex,Operational Efficiency,GCP,Network,Cloud CDN,★★,★★
86,VM Instance Scheduling,Implement instance scheduling for non-production workloads to reduce costs.,@alex please identify development/staging instances running 24/7. #recommend scheduling policies for 60% cost savings.,@alex,Cost Optimization,GCP,Compute,Compute Engine,★★★,★★
87,BigQuery Geographic Optimization,Optimize BigQuery dataset locations based on data source and query origin patterns.,@alex please analyze cross-region BigQuery queries causing high network costs. #recommend data locality improvements.,@alex,Cost Optimization,GCP,Database,BigQuery,★★,★★
88,Cloud Functions Environment Variables,Optimize function environment variable usage for security and performance.,@alex please review functions with excessive environment variables >50 or sensitive data in env vars. #recommend optimization.,@alex,Security,GCP,Serverless,Cloud Functions,★★,★★
89,Network Route Advertisement,Optimize route advertisements in VPC networks to reduce unnecessary routing overhead.,@alex please review custom routes with broad IP ranges or unused route advertisements. #recommend route optimization.,@alex,Operational Efficiency,GCP,Network,VPC,★★,★★
90,Cloud Storage Transfer Service,Optimize data transfer services for cost-effective bulk data movement.,@alex please review transfer jobs with suboptimal bandwidth utilization <50%. #recommend transfer optimization strategies.,@alex,Operational Efficiency,GCP,Storage,Cloud Storage,★★,★★
91,App Engine Service Versions,Manage service versions efficiently to reduce complexity and storage costs.,@alex please implement version lifecycle policies for services with >10 versions. #recommend automated version management.,@alex,Operational Efficiency,GCP,Serverless,App Engine,★★,★★
92,Compute Engine Disk Performance,Monitor disk performance metrics to identify upgrade needs and bottlenecks.,@alex please find instances with disk queue depth consistently >10 or latency >50ms. #recommend disk upgrades.,@alex,Operational Efficiency,GCP,Compute,Compute Engine,★★,★★
93,BigQuery Federated Queries,Optimize federated queries to external data sources for performance and cost.,@alex please analyze federated queries with execution time >10 minutes. #recommend query optimization and data caching.,@alex,Operational Efficiency,GCP,Database,BigQuery,★★,★★
94,Cloud CDN Custom Headers,Configure custom headers appropriately for caching optimization and security.,@alex please review CDN configurations with missing cache headers or security headers. #recommend header optimization.,@alex,Security,GCP,Network,Cloud CDN,★★,★★
95,VM Instance Metadata Server,Secure and optimize metadata server access for improved security posture.,@alex please identify instances with unrestricted metadata server access. #recommend security hardening measures.,@alex,Security,GCP,Compute,Compute Engine,★★,★★
96,Cloud SQL Connection Limits,Monitor and optimize connection limits to prevent application connectivity issues.,@alex please review Cloud SQL instances approaching connection limits >80% of max. #recommend connection optimization.,@alex,Operational Efficiency,GCP,Database,Cloud SQL,★★,★★
97,Load Balancer Timeout Configuration,Optimize timeout settings for load balancers to improve user experience.,@alex please review load balancers with default timeouts causing user-facing errors. #recommend timeout optimization.,@alex,Operational Efficiency,GCP,Network,Load Balancing,★★,★★
98,Cloud Storage Bucket Notifications,Configure bucket notifications efficiently to reduce costs and improve automation.,@alex please review bucket notifications with high frequency >10000/day. #recommend notification filtering and batching.,@alex,Cost Optimization,GCP,Storage,Cloud Storage,★★,★★
99,App Engine Cron Job Optimization,Optimize cron job configurations to reduce unnecessary executions and costs.,@alex please review cron jobs with high frequency executions and low success rates. #recommend scheduling optimization.,@alex,Cost Optimization,GCP,Serverless,App Engine,★★,★★
100,Resource Utilization Dashboard,Create comprehensive dashboards for monitoring GCP resource utilization and costs.,@alex please build comprehensive resource utilization #dashboard covering top 20 cost-driving services. #visual cost trends and utilization metrics.,@alex,Operational Efficiency,GCP,Multiple,Monitoring,★★★,★★★
1,Azure VM Right-Sizing Analysis,Identify underutilized Azure VMs running below 20% CPU for 14+ days and recommend optimal sizing,@alex please analyze Azure VMs with <20% CPU utilization over 2 weeks. #recommend right-sizing options and #alert with VMs that could be downsized.,@alex,Cost Optimization,Azure,Compute,Virtual Machines,★★★,★★★
2,Orphaned Azure Disks Cleanup,Find unattached managed disks consuming storage costs without being used by any VM,@alex please identify orphaned Azure managed disks not attached to VMs for 30+ days. #recommend cleanup actions and #alert with potential savings.,@alex,Cost Optimization,Azure,Storage,Managed Disks,★★★,★★★
3,Azure Storage Cost Optimization,Analyze storage accounts for blob lifecycle management and tier optimization opportunities,@alex please review Azure Storage accounts for cold/archive tier candidates and lifecycle policies. #recommend tier changes and #visual cost impact.,@alex,Cost Optimization,Azure,Storage,Storage Accounts,★★★,★★★
4,Reserved Instance Coverage Gap,Identify VMs running without Reserved Instance coverage to maximize cost savings,@alex please find Azure VMs eligible for Reserved Instance coverage. #recommend RI purchases and #report potential annual savings.,@alex,Cost Optimization,Azure,Compute,Reserved Instances,★★★,★★★
5,Azure SQL Database DTU Analysis,Monitor SQL databases for DTU utilization and scaling opportunities,@alex please analyze Azure SQL databases with <30% DTU usage over 7 days. #recommend scaling down and #alert with optimization opportunities.,@alex,Cost Optimization,Azure,Database,SQL Database,★★★,★★★
6,Unused Public IP Addresses,Locate static public IPs not associated with any resource generating unnecessary costs,@alex please identify unassociated Azure public IP addresses idle for 7+ days. #recommend deletion and #report monthly savings potential.,@alex,Cost Optimization,Azure,Network,Public IP,★★★,★★
7,Azure Monitor Alert Configuration,Ensure critical VMs have essential monitoring alerts configured for proactive management,"@alex please check production VMs missing CPU, memory, or disk space alerts. #recommend alert templates and #dashboard monitoring coverage.",@alex,Operational Efficiency,Azure,Compute,Azure Monitor,★★★,★★★
8,App Service Plan Optimization,Review App Service plans for underutilized instances and consolidation opportunities,@alex please analyze App Service plans with <40% resource utilization. #recommend plan consolidation and #visual utilization patterns.,@alex,Cost Optimization,Azure,Compute,App Service,★★★,★★
9,Network Security Group Audit,Review NSG rules for overly permissive configurations and security gaps,@alex please audit NSGs with 0.0.0.0/0 source rules and unused rules. #recommend security improvements and #alert critical exposures.,@alex,Security,Azure,Network,NSG,★★★,★★★
10,Azure Function Consumption Analysis,Monitor serverless functions for execution patterns and cost optimization,@alex please review Azure Functions with low invocation rates over 30 days. #recommend optimization or consolidation and #report cost impact.,@alex,Cost Optimization,Azure,Serverless,Functions,★★,★★
11,VM Auto-Shutdown Configuration,Implement automatic shutdown for development/test VMs to reduce costs,@alex please identify dev/test VMs without auto-shutdown policies. #recommend shutdown schedules and #report potential monthly savings.,@alex,Cost Optimization,Azure,Compute,Virtual Machines,★★★,★★
12,Azure Backup Policy Review,Analyze backup policies for over-retention and storage optimization,@alex please review backup retention policies exceeding business requirements. #recommend optimal retention periods and #visual storage cost trends.,@alex,Cost Optimization,Azure,Storage,Backup,★★,★★
13,Load Balancer Health Probe Check,Ensure load balancers have properly configured health probes for reliability,@alex please check Azure Load Balancers missing health probes or with failing backends. #recommend probe configurations and #alert unhealthy services.,@alex,Operational Efficiency,Azure,Network,Load Balancer,★★★,★★★
14,Resource Group Tagging Compliance,Audit resource groups and resources for proper tagging according to governance policies,"@alex please identify resources missing required tags (cost-center, environment, owner). #recommend tagging strategy and #report compliance percentage.",@alex,Operational Efficiency,Azure,Compute,Resource Groups,★★,★★
15,Azure CDN Performance Analysis,Review CDN endpoints for cache hit ratios and geographic optimization,@alex please analyze CDN endpoints with <80% cache hit ratio. #recommend cache rules optimization and #visual performance metrics.,@alex,Scalability,Azure,Network,CDN,★★,★★
16,Spot Instance Opportunity Assessment,Identify workloads suitable for Azure Spot VMs to achieve significant cost savings,@alex please find stateless workloads eligible for Spot VM conversion. #recommend Spot instance candidates and #report savings potential.,@alex,Cost Optimization,Azure,Compute,Spot VMs,★★,★★★
17,Application Gateway SSL Certificate Expiry,Monitor SSL certificates on Application Gateways to prevent service disruptions,@alex please check Application Gateway SSL certificates expiring within 30 days. #alert certificate renewals needed and #dashboard certificate status.,@alex,Operational Efficiency,Azure,Network,Application Gateway,★★★,★★
18,Azure Key Vault Access Review,Audit Key Vault access policies for excessive permissions and unused keys,@alex please review Key Vault access policies and keys unused for 90+ days. #recommend access cleanup and #report security improvements.,@alex,Security,Azure,Security,Key Vault,★★,★★
19,VNet Peering Optimization,Analyze virtual network peering for unnecessary connections and cost reduction,@alex please identify unused VNet peerings with zero traffic for 30 days. #recommend peering cleanup and #visual network topology.,@alex,Cost Optimization,Azure,Network,VNet,★★,★★
20,Azure Advisor Recommendations Review,"Implement Azure Advisor suggestions for cost, performance, and security improvements",@alex please prioritize Azure Advisor high-impact recommendations across subscriptions. #recommend implementation roadmap and #report expected benefits.,@alex,Operational Efficiency,Azure,Compute,Azure Advisor,★★★,★★★
21,Database Backup Verification,Ensure database backups are completing successfully and meet RTO/RPO requirements,@alex please check Azure SQL backup job failures and verify restoration capabilities. #alert backup issues and #dashboard backup health status.,@alex,Operational Efficiency,Azure,Database,SQL Database,★★★,★★★
22,VM Disk Performance Analysis,Monitor VM disk IOPS and latency to identify performance bottlenecks,@alex please analyze VMs with high disk latency (>20ms) or IOPS throttling. #recommend disk upgrades and #visual performance trends.,@alex,Scalability,Azure,Storage,VM Disks,★★★,★★
23,Azure Service Health Monitoring,Track service health issues affecting resources and implement proactive measures,@alex please monitor Azure Service Health alerts affecting production resources. #alert service impacts and #dashboard regional health status.,@alex,Operational Efficiency,Azure,Compute,Service Health,★★★,★★
24,Hybrid Connection Optimization,Review ExpressRoute and VPN connections for utilization and performance,@alex please analyze hybrid connections with <30% bandwidth utilization. #recommend connection optimization and #report connectivity costs.,@alex,Cost Optimization,Azure,Network,ExpressRoute,★★,★★
25,Azure Automation Runbook Review,Audit automation runbooks for efficiency and error handling improvements,@alex please review failed automation runbooks and optimization opportunities. #recommend runbook improvements and #visual automation success rates.,@alex,Operational Efficiency,Azure,Compute,Automation,★★,★★
26,Storage Account Access Pattern Analysis,Analyze storage access patterns to optimize performance tiers and costs,@alex please review storage accounts for hot/cool tier optimization opportunities. #recommend tier changes and #report monthly cost impact.,@alex,Cost Optimization,Azure,Storage,Storage Tiers,★★★,★★
27,Azure DevOps Pipeline Resource Usage,Monitor build agents and pipeline resource consumption for optimization,@alex please analyze self-hosted agents with low utilization patterns. #recommend agent pool optimization and #visual pipeline metrics.,@alex,Cost Optimization,Azure,Compute,DevOps,★★,★★
28,Container Registry Image Cleanup,Clean up old and unused container images to reduce storage costs,@alex please identify container images in ACR older than 6 months without pulls. #recommend image retention policies and #report storage savings.,@alex,Cost Optimization,Azure,Storage,Container Registry,★★,★★
29,Azure DNS Zone Optimization,Review DNS zones for unused records and consolidation opportunities,@alex please audit DNS zones with inactive records and redundant configurations. #recommend DNS optimization and #report management simplification.,@alex,Operational Efficiency,Azure,Network,DNS,★,★
30,Virtual Machine Scale Set Tuning,Optimize VMSS scaling rules and instance sizing for workload demands,@alex please review VMSS with ineffective scaling rules or oversized instances. #recommend scaling optimization and #visual scaling events.,@alex,Scalability,Azure,Compute,VMSS,★★,★★★
31,Azure Bastion Usage Analysis,Evaluate Bastion host utilization and cost-effectiveness for secure access,@alex please analyze Bastion hosts with low connection frequency. #recommend usage optimization and #report access patterns.,@alex,Cost Optimization,Azure,Network,Bastion,★,★
32,Log Analytics Workspace Optimization,Review log retention policies and query performance for cost management,@alex please analyze Log Analytics workspaces with excessive data retention. #recommend retention optimization and #visual ingestion trends.,@alex,Cost Optimization,Azure,Storage,Log Analytics,★★,★★
33,Azure Firewall Rule Efficiency,Audit firewall rules for consolidation and performance improvements,@alex please review Azure Firewall rules for overlaps and unused configurations. #recommend rule optimization and #dashboard traffic patterns.,@alex,Security,Azure,Network,Firewall,★★,★★
34,Managed Identity Implementation,Identify applications using service principals that could use managed identities,@alex please find applications with hardcoded credentials eligible for managed identity. #recommend identity migration and #report security improvements.,@alex,Security,Azure,Security,Managed Identity,★★,★★★
35,Azure Site Recovery Testing,Verify disaster recovery configurations and test failover procedures,@alex please check ASR replication health and last successful failover tests. #alert DR issues and #dashboard recovery readiness status.,@alex,Operational Efficiency,Azure,Storage,Site Recovery,★★★,★★★
36,Cost Anomaly Detection,Monitor unusual spending patterns and unexpected cost increases,@alex please identify cost spikes >50% from baseline in any resource group. #alert cost anomalies and #visual spending trend analysis.,@alex,Cost Optimization,Azure,Compute,Cost Management,★★★,★★★
37,Azure Policy Compliance Check,Ensure resources comply with organizational governance policies,@alex please audit policy violations across subscriptions and resource groups. #recommend compliance fixes and #report policy adherence rates.,@alex,Security,Azure,Compute,Policy,★★,★★
38,Network Traffic Analysis,Monitor network flows for security and performance optimization,@alex please analyze NSG flow logs for unusual traffic patterns or security issues. #recommend network optimization and #visual traffic flows.,@alex,Security,Azure,Network,NSG Flow Logs,★★,★★
39,Azure Resource Health Monitoring,Track resource health events and availability metrics,@alex please monitor resource health degradations affecting production workloads. #alert health issues and #dashboard availability trends.,@alex,Operational Efficiency,Azure,Compute,Resource Health,★★★,★★
40,Storage Encryption Compliance,Verify all storage accounts have proper encryption configurations,@alex please audit storage accounts without customer-managed encryption keys. #recommend encryption improvements and #report compliance status.,@alex,Security,Azure,Storage,Encryption,★★,★★
41,Application Insights Performance,Monitor application performance metrics and identify optimization opportunities,@alex please review applications with high response times or error rates. #recommend performance improvements and #visual app metrics.,@alex,Scalability,Azure,Compute,App Insights,★★,★★
42,Azure Kubernetes Service Health,"Monitor AKS cluster health, node performance, and resource utilization",@alex please check AKS clusters with node failures or resource constraints. #alert cluster issues and #dashboard kubernetes metrics.,@alex,Scalability,Azure,Kubernetes,AKS,★★★,★★★
43,Service Bus Queue Management,Monitor message queues for deadletter patterns and processing delays,@alex please analyze Service Bus queues with growing deadletter counts. #recommend queue optimization and #visual message flow patterns.,@alex,Operational Efficiency,Azure,Serverless,Service Bus,★★,★★
44,Azure Data Factory Pipeline Optimization,Review data pipeline performance and cost optimization opportunities,@alex please analyze ADF pipelines with long execution times or high costs. #recommend pipeline optimization and #report processing efficiency.,@alex,Cost Optimization,Azure,Compute,Data Factory,★★,★★
45,Event Grid Subscription Health,Monitor event subscriptions for delivery failures and performance issues,@alex please check Event Grid subscriptions with high failure rates. #alert subscription issues and #dashboard event delivery metrics.,@alex,Operational Efficiency,Azure,Serverless,Event Grid,★★,★★
46,Azure Search Service Optimization,Analyze search service performance and scaling requirements,@alex please review Cognitive Search services with high latency or low utilization. #recommend scaling adjustments and #visual search metrics.,@alex,Scalability,Azure,Compute,Cognitive Search,★,★★
47,Private Endpoint Configuration,Ensure services use private endpoints for secure connectivity,@alex please identify public services eligible for private endpoint configuration. #recommend private connectivity and #report security improvements.,@alex,Security,Azure,Network,Private Endpoint,★★,★★
48,Azure Synapse Analytics Optimization,Monitor data warehouse performance and cost management,@alex please analyze Synapse pools with low query performance or high costs. #recommend optimization strategies and #visual workload patterns.,@alex,Cost Optimization,Azure,Database,Synapse,★,★★
49,Logic Apps Performance Review,Monitor workflow executions and identify performance bottlenecks,@alex please check Logic Apps with high failure rates or long execution times. #recommend workflow optimization and #dashboard app performance.,@alex,Operational Efficiency,Azure,Serverless,Logic Apps,★★,★★
50,Azure Machine Learning Resource Management,Optimize ML compute resources and model deployment costs,@alex please review ML compute instances with low utilization rates. #recommend resource optimization and #report ML infrastructure costs.,@alex,Cost Optimization,Azure,Compute,Machine Learning,★,★★
51,Traffic Manager Profile Health,Monitor Traffic Manager endpoint health and routing efficiency,@alex please check Traffic Manager profiles with unhealthy endpoints. #alert routing issues and #dashboard endpoint health status.,@alex,Scalability,Azure,Network,Traffic Manager,★★,★★
52,Azure Cache Performance Analysis,Monitor Redis cache hit ratios and memory utilization patterns,@alex please analyze Redis caches with <80% hit ratio or high memory usage. #recommend cache optimization and #visual performance metrics.,@alex,Scalability,Azure,Database,Redis Cache,★★,★★
53,Resource Lock Management,Ensure critical resources have appropriate locks to prevent accidental deletion,@alex please identify production resources without delete locks. #recommend lock implementation and #report resource protection status.,@alex,Security,Azure,Compute,Resource Locks,★★,★★
54,Azure Batch Job Optimization,Monitor batch processing jobs for efficiency and cost optimization,@alex please review Batch pools with low utilization or long job queues. #recommend pool optimization and #visual job execution patterns.,@alex,Cost Optimization,Azure,Compute,Batch,★,★★
55,Front Door Performance Monitoring,Analyze CDN and WAF performance for global application delivery,@alex please check Front Door backends with high latency or security blocks. #recommend routing optimization and #dashboard global performance.,@alex,Scalability,Azure,Network,Front Door,★★,★★
56,Azure Stream Analytics Optimization,Monitor real-time data processing jobs for performance and costs,@alex please analyze Stream Analytics jobs with high streaming unit usage. #recommend query optimization and #report processing efficiency.,@alex,Cost Optimization,Azure,Compute,Stream Analytics,★,★★
57,Cosmos DB Performance Tuning,Monitor database performance metrics and RU consumption patterns,@alex please review Cosmos DB collections with high RU consumption or throttling. #recommend throughput optimization and #visual query performance.,@alex,Scalability,Azure,Database,Cosmos DB,★★,★★
58,API Management Gateway Health,Monitor API gateway performance and policy effectiveness,@alex please check APIM gateways with high latency or error rates. #recommend gateway optimization and #dashboard API metrics.,@alex,Operational Efficiency,Azure,Network,API Management,★★,★★
59,Azure IoT Hub Message Processing,Monitor IoT message ingestion and processing efficiency,@alex please analyze IoT Hubs with message throttling or routing failures. #recommend throughput optimization and #visual device connectivity.,@alex,Scalability,Azure,Serverless,IoT Hub,★,★★
60,Notification Hub Delivery Analysis,Monitor push notification delivery rates and engagement metrics,@alex please review Notification Hubs with low delivery rates or registration issues. #recommend notification optimization and #report delivery metrics.,@alex,Operational Efficiency,Azure,Serverless,Notification Hub,★,★
61,Azure Spring Cloud Resource Management,Optimize Java application deployments and resource allocation,@alex please analyze Spring Cloud apps with high resource consumption. #recommend scaling optimization and #visual application metrics.,@alex,Cost Optimization,Azure,Compute,Spring Cloud,★,★★
62,Power BI Embedded Capacity Planning,Monitor report usage and optimize capacity allocation,@alex please review Power BI Embedded with low query volume or high costs. #recommend capacity optimization and #report usage patterns.,@alex,Cost Optimization,Azure,Compute,Power BI,★,★
63,Azure Firewall Premium Features,Leverage advanced threat protection and TLS inspection capabilities,@alex please check Firewall Premium configurations missing threat intelligence rules. #recommend security enhancements and #dashboard threat detections.,@alex,Security,Azure,Network,Firewall Premium,★★,★★
64,Static Web Apps Performance,Monitor static site hosting performance and global distribution,@alex please analyze Static Web Apps with high load times or deployment issues. #recommend performance optimization and #visual site metrics.,@alex,Scalability,Azure,Compute,Static Web Apps,★,★
65,Azure Purview Data Governance,Monitor data catalog health and governance policy compliance,@alex please check Purview scans with failures or missing data classifications. #recommend governance improvements and #report data quality metrics.,@alex,Security,Azure,Storage,Purview,★,★★
66,Communication Services Usage Analysis,Monitor communication API usage and cost optimization,@alex please review Communication Services with unused features or high costs. #recommend service optimization and #report usage efficiency.,@alex,Cost Optimization,Azure,Serverless,Communication Services,★,★
67,Azure Digital Twins Performance,Monitor IoT digital twin models and query performance,@alex please analyze Digital Twins with high query latency or model complexity. #recommend optimization strategies and #visual twin relationships.,@alex,Scalability,Azure,Serverless,Digital Twins,★,★
68,Container Instances Right-Sizing,Optimize container instance resources and scaling patterns,@alex please review Container Instances with resource over-provisioning. #recommend sizing optimization and #report container efficiency.,@alex,Cost Optimization,Azure,Compute,Container Instances,★★,★★
69,Azure Maps Usage Optimization,Monitor mapping service transactions and feature utilization,@alex please analyze Maps services with low transaction volume or unused features. #recommend service optimization and #visual usage patterns.,@alex,Cost Optimization,Azure,Serverless,Maps,★,★
70,Video Analyzer Resource Management,Optimize video processing pipelines and storage costs,@alex please review Video Analyzer with high processing costs or unused pipelines. #recommend resource optimization and #report processing efficiency.,@alex,Cost Optimization,Azure,Compute,Video Analyzer,★,★
71,Azure Attestation Service Health,Monitor attestation service availability and policy compliance,@alex please check Attestation services with policy violations or availability issues. #alert service problems and #dashboard attestation metrics.,@alex,Security,Azure,Security,Attestation,★,★
72,Spatial Anchors Performance,Monitor mixed reality anchor creation and retrieval performance,@alex please analyze Spatial Anchors with high latency or low success rates. #recommend performance optimization and #visual anchor metrics.,@alex,Scalability,Azure,Compute,Spatial Anchors,★,★
73,Azure Quantum Resource Planning,Monitor quantum computing job queues and resource allocation,@alex please review Quantum workspaces with unused job allocations. #recommend resource optimization and #report quantum usage patterns.,@alex,Cost Optimization,Azure,Compute,Quantum,★,★
74,Form Recognizer Cost Analysis,Monitor document processing costs and model efficiency,@alex please analyze Form Recognizer with high processing costs or low accuracy. #recommend model optimization and #report processing metrics.,@alex,Cost Optimization,Azure,Compute,Form Recognizer,★,★
75,Time Series Insights Data Management,Optimize time series data retention and query performance,@alex please review TSI environments with high storage costs or slow queries. #recommend data optimization and #visual time series patterns.,@alex,Cost Optimization,Azure,Database,Time Series Insights,★,★
76,Azure Defender Security Alerts,Monitor security recommendations and threat detections,@alex please prioritize Defender security alerts by severity and resource impact. #alert critical threats and #dashboard security posture.,@alex,Security,Azure,Security,Defender,★★★,★★★
77,Data Share Usage Monitoring,Monitor data sharing agreements and transfer efficiency,@alex please check Data Share with failed synchronizations or unused shares. #recommend sharing optimization and #report transfer success rates.,@alex,Operational Efficiency,Azure,Storage,Data Share,★,★
78,Cognitive Services API Management,Optimize AI service usage and cost management,@alex please review Cognitive Services with low API usage or high costs per transaction. #recommend service optimization and #visual API metrics.,@alex,Cost Optimization,Azure,Compute,Cognitive Services,★★,★★
79,Azure Arc Resource Management,Monitor hybrid and multi-cloud resource governance,@alex please check Arc-enabled servers with connectivity issues or policy violations. #alert connectivity problems and #dashboard hybrid resources.,@alex,Operational Efficiency,Azure,Compute,Arc,★,★★
80,Confidential Computing Security,Monitor confidential VM security and attestation health,@alex please review confidential VMs with attestation failures or security issues. #recommend security improvements and #report confidential workloads.,@alex,Security,Azure,Compute,Confidential Computing,★,★★
81,Azure Virtual Desktop Optimization,Monitor VDI performance and user session efficiency,@alex please analyze AVD host pools with high resource usage or user complaints. #recommend scaling optimization and #visual session metrics.,@alex,Scalability,Azure,Compute,Virtual Desktop,★★,★★
82,HPC Cluster Resource Management,Optimize high-performance computing cluster utilization,@alex please review HPC clusters with low job throughput or resource waste. #recommend cluster optimization and #report computational efficiency.,@alex,Cost Optimization,Azure,Compute,HPC,★,★★
83,Azure Migrate Assessment,Evaluate migration readiness and cost projections,@alex please check migrate assessments with outdated data or failed discoveries. #recommend migration planning and #visual readiness reports.,@alex,Operational Efficiency,Azure,Compute,Migrate,★,★★
84,Database Migration Health,Monitor database migration status and performance impact,@alex please check DMS migration jobs with errors or performance degradation. #alert migration issues and #dashboard migration progress.,@alex,Operational Efficiency,Azure,Database,Database Migration,★★,★★
85,Azure Stack Hub Management,Monitor hybrid cloud stack health and capacity planning,@alex please review Stack Hub with capacity warnings or update failures. #recommend capacity planning and #dashboard stack metrics.,@alex,Operational Efficiency,Azure,Compute,Stack Hub,★,★★
86,Azure Lighthouse Delegation,Monitor cross-tenant resource management and security,@alex please check Lighthouse delegations with excessive permissions or inactive management. #recommend delegation optimization and #report governance status.,@alex,Security,Azure,Compute,Lighthouse,★,★
87,Peering Service Performance,Monitor internet peering performance and routing optimization,@alex please analyze Peering Service with high latency or routing inefficiencies. #recommend peering optimization and #visual network performance.,@alex,Scalability,Azure,Network,Peering Service,★,★
88,Azure VMware Solution Management,Monitor VMware workload performance and cost optimization,@alex please review AVS clusters with resource constraints or high costs. #recommend workload optimization and #report VMware efficiency.,@alex,Cost Optimization,Azure,Compute,VMware Solution,★,★★
89,Orbital Ground Station Planning,Optimize satellite communication scheduling and costs,@alex please check Orbital contacts with scheduling conflicts or unused capacity. #recommend scheduling optimization and #report satellite usage.,@alex,Cost Optimization,Azure,Network,Orbital,★,★
90,Azure Lab Services Management,Monitor educational lab resource usage and cost control,@alex please review Lab Services with over-allocated resources or unused labs. #recommend resource optimization and #visual lab utilization.,@alex,Cost Optimization,Azure,Compute,Lab Services,★,★
91,Managed Applications Health,Monitor custom application deployments and update status,@alex please check Managed Applications with deployment failures or outdated versions. #alert application issues and #dashboard deployment health.,@alex,Operational Efficiency,Azure,Compute,Managed Applications,★,★
92,Azure Edge Zones Performance,Monitor edge computing workload latency and distribution,@alex please analyze Edge Zone workloads with high latency or uneven distribution. #recommend edge optimization and #visual geographic performance.,@alex,Scalability,Azure,Network,Edge Zones,★,★
93,Blueprint Compliance Monitoring,Ensure infrastructure deployments follow architectural blueprints,@alex please check blueprint assignments with compliance violations or deployment failures. #recommend compliance fixes and #report blueprint adherence.,@alex,Security,Azure,Compute,Blueprints,★,★★
94,Import/Export Job Tracking,Monitor large data transfer jobs for completion and efficiency,@alex please review Import/Export jobs with delays or failures. #alert transfer issues and #dashboard data migration progress.,@alex,Operational Efficiency,Azure,Storage,Import/Export,★,★
95,Azure Sphere Device Management,Monitor IoT device security and connectivity health,@alex please check Sphere devices with security updates pending or connectivity issues. #alert device problems and #dashboard device security status.,@alex,Security,Azure,Serverless,Sphere,★,★
96,Multi-Access Edge Computing,Optimize edge computing deployment and performance,@alex please analyze MEC deployments with latency issues or resource constraints. #recommend edge optimization and #report computing efficiency.,@alex,Scalability,Azure,Network,MEC,★,★
97,Azure Resource Mover Planning,Plan and execute resource region migrations efficiently,@alex please check Resource Mover with pending migrations or validation failures. #recommend migration planning and #visual resource dependencies.,@alex,Operational Efficiency,Azure,Compute,Resource Mover,★,★
98,Chaos Studio Experiment Management,Monitor chaos engineering experiments and system resilience,@alex please review Chaos Studio experiments with failures or incomplete coverage. #recommend resilience testing and #dashboard chaos metrics.,@alex,Operational Efficiency,Azure,Compute,Chaos Studio,★,★
99,Azure Load Testing Performance,Monitor application load testing results and optimization recommendations,@alex please analyze Load Testing with performance bottlenecks or capacity limits. #recommend performance improvements and #visual load test results.,@alex,Scalability,Azure,Compute,Load Testing,★★,★★
100,Resource Graph Query Optimization,Optimize resource inventory queries and governance reporting,@alex please review Resource Graph queries with slow execution or missing data. #recommend query optimization and #dashboard resource inventory.,@alex,Operational Efficiency,Azure,Compute,Resource Graph,★,★★
1,IAM Root Account Security,Audit root account usage and ensure MFA is enabled with proper access controls,@olivier please audit AWS root account access patterns and security settings. #alert if MFA is disabled or unusual activity detected. #recommend security hardening steps.,@olivier,Security,AWS,Security,IAM,★★★,★★★
2,Overprivileged IAM Users,Identify IAM users with excessive permissions that haven't been used in 90+ days,@olivier please analyze IAM users with admin privileges unused for 90+ days. #recommend privilege reduction and #alert for potential security risks.,@olivier,Security,AWS,Security,IAM,★★★,★★★
3,Public S3 Buckets,Scan for S3 buckets with public read/write access and assess data sensitivity,@olivier please identify publicly accessible S3 buckets and analyze content sensitivity. #alert for immediate risks and #recommend access controls.,@olivier,Security,AWS,Storage,S3,★★★,★★★
4,Security Groups Audit,Review security groups with overly permissive inbound rules (0.0.0.0/0),@olivier please audit security groups allowing unrestricted inbound access. #recommend least-privilege rules and #alert for critical exposures.,@olivier,Security,AWS,Network,Security Groups,★★★,★★★
5,Unused IAM Credentials,Find IAM users with access keys that haven't been rotated in 90+ days,@olivier please identify IAM access keys older than 90 days without rotation. #alert for stale credentials and #recommend rotation schedule.,@olivier,Security,AWS,Security,IAM,★★★,★★★
6,CloudTrail Configuration,Verify CloudTrail is enabled across all regions with proper log integrity,@olivier please check CloudTrail configuration in all regions for gaps. #alert for disabled trails and #recommend compliance settings.,@olivier,Security,AWS,Security,CloudTrail,★★★,★★★
7,VPC Flow Logs Status,Ensure VPC Flow Logs are enabled for network traffic monitoring,@olivier please verify VPC Flow Logs are enabled across all VPCs. #alert for monitoring gaps and #recommend logging configuration.,@olivier,Security,AWS,Network,VPC,★★★,★★★
8,EBS Encryption Check,Identify unencrypted EBS volumes and assess data classification,@olivier please scan for unencrypted EBS volumes and classify data sensitivity. #alert for compliance violations and #recommend encryption strategy.,@olivier,Security,AWS,Storage,EBS,★★★,★★★
9,GuardDuty Findings,Review and prioritize high-severity GuardDuty security findings,@olivier please analyze recent GuardDuty findings with high/critical severity. #alert for immediate threats and #recommend remediation actions.,@olivier,Security,AWS,Security,GuardDuty,★★★,★★★
10,Password Policy Compliance,Audit IAM password policies against security best practices,@olivier please review IAM password policies for compliance with standards. #recommend policy improvements and #alert for weak configurations.,@olivier,Security,AWS,Security,IAM,★★★,★★★
11,MFA Enforcement Check,"Identify IAM users without MFA enabled, especially privileged accounts","@olivier please find IAM users lacking MFA, prioritizing admin accounts. #alert for high-risk users and #recommend MFA deployment.",@olivier,Security,AWS,Security,IAM,★★★,★★★
12,RDS Encryption Status,Verify RDS instances have encryption at rest enabled,@olivier please check RDS instances for encryption at rest compliance. #alert for unencrypted databases and #recommend encryption migration.,@olivier,Security,AWS,Database,RDS,★★★,★★★
13,AWS Config Rules,Monitor AWS Config compliance rules and address violations,@olivier please review AWS Config rule violations and compliance status. #alert for critical non-compliance and #recommend remediation plans.,@olivier,Security,AWS,Security,Config,★★★,★★★
14,Lambda Function Security,Audit Lambda functions for security misconfigurations and excessive permissions,@olivier please analyze Lambda functions for security issues and IAM roles. #recommend permission reduction and #alert for vulnerabilities.,@olivier,Security,AWS,Serverless,Lambda,★★★,★★★
15,SSL Certificate Expiry,Monitor SSL certificates in ACM and ELB for upcoming expiration,@olivier please check SSL certificates expiring within 30 days. #alert for renewal requirements and #recommend automated renewal setup.,@olivier,Security,AWS,Security,ACM,★★★,★★★
16,Network ACL Review,Audit Network ACLs for overly permissive or conflicting rules,@olivier please review Network ACL rules for security gaps and conflicts. #recommend rule optimization and #alert for risky configurations.,@olivier,Security,AWS,Network,NACL,★★★,★★
17,CloudWatch Security Alarms,Ensure critical security events have proper CloudWatch alarms configured,@olivier please verify CloudWatch alarms exist for security events. #alert for monitoring gaps and #recommend alarm templates.,@olivier,Security,AWS,Security,CloudWatch,★★★,★★
18,IAM Policy Analyzer,Use IAM Access Analyzer to identify external resource access,@olivier please run IAM Access Analyzer to find external access grants. #alert for unexpected permissions and #recommend access review.,@olivier,Security,AWS,Security,IAM,★★★,★★★
19,S3 Bucket Versioning,Verify S3 buckets have versioning and MFA delete enabled,@olivier please check S3 buckets for versioning and MFA delete configuration. #recommend data protection improvements and #alert for risks.,@olivier,Security,AWS,Storage,S3,★★,★★★
20,EC2 Security Patching,Monitor EC2 instances for missing security patches using Systems Manager,@olivier please check EC2 patch compliance status via Systems Manager. #alert for critical missing patches and #recommend patching schedule.,@olivier,Security,AWS,Compute,EC2,★★★,★★
21,Inspector Vulnerability Scan,Review Amazon Inspector findings for EC2 and container vulnerabilities,@olivier please analyze Inspector vulnerability assessments and severity. #alert for critical vulnerabilities and #recommend remediation priorities.,@olivier,Security,AWS,Security,Inspector,★★★,★★
22,KMS Key Rotation,Ensure KMS keys have automatic rotation enabled where appropriate,@olivier please verify KMS customer-managed keys have rotation enabled. #alert for non-rotating keys and #recommend rotation policies.,@olivier,Security,AWS,Security,KMS,★★★,★★
23,ECR Image Scanning,Scan ECR container images for known vulnerabilities,@olivier please run vulnerability scans on ECR images and review findings. #alert for high-severity issues and #recommend image updates.,@olivier,Security,AWS,Security,ECR,★★★,★★
24,WAF Rule Effectiveness,Analyze AWS WAF logs to assess rule effectiveness and adjust as needed,@olivier please review WAF logs and rule performance metrics. #recommend rule tuning and #alert for potential bypass attempts.,@olivier,Security,AWS,Security,WAF,★★,★★★
25,Secrets Manager Rotation,Verify secrets in AWS Secrets Manager have automatic rotation configured,@olivier please check Secrets Manager entries for rotation configuration. #alert for stale secrets and #recommend rotation automation.,@olivier,Security,AWS,Security,Secrets Manager,★★★,★★
26,Route 53 DNS Security,Monitor Route 53 for suspicious DNS queries and configuration changes,@olivier please analyze Route 53 resolver logs for anomalous activity. #alert for potential DNS threats and #recommend security measures.,@olivier,Security,AWS,Network,Route 53,★★,★★
27,ELB Security Headers,Verify ELBs have proper security headers configured,@olivier please check Application Load Balancers for security header configuration. #recommend header improvements and #alert for missing protections.,@olivier,Security,AWS,Network,ELB,★★,★★
28,Organizations SCP Audit,Review Service Control Policies for proper permission boundaries,@olivier please audit AWS Organizations SCPs for security compliance. #recommend policy improvements and #alert for gaps.,@olivier,Security,AWS,Security,Organizations,★★★,★★
29,CloudFront Security,Audit CloudFront distributions for security best practices,@olivier please review CloudFront configurations for security settings. #recommend improvements and #alert for misconfigurations.,@olivier,Security,AWS,Network,CloudFront,★★,★★
30,API Gateway Security,Verify API Gateway endpoints have proper authentication and authorization,@olivier please audit API Gateway security configurations and access controls. #alert for open endpoints and #recommend security enhancements.,@olivier,Security,AWS,Serverless,API Gateway,★★★,★★
31,Trusted Advisor Security,Review Trusted Advisor security recommendations and implementation status,@olivier please analyze Trusted Advisor security checks and recommendations. #alert for unaddressed items and #recommend implementation priorities.,@olivier,Security,AWS,Security,Trusted Advisor,★★,★★
32,IAM Cross-Account Access,Audit cross-account IAM roles and trust relationships,@olivier please review cross-account IAM roles for security risks. #alert for overprivileged access and #recommend trust policy updates.,@olivier,Security,AWS,Security,IAM,★★★,★★
33,Data Loss Prevention,Implement and monitor DLP policies for sensitive data in S3,@olivier please configure Macie for S3 data classification and monitoring. #alert for sensitive data exposure and #recommend protection measures.,@olivier,Security,AWS,Security,Macie,★★,★★★
34,Network Security Monitoring,Set up VPC traffic analysis for anomaly detection,@olivier please configure network monitoring for unusual traffic patterns. #alert for suspicious activity and #recommend investigation procedures.,@olivier,Security,AWS,Network,VPC,★★,★★★
35,Compliance Framework Check,Verify AWS resources meet SOC2/ISO27001 compliance requirements,@olivier please audit resources against compliance framework requirements. #alert for violations and #recommend remediation roadmap.,@olivier,Security,AWS,Security,Config,★★★,★★
36,Identity Center Security,Audit AWS SSO/Identity Center configurations and user access,@olivier please review Identity Center user permissions and group assignments. #alert for excessive access and #recommend access optimization.,@olivier,Security,AWS,Security,Identity Center,★★,★★
37,Database Access Logging,Ensure RDS and Aurora have proper access logging enabled,@olivier please verify database audit logging is enabled and configured. #alert for logging gaps and #recommend log analysis setup.,@olivier,Security,AWS,Database,RDS,★★★,★★
38,Container Security Scanning,Implement security scanning for ECS and EKS workloads,@olivier please set up container security scanning for running workloads. #alert for vulnerabilities and #recommend remediation workflow.,@olivier,Security,AWS,Compute,ECS/EKS,★★,★★
39,Backup Encryption Check,Verify AWS Backup jobs use encryption for data protection,@olivier please audit AWS Backup configurations for encryption settings. #alert for unencrypted backups and #recommend security improvements.,@olivier,Security,AWS,Storage,Backup,★★,★★
40,Session Manager Audit,Review Systems Manager Session Manager access and logging,@olivier please audit Session Manager usage and access patterns. #alert for suspicious sessions and #recommend monitoring enhancements.,@olivier,Security,AWS,Security,Systems Manager,★★,★★
41,Security Hub Findings,Prioritize and track AWS Security Hub findings across services,@olivier please review Security Hub findings and prioritize remediation. #alert for critical findings and #recommend action plans.,@olivier,Security,AWS,Security,Security Hub,★★★,★★
42,DocumentDB Security,Audit DocumentDB clusters for security configuration and access,@olivier please check DocumentDB security settings and access controls. #alert for misconfigurations and #recommend security hardening.,@olivier,Security,AWS,Database,DocumentDB,★★,★★
43,Redshift Security Review,Verify Redshift clusters have proper security and encryption,@olivier please audit Redshift security configurations and access patterns. #alert for vulnerabilities and #recommend security improvements.,@olivier,Security,AWS,Database,Redshift,★★,★★
44,ElastiCache Security,Check ElastiCache clusters for security best practices,@olivier please review ElastiCache security settings and network access. #alert for misconfigurations and #recommend protection measures.,@olivier,Security,AWS,Database,ElastiCache,★★,★★
45,OpenSearch Security,Audit OpenSearch domains for security and access controls,@olivier please check OpenSearch security configurations and policies. #alert for open access and #recommend security enhancements.,@olivier,Security,AWS,Database,OpenSearch,★★,★★
46,EventBridge Security,Review EventBridge rules and cross-account access permissions,@olivier please audit EventBridge rules for security implications. #alert for excessive permissions and #recommend access controls.,@olivier,Security,AWS,Serverless,EventBridge,★★,★★
47,Step Functions Security,Verify Step Functions have proper IAM roles and permissions,@olivier please review Step Functions security configurations. #alert for overprivileged roles and #recommend permission optimization.,@olivier,Security,AWS,Serverless,Step Functions,★★,★★
48,SQS Queue Security,Audit SQS queues for proper access policies and encryption,@olivier please check SQS queue policies and encryption settings. #alert for open access and #recommend security improvements.,@olivier,Security,AWS,Serverless,SQS,★★,★★
49,SNS Topic Security,Review SNS topics for access policies and subscription security,@olivier please audit SNS topic policies and subscriber access. #alert for insecure configurations and #recommend policy updates.,@olivier,Security,AWS,Serverless,SNS,★★,★★
50,Kinesis Security Check,Verify Kinesis streams have encryption and proper access controls,@olivier please check Kinesis streams for security configurations. #alert for unencrypted streams and #recommend security enhancements.,@olivier,Security,AWS,Serverless,Kinesis,★★,★★
51,Direct Connect Security,Audit Direct Connect virtual interfaces for security settings,@olivier please review Direct Connect security configurations. #alert for vulnerabilities and #recommend network security improvements.,@olivier,Security,AWS,Network,Direct Connect,★★,★★
52,VPN Security Review,Check Site-to-Site VPN configurations for security best practices,@olivier please audit VPN connections for security settings. #alert for weak configurations and #recommend security hardening.,@olivier,Security,AWS,Network,VPN,★★,★★
53,Transit Gateway Security,Review Transit Gateway route tables and security group associations,@olivier please audit Transit Gateway security configurations. #alert for routing risks and #recommend security improvements.,@olivier,Security,AWS,Network,Transit Gateway,★★,★★
54,EFS Security Audit,Verify EFS file systems have proper access controls and encryption,@olivier please check EFS security settings and access policies. #alert for misconfigurations and #recommend security enhancements.,@olivier,Security,AWS,Storage,EFS,★★,★★
55,FSx Security Check,Audit FSx file systems for security and access configurations,@olivier please review FSx security settings and network access. #alert for vulnerabilities and #recommend protection measures.,@olivier,Security,AWS,Storage,FSx,★★,★★
56,WorkSpaces Security,Review Amazon WorkSpaces security configurations and user access,@olivier please audit WorkSpaces security settings and policies. #alert for misconfigurations and #recommend security improvements.,@olivier,Security,AWS,Compute,WorkSpaces,★★,★★
57,AppStream Security,Check AppStream fleets for security configurations and access,@olivier please review AppStream security settings and user policies. #alert for vulnerabilities and #recommend security hardening.,@olivier,Security,AWS,Compute,AppStream,★★,★★
58,Connect Security Audit,Verify Amazon Connect instances have proper security settings,@olivier please audit Connect security configurations and access controls. #alert for misconfigurations and #recommend improvements.,@olivier,Security,AWS,Serverless,Connect,★★,★★
59,Chime Security Review,Check Amazon Chime security settings and user access controls,@olivier please review Chime security configurations and policies. #alert for vulnerabilities and #recommend security enhancements.,@olivier,Security,AWS,Serverless,Chime,★★,★★
60,IoT Core Security,Audit AWS IoT Core policies and device certificates,@olivier please check IoT Core security policies and device access. #alert for insecure configurations and #recommend policy updates.,@olivier,Security,AWS,Serverless,IoT Core,★★,★★
61,Batch Security Check,Review AWS Batch job definitions and compute environment security,@olivier please audit Batch security configurations and IAM roles. #alert for overprivileged access and #recommend improvements.,@olivier,Security,AWS,Compute,Batch,★★,★★
62,Glue Security Audit,Verify AWS Glue jobs have proper security and access controls,@olivier please check Glue security settings and data access policies. #alert for vulnerabilities and #recommend security hardening.,@olivier,Security,AWS,Serverless,Glue,★★,★★
63,EMR Security Review,Audit EMR clusters for security configurations and access,@olivier please review EMR security settings and network controls. #alert for misconfigurations and #recommend security improvements.,@olivier,Security,AWS,Compute,EMR,★★,★★
64,SageMaker Security,Check SageMaker endpoints and notebooks for security settings,@olivier please audit SageMaker security configurations and access. #alert for vulnerabilities and #recommend protection measures.,@olivier,Security,AWS,Serverless,SageMaker,★★,★★
65,CodeCommit Security,Review CodeCommit repositories for access controls and encryption,@olivier please check CodeCommit security settings and policies. #alert for open access and #recommend security enhancements.,@olivier,Security,AWS,Security,CodeCommit,★★,★★
66,CodeBuild Security,Audit CodeBuild projects for security configurations and permissions,@olivier please review CodeBuild security settings and IAM roles. #alert for excessive permissions and #recommend improvements.,@olivier,Security,AWS,Serverless,CodeBuild,★★,★★
67,CodePipeline Security,Verify CodePipeline security settings and cross-account access,@olivier please audit CodePipeline security configurations. #alert for vulnerabilities and #recommend security hardening.,@olivier,Security,AWS,Serverless,CodePipeline,★★,★★
68,CodeDeploy Security,Check CodeDeploy applications for security best practices,@olivier please review CodeDeploy security settings and access controls. #alert for misconfigurations and #recommend improvements.,@olivier,Security,AWS,Serverless,CodeDeploy,★★,★★
69,Systems Manager Security,Audit Systems Manager parameters and automation security,@olivier please check Systems Manager security configurations. #alert for insecure settings and #recommend security enhancements.,@olivier,Security,AWS,Security,Systems Manager,★★,★★
70,OpsWorks Security,Review OpsWorks stacks for security configurations,@olivier please audit OpsWorks security settings and access controls. #alert for vulnerabilities and #recommend protection measures.,@olivier,Security,AWS,Compute,OpsWorks,★★,★★
71,Lightsail Security,Check Lightsail instances for security best practices,@olivier please review Lightsail security configurations and access. #alert for misconfigurations and #recommend security improvements.,@olivier,Security,AWS,Compute,Lightsail,★★,★★
72,DataSync Security,Verify DataSync tasks have proper security and encryption,@olivier please check DataSync security settings and data protection. #alert for unencrypted transfers and #recommend improvements.,@olivier,Security,AWS,Storage,DataSync,★★,★★
73,Storage Gateway Security,Audit Storage Gateway security configurations and access,@olivier please review Storage Gateway security settings. #alert for vulnerabilities and #recommend security hardening.,@olivier,Security,AWS,Storage,Storage Gateway,★★,★★
74,Directory Service Security,Check AWS Directory Service security settings and policies,@olivier please audit Directory Service security configurations. #alert for misconfigurations and #recommend security improvements.,@olivier,Security,AWS,Security,Directory Service,★★,★★
75,Certificate Manager Audit,Review ACM certificates for proper validation and deployment,@olivier please audit ACM certificates and deployment status. #alert for validation issues and #recommend certificate management.,@olivier,Security,AWS,Security,ACM,★★,★★
76,CloudHSM Security,Verify CloudHSM clusters have proper security configurations,@olivier please check CloudHSM security settings and access controls. #alert for vulnerabilities and #recommend security enhancements.,@olivier,Security,AWS,Security,CloudHSM,★★,★★
77,Cognito Security Review,Audit Cognito user pools and identity pools for security,@olivier please review Cognito security configurations and policies. #alert for misconfigurations and #recommend improvements.,@olivier,Security,AWS,Security,Cognito,★★,★★
78,App Mesh Security,Check App Mesh security settings and service communication,@olivier please audit App Mesh security configurations. #alert for insecure settings and #recommend security hardening.,@olivier,Security,AWS,Network,App Mesh,★★,★★
79,X-Ray Security Audit,Review X-Ray service configurations for security implications,@olivier please check X-Ray security settings and data access. #alert for vulnerabilities and #recommend protection measures.,@olivier,Security,AWS,Security,X-Ray,★★,★★
80,MediaConnect Security,Verify MediaConnect flows have proper security configurations,@olivier please audit MediaConnect security settings and access. #alert for misconfigurations and #recommend security improvements.,@olivier,Security,AWS,Network,MediaConnect,★★,★★
81,GameLift Security,Check GameLift security configurations and access controls,@olivier please review GameLift security settings and policies. #alert for vulnerabilities and #recommend security enhancements.,@olivier,Security,AWS,Compute,GameLift,★★,★★
82,Quantum Ledger Security,Audit QLDB security settings and access permissions,@olivier please check QLDB security configurations and access. #alert for misconfigurations and #recommend improvements.,@olivier,Security,AWS,Database,QLDB,★★,★★
83,Timestream Security,Review Timestream database security and access controls,@olivier please audit Timestream security settings and policies. #alert for vulnerabilities and #recommend security hardening.,@olivier,Security,AWS,Database,Timestream,★★,★★
84,Keyspaces Security,Check Amazon Keyspaces security configurations,@olivier please review Keyspaces security settings and access. #alert for misconfigurations and #recommend security improvements.,@olivier,Security,AWS,Database,Keyspaces,★★,★★
85,MemoryDB Security,Audit MemoryDB clusters for security best practices,@olivier please check MemoryDB security configurations and access. #alert for vulnerabilities and #recommend protection measures.,@olivier,Security,AWS,Database,MemoryDB,★★,★★
86,Neptune Security Review,Verify Neptune clusters have proper security settings,@olivier please audit Neptune security configurations and policies. #alert for misconfigurations and #recommend security enhancements.,@olivier,Security,AWS,Database,Neptune,★★,★★
87,Managed Blockchain Security,Check Managed Blockchain network security configurations,@olivier please review Blockchain security settings and access. #alert for vulnerabilities and #recommend security improvements.,@olivier,Security,AWS,Serverless,Managed Blockchain,★★,★★
88,Ground Station Security,Audit Ground Station security configurations,@olivier please check Ground Station security settings. #alert for misconfigurations and #recommend security hardening.,@olivier,Security,AWS,Network,Ground Station,★★,★★
89,Braket Security Check,Review Amazon Braket security configurations and access,@olivier please audit Braket security settings and policies. #alert for vulnerabilities and #recommend protection measures.,@olivier,Security,AWS,Compute,Braket,★★,★★
90,Detective Security Analysis,Use Amazon Detective to analyze security investigation findings,@olivier please review Detective findings and investigation data. #alert for security threats and #recommend response actions.,@olivier,Security,AWS,Security,Detective,★★,★★
91,Fraud Detector Security,Audit Fraud Detector models and security configurations,@olivier please check Fraud Detector security settings. #alert for misconfigurations and #recommend security improvements.,@olivier,Security,AWS,Security,Fraud Detector,★★,★★
92,Network Firewall Rules,Review AWS Network Firewall rule groups and policies,@olivier please audit Network Firewall configurations and rules. #alert for security gaps and #recommend rule optimization.,@olivier,Security,AWS,Security,Network Firewall,★★,★★
93,Resource Access Manager,Check RAM resource sharing for security implications,@olivier please review RAM shared resources and access policies. #alert for oversharing and #recommend access controls.,@olivier,Security,AWS,Security,RAM,★★,★★
94,License Manager Security,Audit License Manager configurations for security compliance,@olivier please check License Manager security settings. #alert for vulnerabilities and #recommend security enhancements.,@olivier,Security,AWS,Security,License Manager,★★,★★
95,Cost Anomaly Detection,Monitor Cost Anomaly Detection for potential security incidents,@olivier please review cost anomalies that might indicate security breaches. #alert for suspicious spending and #recommend investigation.,@olivier,Security,AWS,Security,Cost Anomaly,★★,★★
96,Application Discovery Security,Check Application Discovery Agent security configurations,@olivier please audit Application Discovery security settings. #alert for misconfigurations and #recommend improvements.,@olivier,Security,AWS,Security,Application Discovery,★★,★★
97,Migration Hub Security,Review Migration Hub security configurations and access,@olivier please check Migration Hub security settings. #alert for vulnerabilities and #recommend security hardening.,@olivier,Security,AWS,Security,Migration Hub,★★,★★
98,Server Migration Security,Audit Server Migration Service security configurations,@olivier please review SMS security settings and access controls. #alert for misconfigurations and #recommend improvements.,@olivier,Security,AWS,Security,SMS,★★,★★
99,Database Migration Security,Check DMS security configurations and encryption settings,@olivier please audit DMS security settings and data protection. #alert for vulnerabilities and #recommend security enhancements.,@olivier,Security,AWS,Security,DMS,★★,★★
100,Global Accelerator Security,Review Global Accelerator security configurations and access,@olivier please audit Global Accelerator security settings. #alert for misconfigurations and #recommend security improvements.,@olivier,Security,AWS,Network,Global Accelerator,★★,★★
1,Slow Query Analysis,Identify and analyze queries consuming excessive CPU time and I/O resources,@tony please analyze pg_stat_statements for queries with >5s execution time. #recommend optimization strategies and #alert for critical performance issues.,@tony,Operational Efficiency,Database,Query,PostgreSQL,★★★,★★★
2,Index Usage Optimization,Review unused and redundant indexes impacting write performance,@tony please query pg_stat_user_indexes for unused indexes with 0 scans. #recommend index removal strategies and #report storage savings potential.,@tony,Cost Optimization,Database,Optimization,PostgreSQL,★★★,★★★
3,Table Bloat Detection,Monitor table and index bloat affecting query performance,@tony please calculate table bloat ratios using pg_stat_user_tables. #visual bloat trends and #alert when bloat exceeds 30% threshold.,@tony,Operational Efficiency,Database,Optimization,PostgreSQL,★★★,★★★
4,Connection Pool Monitoring,Track connection usage patterns and identify bottlenecks,@tony please analyze pg_stat_activity for connection patterns. #dashboard connection metrics and #recommend pool size adjustments.,@tony,Scalability,Database,Query,PostgreSQL,★★★,★★★
5,Vacuum Performance Analysis,Monitor autovacuum effectiveness and table maintenance,@tony please query pg_stat_progress_vacuum for active operations. #recommend vacuum scheduling improvements and #alert for stalled processes.,@tony,Operational Efficiency,Database,Optimization,PostgreSQL,★★★,★★★
6,Lock Contention Detection,Identify blocking queries and lock wait scenarios,@tony please monitor pg_locks for blocking relationships >30s. #alert for deadlock situations and #recommend query optimization.,@tony,Operational Efficiency,Database,Query,PostgreSQL,★★★,★★★
7,Database Size Monitoring,Track database growth patterns and space utilization,@tony please query pg_database_size for growth trends over 30 days. #visual size progression and #alert for unexpected growth spikes.,@tony,Cost Optimization,Database,Query,PostgreSQL,★★★,★★
8,Query Plan Regression,Detect performance degradation in execution plans,@tony please compare query plans in pg_stat_statements over time. #alert for plan changes causing >50% performance degradation.,@tony,Operational Efficiency,Database,Optimization,PostgreSQL,★★★,★★★
9,Statistics Staleness Check,Ensure table statistics are current for optimal planning,@tony please query pg_stat_user_tables for stale statistics >7 days. #recommend analyze schedule improvements and #alert critical tables.,@tony,Operational Efficiency,Database,Optimization,PostgreSQL,★★★,★★
10,Missing Index Detection,Identify sequential scans on large tables needing indexes,@tony please analyze pg_stat_user_tables for high seq_scan ratios. #recommend index candidates and #report potential performance gains.,@tony,Operational Efficiency,Database,Optimization,PostgreSQL,★★★,★★★
11,Buffer Cache Hit Ratio,Monitor memory efficiency and cache performance,@tony please calculate buffer hit ratios from pg_stat_database. #dashboard cache metrics and #alert when hit ratio drops below 95%.,@tony,Operational Efficiency,Database,Optimization,PostgreSQL,★★★,★★
12,Checkpoint Performance,Analyze checkpoint frequency and I/O impact,@tony please monitor checkpoint statistics in pg_stat_bgwriter. #recommend checkpoint tuning and #visual I/O patterns during checkpoints.,@tony,Operational Efficiency,Database,Optimization,PostgreSQL,★★,★★★
13,Replication Lag Monitoring,Track streaming replication delay and health,@tony please query pg_stat_replication for lag metrics. #alert when lag exceeds 1MB or 30s and #dashboard replication status.,@tony,Operational Efficiency,Database,Query,PostgreSQL,★★★,★★★
14,User Permission Audit,Review database user privileges and access patterns,@tony please audit user permissions via information_schema. #report excessive privileges and #recommend principle of least access.,@tony,Security,Database,Query,PostgreSQL,★★★,★★
15,Query Cost Analysis,Identify high-cost queries impacting system resources,@tony please analyze query costs from pg_stat_statements. #recommend optimization for queries with cost >1000 and #visual cost distribution.,@tony,Cost Optimization,Database,Optimization,PostgreSQL,★★★,★★
16,Temporary File Usage,Monitor temp file creation and disk space impact,@tony please track temp_files and temp_bytes from pg_stat_database. #alert for excessive temp usage and #recommend work_mem adjustments.,@tony,Operational Efficiency,Database,Optimization,PostgreSQL,★★,★★★
17,WAL File Management,Monitor Write-Ahead Log file accumulation and archiving,@tony please check WAL file counts and archive status. #alert for archive failures and #recommend WAL retention policies.,@tony,Operational Efficiency,Database,Query,PostgreSQL,★★★,★★
18,Parameter Drift Analysis,Track PostgreSQL configuration changes and impacts,@tony please compare current settings with baseline configuration. #alert for unauthorized changes and #report parameter optimization opportunities.,@tony,Security,Database,Query,PostgreSQL,★★,★★
19,Constraint Violation Monitoring,Track foreign key and check constraint failures,@tony please monitor constraint violations in application logs. #alert for data integrity issues and #recommend validation improvements.,@tony,Operational Efficiency,Database,Query,PostgreSQL,★★,★★
20,Sequence Exhaustion Check,Monitor sequence usage approaching maximum values,@tony please check sequence usage via information_schema. #alert for sequences >80% exhausted and #recommend sequence extension strategies.,@tony,Operational Efficiency,Database,Query,PostgreSQL,★★,★★★
21,Schema Drift Detection,Compare schema changes across environments,@tony please compare table structures between prod/staging via information_schema. #report schema differences and #alert for unexpected changes.,@tony,Operational Efficiency,Database,Query,PostgreSQL,★★,★★
22,Long Running Transactions,Identify transactions holding locks for extended periods,@tony please query pg_stat_activity for transactions >1 hour. #alert for blocking transactions and #recommend timeout configurations.,@tony,Operational Efficiency,Database,Query,PostgreSQL,★★★,★★
23,Duplicate Index Detection,Find redundant indexes wasting storage and performance,@tony please identify overlapping indexes via system catalogs. #recommend consolidation strategies and #report storage savings potential.,@tony,Cost Optimization,Database,Optimization,PostgreSQL,★★,★★
24,Extension Security Review,Audit installed PostgreSQL extensions for vulnerabilities,@tony please list installed extensions from pg_extension. #report security implications and #recommend updates for vulnerable extensions.,@tony,Security,Database,Query,PostgreSQL,★★,★★
25,Query Frequency Analysis,Track most frequently executed queries for optimization,@tony please analyze query call frequency from pg_stat_statements. #visual top queries by calls and #recommend caching opportunities.,@tony,Operational Efficiency,Database,Optimization,PostgreSQL,★★,★★
26,Table Inheritance Monitoring,Review partitioned table performance and maintenance,@tony please analyze partition pruning effectiveness. #recommend partition strategy improvements and #alert for ineffective partitioning.,@tony,Operational Efficiency,Database,Optimization,PostgreSQL,★★,★★
27,Function Performance Review,Identify slow user-defined functions impacting queries,@tony please analyze function execution times from pg_stat_user_functions. #recommend optimization for functions with >1s avg time.,@tony,Operational Efficiency,Database,Optimization,PostgreSQL,★★,★★
28,Background Worker Health,Monitor background processes and worker utilization,@tony please check background worker activity and health. #alert for failed workers and #dashboard worker performance metrics.,@tony,Operational Efficiency,Database,Query,PostgreSQL,★★,★★
29,Connection Leak Detection,Identify idle connections consuming resources,@tony please find idle connections >4 hours in pg_stat_activity. #alert for connection leaks and #recommend connection timeout policies.,@tony,Operational Efficiency,Database,Query,PostgreSQL,★★,★★
30,Database Encoding Issues,Check for character encoding problems and data corruption,@tony please validate database encoding consistency across tables. #alert for encoding mismatches and #recommend remediation steps.,@tony,Operational Efficiency,Database,Query,PostgreSQL,★★,★★
31,Column Statistics Analysis,Review column distribution statistics for query planning,@tony please analyze column statistics histograms for skewed data. #recommend statistics targets adjustment and #visual data distribution.,@tony,Operational Efficiency,Database,Optimization,PostgreSQL,★★,★★
32,Trigger Performance Impact,Measure trigger execution overhead on DML operations,@tony please analyze trigger execution times and frequency. #recommend trigger optimization and #alert for triggers causing >10% overhead.,@tony,Operational Efficiency,Database,Optimization,PostgreSQL,★★,★★
33,Archive Mode Health,Monitor WAL archiving success rates and failures,@tony please check archive command success rates. #alert for archive failures >5% and #recommend archive storage optimization.,@tony,Operational Efficiency,Database,Query,PostgreSQL,★★,★★
34,Table Access Patterns,Analyze table scan patterns and index usage effectiveness,@tony please review table access methods from pg_stat_user_tables. #visual scan ratios and #recommend index strategy improvements.,@tony,Operational Efficiency,Database,Optimization,PostgreSQL,★★,★★
35,Materialized View Refresh,Monitor materialized view staleness and refresh performance,@tony please track materialized view refresh times and data freshness. #alert for stale views and #recommend refresh scheduling.,@tony,Operational Efficiency,Database,Query,PostgreSQL,★★,★★
36,Role-Based Access Control,Audit database roles and membership hierarchies,@tony please review role assignments via pg_roles and pg_auth_members. #report privilege escalation risks and #recommend access control improvements.,@tony,Security,Database,Query,PostgreSQL,★★,★★
37,Query Memory Usage,Track memory consumption patterns for query optimization,@tony please analyze work_mem usage patterns in query plans. #recommend memory configuration adjustments and #visual memory utilization.,@tony,Operational Efficiency,Database,Optimization,PostgreSQL,★★,★★
38,Foreign Key Performance,Assess foreign key constraint impact on write operations,@tony please measure foreign key validation overhead. #recommend constraint optimization and #alert for performance bottlenecks.,@tony,Operational Efficiency,Database,Optimization,PostgreSQL,★★,★★
39,Backup Verification,Validate backup integrity and restoration capabilities,@tony please verify backup checksums and test restore procedures. #alert for backup failures and #report backup health metrics.,@tony,Operational Efficiency,Database,Query,PostgreSQL,★★,★★★
40,Data Type Usage Analysis,Review column data types for storage efficiency,@tony please analyze data type usage via information_schema. #recommend type optimization for storage reduction and #report potential savings.,@tony,Cost Optimization,Database,Query,PostgreSQL,★★,★★
41,Log File Analysis,Parse PostgreSQL logs for error patterns and warnings,@tony please analyze error log patterns for recurring issues. #alert for critical errors and #recommend log retention optimization.,@tony,Operational Efficiency,Database,Query,PostgreSQL,★★,★★
42,Resource Queue Monitoring,Track query resource consumption and queuing,@tony please monitor resource queue wait times and throughput. #dashboard queue metrics and #recommend resource allocation improvements.,@tony,Operational Efficiency,Database,Query,PostgreSQL,★★,★★
43,Schema Object Dependencies,Map table and view dependencies for change impact,@tony please analyze object dependencies via information_schema. #visual dependency graphs and #alert for circular dependencies.,@tony,Operational Efficiency,Database,Query,PostgreSQL,★,★★
44,Deadlock Pattern Analysis,Study deadlock causes and prevention strategies,@tony please analyze deadlock patterns from PostgreSQL logs. #recommend transaction design improvements and #visual deadlock frequency.,@tony,Operational Efficiency,Database,Optimization,PostgreSQL,★★,★★
45,Collation Consistency,Verify text collation settings across database objects,@tony please check collation consistency via information_schema. #alert for collation mismatches and #recommend standardization.,@tony,Operational Efficiency,Database,Query,PostgreSQL,★,★★
46,Prepared Statement Usage,Monitor prepared statement cache effectiveness,@tony please analyze prepared statement hit rates and performance. #recommend statement caching improvements and #visual cache metrics.,@tony,Operational Efficiency,Database,Optimization,PostgreSQL,★★,★★
47,TOAST Table Management,Monitor large object storage and compression effectiveness,@tony please analyze TOAST table sizes and compression ratios. #recommend compression strategy improvements and #alert for oversized objects.,@tony,Cost Optimization,Database,Query,PostgreSQL,★★,★★
48,Sequence Usage Patterns,Analyze sequence allocation and gap patterns,@tony please review sequence usage patterns and allocation rates. #recommend sequence caching optimization and #visual usage trends.,@tony,Operational Efficiency,Database,Query,PostgreSQL,★,★★
49,View Performance Analysis,Assess complex view execution performance,@tony please analyze view execution plans and performance metrics. #recommend view optimization and #alert for inefficient view definitions.,@tony,Operational Efficiency,Database,Optimization,PostgreSQL,★★,★★
50,Database Age Monitoring,Track transaction ID wraparound risks,@tony please monitor transaction ID age via pg_database. #alert for wraparound risks >75% and #recommend vacuum freeze scheduling.,@tony,Operational Efficiency,Database,Query,PostgreSQL,★★★,★★
51,Query Result Caching,Analyze query result cache hit rates and effectiveness,@tony please review query result cache performance metrics. #recommend caching strategy improvements and #dashboard cache utilization.,@tony,Operational Efficiency,Database,Optimization,PostgreSQL,★★,★★
52,Schema Version Control,Track schema changes and migration history,@tony please monitor schema version history and migration status. #alert for incomplete migrations and #report schema evolution trends.,@tony,Operational Efficiency,Database,Query,PostgreSQL,★,★★
53,Parallel Query Effectiveness,Measure parallel query performance and resource usage,@tony please analyze parallel query execution patterns. #recommend parallelism tuning and #visual parallel worker utilization.,@tony,Operational Efficiency,Database,Optimization,PostgreSQL,★★,★★
54,Custom Data Types Usage,Review custom type definitions and performance impact,@tony please analyze custom data type usage and performance. #recommend type optimization and #alert for inefficient custom types.,@tony,Operational Efficiency,Database,Query,PostgreSQL,★,★★
55,Partitioned Table Health,Monitor partition pruning and constraint exclusion,@tony please check partition constraint exclusion effectiveness. #recommend partitioning improvements and #visual partition access patterns.,@tony,Operational Efficiency,Database,Optimization,PostgreSQL,★★,★★
56,Statement Timeout Monitoring,Track query cancellations and timeout patterns,@tony please monitor statement timeout occurrences and patterns. #alert for frequent timeouts and #recommend timeout configuration adjustments.,@tony,Operational Efficiency,Database,Query,PostgreSQL,★★,★★
57,Database Object Naming,Ensure consistent naming conventions across objects,@tony please audit object naming conventions via information_schema. #report naming inconsistencies and #recommend standardization guidelines.,@tony,Operational Efficiency,Database,Query,PostgreSQL,★,★★
58,Full Text Search Performance,Optimize text search configurations and indexes,@tony please analyze full-text search query performance. #recommend search optimization and #visual search pattern analysis.,@tony,Operational Efficiency,Database,Optimization,PostgreSQL,★★,★★
59,Connection Pooling Efficiency,Measure connection pool utilization and effectiveness,@tony please analyze connection pool metrics and utilization. #recommend pool configuration improvements and #dashboard connection trends.,@tony,Scalability,Database,Query,PostgreSQL,★★,★★
60,Index Maintenance Scheduling,Optimize index rebuild and reorganization timing,@tony please analyze index fragmentation and maintenance needs. #recommend maintenance scheduling and #alert for heavily fragmented indexes.,@tony,Operational Efficiency,Database,Optimization,PostgreSQL,★★,★★
61,Audit Trail Completeness,Verify database activity logging coverage,@tony please check audit log completeness and coverage. #alert for audit gaps and #recommend logging configuration improvements.,@tony,Security,Database,Query,PostgreSQL,★★,★★
62,Data Compression Analysis,Assess table compression effectiveness and savings,@tony please analyze table compression ratios and storage savings. #recommend compression strategy improvements and #visual compression benefits.,@tony,Cost Optimization,Database,Query,PostgreSQL,★★,★★
63,Join Performance Optimization,Identify inefficient join operations and patterns,@tony please analyze join execution plans and performance. #recommend join optimization strategies and #alert for nested loop joins on large tables.,@tony,Operational Efficiency,Database,Optimization,PostgreSQL,★★,★★
64,Session Configuration Drift,Monitor session-level parameter changes,@tony please track session parameter modifications and impacts. #alert for unauthorized changes and #recommend session management policies.,@tony,Security,Database,Query,PostgreSQL,★,★★
65,Query Plan Cache Health,Monitor query plan cache hit rates and effectiveness,@tony please analyze query plan cache performance metrics. #recommend cache configuration improvements and #dashboard cache utilization trends.,@tony,Operational Efficiency,Database,Optimization,PostgreSQL,★★,★★
66,Large Object Management,Monitor large object storage and cleanup needs,@tony please track large object usage and orphaned objects. #recommend cleanup procedures and #alert for excessive large object growth.,@tony,Cost Optimization,Database,Query,PostgreSQL,★★,★★
67,Multi-Version Concurrency,Monitor MVCC tuple version accumulation,@tony please analyze tuple version counts and visibility. #recommend vacuum tuning for MVCC efficiency and #visual version accumulation trends.,@tony,Operational Efficiency,Database,Optimization,PostgreSQL,★★,★★
68,Foreign Data Wrapper Performance,Assess external data source query performance,@tony please analyze foreign data wrapper query execution. #recommend FDW optimization and #alert for slow external queries.,@tony,Operational Efficiency,Database,Query,PostgreSQL,★,★★
69,Table Column Usage,Identify unused columns for schema optimization,@tony please analyze column access patterns via query logs. #recommend schema simplification and #report unused column storage impact.,@tony,Cost Optimization,Database,Query,PostgreSQL,★,★★
70,Constraint Validation Performance,Measure constraint checking overhead,@tony please analyze constraint validation times and impacts. #recommend constraint optimization and #visual validation performance trends.,@tony,Operational Efficiency,Database,Optimization,PostgreSQL,★,★★
71,Database Locale Settings,Verify locale configuration consistency,@tony please check database locale settings and character handling. #alert for locale mismatches and #recommend locale standardization.,@tony,Operational Efficiency,Database,Query,PostgreSQL,★,★★
72,Aggregate Function Performance,Optimize window functions and aggregations,@tony please analyze aggregate function execution performance. #recommend aggregation optimization and #visual aggregate usage patterns.,@tony,Operational Efficiency,Database,Optimization,PostgreSQL,★★,★★
73,Recursive Query Analysis,Monitor Common Table Expression performance,@tony please analyze recursive CTE execution plans and performance. #recommend CTE optimization and #alert for inefficient recursive queries.,@tony,Operational Efficiency,Database,Optimization,PostgreSQL,★,★★
74,Tablespace Usage Monitoring,Track tablespace storage allocation and usage,@tony please monitor tablespace utilization and growth patterns. #alert for tablespace space issues and #recommend storage rebalancing.,@tony,Cost Optimization,Database,Query,PostgreSQL,★★,★★
75,Error Code Pattern Analysis,Study database error patterns for proactive fixes,@tony please analyze error code frequencies and patterns. #recommend error prevention strategies and #visual error trend analysis.,@tony,Operational Efficiency,Database,Query,PostgreSQL,★,★★
76,Row Security Policy Review,Audit row-level security implementations,@tony please review row security policies and their performance impact. #recommend policy optimization and #alert for policy bypass attempts.,@tony,Security,Database,Query,PostgreSQL,★★,★★
77,Stored Procedure Optimization,Analyze stored procedure execution efficiency,@tony please review stored procedure performance metrics. #recommend procedure optimization and #visual execution time distributions.,@tony,Operational Efficiency,Database,Optimization,PostgreSQL,★★,★★
78,Backup Strategy Effectiveness,Evaluate backup performance and recovery time objectives,@tony please assess backup completion times and compression ratios. #recommend backup strategy improvements and #report RTO/RPO compliance.,@tony,Operational Efficiency,Database,Query,PostgreSQL,★★,★★
79,Database Connection Geography,Monitor connection sources and network latency impact,@tony please analyze connection geography and latency patterns. #recommend connection routing optimization and #visual connection distribution.,@tony,Operational Efficiency,Database,Query,PostgreSQL,★,★★
80,JSON/JSONB Query Optimization,Optimize JSON document queries and indexing,@tony please analyze JSON query performance and index usage. #recommend JSON indexing strategies and #visual JSON access patterns.,@tony,Operational Efficiency,Database,Optimization,PostgreSQL,★★,★★
81,Partition Constraint Validation,Verify partition constraints and data distribution,@tony please validate partition constraints and data distribution. #alert for constraint violations and #recommend partition strategy adjustments.,@tony,Operational Efficiency,Database,Query,PostgreSQL,★,★★
82,Query Execution Plan Stability,Monitor query plan changes and performance regression,@tony please track query execution plan stability over time. #alert for plan regression and #recommend plan hinting strategies.,@tony,Operational Efficiency,Database,Optimization,PostgreSQL,★★,★★
83,Database Schema Documentation,Maintain current schema documentation and comments,@tony please audit table and column comments for completeness. #report documentation gaps and #recommend documentation standards.,@tony,Operational Efficiency,Database,Query,PostgreSQL,★,★★
84,Temporal Data Management,Optimize time-series data queries and archival,@tony please analyze temporal data query patterns and performance. #recommend time-series optimization and #visual data age distribution.,@tony,Operational Efficiency,Database,Optimization,PostgreSQL,★★,★★
85,Extension Performance Impact,Measure third-party extension overhead,@tony please assess installed extension performance impact. #recommend extension optimization and #alert for resource-intensive extensions.,@tony,Operational Efficiency,Database,Query,PostgreSQL,★,★★
86,Database Transaction Patterns,Analyze transaction size and duration patterns,@tony please study transaction characteristics and commit patterns. #recommend transaction design improvements and #visual transaction metrics.,@tony,Operational Efficiency,Database,Query,PostgreSQL,★★,★★
87,Constraint Deferral Usage,Monitor deferred constraint validation patterns,@tony please analyze constraint deferral usage and performance. #recommend constraint timing optimization and #alert for validation bottlenecks.,@tony,Operational Efficiency,Database,Query,PostgreSQL,★,★★
88,Database Clustering Health,Monitor cluster node synchronization and performance,@tony please check cluster node health and synchronization status. #alert for cluster issues and #dashboard cluster performance metrics.,@tony,Operational Efficiency,Database,Query,PostgreSQL,★★,★★
89,Query Result Set Analysis,Optimize queries returning large result sets,@tony please identify queries with large result sets and memory usage. #recommend result set optimization and #visual result size distribution.,@tony,Operational Efficiency,Database,Optimization,PostgreSQL,★★,★★
90,Database Privilege Escalation,Detect unauthorized privilege modifications,@tony please monitor privilege changes and escalation attempts. #alert for suspicious privilege modifications and #report access control violations.,@tony,Security,Database,Query,PostgreSQL,★★,★★
91,Cursor Management Efficiency,Monitor cursor usage patterns and resource consumption,@tony please analyze cursor lifecycle and resource utilization. #recommend cursor optimization and #alert for unclosed cursors.,@tony,Operational Efficiency,Database,Query,PostgreSQL,★,★★
92,Database Object Statistics,Maintain comprehensive object usage statistics,@tony please collect database object usage statistics and trends. #dashboard object utilization metrics and #recommend optimization opportunities.,@tony,Operational Efficiency,Database,Query,PostgreSQL,★,★★
93,Array Data Type Performance,Optimize array column queries and operations,@tony please analyze array data type usage and query performance. #recommend array optimization strategies and #visual array operation patterns.,@tony,Operational Efficiency,Database,Optimization,PostgreSQL,★,★★
94,Database Maintenance Window,Optimize maintenance task scheduling and duration,@tony please analyze maintenance task performance and scheduling. #recommend maintenance window optimization and #visual maintenance impact trends.,@tony,Operational Efficiency,Database,Query,PostgreSQL,★★,★★
95,Query Classification System,Categorize queries by type and performance characteristics,@tony please classify queries by complexity and resource usage patterns. #visual query categories and #recommend optimization priorities.,@tony,Operational Efficiency,Database,Optimization,PostgreSQL,★,★★
96,Database Migration Validation,Verify data integrity after migrations,@tony please validate data consistency post-migration via checksums. #alert for data inconsistencies and #report migration success metrics.,@tony,Operational Efficiency,Database,Query,PostgreSQL,★★,★★
97,Connection String Security,Audit connection string configurations for security issues,@tony please review connection parameters and security settings. #alert for insecure configurations and #recommend security improvements.,@tony,Security,Database,Query,PostgreSQL,★★,★★
98,Database Performance Baselines,Establish and maintain performance benchmark baselines,@tony please create performance baselines for key database metrics. #dashboard baseline comparisons and #alert for performance deviations.,@tony,Operational Efficiency,Database,Query,PostgreSQL,★★,★★
99,Query Complexity Analysis,Assess query complexity and maintainability,@tony please analyze query complexity metrics and readability. #recommend query simplification and #visual complexity distribution trends.,@tony,Operational Efficiency,Database,Optimization,PostgreSQL,★,★★
100,Database Disaster Recovery,Test disaster recovery procedures and data consistency,@tony please validate disaster recovery scripts and data integrity checks. #report DR readiness status and #recommend recovery time improvements.,@tony,Operational Efficiency,Database,Query,PostgreSQL,★★,★★★
1,Pod Resource Optimization,Identify pods with excessive CPU/memory requests vs actual usage to optimize resource allocation and reduce costs.,@kai please analyze pod resource utilization across namespaces. #recommend rightsizing for pods with >50% unused resources and #alert on wasteful allocations.,@kai,Cost Optimization,Kubernetes,Compute,Pods,★★★,★★★
2,Failed Pod Investigation,"Investigate and resolve pods stuck in CrashLoopBackOff, Pending, or Error states to restore service availability.",@kai please identify all failed pods and their root causes. #recommend remediation steps and #alert on critical service impacts.,@kai,Operational Efficiency,Kubernetes,Compute,Pods,★★★,★★★
3,Node Resource Monitoring,Monitor node resource pressure and identify nodes approaching capacity limits to prevent outages.,@kai please check node resource utilization and identify nodes >80% capacity. #alert on resource pressure and #recommend scaling actions.,@kai,Scalability,Kubernetes,Compute,Nodes,★★★,★★★
4,HPA Configuration Audit,Review and optimize Horizontal Pod Autoscaler configurations to ensure proper scaling behavior and cost efficiency.,@kai please audit HPA configurations for optimal scaling thresholds. #recommend improvements for better performance and #alert on misconfigured HPAs.,@kai,Scalability,Kubernetes,Compute,HPA,★★★,★★★
5,Persistent Volume Management,Identify orphaned PVs and optimize storage usage to reduce costs and improve storage efficiency.,@kai please find unbound PVs and analyze storage utilization. #recommend cleanup actions and #alert on storage waste.,@kai,Cost Optimization,Kubernetes,Storage,PersistentVolumes,★★★,★★★
6,Security Policy Compliance,"Audit pods for security best practices including non-root users, resource limits, and security contexts.",@kai please scan pods for security violations. #alert on non-compliant pods and #recommend security hardening measures.,@kai,Security,Kubernetes,Compute,SecurityContext,★★★,★★★
7,Service Discovery Issues,Troubleshoot service connectivity problems and DNS resolution issues affecting application communication.,@kai please diagnose service connectivity failures and DNS issues. #recommend fixes and #alert on service disruptions.,@kai,Operational Efficiency,Kubernetes,Network,Services,★★★,★★★
8,ConfigMap/Secret Management,Audit and optimize ConfigMaps and Secrets usage to improve security and reduce configuration drift.,@kai please review ConfigMaps and Secrets for best practices. #recommend security improvements and #alert on misconfigurations.,@kai,Security,Kubernetes,Compute,ConfigMaps,★★★,★★★
9,Namespace Resource Quotas,Monitor and optimize namespace resource quotas to prevent resource contention and ensure fair allocation.,@kai please check namespace quota utilization and limits. #recommend quota adjustments and #alert on quota violations.,@kai,Operational Efficiency,Kubernetes,Compute,ResourceQuotas,★★★,★★★
10,Deployment Health Check,Monitor deployment rollout status and identify stuck or failed deployments requiring intervention.,@kai please check deployment status across all namespaces. #alert on failed rollouts and #recommend recovery actions.,@kai,Operational Efficiency,Kubernetes,Compute,Deployments,★★★,★★★
11,Pod Restart Analysis,Investigate frequent pod restarts to identify underlying issues and improve application stability.,@kai please identify pods with high restart counts. #recommend stability improvements and #alert on problematic pods.,@kai,Operational Efficiency,Kubernetes,Compute,Pods,★★★,★★★
12,Network Policy Audit,Review network policies to ensure proper micro-segmentation and security isolation between services.,@kai please audit network policies for security gaps. #recommend policy improvements and #alert on insecure configurations.,@kai,Security,Kubernetes,Network,NetworkPolicies,★★★,★★★
13,StatefulSet Monitoring,Monitor StatefulSet health and ensure proper persistent storage and ordering guarantees.,@kai please check StatefulSet status and persistent volume bindings. #alert on issues and #recommend maintenance actions.,@kai,Operational Efficiency,Kubernetes,Compute,StatefulSets,★★★,★★★
14,Node Taints and Tolerations,"Optimize node scheduling by reviewing taints, tolerations, and node affinity rules for efficient resource usage.",@kai please analyze node taints and pod scheduling patterns. #recommend optimization and #alert on scheduling issues.,@kai,Operational Efficiency,Kubernetes,Compute,Scheduling,★★★,★★☆
15,Ingress Configuration Review,Audit ingress controllers and rules to ensure optimal traffic routing and SSL/TLS security.,@kai please review ingress configurations for best practices. #recommend improvements and #alert on security issues.,@kai,Security,Kubernetes,Network,Ingress,★★★,★★★
16,Resource Limit Enforcement,Ensure all containers have appropriate resource limits to prevent noisy neighbor issues.,@kai please identify containers without resource limits. #recommend limit settings and #alert on unlimited containers.,@kai,Operational Efficiency,Kubernetes,Compute,Resources,★★★,★★★
17,DaemonSet Health Monitoring,Monitor DaemonSet deployments to ensure critical system services run on all required nodes.,@kai please check DaemonSet pod distribution and health. #alert on missing pods and #recommend remediation.,@kai,Operational Efficiency,Kubernetes,Compute,DaemonSets,★★★,★★★
18,Pod Anti-Affinity Check,Review pod anti-affinity rules to ensure high availability and proper distribution across failure domains.,@kai please audit pod anti-affinity configurations. #recommend HA improvements and #alert on single points of failure.,@kai,Scalability,Kubernetes,Compute,Affinity,★★★,★★★
19,RBAC Policy Review,Audit Role-Based Access Control policies to ensure least privilege access and security compliance.,@kai please review RBAC policies for over-privileged access. #recommend security improvements and #alert on violations.,@kai,Security,Kubernetes,Compute,RBAC,★★★,★★★
20,Liveness Probe Configuration,Ensure all pods have appropriate liveness and readiness probes for reliable health checking.,@kai please identify pods missing health probes. #recommend probe configurations and #alert on unhealthy pods.,@kai,Operational Efficiency,Kubernetes,Compute,Probes,★★★,★★★
21,Storage Class Optimization,Review storage class usage and optimize for performance and cost based on workload requirements.,@kai please analyze storage class utilization and performance. #recommend optimizations and #alert on inefficiencies.,@kai,Cost Optimization,Kubernetes,Storage,StorageClasses,★★★,★★☆
22,Job and CronJob Monitoring,Monitor batch jobs for completion status and optimize scheduling for resource efficiency.,@kai please check Job and CronJob execution status. #alert on failures and #recommend scheduling improvements.,@kai,Operational Efficiency,Kubernetes,Compute,Jobs,★★★,★★☆
23,Pod Disruption Budget Review,Ensure PDBs are configured to maintain availability during voluntary disruptions like updates.,@kai please audit Pod Disruption Budget configurations. #recommend availability improvements and #alert on missing PDBs.,@kai,Scalability,Kubernetes,Compute,PodDisruptionBudgets,★★★,★★★
24,Container Image Security Scan,Identify containers using outdated or vulnerable images that need security updates.,@kai please scan container images for security vulnerabilities. #alert on high-risk images and #recommend updates.,@kai,Security,Kubernetes,Compute,Images,★★★,★★★
25,Cluster Resource Utilization,Analyze overall cluster resource utilization to identify optimization opportunities and capacity planning needs.,@kai please analyze cluster-wide resource utilization trends. #recommend capacity adjustments and #alert on utilization anomalies.,@kai,Cost Optimization,Kubernetes,Compute,Cluster,★★★,★★★
26,Service Mesh Integration,Monitor service mesh sidecar health and traffic patterns for microservices communication optimization.,@kai please check service mesh sidecar status and traffic metrics. #recommend optimizations and #alert on communication issues.,@kai,Operational Efficiency,Kubernetes,Network,ServiceMesh,★★☆,★★★
27,Prometheus Metrics Collection,Ensure proper metrics collection by monitoring Prometheus targets and scrape configurations.,@kai please verify Prometheus target health and metrics collection. #alert on missing metrics and #recommend monitoring improvements.,@kai,Operational Efficiency,Kubernetes,Compute,Monitoring,★★★,★★☆
28,Pod Security Standards,Implement and audit Pod Security Standards to enforce security policies across namespaces.,@kai please audit pod security standard compliance. #recommend policy enforcement and #alert on violations.,@kai,Security,Kubernetes,Compute,PodSecurity,★★★,★★★
29,Custom Resource Management,Monitor and manage Custom Resource Definitions and their associated controllers for proper operation.,@kai please check CRD status and controller health. #alert on operator issues and #recommend maintenance actions.,@kai,Operational Efficiency,Kubernetes,Compute,CustomResources,★★☆,★★☆
30,Node Drain and Maintenance,Plan and execute node maintenance operations with minimal service disruption.,@kai please plan node maintenance schedule and drain procedures. #recommend maintenance windows and #alert on potential impacts.,@kai,Operational Efficiency,Kubernetes,Compute,NodeMaintenance,★★★,★★☆
31,Pod CPU Throttling Analysis,Identify pods experiencing CPU throttling and optimize resource allocation for better performance.,@kai please find pods with high CPU throttling rates. #recommend resource adjustments and #alert on performance issues.,@kai,Operational Efficiency,Kubernetes,Compute,Performance,★★★,★★★
32,Cluster Autoscaler Review,Monitor and optimize cluster autoscaler configuration for efficient node scaling and cost management.,@kai please review cluster autoscaler metrics and configuration. #recommend scaling optimizations and #alert on scaling issues.,@kai,Scalability,Kubernetes,Compute,ClusterAutoscaler,★★★,★★★
33,Event Log Analysis,"Analyze Kubernetes events to identify patterns, warnings, and errors requiring attention.",@kai please analyze recent cluster events for issues. #alert on critical events and #recommend preventive actions.,@kai,Operational Efficiency,Kubernetes,Compute,Events,★★★,★★☆
34,Load Balancer Optimization,Review LoadBalancer services for cost optimization and performance improvements.,@kai please audit LoadBalancer service configurations. #recommend cost optimizations and #alert on inefficient setups.,@kai,Cost Optimization,Kubernetes,Network,LoadBalancers,★★☆,★★☆
35,Multi-Zone Pod Distribution,Ensure critical workloads are distributed across availability zones for high availability.,@kai please check pod distribution across zones. #recommend HA improvements and #alert on zone concentration risks.,@kai,Scalability,Kubernetes,Compute,HighAvailability,★★★,★★★
36,Container Runtime Security,Monitor container runtime security settings and identify potential security risks.,@kai please audit container runtime configurations. #alert on security risks and #recommend hardening measures.,@kai,Security,Kubernetes,Compute,Runtime,★★☆,★★★
37,Volume Mount Security Review,Audit volume mounts for security vulnerabilities and unnecessary host access.,@kai please review volume mount configurations. #alert on security risks and #recommend safer alternatives.,@kai,Security,Kubernetes,Storage,VolumeMounts,★★★,★★★
38,Replica Set Optimization,Identify orphaned or inefficient ReplicaSets that need cleanup or optimization.,@kai please find unused or problematic ReplicaSets. #recommend cleanup actions and #alert on resource waste.,@kai,Cost Optimization,Kubernetes,Compute,ReplicaSets,★★☆,★★☆
39,Kubernetes API Usage Audit,Monitor API server usage patterns and identify potential performance bottlenecks.,@kai please analyze API server request patterns and performance. #recommend optimizations and #alert on high usage.,@kai,Operational Efficiency,Kubernetes,Compute,APIServer,★★☆,★★☆
40,Pod Scheduling Efficiency,Analyze pod scheduling patterns and optimize for better resource utilization.,@kai please review pod scheduling efficiency and node utilization. #recommend scheduling improvements and #alert on inefficiencies.,@kai,Operational Efficiency,Kubernetes,Compute,Scheduling,★★★,★★☆
41,Secret Rotation Monitoring,Track secret rotation schedules and ensure timely updates for security compliance.,@kai please monitor secret age and rotation schedules. #alert on expired secrets and #recommend rotation procedures.,@kai,Security,Kubernetes,Compute,SecretRotation,★★★,★★★
42,Container Log Management,Optimize container logging configuration to balance observability with resource usage.,@kai please review container logging configurations and volume. #recommend log optimization and #alert on excessive logging.,@kai,Operational Efficiency,Kubernetes,Compute,Logging,★★☆,★★☆
43,Priority Class Usage,Review and optimize pod priority class assignments for better resource scheduling.,@kai please audit priority class usage across workloads. #recommend priority optimizations and #alert on misconfigurations.,@kai,Operational Efficiency,Kubernetes,Compute,PriorityClasses,★★☆,★★☆
44,Admission Controller Review,Audit admission controllers for proper policy enforcement and security compliance.,@kai please review admission controller configurations. #recommend policy improvements and #alert on security gaps.,@kai,Security,Kubernetes,Compute,AdmissionControllers,★★☆,★★★
45,Service Account Security,Audit service account usage and permissions to ensure least privilege access.,@kai please review service account permissions and usage. #recommend security improvements and #alert on over-privileged accounts.,@kai,Security,Kubernetes,Compute,ServiceAccounts,★★★,★★★
46,Endpoint Health Monitoring,Monitor service endpoints for availability and health to ensure reliable service discovery.,@kai please check endpoint health across all services. #alert on unhealthy endpoints and #recommend service improvements.,@kai,Operational Efficiency,Kubernetes,Network,Endpoints,★★★,★★★
47,Container Image Lifecycle,Manage container image lifecycle including cleanup of unused images to optimize storage.,@kai please identify unused container images for cleanup. #recommend image lifecycle policies and #alert on storage waste.,@kai,Cost Optimization,Kubernetes,Storage,ImageManagement,★★☆,★★☆
48,Node Affinity Optimization,Review node affinity rules to ensure optimal workload placement and resource utilization.,@kai please audit node affinity configurations. #recommend placement optimizations and #alert on scheduling conflicts.,@kai,Operational Efficiency,Kubernetes,Compute,NodeAffinity,★★☆,★★☆
49,Quality of Service Classes,Ensure proper QoS class assignments for predictable resource allocation and scheduling.,@kai please review pod QoS class assignments. #recommend QoS optimizations and #alert on resource conflicts.,@kai,Operational Efficiency,Kubernetes,Compute,QoSClasses,★★☆,★★☆
50,Namespace Isolation Review,Audit namespace isolation and security boundaries to prevent unauthorized access.,@kai please review namespace security configurations. #recommend isolation improvements and #alert on security gaps.,@kai,Security,Kubernetes,Compute,NamespaceIsolation,★★★,★★★
51,Container Registry Security,Monitor container registry access and ensure secure image distribution practices.,@kai please audit container registry access patterns. #alert on security risks and #recommend access improvements.,@kai,Security,Kubernetes,Compute,Registry,★★☆,★★★
52,Horizontal Pod Autoscaler Metrics,Monitor HPA custom metrics and ensure accurate scaling based on application-specific indicators.,@kai please review HPA custom metrics configuration. #recommend metric improvements and #alert on scaling issues.,@kai,Scalability,Kubernetes,Compute,HPAMetrics,★★☆,★★☆
53,Pod Security Context Audit,Ensure all pods use appropriate security contexts to minimize privilege escalation risks.,@kai please audit pod security context configurations. #alert on insecure contexts and #recommend security hardening.,@kai,Security,Kubernetes,Compute,SecurityContext,★★★,★★★
54,Vertical Pod Autoscaler Review,Monitor VPA recommendations and implement resource optimization suggestions.,@kai please review VPA recommendations for resource optimization. #recommend implementation and #alert on resource waste.,@kai,Cost Optimization,Kubernetes,Compute,VPA,★★☆,★★☆
55,Init Container Monitoring,Monitor init container execution and optimize initialization processes for faster pod startup.,@kai please check init container performance and failures. #recommend optimizations and #alert on startup delays.,@kai,Operational Efficiency,Kubernetes,Compute,InitContainers,★★☆,★★☆
56,Pod Anti-Pattern Detection,Identify pods following anti-patterns that could impact performance or security.,@kai please scan for pod anti-patterns and bad practices. #recommend improvements and #alert on problematic configurations.,@kai,Operational Efficiency,Kubernetes,Compute,BestPractices,★★☆,★★☆
57,Topology Spread Constraints,Review topology spread constraints to ensure even workload distribution for availability.,@kai please audit topology spread constraint configurations. #recommend distribution improvements and #alert on uneven spreads.,@kai,Scalability,Kubernetes,Compute,TopologySpread,★★☆,★★☆
58,Sidecar Container Optimization,Optimize sidecar container resource usage and communication patterns for efficiency.,@kai please analyze sidecar container resource usage. #recommend optimizations and #alert on resource inefficiencies.,@kai,Cost Optimization,Kubernetes,Compute,Sidecars,★★☆,★★☆
59,Pod Lifecycle Hook Review,Ensure proper pod lifecycle hooks for graceful startup and shutdown procedures.,@kai please review pod lifecycle hook configurations. #recommend hook improvements and #alert on graceful shutdown issues.,@kai,Operational Efficiency,Kubernetes,Compute,LifecycleHooks,★★☆,★★☆
60,Resource Quota Enforcement,Monitor resource quota usage and ensure fair resource allocation across namespaces.,@kai please check resource quota utilization and enforcement. #alert on quota violations and #recommend adjustments.,@kai,Operational Efficiency,Kubernetes,Compute,ResourceQuotas,★★★,★★☆
61,Network Plugin Performance,Monitor network plugin performance and troubleshoot connectivity issues.,@kai please analyze network plugin metrics and performance. #recommend optimizations and #alert on network issues.,@kai,Operational Efficiency,Kubernetes,Network,NetworkPlugins,★★☆,★★★
62,Pod Overhead Analysis,Identify and optimize pod overhead from sidecars and system containers.,@kai please calculate pod overhead and identify optimization opportunities. #recommend overhead reduction and #alert on excessive overhead.,@kai,Cost Optimization,Kubernetes,Compute,PodOverhead,★★☆,★★☆
63,Label and Annotation Management,Audit and standardize labels and annotations across resources for better management.,@kai please review label and annotation consistency. #recommend standardization and #alert on missing labels.,@kai,Operational Efficiency,Kubernetes,Compute,Labels,★★☆,★★☆
64,Container Resource Limits,Ensure appropriate resource limits prevent resource exhaustion and improve stability.,@kai please audit container resource limit configurations. #recommend limit adjustments and #alert on missing limits.,@kai,Operational Efficiency,Kubernetes,Compute,ResourceLimits,★★★,★★★
65,Pod Termination Grace Period,Optimize pod termination grace periods for faster scaling and resource reclamation.,@kai please review pod termination grace period settings. #recommend optimizations and #alert on excessive grace periods.,@kai,Operational Efficiency,Kubernetes,Compute,GracePeriod,★★☆,★★☆
66,Kubernetes Upgrade Readiness,Assess cluster readiness for Kubernetes version upgrades and identify compatibility issues.,@kai please evaluate cluster upgrade readiness and compatibility. #recommend upgrade preparations and #alert on blockers.,@kai,Operational Efficiency,Kubernetes,Compute,Upgrades,★★☆,★★☆
67,Workload Identity Security,Implement and audit workload identity configurations for secure service authentication.,@kai please review workload identity implementations. #recommend security improvements and #alert on misconfigurations.,@kai,Security,Kubernetes,Compute,WorkloadIdentity,★★☆,★★★
68,Pod Communication Patterns,Analyze inter-pod communication patterns to optimize network performance and security.,@kai please analyze pod-to-pod communication patterns. #recommend network optimizations and #alert on inefficient communications.,@kai,Operational Efficiency,Kubernetes,Network,Communication,★★☆,★★☆
69,Cluster DNS Performance,Monitor cluster DNS performance and resolve DNS resolution bottlenecks.,@kai please analyze cluster DNS performance metrics. #recommend DNS optimizations and #alert on resolution issues.,@kai,Operational Efficiency,Kubernetes,Network,DNS,★★★,★★★
70,Pod Startup Time Optimization,Identify pods with slow startup times and optimize initialization processes.,@kai please analyze pod startup time metrics. #recommend startup optimizations and #alert on slow-starting pods.,@kai,Operational Efficiency,Kubernetes,Compute,StartupTime,★★☆,★★☆
71,Multi-Tenancy Security,Implement and audit multi-tenancy security measures to ensure proper isolation.,@kai please review multi-tenancy security configurations. #recommend isolation improvements and #alert on security gaps.,@kai,Security,Kubernetes,Compute,MultiTenancy,★★☆,★★★
72,Container Runtime Metrics,Monitor container runtime performance metrics and identify optimization opportunities.,@kai please analyze container runtime performance data. #recommend runtime optimizations and #alert on performance issues.,@kai,Operational Efficiency,Kubernetes,Compute,Runtime,★★☆,★★☆
73,Pod Preemption Analysis,Monitor pod preemption events and optimize priority scheduling for better resource allocation.,@kai please analyze pod preemption patterns. #recommend priority adjustments and #alert on frequent preemptions.,@kai,Operational Efficiency,Kubernetes,Compute,Preemption,★★☆,★★☆
74,Storage Performance Tuning,Optimize persistent volume performance based on workload requirements and usage patterns.,@kai please analyze storage performance metrics. #recommend storage optimizations and #alert on performance bottlenecks.,@kai,Operational Efficiency,Kubernetes,Storage,Performance,★★☆,★★☆
75,Kubectl Security Audit,Audit kubectl access patterns and ensure secure cluster administration practices.,@kai please review kubectl usage patterns and security. #recommend access improvements and #alert on security risks.,@kai,Security,Kubernetes,Compute,KubectlSecurity,★★☆,★★★
76,Pod Network Policy Testing,Test and validate network policy effectiveness through controlled network isolation tests.,@kai please test network policy implementations. #recommend policy improvements and #alert on policy gaps.,@kai,Security,Kubernetes,Network,PolicyTesting,★★☆,★★★
77,Container Health Check Optimization,Optimize health check configurations for accurate health detection without resource overhead.,@kai please review health check configurations and performance. #recommend optimizations and #alert on check failures.,@kai,Operational Efficiency,Kubernetes,Compute,HealthChecks,★★☆,★★☆
78,Kubernetes ETCD Monitoring,Monitor ETCD cluster health and performance for reliable cluster state management.,@kai please check ETCD cluster health and performance metrics. #alert on ETCD issues and #recommend maintenance actions.,@kai,Operational Efficiency,Kubernetes,Storage,ETCD,★★☆,★★★
79,Pod Security Policy Migration,Migrate from deprecated Pod Security Policies to Pod Security Standards.,@kai please plan PSP to PSS migration strategy. #recommend migration steps and #alert on deprecated policy usage.,@kai,Security,Kubernetes,Compute,PolicyMigration,★★☆,★★★
80,Cluster Resource Fragmentation,Identify and resolve resource fragmentation issues affecting pod scheduling efficiency.,@kai please analyze cluster resource fragmentation. #recommend defragmentation actions and #alert on scheduling issues.,@kai,Operational Efficiency,Kubernetes,Compute,Fragmentation,★★☆,★★☆
81,Container Image Pull Optimization,Optimize container image pull performance and implement image caching strategies.,@kai please analyze image pull times and caching effectiveness. #recommend pull optimizations and #alert on slow pulls.,@kai,Operational Efficiency,Kubernetes,Compute,ImagePull,★★☆,★★☆
82,Pod Scheduling Constraints,Review pod scheduling constraints and ensure optimal resource allocation across nodes.,@kai please audit pod scheduling constraint configurations. #recommend constraint optimizations and #alert on scheduling conflicts.,@kai,Operational Efficiency,Kubernetes,Compute,Constraints,★★☆,★★☆
83,Kubernetes Feature Gate Management,Manage feature gates across cluster components for optimal feature adoption and stability.,@kai please review feature gate configurations. #recommend feature adoption strategy and #alert on deprecated features.,@kai,Operational Efficiency,Kubernetes,Compute,FeatureGates,★☆☆,★★☆
84,Multi-Architecture Support,Ensure proper multi-architecture support for diverse node types and workload requirements.,@kai please audit multi-architecture deployment support. #recommend architecture optimizations and #alert on compatibility issues.,@kai,Operational Efficiency,Kubernetes,Compute,MultiArch,★☆☆,★★☆
85,Pod Disruption Budget Testing,Test Pod Disruption Budget effectiveness during simulated disruption scenarios.,@kai please test PDB effectiveness during disruptions. #recommend PDB improvements and #alert on availability risks.,@kai,Scalability,Kubernetes,Compute,PDBTesting,★★☆,★★☆
86,Container Registry Cleanup,Clean up unused container images and optimize registry storage usage.,@kai please identify unused registry images for cleanup. #recommend cleanup procedures and #alert on storage waste.,@kai,Cost Optimization,Kubernetes,Storage,RegistryCleanup,★★☆,★★☆
87,Kubernetes Garbage Collection,Monitor and optimize Kubernetes garbage collection processes for efficient resource cleanup.,@kai please review garbage collection efficiency. #recommend GC optimizations and #alert on cleanup issues.,@kai,Operational Efficiency,Kubernetes,Compute,GarbageCollection,★★☆,★☆☆
88,Pod Resource Quotas by Team,Implement team-based resource quotas for fair resource allocation and cost accountability.,@kai please implement team-based resource quotas. #recommend quota strategies and #alert on team quota violations.,@kai,Cost Optimization,Kubernetes,Compute,TeamQuotas,★★☆,★★☆
89,Service Mesh Performance,Monitor service mesh sidecar performance impact and optimize mesh configurations.,@kai please analyze service mesh performance overhead. #recommend mesh optimizations and #alert on performance degradation.,@kai,Operational Efficiency,Kubernetes,Network,ServiceMeshPerf,★☆☆,★★☆
90,Kubernetes Audit Log Analysis,Analyze audit logs for security incidents and compliance monitoring.,@kai please review audit log patterns for security insights. #recommend audit improvements and #alert on suspicious activities.,@kai,Security,Kubernetes,Compute,AuditLogs,★★☆,★★☆
91,Pod Environment Variable Security,Audit environment variables for sensitive data exposure and security risks.,@kai please scan environment variables for security risks. #alert on exposed secrets and #recommend secure alternatives.,@kai,Security,Kubernetes,Compute,EnvVarSecurity,★★☆,★★☆
92,Kubernetes Finalizer Management,Monitor and manage resource finalizers to prevent stuck resource deletion.,@kai please check for stuck finalizers blocking resource deletion. #recommend finalizer cleanup and #alert on deletion issues.,@kai,Operational Efficiency,Kubernetes,Compute,Finalizers,★★☆,★☆☆
93,Container Startup Probe Tuning,Optimize startup probe configurations for faster application readiness detection.,@kai please tune startup probe settings for optimal performance. #recommend probe optimizations and #alert on startup failures.,@kai,Operational Efficiency,Kubernetes,Compute,StartupProbes,★☆☆,★★☆
94,Pod CPU and Memory Bursting,Monitor and optimize CPU and memory bursting patterns for better resource efficiency.,@kai please analyze pod bursting patterns and limits. #recommend bursting optimizations and #alert on excessive bursting.,@kai,Cost Optimization,Kubernetes,Compute,ResourceBursting,★★☆,★★☆
95,Kubernetes Admission Webhook Security,Audit admission webhook configurations for security and performance optimization.,@kai please review admission webhook security configurations. #recommend webhook improvements and #alert on security risks.,@kai,Security,Kubernetes,Compute,AdmissionWebhooks,★☆☆,★★☆
96,Pod Annotation Standardization,Standardize pod annotations for better observability and automation integration.,@kai please standardize pod annotation usage across namespaces. #recommend annotation standards and #alert on inconsistencies.,@kai,Operational Efficiency,Kubernetes,Compute,Annotations,★☆☆,★☆☆
97,Container Image Vulnerability Scanning,Implement continuous vulnerability scanning for container images in production.,@kai please scan production images for vulnerabilities. #alert on high-risk vulnerabilities and #recommend image updates.,@kai,Security,Kubernetes,Compute,VulnerabilityScanning,★★☆,★★★
98,Kubernetes Certificate Management,Monitor and manage cluster certificates to prevent expiration and maintain security.,@kai please check certificate expiration dates. #alert on expiring certificates and #recommend renewal procedures.,@kai,Security,Kubernetes,Compute,CertificateManagement,★★☆,★★★
99,Pod Toleration Optimization,Optimize pod toleration configurations for efficient scheduling and node utilization.,@kai please review pod toleration settings. #recommend toleration optimizations and #alert on scheduling inefficiencies.,@kai,Operational Efficiency,Kubernetes,Compute,Tolerations,★☆☆,★☆☆
100,Kubernetes Resource Lock Management,Monitor and manage resource locks to prevent deadlocks and improve system reliability.,@kai please check for resource locks and deadlock conditions. #recommend lock optimizations and #alert on deadlock risks.,@kai,Operational Efficiency,Kubernetes,Compute,ResourceLocks,★☆☆,★★☆