# syntax=docker/dockerfile:1
# Build with: DOCKER_BUILDKIT=1 docker build .
FROM ubuntu:24.04

# Set non-interactive frontend and install base packages in one layer
ENV DEBIAN_FRONTEND=noninteractive
RUN apt-get update && apt-get upgrade -y && \
    apt-get install -y software-properties-common && \
    add-apt-repository ppa:deadsnakes/ppa && \
    apt-get update && \
    apt-get install -y \
        python3.12 \
        python3.12-venv \
        python3-pip \
        curl \
        jq \
        bubblewrap \
        unzip \
        apt-transport-https \
        ca-certificates \
        gnupg \
        lsb-release \
        build-essential \
        git \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# Set Python 3.12 as the default python version
RUN update-alternatives --install /usr/bin/python python /usr/bin/python3.12 1 && \
    update-alternatives --install /usr/bin/python3 python3 /usr/bin/python3.12 1

# Install Cloud CLIs in one layer
RUN ARCH=$(uname -m) && \
    # Install AWS CLI
    if [ "$ARCH" = "x86_64" ]; then \
        curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"; \
    elif [ "$ARCH" = "aarch64" ]; then \
        curl "https://awscli.amazonaws.com/awscli-exe-linux-aarch64.zip" -o "awscliv2.zip"; \
    else \
        echo "Unsupported architecture: $ARCH" && exit 1; \
    fi && \
    unzip awscliv2.zip && \
    ./aws/install && \
    rm -rf aws awscliv2.zip && \
    # Install GCP CLI
    echo "deb [signed-by=/usr/share/keyrings/cloud.google.gpg] https://packages.cloud.google.com/apt cloud-sdk main" \
      | tee /etc/apt/sources.list.d/google-cloud-sdk.list && \
    curl https://packages.cloud.google.com/apt/doc/apt-key.gpg \
      | gpg --dearmor -o /usr/share/keyrings/cloud.google.gpg && \
    # Install Azure CLI
    mkdir -p /etc/apt/keyrings && \
    curl -sLS https://packages.microsoft.com/keys/microsoft.asc | \
    gpg --dearmor | tee /etc/apt/keyrings/microsoft.gpg > /dev/null && \
    chmod go+r /etc/apt/keyrings/microsoft.gpg && \
    AZ_DIST=$(lsb_release -cs) && \
    echo "Types: deb\n\
URIs: https://packages.microsoft.com/repos/azure-cli/\n\
Suites: ${AZ_DIST}\n\
Components: main\n\
Architectures: $(dpkg --print-architecture)\n\
Signed-by: /etc/apt/keyrings/microsoft.gpg" | tee /etc/apt/sources.list.d/azure-cli.sources && \
    # Update and install both GCP and Azure CLI
    apt-get update && \
    apt-get install -y google-cloud-cli azure-cli && \
    apt-get clean && rm -rf /var/lib/apt/lists/*

# Install kubectl
RUN ARCH=$(uname -m) && \
    if [ "$ARCH" = "x86_64" ]; then \
        curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"; \
    elif [ "$ARCH" = "aarch64" ]; then \
        curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/arm64/kubectl"; \
    else \
        echo "Unsupported architecture: $ARCH" && exit 1; \
    fi && \
    chmod +x kubectl && \
    mv kubectl /usr/local/bin/kubectl

# Install Node.js (20.x LTS) and enable Corepack (Yarn/pnpm)
RUN curl -fsSL https://deb.nodesource.com/setup_20.x | bash - && \
    apt-get update && apt-get install -y nodejs && \
    corepack enable && \
    apt-get clean && rm -rf /var/lib/apt/lists/*

# Install common command-line utilities
RUN apt-get update && apt-get install -y \
    # Common utilities
    bc \
    # Text processing & search (some are pre-installed but ensuring availability)
    grep \
    sed \
    gawk \
    tree \
    rsync \
    # Network tools
    wget \
    netcat-openbsd \
    nmap \
    dnsutils \
    iputils-ping \
    traceroute \
    # File operations
    findutils \
    tar \
    # JSON/Data processing
    xmlstarlet \
    # Log analysis & monitoring
    logrotate \
    multitail \
    # Compression & archives
    p7zip-full \
    && apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Install Rust and essential CLI tools (optimized layer)
RUN curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y && \
    . /root/.cargo/env && \
    # Install only essential modern CLI tools
    cargo install ripgrep fd-find bat && \
    # Install fzf
    git clone --depth 1 https://github.com/junegunn/fzf.git /opt/fzf && \
    /opt/fzf/install --bin && \
    ln -s /opt/fzf/bin/fzf /usr/local/bin/fzf && \
    # Install yq
    ARCH=$(uname -m) && \
    if [ "$ARCH" = "x86_64" ]; then \
        curl -LO https://github.com/mikefarah/yq/releases/download/v4.40.5/yq_linux_amd64 && \
        chmod +x yq_linux_amd64 && \
        mv yq_linux_amd64 /usr/local/bin/yq; \
    elif [ "$ARCH" = "aarch64" ]; then \
        curl -LO https://github.com/mikefarah/yq/releases/download/v4.40.5/yq_linux_arm64 && \
        chmod +x yq_linux_arm64 && \
        mv yq_linux_arm64 /usr/local/bin/yq; \
    fi

ENV PATH="/root/.cargo/bin:/opt/fzf/bin:${PATH}"

# Install remaining networking tools
RUN apt-get update && apt-get install -y \
    iproute2 \
    iptables \
    netfilter-persistent \
    net-tools \
    && apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Enable IP forwarding for network namespace routing
RUN echo "net.ipv4.ip_forward=1" >> /etc/sysctl.conf

# Copy entrypoint script for network configuration
COPY entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh


WORKDIR /app/

# Install uv
COPY --from=ghcr.io/astral-sh/uv:0.4.15 /uv /bin/uv

# Place executables in the environment at the front of the path
ENV PATH="/app/.venv/bin:$PATH"

# Compile bytecode
ENV UV_COMPILE_BYTECODE=1

# uv Cache
ENV UV_LINK_MODE=copy

ENV PYTHONPATH=/app

# Copy dependency files first for better caching
COPY ./pyproject.toml ./uv.lock /app/

# Install dependencies (this layer will be cached unless dependencies change)
RUN --mount=type=cache,target=/root/.cache/uv \
    uv sync --no-dev

# Copy application code last (most frequently changing)
COPY ./app /app/app

# Pre-install Slidev dependencies in template to avoid slow installs during runtime
RUN cd /app/app/templates/slidev && \
    echo 'Y' | pnpm install && \
    echo "Pre-installed Slidev dependencies in template"

# Set entrypoint to configure networking, then start the application
ENTRYPOINT ["/entrypoint.sh"]
CMD ["fastapi", "run", "--workers", "1", "app/main.py"]
