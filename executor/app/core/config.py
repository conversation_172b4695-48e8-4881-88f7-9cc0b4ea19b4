import uuid

from pydantic import BaseModel
from pydantic_settings import BaseSettings, SettingsConfigDict

from app.schemas import CloudProvider


class Settings(BaseSettings):
    model_config = SettingsConfigDict(
        env_file="../.env",
        env_ignore_empty=True,
        extra="ignore",
    )
    # Application settings
    PROJECT_NAME: str = "executor"
    API_V1_STR: str = "/api/v1"

    # Sandbox settings
    # Global root for all workspace data (persistent volume mount point)
    # Each workspace will be mounted as a subdirectory under this path {SANDBOX_DIR}/{workspace_id}
    SANDBOX_DIR: str = "/tmp/sandbox"
    SANDBOX_WORKSPACE_SPARSE_IMAGE: str = "sandbox.img"
    SANDBOX_WORKSPACE_MOUNT_POINT: str = "workspace"

    # Absolute path inside the sandbox (bwrap mount target) that represents
    # the user's HOME directory
    SANDBOX_VIRTUAL_HOME: str = "/home/<USER>"
    SANDBOX_CREDENTIALS_SUBDIR: str = ".credentials"
    SANDBOX_MAX_DISK_USAGE_MB: int = 10240  # 10 GB

    # Workspace manager type: "loop" for loop devices, "tmpfs" for high concurrency, "size" for directory size limits
    WORKSPACE_MANAGER_TYPE: str = "size"

    SANDBOX_MAX_RUNTIME: int = 60  # Seconds

    # Network configuration (SANDBOX - blocked by iptables)
    NETWORK_PREFIX: str = "sandbox"
    NETWORK_BASE_IP_RANGE: str = "********"
    NETWORK_MASK: int = 24
    NETWORK_SUBNET_MASK: int = 30
    NETWORK_NAMESPACE_TIMEOUT: int = (
        60  # Seconds before cleaning up inactive namespaces
    )

    # Web app network configuration (for apps that need inter-container communication)
    # Uses 10.0.0.0/24 range which is NOT blocked by iptables rules
    WEBAPP_NETWORK_BASE_IP_RANGE: str = "10.0.0.0"
    WEBAPP_NETWORK_MASK: int = 24


settings = Settings()


class SandboxConfig(BaseModel):
    workspace_id: uuid.UUID
    provider: CloudProvider | None = None
    env_vars: dict[str, str] | None = None


class EnvironmentConfig(BaseModel):
    """Environment configuration for sandboxed execution"""

    # System paths
    essential_paths: list[str] = [
        "/usr/local/sbin",
        "/usr/local/bin",
        "/usr/sbin",
        "/usr/bin",
        "/sbin",
        "/bin",
        "/.venv/bin",
        f"{settings.SANDBOX_VIRTUAL_HOME}/.cargo/bin",
        f"{settings.SANDBOX_VIRTUAL_HOME}/.kube",
    ]

    # Base environment variables
    base_env: dict[str, str] = {
        "LANG": "C.UTF-8",
        "LC_ALL": "C.UTF-8",
        "TIMEZONE": "UTC",
        "TERM": "xterm-256color",
        "SHELL": "/bin/bash",
        "PATH": ":".join(essential_paths),
        "USER": "user",  # Set as regular user
        "PWD": settings.SANDBOX_VIRTUAL_HOME,
        "HOME": settings.SANDBOX_VIRTUAL_HOME,
        "PYTHONPATH": settings.SANDBOX_VIRTUAL_HOME,
    }


class BwrapConfig(BaseModel):
    """Bubblewrap sandbox configuration"""

    network_enabled: bool = True

    # Core system paths that should be read-only
    readonly_system_paths: list[str] = [
        "/usr",
        "/bin",
        "/lib",
        "/lib64",  # Will be skipped if doesn't exist
        "/sbin",
        "/etc/resolv.conf",  # DNS resolution
        "/etc/hosts",  # Host resolution
        "/etc/ssl/certs",  # SSL certificates
        "/etc/ssl",  # Full SSL directory for CA certificates
        "/etc/ca-certificates",  # Additional CA certificates
        "/opt",  # Third party tools like az
    ]

    # Paths that should be completely hidden
    hidden_paths: list[str] = [
        "/etc/passwd",
        "/etc/shadow",
    ]

    # Temporary filesystem mounts
    tmpfs_paths: list[str] = [
        "/tmp",
    ]


class SandboxPTYConfig(BaseModel):
    sandbox_pty_env: dict[str, str] = {
        "TERM": "xterm-256color",
        "COLORTERM": "truecolor",
        "PS1": "\\[\\033[32m\\]sandbox$\\[\\033[0m\\] ",
    }


class NetworkConfig(BaseModel):
    """Configuration for network namespace management."""

    network_prefix: str = settings.NETWORK_PREFIX
    base_ip_range: str = settings.NETWORK_BASE_IP_RANGE
    network_mask: int = settings.NETWORK_MASK
    subnet_mask: int = settings.NETWORK_SUBNET_MASK
    namespace_timeout: int = settings.NETWORK_NAMESPACE_TIMEOUT


class NetworkNamespace(BaseModel):
    """Represents an isolated network namespace with allocated subnet."""

    network_namespace: str
    network_prefix: str
    subnet_cidr: str
    veth0_ip: str
    veth1_ip: str
    resolv_conf_path: str | None = None
