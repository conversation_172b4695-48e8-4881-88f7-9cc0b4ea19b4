from fastapi import HTTPException, status


class WorkspaceManagerError(HTTPException):
    def __init__(
        self,
        detail: str = "Workspace manager error",
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
    ):
        super().__init__(status_code=status_code, detail=detail)


class WorkspaceProvisionerError(HTTPException):
    def __init__(
        self,
        detail: str = "Workspace provisioner error",
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
    ):
        super().__init__(status_code=status_code, detail=detail)
