import asyncio
import os
import signal

from app.core.config import settings
from app.logger import logger
from app.schemas import RunCommandResponse


async def run_command(
    command: list[str],
    env: dict[str, str] | None = None,
    setid: bool = False,
    timeout: int = settings.SANDBOX_MAX_RUNTIME,
) -> RunCommandResponse:
    """Run a command in a subprocess and return the output"""
    process = await asyncio.create_subprocess_exec(
        *command,
        env=env,
        stdout=asyncio.subprocess.PIPE,
        stderr=asyncio.subprocess.PIPE,
        preexec_fn=os.setsid if setid else None,
    )
    try:
        stdout, stderr = await asyncio.wait_for(process.communicate(), timeout=timeout)
        return RunCommandResponse(
            stdout=stdout.decode().strip(),
            stderr=stderr.decode().strip(),
            return_code=process.returncode,
        )
    except asyncio.TimeoutError:
        # Kill the process group if timeout occurs
        try:
            os.killpg(os.getpgid(process.pid), signal.SIGTERM)
            await asyncio.sleep(1)
            if process.returncode is None:
                os.killpg(os.getpgid(process.pid), signal.SIGKILL)
        except Exception as kill_error:
            logger.error(f"Failed to kill process: {str(kill_error)}")

        return RunCommandResponse(
            stdout="",
            stderr=f"Command timed out after {timeout} seconds",
            return_code=-1,
        )
    except Exception as e:
        return RunCommandResponse(
            stdout="",
            stderr=str(e),
            return_code=-1,
        )
