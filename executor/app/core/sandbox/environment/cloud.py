import json
from pathlib import Path

from app.core.config import settings
from app.core.exception import BaseAppException
from app.schemas import (
    AWSCredentials,
    AzureCredentials,
    CloudCredentials,
    CloudProvider,
    GCPCredentials,
)


class CloudCredentialsManager:
    def __init__(self, credentials: CloudCredentials | None = None):
        self.credentials = credentials

    def create_or_update_gcp_credentials(self, workspace_mount_path: Path) -> bool:
        if self.credentials and self.credentials.provider == CloudProvider.GCP:
            assert isinstance(self.credentials, GCPCredentials)
            credentials_path = (
                workspace_mount_path
                / settings.SANDBOX_CREDENTIALS_SUBDIR
                / "gcp_credentials.json"
            )
            credentials_path.parent.mkdir(parents=True, exist_ok=True)
            with open(credentials_path, "w") as f:
                if self.credentials.service_account_key:
                    json.dump(json.loads(self.credentials.service_account_key), f)
                else:
                    json.dump({}, f)  # empty json object
            return True
        return False

    def get_environment_variables(self) -> dict[str, str]:
        if not self.credentials:
            return {}

        if self.credentials.provider == CloudProvider.AWS:
            assert isinstance(self.credentials, AWSCredentials)
            return {
                "AWS_ACCESS_KEY_ID": self.credentials.aws_access_key_id,
                "AWS_SECRET_ACCESS_KEY": self.credentials.aws_secret_access_key,
                "AWS_DEFAULT_REGION": self.credentials.aws_default_region,
                "AWS_PROFILE_CONFIG": "/dev/null",
                "AWS_SHARED_CREDENTIALS_FILE": "/dev/null",
            }
        elif self.credentials.provider == CloudProvider.GCP:
            assert isinstance(self.credentials, GCPCredentials)
            return {
                "GOOGLE_APPLICATION_CREDENTIALS": str(
                    Path(settings.SANDBOX_VIRTUAL_HOME)
                    / settings.SANDBOX_CREDENTIALS_SUBDIR
                    / "gcp_credentials.json"
                )
            }
        elif self.credentials.provider == CloudProvider.AZURE:
            assert isinstance(self.credentials, AzureCredentials)
            return {
                "AZURE_USERNAME": self.credentials.username,
                "AZURE_PASSWORD": self.credentials.password,
                "AZURE_TENANT": self.credentials.tenant,
            }
        else:
            raise BaseAppException(
                f"Unsupported cloud provider: {self.credentials}", 400
            )
