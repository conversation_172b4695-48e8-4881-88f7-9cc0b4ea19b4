from pathlib import Path

from app.core.config import EnvironmentConfig
from app.core.sandbox.environment.cloud import CloudCredentialsManager
from app.core.sandbox.environment.k8s import K8SCredentialsManager


class EnvironmentManager:
    """Manages environment variables for sandboxed execution"""

    def __init__(
        self,
        cloud_credentials_manager: CloudCredentialsManager,
        k8s_credentials_manager: K8SCredentialsManager,
        config: EnvironmentConfig = EnvironmentConfig(),
    ):
        self.config = config
        self.cloud_credentials_manager = cloud_credentials_manager
        self.k8s_credentials_manager = k8s_credentials_manager

    def create_clean_environment(
        self, workspace_mount_path: Path, extra_vars: dict[str, str] | None = None
    ) -> dict[str, str]:
        """Create a clean environment for sandbox execution"""
        env = self.config.base_env.copy()

        # Create or update GCP credentials
        self.cloud_credentials_manager.create_or_update_gcp_credentials(
            workspace_mount_path
        )
        self.k8s_credentials_manager.create_or_update_kubeconfig(workspace_mount_path)

        # Add cloud environment variables
        env.update(self.cloud_credentials_manager.get_environment_variables())

        # Add k8s environment variables
        env.update(self.k8s_credentials_manager.get_environment_variables())

        # Add extra variables if provided
        if extra_vars:
            env.update(extra_vars)

        return env
