from pathlib import Path

from app.core.config import (
    BwrapConfig,
    NetworkNamespace,
    settings,
)


class BwrapCommandBuilder:
    """Builds secure bwrap commands with proper isolation and disk usage limiting"""

    def __init__(
        self,
        config: BwrapConfig,
        workspace_mount_path: Path,
        network_namespace: NetworkNamespace | None = None,
    ):
        self.config = config
        self.workspace_mount_path = workspace_mount_path
        self.network_namespace = network_namespace

    def build_base_command(self) -> list[str]:
        """Build base bwrap command with core isolation
        https://sloonz.github.io/posts/sandboxing-1/
        """
        cmd = [
            "bwrap",
            "--unshare-all",  # Unshare all namespaces
            "--unshare-ipc",  # Unshare IPC namespace
            "--unshare-pid",  # Unshare PID namespace
            "--unshare-uts",  # Unshare UTS namespace
            "--unshare-cgroup",  # Unshare cgroup namespace
            "--unshare-user",  # Unshare user namespace
            "--die-with-parent",  # Ensure cleanup on parent process exit
            "--share-net",  # Share network namespace
            "--dev",
            "/dev",  # Required for /dev/null and other device files
            "--proc",
            "/proc",  # Required for /proc/self/fd and other proc files
        ]

        # Add read-only system paths (excluding resolv.conf which we'll handle separately)
        for path in self.config.readonly_system_paths:
            if path == "/lib64" and not Path("/lib64").exists():
                continue
            if path == "/etc/resolv.conf":
                continue  # Skip resolv.conf, we'll mount a custom one
            cmd.extend(["--ro-bind", path, path])

        # Mount custom resolv.conf if we have a network namespace
        if self.network_namespace and self.network_namespace.resolv_conf_path:
            cmd.extend(
                [
                    "--ro-bind",
                    self.network_namespace.resolv_conf_path,
                    "/etc/resolv.conf",
                ]
            )
        else:
            # Fallback to host resolv.conf if no network namespace
            cmd.extend(["--ro-bind", "/etc/resolv.conf", "/etc/resolv.conf"])

        # Add cargo bin
        cmd.extend(["--ro-bind", "/root/.cargo/bin", "/home/<USER>/.cargo/bin"])

        # Add venv bin
        cmd.extend(["--ro-bind", "./.venv", f"{settings.SANDBOX_VIRTUAL_HOME}/.venv"])

        # Setup disk-limited workspace and bind it
        cmd.extend(
            [
                "--bind",
                str(self.workspace_mount_path),
                settings.SANDBOX_VIRTUAL_HOME,
                "--chdir",
                settings.SANDBOX_VIRTUAL_HOME,
            ]
        )

        # Hide sensitive paths
        for path in self.config.hidden_paths:
            cmd.extend(["--tmpfs", path])

        # Add tmpfs mounts
        for path in self.config.tmpfs_paths:
            cmd.extend(["--tmpfs", path])

        return cmd

    def add_env_vars(self, cmd: list[str], env_vars: dict[str, str]) -> list[str]:
        """Add environment variables to bwrap command"""
        for key, value in env_vars.items():
            cmd.extend(["--setenv", key, value])
        return cmd

    def build_command(
        self, commands: list[str], env_vars: dict[str, str] | None = None
    ) -> list[str]:
        """Build complete bwrap command with all isolation features"""
        cmd = self.build_base_command()

        # Add environment variables
        if env_vars:
            cmd = self.add_env_vars(cmd, env_vars)

        # Add the actual command to run
        cmd.extend(commands)
        return cmd
