import asyncio
import hashlib
import os
import threading
import time

from app.core.config import NetworkConfig, NetworkNamespace
from app.core.exception import BaseAppException
from app.core.helper_utils import run_command
from app.core.sandbox.network.network_registry import NetworkRegistry
from app.logger import logger


class WorkspaceNamespace:
    """Cached namespace with last access tracking."""

    def __init__(self, namespace: NetworkNamespace):
        self.namespace = namespace
        self.last_accessed = time.time()

    def update_access_time(self):
        """Update the last accessed timestamp."""
        self.last_accessed = time.time()


class NetworkManager:
    """Creates and manages network namespaces by workspace ID with automatic cleanup."""

    def __init__(self, config: NetworkConfig):
        self.config = config
        self.registry = NetworkRegistry(config)
        self._namespaces_cache: dict[str, WorkspaceNamespace] = {}
        self._cleanup_lock = threading.Lock()
        self._orphaned_cleanup_done = False
        # Shared resolv.conf configuration
        self._shared_resolv_conf_path = "/tmp/sandbox_resolv.conf"

    def _ensure_shared_resolv_conf(self) -> None:
        """Ensure a shared resolv.conf exists with public DNS servers.

        Using a single shared file avoids creating per-workspace resolv.conf files
        since the content is identical across workspaces.
        """
        try:
            if not os.path.exists(self._shared_resolv_conf_path):
                resolv_conf_content = "nameserver 8.8.8.8\nnameserver 1.1.1.1\n"
                with open(self._shared_resolv_conf_path, "w") as f:
                    f.write(resolv_conf_content)
        except Exception as e:
            logger.warning(
                f"Failed to ensure shared resolv.conf at {self._shared_resolv_conf_path}: {str(e)}"
            )

    def _generate_interface_id(self, workspace_id: str) -> str:
        """Generate a unique short identifier from workspace_id using hash."""
        # Use SHA256 hash and take first 8 characters for uniqueness
        hash_obj = hashlib.sha256(workspace_id.encode("utf-8"))
        return hash_obj.hexdigest()[:8]

    async def _namespace_exists(self, namespace_name: str) -> bool:
        """Check if a network namespace already exists."""
        try:
            result = await run_command(["ip", "netns", "list"])
            if result.return_code == 0:
                existing_namespaces = result.stdout.split("\n")
                return any(namespace_name in ns for ns in existing_namespaces)
            return False
        except Exception:
            return False

    async def _cleanup_orphaned_namespaces(self):
        """Clean up orphaned namespaces that match our prefix pattern."""
        try:
            result = await run_command(["ip", "netns", "list"])
            if result.return_code != 0:
                return

            existing_namespaces = [
                ns.split()[0] for ns in result.stdout.strip().split("\n") if ns.strip()
            ]

            # Find namespaces that match our prefix pattern
            orphaned_namespaces = [
                ns
                for ns in existing_namespaces
                if ns.startswith(f"{self.config.network_prefix}_")
                and ns
                not in [
                    cached_ns.namespace.network_namespace
                    for cached_ns in self._namespaces_cache.values()
                ]
            ]

            for namespace_name in orphaned_namespaces:
                try:
                    # Extract workspace_id from namespace name
                    workspace_id = namespace_name.replace(
                        f"{self.config.network_prefix}_", ""
                    )
                    await self._force_cleanup_namespace(workspace_id, namespace_name)
                    logger.info(f"Cleaned up orphaned namespace: {namespace_name}")
                except Exception as e:
                    logger.warning(
                        f"Failed to clean up orphaned namespace {namespace_name}: {str(e)}"
                    )

        except Exception as e:
            logger.warning(f"Failed to cleanup orphaned namespaces: {str(e)}")

    async def _force_cleanup_namespace(self, workspace_id: str, namespace_name: str):
        """Force cleanup of a specific namespace by name."""
        interface_id = self._generate_interface_id(workspace_id)
        veth0_name = f"v0_{interface_id}"

        try:
            # Delete the namespace (this will also clean up interfaces inside it)
            await run_command(["ip", "netns", "delete", namespace_name])
            logger.debug(f"Deleted namespace: {namespace_name}")
        except Exception as e:
            logger.warning(f"Failed to delete namespace {namespace_name}: {str(e)}")

        try:
            # Clean up any remaining veth interfaces
            await run_command(["ip", "link", "delete", veth0_name])
            logger.debug(f"Deleted veth interface: {veth0_name}")
        except Exception as e:
            logger.warning(f"Failed to delete veth interface {veth0_name}: {str(e)}")

    async def get_network_namespace(self, workspace_id: str) -> NetworkNamespace:
        """Get or create a network namespace for the given workspace ID."""
        # Clean up orphaned namespaces on first request
        if not self._orphaned_cleanup_done:
            await self._cleanup_orphaned_namespaces()
            self._orphaned_cleanup_done = True

        with self._cleanup_lock:
            if workspace_id in self._namespaces_cache:
                cached = self._namespaces_cache[workspace_id]
                cached.update_access_time()
                logger.debug(
                    f"Retrieved cached namespace for workspace: {workspace_id}"
                )
                return cached.namespace

            # Create new namespace
            namespace = await self._create_network_namespace(workspace_id)
            self._namespaces_cache[workspace_id] = WorkspaceNamespace(namespace)
            logger.info(f"Created new namespace for workspace: {workspace_id}")
            return namespace

    async def _create_network_namespace(self, workspace_id: str) -> NetworkNamespace:
        """Create a network namespace with configured veth pair."""
        try:
            namespace = self.registry.register_network_namespace(workspace_id)

            # Check if namespace already exists and clean it up if it does
            if await self._namespace_exists(namespace.network_namespace):
                logger.warning(
                    f"Namespace {namespace.network_namespace} already exists, cleaning up..."
                )
                await self._force_cleanup_namespace(
                    workspace_id, namespace.network_namespace
                )
                # Wait a moment for cleanup to complete
                await asyncio.sleep(0.1)

            # Create the network namespace
            await run_command(["ip", "netns", "add", namespace.network_namespace])
            logger.info(f"Created network namespace: {namespace.network_namespace}")

            # Generate unique short interface names using hash
            interface_id = self._generate_interface_id(workspace_id)
            veth0_name = f"v0_{interface_id}"  # Max 11 chars (v0_ + 8 hash chars)
            veth1_name = f"v1_{interface_id}"  # Max 11 chars (v1_ + 8 hash chars)

            # Create veth pair
            await run_command(
                [
                    "ip",
                    "link",
                    "add",
                    veth0_name,
                    "type",
                    "veth",
                    "peer",
                    "name",
                    veth1_name,
                ]
            )

            # Move veth1 into namespace
            await run_command(
                ["ip", "link", "set", veth1_name, "netns", namespace.network_namespace]
            )

            # Configure veth0 (host side)
            await run_command(["ip", "link", "set", veth0_name, "up"])
            await run_command(
                [
                    "ip",
                    "addr",
                    "add",
                    f"{namespace.veth0_ip}/{self.config.subnet_mask}",
                    "dev",
                    veth0_name,
                ]
            )

            # Configure veth1 (sandbox side)
            await run_command(
                [
                    "ip",
                    "netns",
                    "exec",
                    namespace.network_namespace,
                    "ip",
                    "link",
                    "set",
                    veth1_name,
                    "up",
                ]
            )
            await run_command(
                [
                    "ip",
                    "netns",
                    "exec",
                    namespace.network_namespace,
                    "ip",
                    "addr",
                    "add",
                    f"{namespace.veth1_ip}/{self.config.subnet_mask}",
                    "dev",
                    veth1_name,
                ]
            )

            # Configure default route in namespace
            await run_command(
                [
                    "ip",
                    "netns",
                    "exec",
                    namespace.network_namespace,
                    "ip",
                    "route",
                    "add",
                    "default",
                    "via",
                    namespace.veth0_ip,
                ]
            )

            # Configure loopback interface in namespace
            await run_command(
                [
                    "ip",
                    "netns",
                    "exec",
                    namespace.network_namespace,
                    "ip",
                    "link",
                    "set",
                    "lo",
                    "up",
                ]
            )

            # Configure DNS resolution in namespace using a shared resolv.conf
            # Use public DNS servers since Docker's 127.0.0.11 is not accessible from network namespace
            self._ensure_shared_resolv_conf()
            namespace.resolv_conf_path = self._shared_resolv_conf_path
            return namespace

        except Exception as e:
            await self._cleanup_namespace(workspace_id)
            raise BaseAppException(
                f"Failed to create network namespace: {str(e)}", status_code=500
            )

    async def _destroy_network_namespace(
        self, workspace_id: str, namespace: NetworkNamespace
    ) -> bool:
        """Destroy a specific network namespace."""
        try:
            await self._cleanup_namespace(workspace_id)
            success = self.registry.unregister_network_namespace(workspace_id)
            if success:
                logger.info(
                    f"Destroyed network namespace: {namespace.network_namespace}"
                )
                return True
            else:
                logger.warning(
                    f"Network namespace not found in registry: {namespace.network_namespace}"
                )
                return False

        except Exception as e:
            logger.error(
                f"Failed to destroy network namespace {workspace_id}: {str(e)}"
            )
            raise BaseAppException(
                f"Failed to destroy network namespace: {str(e)}", status_code=500
            )

    async def _cleanup_namespace(self, workspace_id: str):
        """Clean up network namespace and veth interfaces."""
        namespace_name = f"{self.config.network_prefix}_{workspace_id}"
        await self._force_cleanup_namespace(workspace_id, namespace_name)

    async def _cleanup_inactive_namespaces(self):
        """Remove namespaces that haven't been accessed within the timeout period."""
        current_time = time.time()
        timeout_threshold = current_time - self.config.namespace_timeout

        with self._cleanup_lock:
            inactive_workspaces = [
                workspace_id
                for workspace_id, cached_ns in self._namespaces_cache.items()
                if cached_ns.last_accessed < timeout_threshold
            ]

            for workspace_id in inactive_workspaces:
                try:
                    cached_ns = self._namespaces_cache[workspace_id]
                    await self._destroy_network_namespace(
                        workspace_id, cached_ns.namespace
                    )
                    del self._namespaces_cache[workspace_id]
                    logger.info(
                        f"Cleaned up inactive namespace for workspace: {workspace_id}"
                    )
                except Exception as e:
                    logger.error(
                        f"Failed to cleanup namespace for workspace {workspace_id}: {str(e)}"
                    )


_network_manager_instance = None


def get_network_manager() -> NetworkManager:
    """Get the network manager instance (singleton)."""
    global _network_manager_instance
    if _network_manager_instance is None:
        _network_manager_instance = NetworkManager(NetworkConfig())
    return _network_manager_instance
