import asyncio
import fcntl
import json
import os
import subprocess
import termios
from pathlib import Path

from fastapi import WebSocket, WebSocketDisconnect

from app.core.config import SandboxPTYConfig, settings
from app.core.exception import BaseAppException
from app.core.sandbox.bwrap import Bwrap<PERSON>andbox
from app.core.sandbox.pty.manager import PTYManager
from app.logger import logger
from app.schemas import CloudProvider


class SandboxPTY:
    """Manages a sandboxed PTY session"""

    def __init__(self, bwrap_sandbox: BwrapSandbox, websocket: WebSocket):
        self.bwrap_sandbox = bwrap_sandbox
        self.pty_manager = PTYManager()
        self.websocket = websocket
        self.buffer = bytearray()
        self.pty_config = SandboxPTYConfig()

    def is_websocket_connected(self) -> bool:
        """Check if websocket connection is valid"""
        return (
            self.websocket is not None and not self.websocket.client_state.DISCONNECTED
        )

    async def create_pty(self) -> None:
        """Create a new pseudo-terminal in a sandboxed environment"""
        # try:
        self.pty_manager.create()

        bwrap_cmd = await self.bwrap_sandbox.get_bwrap_command(
            ["bash", "--norc", "--noprofile", "-i"]
        )
        self.bwrap_sandbox.update_env(self.pty_config.sandbox_pty_env)

        def preexec_fn():
            os.setsid()  # Create new session
            try:
                # Make this the controlling terminal
                fcntl.ioctl(self.pty_manager.slave_fd, termios.TIOCSCTTY, 0)  # type: ignore
            except OSError:
                pass  # May fail in some environments, that's ok

        # Start shell process in sandbox with proper PTY handling
        self.pty_manager.process = subprocess.Popen(
            bwrap_cmd,
            env=self.bwrap_sandbox.env,
            stdin=self.pty_manager.slave_fd,
            stdout=self.pty_manager.slave_fd,
            stderr=self.pty_manager.slave_fd,
            preexec_fn=preexec_fn,  # Create new process group
            restore_signals=True,
        )

        # Wait a bit for the process to initialize
        await asyncio.sleep(0.1)

        # # Close slave fd in parent process
        if self.pty_manager.slave_fd is not None:
            os.close(self.pty_manager.slave_fd)
            self.pty_manager.slave_fd = None

        # Make master fd non-blocking
        if self.pty_manager.master_fd:
            flags = fcntl.fcntl(self.pty_manager.master_fd, fcntl.F_GETFL)
            fcntl.fcntl(
                self.pty_manager.master_fd, fcntl.F_SETFL, flags | os.O_NONBLOCK
            )
        else:
            raise BaseAppException("Slave fd is not set", 500)

        # Run gcloud auth command if needed
        if self.bwrap_sandbox.config.provider:
            if self.bwrap_sandbox.config.provider == CloudProvider.GCP:
                self.pty_manager.write(
                    f"gcloud auth activate-service-account --key-file={Path(settings.SANDBOX_VIRTUAL_HOME) / settings.SANDBOX_CREDENTIALS_SUBDIR / 'gcp_credentials.json'}\n"
                )

            if self.bwrap_sandbox.config.provider == CloudProvider.AZURE:
                self.pty_manager.write(
                    "az account show > /dev/null 2>&1 || az login --service-principal --username $AZURE_USERNAME --password $AZURE_PASSWORD --tenant $AZURE_TENANT > /dev/null 2>&1\n"
                )

        # except Exception as e:
        #     logger.error(f"Failed to create secure PTY: {e}")
        #     await self.cleanup()
        #     raise BaseAppException("Failed to create PTY", 500)

    async def read_from_pty(self) -> None:
        """Read data from PTY and send to WebSocket"""
        self.pty_manager.running = True

        try:
            while self.pty_manager.running and self.pty_manager.master_fd is not None:
                try:
                    chunk = os.read(self.pty_manager.master_fd, 1024)
                    if chunk:
                        self.buffer.extend(chunk)
                        try:
                            text = self.buffer.decode("utf-8")
                            await self.send_to_websocket(text)
                            self.buffer.clear()
                        except UnicodeDecodeError as e:
                            logger.error(f"UnicodeDecodeError: {e}")
                    else:
                        if (
                            self.pty_manager.process
                            and self.pty_manager.process.poll() is not None
                        ):
                            logger.info("Shell process terminated")
                            break
                        await asyncio.sleep(0.01)
                except BlockingIOError:
                    if (
                        self.pty_manager.process
                        and self.pty_manager.process.poll() is not None
                    ):
                        logger.info("Shell process terminated")
                        break
                    await asyncio.sleep(0.01)
                except OSError as e:
                    if (
                        e.errno == 5
                        and self.pty_manager.process
                        and self.pty_manager.process.poll() is not None
                    ):
                        logger.info("Shell process terminated")
                        break
                    else:
                        logger.error(f"OSError reading from PTY: {e}")
                        break

        except Exception as e:
            logger.error(f"Error reading from PTY: {e}")
        finally:
            await self.cleanup()

    def write_to_pty(self, data: str) -> None:
        """Write data to PTY"""
        self.pty_manager.write(data)

    async def send_to_websocket(self, data: str) -> None:
        """Send data to WebSocket client"""
        try:
            message = json.dumps({"type": "output", "data": data})
            await self.websocket.send_text(message)
        except WebSocketDisconnect:
            logger.info("WebSocket connection closed normally")
            self.pty_manager.running = False
        except Exception as e:
            logger.error(f"Error sending to WebSocket: {e}")
            self.pty_manager.running = False

    def set_terminal_size(self, rows: int, cols: int) -> None:
        """Set terminal size"""
        self.pty_manager.set_window_size(rows, cols)

    async def cleanup(self) -> None:
        """Clean up PTY and process"""
        await self.pty_manager.cleanup()
        if self.websocket and self.is_websocket_connected():
            await self.websocket.close(code=1000, reason="Terminal session ended")
