import fcntl
import os
import pty
import signal
import struct
import subprocess
import termios

from app.core.exception import BaseAppException
from app.logger import logger


class PTYManager:
    """Manages PTY operations and lifecycle"""

    def __init__(self):
        self.master_fd: int | None = None
        self.slave_fd: int | None = None
        self.process: subprocess.Popen | None = None
        self.running: bool = False

    def create(self) -> None:
        """Create a new PTY"""
        try:
            self.master_fd, self.slave_fd = pty.openpty()
            self.set_attributes()
        except Exception as e:
            logger.error(f"Failed to create PTY: {e}")
            raise BaseAppException("Failed to create PTY", 500)

    def set_attributes(self) -> None:
        if self.slave_fd is None:
            raise BaseAppException("Slave fd is not set", 500)

        # Set terminal attributes for proper job control and echoing
        attrs = termios.tcgetattr(self.slave_fd)
        # Input flags
        attrs[0] = attrs[0] | termios.BRKINT | termios.ICRNL | termios.IMAXBEL
        # Output flags
        attrs[1] = attrs[1] | termios.OPOST | termios.ONLCR
        # Control flags
        attrs[2] = attrs[2] | termios.CS8 | termios.CREAD
        # Local flags - enable all echo and job control
        attrs[3] = attrs[3] | (
            termios.ISIG
            | termios.ICANON
            | termios.IEXTEN
            | termios.ECHO
            | termios.ECHOE
            | termios.ECHOK
            | termios.ECHOCTL
            | termios.ECHOKE
        )
        termios.tcsetattr(self.slave_fd, termios.TCSANOW, attrs)

    def set_window_size(self, rows: int, cols: int) -> None:
        """Set terminal window size"""
        if self.master_fd is not None:
            try:
                fcntl.ioctl(
                    self.master_fd,
                    termios.TIOCSWINSZ,
                    struct.pack("HHHH", rows, cols, 0, 0),
                )
            except Exception as e:
                logger.error(f"Failed to set terminal size: {e}")

    def write(self, data: str) -> None:
        """Write data to PTY"""
        if (
            self.master_fd is not None
            and self.process is not None
            and self.process.poll() is None
        ):
            try:
                os.write(self.master_fd, data.encode("utf-8"))
            except Exception as e:
                logger.error(f"Error writing to PTY: {e}")

    async def cleanup(self) -> None:
        """Clean up PTY resources"""
        self.running = False

        if self.process:
            try:
                os.killpg(os.getpgid(self.process.pid), signal.SIGTERM)
                try:
                    self.process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    os.killpg(os.getpgid(self.process.pid), signal.SIGKILL)
                    self.process.wait()
            except ProcessLookupError:
                pass

        if self.master_fd is not None:
            os.close(self.master_fd)
            self.master_fd = None

        if self.slave_fd is not None:
            try:
                os.close(self.slave_fd)
            except OSError:
                pass
            self.slave_fd = None
