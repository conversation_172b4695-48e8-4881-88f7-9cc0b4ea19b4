import uuid
from pathlib import Path

from app.core.config import settings
from app.core.exceptions.workspace_exceptions import WorkspaceManagerError
from app.core.helper_utils import run_command
from app.logger import logger


class DiskQuotaExceeded(Exception):
    """Raised when workspace exceeds disk size limit."""

    pass


class SizeLimitedWorkspaceManager:
    """Directory-based workspace manager with real-time size enforcement.

    Advantages:
    - No RAM consumption (uses actual disk)
    - No loop device limits
    - No special filesystem requirements
    - Real-time size monitoring
    - Scales to 1000+ users
    - Simple and reliable
    """

    def __init__(self, base_path: Path = Path(settings.SANDBOX_DIR)):
        self.base_path: Path = base_path
        self.base_path.mkdir(parents=True, exist_ok=True)

    def _workspace_root(self, workspace_id: uuid.UUID) -> Path:
        return self.base_path / str(workspace_id)

    def get_workspace_mount_point(self, workspace_id: uuid.UUID) -> Path:
        return self.base_path / str(workspace_id) / "workspace"

    def get_workspace_root(self, workspace_id: uuid.UUID) -> Path:
        return self._workspace_root(workspace_id)

    async def create_or_reuse_workspace(self, workspace_id: uuid.UUID) -> Path:
        """Create or reuse a size-controlled workspace."""
        try:
            root = self._workspace_root(workspace_id)
            workspace_mount = self.get_workspace_mount_point(workspace_id)

            # Create workspace directory structure
            root.mkdir(parents=True, exist_ok=True)
            workspace_mount.mkdir(parents=True, exist_ok=True)

            # Set proper permissions (world-writable for bwrap compatibility)
            await run_command(["chmod", "-R", "755", str(root)])
            await run_command(["chmod", "-R", "777", str(workspace_mount)])

            # Check and enforce size limit on existing workspace
            await self._check_and_enforce_size_limit(
                workspace_mount, settings.SANDBOX_MAX_DISK_USAGE_MB
            )

            logger.info(
                f"Created/reused workspace {workspace_id} with {settings.SANDBOX_MAX_DISK_USAGE_MB}MB limit"
            )
            return root

        except Exception as e:
            logger.error(f"Failed to create size-limited workspace {workspace_id}: {e}")
            raise WorkspaceManagerError(
                f"Failed to create size-limited workspace {workspace_id}: {e}",
                status_code=500,
            )

    async def cleanup_workspace(self, workspace_id: uuid.UUID) -> None:
        """Clean up workspace directory."""
        try:
            workspace_root = self._workspace_root(workspace_id)
            if workspace_root.exists():
                # Reset permissions before cleanup
                await run_command(["chmod", "-R", "755", str(workspace_root)])
                logger.info(f"Cleaned up workspace {workspace_id}")

        except Exception as e:
            logger.error(f"Failed to cleanup workspace {workspace_id}: {e}")
            raise WorkspaceManagerError(
                f"Failed to cleanup workspace {workspace_id}: {e}",
                status_code=500,
            )

    async def _check_and_enforce_size_limit(
        self, workspace_path: Path, limit_mb: int
    ) -> None:
        """Check workspace size and enforce limit by changing permissions."""
        try:
            # Get current directory size in MB
            result = await run_command(["du", "-sm", str(workspace_path)])
            current_mb = int(result.stdout.split()[0])

            logger.debug(
                f"Workspace {workspace_path.parent.name} size: {current_mb}MB/{limit_mb}MB"
            )

            if current_mb >= limit_mb:
                # Workspace exceeded limit - make it read-only to prevent further writes
                await run_command(["chmod", "-R", "444", str(workspace_path)])
                logger.warning(
                    f"Workspace {workspace_path.parent.name} exceeded {limit_mb}MB limit ({current_mb}MB) - now read-only"
                )
                raise DiskQuotaExceeded(
                    f"Workspace exceeded {limit_mb}MB limit (currently {current_mb}MB)"
                )
            else:
                # Workspace within limit - ensure it's writable
                await run_command(["chmod", "-R", "777", str(workspace_path)])

        except DiskQuotaExceeded:
            # Re-raise quota exceeded errors
            raise
        except Exception as e:
            logger.warning(f"Failed to check size for {workspace_path}: {e}")
            # Don't fail workspace operations if size checking fails

    async def get_workspace_usage(self, workspace_id: uuid.UUID) -> dict:
        """Get current disk usage for workspace."""
        try:
            workspace_path = self.get_workspace_mount_point(workspace_id)

            if not workspace_path.exists():
                return {
                    "used_mb": 0,
                    "limit_mb": settings.SANDBOX_MAX_DISK_USAGE_MB,
                    "usage_percent": 0,
                    "status": "not_created",
                }

            # Get size using du command
            result = await run_command(["du", "-sm", str(workspace_path)])
            used_mb = int(result.stdout.split()[0])

            usage_percent = used_mb / settings.SANDBOX_MAX_DISK_USAGE_MB * 100
            status = (
                "read_only"
                if used_mb >= settings.SANDBOX_MAX_DISK_USAGE_MB
                else "writable"
            )

            return {
                "used_mb": used_mb,
                "limit_mb": settings.SANDBOX_MAX_DISK_USAGE_MB,
                "usage_percent": usage_percent,
                "status": status,
            }

        except Exception as e:
            logger.error(f"Failed to get usage for workspace {workspace_id}: {e}")
            return {
                "used_mb": 0,
                "limit_mb": settings.SANDBOX_MAX_DISK_USAGE_MB,
                "usage_percent": 0,
                "status": "error",
            }
