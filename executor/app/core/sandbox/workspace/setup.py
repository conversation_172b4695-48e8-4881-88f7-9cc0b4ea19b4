import asyncio
from pathlib import Path

from app.core.exceptions.workspace_exceptions import WorkspaceProvisionerError


class WorkspaceProvisioner:
    """Provision and manage per-workspace loopback ext4 mounts.

    This class encapsulates the OS-level operations (create sparse image,
    format ext4, losetup attach, mount/umount) so callers (e.g., WorkspaceManager)
    don't need to know implementation details.
    """

    async def ensure_mounted(
        self,
        workspace_root: Path,
        mount_point_dirname: str,
        image_filename: str,
        size_bytes: int,
    ) -> None:
        img = workspace_root / image_filename
        mnt = workspace_root / mount_point_dirname

        # 1. Create the mount point directory if it doesn't exist
        mnt.mkdir(parents=True, exist_ok=True)

        # 2. Create sparse image if missing
        if not img.exists():
            await self._create_sparse_ext4_image(img, size_bytes)

        # 3. Verify image file was created successfully
        if not img.exists():
            raise WorkspaceProvisionerError(
                f"Image file {img} was not created successfully", status_code=500
            )

        # 4. Attach to loop device
        loop_dev = await self._get_loop_device(img)
        if not loop_dev:
            await self._attach_loop(img)
            loop_dev = await self._get_loop_device(img)
            if not loop_dev:
                raise WorkspaceProvisionerError(
                    "Failed to attach loop device for workspace image", status_code=500
                )

        # 5. Mount if not already mounted
        if not self._is_mounted(mnt):
            await self._mount_loop(loop_dev, mnt)

    async def unmount_and_detach(
        self, workspace_root: Path, mount_point_dirname: str, image_filename: str
    ) -> None:
        mnt = workspace_root / mount_point_dirname
        img = workspace_root / image_filename

        # 1. Unmount if mounted
        if self._is_mounted(mnt):
            proc = await asyncio.create_subprocess_exec(
                "umount",
                "-l",
                str(mnt),
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
            )
            await proc.communicate()

        # 2. Detach loop device
        loop_dev = await self._get_loop_device(img)
        if loop_dev:
            proc = await asyncio.create_subprocess_exec(
                "losetup",
                "-d",
                loop_dev,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
            )
            await proc.communicate()

    async def _create_sparse_ext4_image(self, img_path: Path, size_bytes: int) -> None:
        # Ensure parent directory exists with proper permissions
        try:
            img_path.parent.mkdir(parents=True, exist_ok=True, mode=0o755)
        except Exception as e:
            raise WorkspaceProvisionerError(
                f"Failed to create workspace directory {img_path.parent}: {e}",
                status_code=500,
            )

        # truncate
        proc = await asyncio.create_subprocess_exec(
            "truncate",
            "-s",
            str(size_bytes),
            str(img_path),
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
        )
        stdout, stderr = await proc.communicate()
        if proc.returncode != 0:
            if img_path.exists():
                try:
                    img_path.unlink()
                except Exception:
                    pass
            raise WorkspaceProvisionerError(
                f"truncate failed for {img_path}: {stderr.decode().strip()}",
                status_code=500,
            )

        # mkfs.ext4
        proc = await asyncio.create_subprocess_exec(
            "mkfs.ext4",
            "-q",
            str(img_path),
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
        )
        stdout, stderr = await proc.communicate()
        if proc.returncode != 0:
            if img_path.exists():
                try:
                    img_path.unlink()
                except Exception:
                    pass
            raise WorkspaceProvisionerError(
                f"mkfs.ext4 failed: {stderr.decode().strip()}", status_code=500
            )

        # tune2fs -m 0
        proc = await asyncio.create_subprocess_exec(
            "tune2fs",
            "-m",
            "0",
            str(img_path),
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
        )
        stdout, stderr = await proc.communicate()
        if proc.returncode != 0:
            raise WorkspaceProvisionerError(
                f"tune2fs failed: {stderr.decode().strip()}", status_code=500
            )

        # Final verification that the image file exists and has correct size
        if not img_path.exists():
            raise WorkspaceProvisionerError(
                f"Image file {img_path} was not created after all operations",
                status_code=500,
            )

        actual_size = img_path.stat().st_size
        if actual_size != size_bytes:
            img_path.unlink()  # Clean up malformed file
            raise WorkspaceProvisionerError(
                f"Image file size mismatch: expected {size_bytes}, got {actual_size}",
                status_code=500,
            )

    async def _attach_loop(self, img_path: Path) -> None:
        # Verify image file exists before attempting to attach
        if not img_path.exists():
            raise WorkspaceProvisionerError(
                f"Cannot attach loop device: image file {img_path} does not exist",
                status_code=500,
            )

        proc = await asyncio.create_subprocess_exec(
            "losetup",
            "-f",
            str(img_path),
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
        )
        stdout, stderr = await proc.communicate()
        if proc.returncode != 0:
            raise WorkspaceProvisionerError(
                f"losetup failed for {img_path}: {stderr.decode().strip()}",
                status_code=500,
            )

    async def _get_loop_device(self, img_path: Path) -> str | None:
        proc = await asyncio.create_subprocess_exec(
            "losetup",
            "-j",
            str(img_path),
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
        )
        stdout, _ = await proc.communicate()
        if proc.returncode != 0:
            return None
        output = stdout.decode().strip()
        if not output:
            return None
        first = output.splitlines()[0]
        dev = first.split(":", 1)[0].strip()
        return dev if dev.startswith("/dev/loop") else None

    def _is_mounted(self, mount_path: Path) -> bool:
        try:
            with open("/proc/mounts") as f:
                for line in f:
                    parts = line.split()
                    if len(parts) >= 2 and parts[1] == str(mount_path):
                        return True
            return False
        except Exception:
            return False

    async def _mount_loop(self, loop_dev: str, mount_path: Path) -> None:
        mount_path.mkdir(parents=True, exist_ok=True)
        proc = await asyncio.create_subprocess_exec(
            "mount",
            "-o",
            "nodev,nosuid,noexec",
            loop_dev,
            str(mount_path),
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
        )
        stdout, stderr = await proc.communicate()
        if proc.returncode != 0:
            raise WorkspaceProvisionerError(
                f"mount failed: {stderr.decode().strip()}", status_code=500
            )
