from app.core.config import settings
from app.core.sandbox.workspace.manager import WorkspaceManager
from app.core.sandbox.workspace.size_manager import SizeLimitedWorkspaceManager
from app.core.sandbox.workspace.tmpfs_manager import TmpfsWorkspaceManager


def create_workspace_manager():
    """Factory function to create appropriate workspace manager based on configuration.

    Returns:
        WorkspaceManager: Loop device based manager (legacy, <100 users)
        TmpfsWorkspaceManager: Tmpfs based manager (high memory usage, 1000+ users)
        SizeLimitedWorkspaceManager: Directory-based manager with size enforcement (recommended)
    """
    manager_type = settings.WORKSPACE_MANAGER_TYPE.lower()

    if manager_type == "tmpfs":
        return TmpfsWorkspaceManager()
    elif manager_type == "size":
        return SizeLimitedWorkspaceManager()
    else:
        # Default to loop device manager for backward compatibility
        return WorkspaceManager()
