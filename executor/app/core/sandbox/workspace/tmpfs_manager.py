import asyncio
import uuid
from pathlib import Path

from app.core.config import settings
from app.core.exceptions.workspace_exceptions import WorkspaceManagerError
from app.logger import logger


class TmpfsWorkspaceManager:
    """High-performance workspace manager using tmpfs + overlayfs for 1000+ concurrent users.

    Advantages over loop devices:
    - No kernel loop device limits
    - Faster I/O (memory-based)
    - Better scalability
    - Automatic cleanup on container restart
    """

    def __init__(self, base_path: Path = Path(settings.SANDBOX_DIR)):
        self.base_path: Path = base_path
        self.base_path.mkdir(parents=True, exist_ok=True)

    def _workspace_root(self, workspace_id: uuid.UUID) -> Path:
        return self.base_path / str(workspace_id)

    def get_workspace_mount_point(self, workspace_id: uuid.UUID) -> Path:
        return self.base_path / str(workspace_id) / "workspace"

    def get_workspace_root(self, workspace_id: uuid.UUID) -> Path:
        return self._workspace_root(workspace_id)

    async def create_or_reuse_workspace(self, workspace_id: uuid.UUID) -> Path:
        """Create or reuse a tmpfs-based workspace with disk quota enforcement."""
        try:
            root = self._workspace_root(workspace_id)
            workspace_mount = self.get_workspace_mount_point(workspace_id)

            # Create workspace directory structure
            root.mkdir(parents=True, exist_ok=True)
            workspace_mount.mkdir(parents=True, exist_ok=True)

            # Check if already mounted
            if self._is_mounted(workspace_mount):
                return root

            # Mount tmpfs with size limit (much faster than loop devices)
            max_size_mb = int(settings.SANDBOX_MAX_DISK_USAGE_MB)
            await self._mount_tmpfs(workspace_mount, max_size_mb)

            return root
        except Exception as e:
            logger.error(f"Failed to create tmpfs workspace {workspace_id}: {e}")
            raise WorkspaceManagerError(
                f"Failed to create tmpfs workspace {workspace_id}: {e}", status_code=500
            )

    async def cleanup_workspace(self, workspace_id: uuid.UUID) -> None:
        """Unmount tmpfs workspace but keep directory persistent."""
        try:
            workspace_mount = self.get_workspace_mount_point(workspace_id)

            # Only unmount tmpfs if mounted, keep directory structure
            if self._is_mounted(workspace_mount):
                await self._unmount_tmpfs(workspace_mount)
                logger.info(
                    f"Unmounted tmpfs workspace {workspace_id}, directory remains persistent"
                )

        except Exception as e:
            logger.error(f"Failed to unmount tmpfs workspace {workspace_id}: {e}")
            raise WorkspaceManagerError(
                f"Failed to unmount tmpfs workspace {workspace_id}: {e}",
                status_code=500,
            )

    async def _mount_tmpfs(self, mount_point: Path, size_mb: int) -> None:
        """Mount tmpfs with size limit - no loop devices needed."""
        proc = await asyncio.create_subprocess_exec(
            "mount",
            "-t",
            "tmpfs",
            "-o",
            f"size={size_mb}M,nodev,nosuid",
            "tmpfs",
            str(mount_point),
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
        )
        stdout, stderr = await proc.communicate()
        if proc.returncode != 0:
            raise WorkspaceManagerError(
                f"tmpfs mount failed for {mount_point}: {stderr.decode().strip()}",
                status_code=500,
            )

    async def _unmount_tmpfs(self, mount_point: Path) -> None:
        """Unmount tmpfs - instant cleanup."""
        proc = await asyncio.create_subprocess_exec(
            "umount",
            "-l",  # Lazy unmount
            str(mount_point),
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
        )
        await proc.communicate()  # Don't fail on umount errors

    def _is_mounted(self, mount_path: Path) -> bool:
        """Check if path is mounted."""
        try:
            with open("/proc/mounts") as f:
                for line in f:
                    parts = line.split()
                    if len(parts) >= 2 and parts[1] == str(mount_path):
                        return True
            return False
        except Exception:
            return False
