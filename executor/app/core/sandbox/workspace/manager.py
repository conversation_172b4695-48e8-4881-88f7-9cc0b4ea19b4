import uuid
from pathlib import Path

from app.core.config import settings
from app.core.exceptions.workspace_exceptions import WorkspaceManagerError
from app.logger import logger

from .setup import WorkspaceProvisioner


class WorkspaceManager:
    """Creates and reuses per-workspace persistent sandboxes backed by loop images."""

    def __init__(self, base_path: Path = Path(settings.SANDBOX_DIR)):
        self.base_path: Path = base_path
        self.base_path.mkdir(parents=True, exist_ok=True)

    def _workspace_root(self, workspace_id: uuid.UUID) -> Path:
        return self.base_path / str(workspace_id)

    def get_workspace_mount_point(self, workspace_id: uuid.UUID) -> Path:
        return (
            self.base_path / str(workspace_id) / settings.SANDBOX_WORKSPACE_MOUNT_POINT
        )

    def get_workspace_root(self, workspace_id: uuid.UUID) -> Path:
        return self._workspace_root(workspace_id)

    async def create_or_reuse_workspace(self, workspace_id: uuid.UUID) -> Path:
        """Create or reuse a persistent workspace and ensure it is mounted."""
        try:
            root = self._workspace_root(workspace_id)

            await WorkspaceProvisioner().ensure_mounted(
                workspace_root=root,
                mount_point_dirname=settings.SANDBOX_WORKSPACE_MOUNT_POINT,
                image_filename=settings.SANDBOX_WORKSPACE_SPARSE_IMAGE,
                size_bytes=int(settings.SANDBOX_MAX_DISK_USAGE_MB) * 1024 * 1024,
            )
            return root
        except Exception as e:
            logger.error(f"Failed to create workspace {workspace_id}: {e}")
            raise WorkspaceManagerError(
                f"Failed to create workspace {workspace_id}: {e}", status_code=500
            )

    async def cleanup_workspace(self, workspace_id: uuid.UUID) -> None:
        """Unmount loop device but keep workspace directory persistent."""
        try:
            root = self._workspace_root(workspace_id)
            # Only unmount and detach loop device, keep workspace files
            await WorkspaceProvisioner().unmount_and_detach(
                workspace_root=root,
                mount_point_dirname=settings.SANDBOX_WORKSPACE_MOUNT_POINT,
                image_filename=settings.SANDBOX_WORKSPACE_SPARSE_IMAGE,
            )
            logger.info(
                f"Unmounted loop device for workspace {workspace_id}, files remain persistent"
            )
        except Exception as e:
            logger.error(f"Failed to unmount workspace {workspace_id}: {e}")
            raise WorkspaceManagerError(
                f"Failed to unmount workspace {workspace_id}: {e}", status_code=500
            )
