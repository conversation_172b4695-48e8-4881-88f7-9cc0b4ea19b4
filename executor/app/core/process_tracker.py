import asyncio
import uuid
from datetime import datetime
from enum import Enum
from pathlib import Path


class ProcessPhase(str, Enum):
    IDLE = "idle"
    INSTALLING = "installing"
    INSTALL_COMPLETE = "install_complete"
    INSTALL_FAILED = "install_failed"
    STARTING = "starting"
    RUNNING = "running"
    FAILED = "failed"
    STOPPED = "stopped"


class ProcessTracker:
    def __init__(self, workspace_id: uuid.UUID, workspace_mount: Path):
        self.workspace_id = workspace_id
        self.workspace_mount = workspace_mount
        self.phase = ProcessPhase.IDLE
        self.install_proc: asyncio.subprocess.Process | None = None
        self.server_proc: asyncio.subprocess.Process | None = None
        self.logs: list[str] = []
        self.error: str | None = None
        self.install_started_at: datetime | None = None
        self.server_started_at: datetime | None = None
        self._log_lock = asyncio.Lock()

    async def add_log(self, message: str, level: str = "INFO"):
        async with self._log_lock:
            timestamp = datetime.now().isoformat()
            log_entry = f"[{timestamp}] [{level}] {message}"
            self.logs.append(log_entry)
            # Keep only last 200 lines
            if len(self.logs) > 200:
                self.logs = self.logs[-200:]

    async def start_install(self, template_subdir: str) -> bool:
        """Start the installation phase - runs directly in workspace"""
        if self.phase != ProcessPhase.IDLE:
            await self.add_log(f"Cannot start install from phase {self.phase}", "ERROR")
            return False

        self.phase = ProcessPhase.INSTALLING
        self.install_started_at = datetime.now()
        await self.add_log("Starting pnpm install...")

        # Build install command to run directly in workspace
        workspace_slidev_dir = self.workspace_mount / template_subdir

        try:
            self.install_proc = await asyncio.create_subprocess_exec(
                "bash",
                "-c",
                "echo 'Y' | pnpm install",
                cwd=workspace_slidev_dir,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.STDOUT,
            )

            await self.add_log(
                f"Install process started with PID: {self.install_proc.pid}"
            )

            # Start background task to monitor install
            asyncio.create_task(self._monitor_install_process())
            return True

        except Exception as e:
            self.phase = ProcessPhase.INSTALL_FAILED
            self.error = str(e)
            await self.add_log(f"Failed to start install: {e}", "ERROR")
            return False

    async def _monitor_install_process(self):
        """Monitor installation process and capture output"""
        if not self.install_proc or not self.install_proc.stdout:
            return

        try:
            # Read output line by line
            while True:
                line = await self.install_proc.stdout.readline()
                if not line:
                    break
                log_line = line.decode("utf-8", errors="ignore").strip()
                if log_line:
                    await self.add_log(f"[INSTALL] {log_line}")

            # Wait for process to complete
            await self.install_proc.wait()

            if self.install_proc.returncode == 0:
                self.phase = ProcessPhase.INSTALL_COMPLETE
                await self.add_log("Installation completed successfully")
            else:
                self.phase = ProcessPhase.INSTALL_FAILED
                self.error = (
                    f"Install failed with exit code {self.install_proc.returncode}"
                )
                await self.add_log(f"Installation failed: {self.error}", "ERROR")

        except Exception as e:
            self.phase = ProcessPhase.INSTALL_FAILED
            self.error = str(e)
            await self.add_log(f"Install monitoring error: {e}", "ERROR")

    async def start_smart(self, template_subdir: str, port: int) -> bool:
        """Smart start that installs first if needed, then starts server"""
        # If we're idle, start installation first
        if self.phase == ProcessPhase.IDLE:
            await self.add_log("Starting installation phase...")
            install_success = await self.start_install(template_subdir)
            if not install_success:
                return False

            # Wait for installation to complete
            while self.phase == ProcessPhase.INSTALLING:
                await asyncio.sleep(0.5)

            if self.phase != ProcessPhase.INSTALL_COMPLETE:
                await self.add_log("Installation failed, cannot start server", "ERROR")
                return False

        # If installation is complete, start server
        if self.phase == ProcessPhase.INSTALL_COMPLETE:
            return await self.start_server(template_subdir, port)

        # If already running, return success
        if self.phase == ProcessPhase.RUNNING:
            await self.add_log("Server is already running")
            return True

        await self.add_log(f"Cannot start from phase {self.phase}", "ERROR")
        return False

    async def start_server(self, template_subdir: str, port: int) -> bool:
        """Start the server phase - runs directly on host without sandbox"""
        if self.phase != ProcessPhase.INSTALL_COMPLETE:
            await self.add_log(f"Cannot start server from phase {self.phase}", "ERROR")
            return False

        self.phase = ProcessPhase.STARTING
        self.server_started_at = datetime.now()
        await self.add_log(f"Starting Slidev server on port {port}...")

        # Build simple command to run directly in workspace
        workspace_slidev_dir = self.workspace_mount / template_subdir

        try:
            self.server_proc = await asyncio.create_subprocess_exec(
                "pnpm",
                "exec",
                "slidev",
                "--port",
                str(port),
                "--remote",
                "--bind",
                "0.0.0.0",
                cwd=workspace_slidev_dir,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.STDOUT,
            )

            await self.add_log(
                f"Server process started with PID: {self.server_proc.pid}"
            )

            # Save PID for external status checks in root workspace directory
            pid_file = self.workspace_mount / "run.pid"
            pid_file.write_text(str(self.server_proc.pid))

            # Start background task to monitor server
            asyncio.create_task(self._monitor_server_process())

            # Give server time to start
            await asyncio.sleep(2)

            # Check if still running
            if self.server_proc.returncode is None:
                self.phase = ProcessPhase.RUNNING
                await self.add_log(f"Server is running on port {port}")
                return True
            else:
                self.phase = ProcessPhase.FAILED
                self.error = (
                    f"Server exited immediately with code {self.server_proc.returncode}"
                )
                await self.add_log(f"Server failed: {self.error}", "ERROR")
                return False

        except Exception as e:
            self.phase = ProcessPhase.FAILED
            self.error = str(e)
            await self.add_log(f"Failed to start server: {e}", "ERROR")
            return False

    async def _monitor_server_process(self):
        """Monitor server process and capture output"""
        if not self.server_proc or not self.server_proc.stdout:
            return

        try:
            # Read output line by line
            while True:
                line = await self.server_proc.stdout.readline()
                if not line:
                    break
                log_line = line.decode("utf-8", errors="ignore").strip()
                if log_line:
                    await self.add_log(f"[SERVER] {log_line}")

            # Wait for process to complete
            await self.server_proc.wait()

            if self.phase == ProcessPhase.RUNNING:
                self.phase = ProcessPhase.STOPPED
                await self.add_log("Server process stopped")

        except Exception as e:
            if self.phase == ProcessPhase.RUNNING:
                self.phase = ProcessPhase.FAILED
                self.error = str(e)
                await self.add_log(f"Server monitoring error: {e}", "ERROR")

    def is_server_running(self) -> bool:
        """Check if server process is still running"""
        return (
            self.phase == ProcessPhase.RUNNING
            and self.server_proc is not None
            and self.server_proc.returncode is None
        )

    async def stop(self):
        """Stop all processes"""
        if self.install_proc and self.install_proc.returncode is None:
            try:
                self.install_proc.terminate()
                await asyncio.wait_for(self.install_proc.wait(), timeout=5)
            except Exception:
                try:
                    self.install_proc.kill()
                except Exception:
                    pass

        if self.server_proc and self.server_proc.returncode is None:
            try:
                self.server_proc.terminate()
                await asyncio.wait_for(self.server_proc.wait(), timeout=5)
            except Exception:
                try:
                    self.server_proc.kill()
                except Exception:
                    pass

        self.phase = ProcessPhase.STOPPED
        await self.add_log("Stopped all processes")


# Global process trackers by workspace_id
_process_trackers: dict[str, ProcessTracker] = {}
_tracker_lock = asyncio.Lock()


async def get_process_tracker(
    workspace_id: uuid.UUID, workspace_mount: Path
) -> ProcessTracker:
    """Get or create a process tracker for a workspace"""
    workspace_key = str(workspace_id)

    async with _tracker_lock:
        if workspace_key not in _process_trackers:
            _process_trackers[workspace_key] = ProcessTracker(
                workspace_id, workspace_mount
            )

        return _process_trackers[workspace_key]


async def cleanup_process_tracker(workspace_id: uuid.UUID):
    """Clean up and remove a process tracker"""
    workspace_key = str(workspace_id)

    async with _tracker_lock:
        if workspace_key in _process_trackers:
            tracker = _process_trackers[workspace_key]
            await tracker.stop()
            del _process_trackers[workspace_key]
