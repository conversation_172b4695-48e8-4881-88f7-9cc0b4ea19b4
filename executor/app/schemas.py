import uuid
from enum import Enum

from pydantic import BaseModel


class CloudProvider(str, Enum):
    AWS = "AWS"
    GCP = "GCP"
    AZURE = "AZURE"


class CloudCredentials(BaseModel):
    provider: CloudProvider


class AWSCredentials(CloudCredentials):
    aws_access_key_id: str
    aws_secret_access_key: str
    aws_default_region: str


class GCPCredentials(CloudCredentials):
    service_account_key: str


class AzureCredentials(CloudCredentials):
    username: str
    password: str
    tenant: str


class SandboxResponse(BaseModel):
    return_code: int
    stdout: str
    stderr: str


class K8SCredentials(BaseModel):
    kubeconfig: str


class ScriptExecutionRequest(BaseModel):
    script: str
    workspace_id: uuid.UUID
    cloud_credentials: AWSCredentials | GCPCredentials | AzureCredentials | None = None
    k8s_credentials: K8SCredentials | None = None


class ScriptExecutionResponse(BaseModel):
    result: SandboxResponse


class RunCommandResponse(BaseModel):
    stdout: str
    stderr: str
    return_code: int


# Slidev Schemas
class SlidevCloneRequest(BaseModel):
    workspace_id: uuid.UUID
    template_subdir: str = "slidev"
    overwrite: bool = True


class SlidevCloneResponse(BaseModel):
    workspace_id: uuid.UUID
    target_dir: str


class SlidevRunRequest(BaseModel):
    workspace_id: uuid.UUID
    port_in_sandbox: int = 3000
    template_subdir: str = "slidev"


class SlidevRunResponse(BaseModel):
    workspace_id: uuid.UUID
    external_port: int
    sandbox_ip: str
    port_in_sandbox: int
    status: str
    phase: str
    message: str


class SlidevStatusResponse(BaseModel):
    workspace_id: uuid.UUID
    phase: str
    install_started_at: str | None = None
    server_started_at: str | None = None
    pid: int | None = None
    port_in_sandbox: int | None = None
    external_port: int | None = None
    sandbox_ip: str | None = None
    error: str | None = None
    is_server_running: bool = False


class SlidevLogsResponse(BaseModel):
    workspace_id: uuid.UUID
    logs: list[str]
    phase: str
