import asyncio
import json
import os
import shutil
import uuid
from pathlib import Path
from typing import Any, cast

from fastapi import APIRouter, HTTPException
from redis.asyncio.client import Redis as AsyncRedis

from app.core.process_tracker import (
    ProcessPhase,
    cleanup_process_tracker,
    get_process_tracker,
)
from app.core.sandbox.workspace.factory import create_workspace_manager
from app.logger import logger
from app.schemas import (
    SlidevCloneRequest,
    SlidevCloneResponse,
    SlidevLogsResponse,
    SlidevRunRequest,
    SlidevRunResponse,
    SlidevStatusResponse,
)

router = APIRouter(prefix="/sandbox-apps", tags=["sandbox-apps"])

# Use webapp port range (not blocked by iptables)
_BASE_PORT: int = 21000  # Different range for web apps
_PORT_STEP: int = 1
_NEXT_PORT: int = _BASE_PORT
_ALLOCATED: dict[str, int] = {}
_ALLOC_LOCK = asyncio.Lock()


async def _allocate_port(workspace_id: str) -> int:
    global _NEXT_PORT
    async with _ALLOC_LOCK:
        if workspace_id in _ALLOCATED:
            return _ALLOCATED[workspace_id]
        port = _NEXT_PORT
        _NEXT_PORT += _PORT_STEP
        _ALLOCATED[workspace_id] = port
        return port


def _get_workspace_manager():
    return create_workspace_manager()


def _get_redis() -> AsyncRedis:
    url = os.getenv("REDIS_URL")
    if not url:
        host = os.getenv("REDIS_SERVER", "redis")
        url = f"redis://{host}:6379"
    return AsyncRedis.from_url(url, decode_responses=True)


def _paths_for_template(
    workspace_mount: Path, template_subdir: str
) -> tuple[Path, Path]:
    template_src = Path(__file__).resolve().parents[2] / "templates" / template_subdir
    target_dir = workspace_mount / template_subdir
    return template_src, target_dir


@router.post("/slidev/clone", response_model=SlidevCloneResponse)
async def clone_slidev(req: SlidevCloneRequest) -> SlidevCloneResponse:
    """Clone the Slidev template into the workspace mount point."""
    workspace_manager = _get_workspace_manager()
    await workspace_manager.create_or_reuse_workspace(req.workspace_id)
    workspace_mount = workspace_manager.get_workspace_mount_point(req.workspace_id)

    template_src, target_dir = _paths_for_template(workspace_mount, req.template_subdir)
    if not template_src.exists():
        raise HTTPException(
            status_code=400, detail=f"Template not found: {template_src}"
        )

    if target_dir.exists():
        if not req.overwrite:
            return SlidevCloneResponse(
                workspace_id=req.workspace_id, target_dir=str(target_dir)
            )
        shutil.rmtree(target_dir)
    shutil.copytree(template_src, target_dir)

    return SlidevCloneResponse(
        workspace_id=req.workspace_id, target_dir=str(target_dir)
    )


@router.post("/slidev/run", response_model=SlidevRunResponse)
async def run_slidev(req: SlidevRunRequest) -> SlidevRunResponse:
    """Smart run that installs if needed, then starts Slidev server directly on host."""
    try:
        # Set up workspace
        workspace_manager = _get_workspace_manager()
        await workspace_manager.create_or_reuse_workspace(req.workspace_id)
        workspace_mount = workspace_manager.get_workspace_mount_point(req.workspace_id)

        # Verify template exists in workspace
        target_dir = workspace_mount / req.template_subdir
        if not target_dir.exists():
            raise HTTPException(
                status_code=400, detail="Template not cloned. Call /slidev/clone first."
            )

        # Get process tracker
        tracker = await get_process_tracker(req.workspace_id, workspace_mount)

        # Allocate a port for this workspace
        external_port = await _allocate_port(str(req.workspace_id))

        # Smart start (installs if needed, then starts server directly)
        success = await tracker.start_smart(req.template_subdir, external_port)

        if not success:
            return SlidevRunResponse(
                workspace_id=req.workspace_id,
                external_port=external_port,
                sandbox_ip="127.0.0.1",
                port_in_sandbox=external_port,
                status="failed",
                phase=tracker.phase.value,
                message=f"Failed to run: {tracker.error}",
            )

        # Register route in Redis only if server is running
        if tracker.phase == ProcessPhase.RUNNING:
            try:
                redis = _get_redis()
                await cast(Any, redis).ping()

                subdomain = str(req.workspace_id)
                route_key = f"route:{subdomain}"
                value = {
                    "target_host": "executor",
                    "target_port": external_port,
                    "ttl_ms": -1,
                    "workspace_id": str(req.workspace_id),
                }

                ok = await cast(Any, redis).set(route_key, json.dumps(value))
                if not ok:
                    raise RuntimeError("Failed to write route metadata to Redis")

                await cast(Any, redis).persist(route_key)
                stored = await cast(Any, redis).get(route_key)
                if not stored:
                    raise RuntimeError("Route not found after write to Redis")
            except Exception as e:
                logger.exception(f"Failed to set route in Redis: {e}")
                raise HTTPException(
                    status_code=500, detail="Failed to register route in Redis"
                )

        # Determine message based on phase
        if tracker.phase == ProcessPhase.INSTALLING:
            message = "Installation in progress..."
        elif tracker.phase == ProcessPhase.STARTING:
            message = "Server starting..."
        elif tracker.phase == ProcessPhase.RUNNING:
            message = f"Slidev server is running on port {external_port}"
        else:
            message = f"Current phase: {tracker.phase.value}"

        return SlidevRunResponse(
            workspace_id=req.workspace_id,
            external_port=external_port,
            sandbox_ip="127.0.0.1",
            port_in_sandbox=external_port,
            status="success"
            if tracker.phase == ProcessPhase.RUNNING
            else "in_progress",
            phase=tracker.phase.value,
            message=message,
        )

    except Exception as e:
        logger.error(f"Failed to run Slidev for {req.workspace_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/slidev/status/{workspace_id}", response_model=SlidevStatusResponse)
async def get_slidev_status(workspace_id: uuid.UUID) -> SlidevStatusResponse:
    """Get the current status of Slidev installation and server."""
    try:
        # Set up workspace
        workspace_manager = _get_workspace_manager()
        await workspace_manager.create_or_reuse_workspace(workspace_id)
        workspace_mount = workspace_manager.get_workspace_mount_point(workspace_id)

        # Get process tracker
        tracker = await get_process_tracker(workspace_id, workspace_mount)

        # Get port info if server is running
        external_port = None
        pid = None

        if tracker.is_server_running():
            try:
                external_port = await _allocate_port(str(workspace_id))
                if tracker.server_proc:
                    pid = tracker.server_proc.pid
            except Exception:
                pass

        return SlidevStatusResponse(
            workspace_id=workspace_id,
            phase=tracker.phase.value,
            install_started_at=tracker.install_started_at.isoformat()
            if tracker.install_started_at
            else None,
            server_started_at=tracker.server_started_at.isoformat()
            if tracker.server_started_at
            else None,
            pid=pid,
            port_in_sandbox=external_port if tracker.is_server_running() else None,
            external_port=external_port,
            sandbox_ip="127.0.0.1" if tracker.is_server_running() else None,
            error=tracker.error,
            is_server_running=tracker.is_server_running(),
        )

    except Exception as e:
        logger.error(f"Failed to get Slidev status for {workspace_id}: {e}")
        return SlidevStatusResponse(
            workspace_id=workspace_id,
            phase=ProcessPhase.FAILED.value,
            error=str(e),
            is_server_running=False,
        )


@router.get("/slidev/logs/{workspace_id}", response_model=SlidevLogsResponse)
async def get_slidev_logs(workspace_id: uuid.UUID) -> SlidevLogsResponse:
    """Get real-time logs from Slidev installation and server."""
    try:
        # Set up workspace
        workspace_manager = _get_workspace_manager()
        await workspace_manager.create_or_reuse_workspace(workspace_id)
        workspace_mount = workspace_manager.get_workspace_mount_point(workspace_id)

        # Get process tracker
        tracker = await get_process_tracker(workspace_id, workspace_mount)

        return SlidevLogsResponse(
            workspace_id=workspace_id,
            logs=tracker.logs.copy(),
            phase=tracker.phase.value,
        )

    except Exception as e:
        logger.error(f"Failed to get Slidev logs for {workspace_id}: {e}")
        return SlidevLogsResponse(
            workspace_id=workspace_id,
            logs=[f"Error getting logs: {str(e)}"],
            phase=ProcessPhase.FAILED.value,
        )


@router.delete("/slidev/stop/{workspace_id}")
async def stop_slidev(workspace_id: uuid.UUID):
    """Stop Slidev processes and clean up resources."""
    try:
        # Set up workspace
        workspace_manager = _get_workspace_manager()
        await workspace_manager.create_or_reuse_workspace(workspace_id)
        workspace_mount = workspace_manager.get_workspace_mount_point(workspace_id)

        # Get process tracker and stop processes
        tracker = await get_process_tracker(workspace_id, workspace_mount)
        await tracker.stop()

        # Clean up the tracker
        await cleanup_process_tracker(workspace_id)

        return {"message": "Slidev processes stopped successfully"}

    except Exception as e:
        logger.error(f"Failed to stop Slidev for {workspace_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))
