#!/bin/bash

# Apply sysctl changes
sysctl -p /etc/sysctl.conf

# Load saved iptables rules (if using netfilter-persistent)
netfilter-persistent start

# Enable forwarding for our custom network namespaces (********/24 - sandbox)
if ! iptables -t nat -C POSTROUTING -s ********/24 -o eth0 -j MASQUERADE 2>/dev/null; then
    iptables -t nat -A POSTROUTING -s ********/24 -o eth0 -j MASQUERADE
fi

# Allow established/related connections for our network namespaces
if ! iptables -C FORWARD -m conntrack --ctstate ESTABLISHED,RELATED -j ACCEPT 2>/dev/null; then
    iptables -A FORWARD -m conntrack --ctstate ESTABLISHED,RELATED -j ACCEPT
fi

# === SECURITY ISOLATION RULES ===
# IMPORTANT: Order matters - specific ACCEPT rules must come before general DROP rules

# === SANDBOX ISOLATION (********/24) ===
# Only block sandbox namespace networks, not webapp networks (10.0.0.0/24)
# IMPORTANT: Allow internet access FIRST, then block private networks

# 1. Allow outbound to internet FIRST (before blocking private networks)
if ! iptables -C FORWARD -s ********/24 -o eth0 -j ACCEPT 2>/dev/null; then
    iptables -I FORWARD -s ********/24 -o eth0 -j ACCEPT
fi

# 2. Block inter-sandbox communication (********/24 to ********/24)
if ! iptables -C FORWARD -s ********/24 -d ********/24 -j DROP 2>/dev/null; then
    iptables -I FORWARD -s ********/24 -d ********/24 -j DROP
fi

# 3. Block sandbox access to Docker internal networks (all 172.x.x.x)
if ! iptables -C FORWARD -s ********/24 -d *********/8 -j DROP 2>/dev/null; then
    iptables -I FORWARD -s ********/24 -d *********/8 -j DROP
fi

# 4. Block sandbox access to private networks (192.168.x.x)
if ! iptables -C FORWARD -s ********/24 -d ***********/16 -j DROP 2>/dev/null; then
    iptables -I FORWARD -s ********/24 -d ***********/16 -j DROP
fi

# 5. Block sandbox access to localhost/loopback (127.x.x.x)
if ! iptables -C FORWARD -s ********/24 -d *********/8 -j DROP 2>/dev/null; then
    iptables -I FORWARD -s ********/24 -d *********/8 -j DROP
fi

# 6. Block sandbox access to link-local addresses (169.254.x.x)
if ! iptables -C FORWARD -s ********/24 -d ***********/16 -j DROP 2>/dev/null; then
    iptables -I FORWARD -s ********/24 -d ***********/16 -j DROP
fi

# 7. Block sandbox access to multicast addresses (224.x.x.x)
if ! iptables -C FORWARD -s ********/24 -d *********/4 -j DROP 2>/dev/null; then
    iptables -I FORWARD -s ********/24 -d *********/4 -j DROP
fi

# NOTE: Web apps using 10.0.0.0/24 range are NOT blocked and can communicate with containers

# Save rules
netfilter-persistent save

# Start the FastAPI application
echo "Starting FastAPI application..."
exec "$@"
