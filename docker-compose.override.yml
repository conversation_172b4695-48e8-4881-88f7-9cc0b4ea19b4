services:
  # Local services are available on their ports, but also available on:
  # http://api.localhost.tiangolo.com: backend
  # http://dashboard.localhost.tiangolo.com: frontend
  # etc. To enable it, update .env, set:
  # DOMAIN=localhost.tiangolo.com
  db:
    restart: "no"
    ports:
      - "5432:5432"
  redis:
    restart: "no"
    ports:
      - "6379:6379"

  backend:
    restart: "no"
    ports:
      - "8000:8000"
    build:
      context: ./backend
    # command: sleep infinity  # Infinite loop to keep container alive doing nothing
    command:
      - fastapi
      - run
      - --reload
      - "app/main.py"
    develop:
      watch:
        - path: ./backend
          action: sync
          target: /app
          ignore:
            - ./backend/.venv
            - .venv
        - path: ./backend/pyproject.toml
          action: rebuild
    env_file:
      - .env
    # TODO: remove once coverage is done locally
    volumes:
      - ./backend/htmlcov:/app/htmlcov
      - ~/.aws/:/root/.aws:rw
    environment:
      PYTHONPATH: /app
      WATCHFILES_FORCE_POLLING: "true"
      SMTP_HOST: "mailcatcher"
      SMTP_PORT: "1025"
      SMTP_TLS: "false"
      SMTP_USER: ""
      SMTP_PASSWORD: ""
      SMTP_SSL: "false"
      EMAILS_FROM_EMAIL: "<EMAIL>"
      GROWTH_TEAM_EMAIL: "<EMAIL>"
      # OpenTelemetry batch size limits to prevent 413 errors
      OTEL_BSP_MAX_EXPORT_BATCH_SIZE: "64"
      OTEL_BSP_EXPORT_TIMEOUT: "30000"
      OTEL_BSP_MAX_QUEUE_SIZE: "256"

  worker:
    restart: "no"
    build:
      context: ./backend
    env_file:
      - .env
    volumes:
      - ~/.aws/:/root/.aws:rw
    environment:
      SMTP_HOST: "mailcatcher"
      SMTP_PORT: "1025"
      SMTP_TLS: "false"
      SMTP_USER: ""
      SMTP_PASSWORD: ""
      SMTP_SSL: "false"
      EMAILS_FROM_EMAIL: "<EMAIL>"
      GROWTH_TEAM_EMAIL: "<EMAIL>"

  executor:
    restart: "no"
    build:
      context: ./executor
    # command: sleep infinity  # Infinite loop to keep container alive doing nothing
    ports:
      - "8001:8000"
    command:
      - fastapi
      - run
      - --reload
      - "app/main.py"
    develop:
      watch:
        - path: ./executor
          action: sync
          target: /app
          ignore:
            - ./executor/.venv
            - .venv
        - path: ./backend/pyproject.toml
          action: rebuild

  payment:
    profiles:
      - payment
    restart: "no"
    build:
      context: ./payment
    ports:
      - "8002:8000"
    command:
      - fastapi
      - run
      - --reload
      - "app/main.py"
    develop:
      watch:
        - path: ./payment
          action: sync
          target: /app
          ignore:
            - ./payment/.venv
            - .venv
        - path: ./payment/pyproject.toml
          action: rebuild

  payment-prestart:
    profiles:
      - payment
    restart: "no"

  prestart:
    volumes:
      - ~/.aws/:/root/.aws:rw
    env_file:
      - .env
    restart: "no"

  mailcatcher:
    image: schickling/mailcatcher
    ports:
      - "1080:1080"
      - "1025:1025"

  # frontend:
  #   restart: "no"
  #   ports:
  #     - "5173:5173"
  #   build:
  #     context: ./frontend
  #     args:
  #       - NEXT_PUBLIC_API_URL=http://localhost:8000
  #       - NEXT_PUBLIC_RECAPTCHA_V3_SITE_KEY=${RECAPTCHA_V3_SITE_KEY}

  connection-manager:
    restart: "no"
    ports:
      - "3124:8000"
    build:
      context: ./connection-manager
    command:
      [
        "uv",
        "run",
        "uvicorn",
        "app.main:app",
        "--host",
        "0.0.0.0",
        "--port",
        "8000",
        "--reload",
      ]
    volumes:
      - ./connection-manager/app:/app/app
    develop:
      watch:
        - path: ./connection-manager
          action: sync
          target: /app
          ignore:
            - ./connection-manager/.venv
            - .venv
        - path: ./connection-manager/pyproject.toml
          action: rebuild

  # minio:
  #   restart: "no"
  #   ports:
  #     - "9000:9000" # API port
  #     - "9001:9001" # Console port

  slack-integration:
    restart: "no"
    ports:
      - "3000:3000"
    volumes:
      - ./slack-integration:/app
      - slack-integration-venv:/app/.venv
    command: uvicorn app:fastapi_app --host 0.0.0.0 --port 3000 --reload

  qdrant:
    restart: "no"
    ports:
      - 6333:6333
      - 6334:6334

  litellm:
    restart: "no"
    ports:
      - "4000:4000"
    volumes:
      - ~/.aws/:/root/.aws:rw
    environment:
      LITELLM_LOG: "DEBUG"

volumes:
  slack-integration-venv:
