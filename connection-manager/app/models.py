from mcp.types import CallToolResult, Tool
from pydantic import BaseModel


class ConnectionConfig(BaseModel):
    command: str
    args: list[str]
    env: dict[str, str] | None = None


class ListConnectionToolsRequest(BaseModel):
    connection_config: ConnectionConfig


class ListConnectionToolsResponse(BaseModel):
    tools: list[Tool]


class ExecuteConnectionToolRequest(BaseModel):
    tool_name: str
    arguments: dict
    connection_config: ConnectionConfig


class ExecuteConnectionToolResponse(BaseModel):
    tool_result: CallToolResult


class HealthResponse(BaseModel):
    """Health check response model."""

    status: str
    message: str
    version: str = "1.0.0"
