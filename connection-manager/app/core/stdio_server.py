import asyncio
from typing import Optional

from mcp import Client<PERSON>ession, StdioServerParameters
from mcp.client.stdio import stdio_client
from mcp.types import CallToolResult, ListToolsResult


class MCPStdioServer:
    """
    Represents an MCP server connected via stdio (local process).
    Supports cross-task disconnect requests via an asyncio.Event.
    """

    def __init__(self, params: StdioServerParameters):
        self.params = params
        self.client_ctx = None
        self.session: Optional[ClientSession] = None
        self._disconnect_event = asyncio.Event()
        self._main_task = None
        self._is_connected = False

    async def __aenter__(self):
        from app.core.config import settings

        asyncio.create_task(self.run())
        start_time = asyncio.get_event_loop().time()
        retry_count = 0
        max_retries = 3

        while not self._is_connected:
            if asyncio.get_event_loop().time() - start_time > settings.start_timeout:
                if retry_count < max_retries:
                    retry_count += 1
                    from app.logger import logger

                    logger.warning(f"Connection attempt {retry_count} failed, retrying...")
                    # Reset timer for retry
                    start_time = asyncio.get_event_loop().time()
                    continue
                else:
                    raise TimeoutError(
                        f"Connection timed out after {settings.start_timeout} seconds "
                        f"after {max_retries} retry attempts. "
                        f"Command: {self.params.command} {' '.join(self.params.args)}"
                    )
            await asyncio.sleep(0.01)
        return self

    async def __aexit__(self, *excinfo):
        await self.request_disconnect()
        if self._main_task:
            await self._main_task

    async def connect(self):
        """Connect to the server."""
        try:
            self.client_ctx = stdio_client(self.params)
            read, write = await self.client_ctx.__aenter__()
            self.session = await ClientSession(read, write).__aenter__()
            await self.session.initialize()
            self._is_connected = True
        except Exception as e:
            # Log the specific error for debugging
            from app.logger import logger

            logger.error(f"Failed to connect to MCP server: {e}")
            logger.error(f"Command: {self.params.command}")
            logger.error(f"Args: {self.params.args}")
            logger.error(f"Env keys: {list(self.params.env.keys()) if self.params.env else 'None'}")
            raise

    async def run(self):
        """Main server task: connect, then wait for disconnect event, then disconnect."""
        await self.connect()
        self._main_task = asyncio.current_task()
        try:
            await self._disconnect_event.wait()
        finally:
            await self.disconnect()

    async def request_disconnect(self):
        """Request disconnect from any task/context."""
        self._disconnect_event.set()

    async def disconnect(self):
        """Disconnect from the server (must be called from main task)."""
        # Disconnect from the server
        if self.session:
            await self.session.__aexit__(None, None, None)
            self.session = None
        if self.client_ctx:
            await self.client_ctx.__aexit__(None, None, None)
            self.client_ctx = None
        self._disconnect_event.clear()

    async def list_tools(self, cursor: str | None = None) -> ListToolsResult:
        if not self.session:
            raise RuntimeError("Not connected.")
        tools = await self.session.list_tools(cursor=cursor)
        return tools

    async def call_tool(self, tool_name: str, arguments: dict) -> CallToolResult:
        if not self.session:
            raise RuntimeError("Not connected.")
        tool_result = await self.session.call_tool(tool_name, arguments)
        return tool_result
