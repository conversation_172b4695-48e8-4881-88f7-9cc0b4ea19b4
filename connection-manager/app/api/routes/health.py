from fastapi import APIRouter, status

from app.models import HealthResponse

router = APIRouter()


@router.get("", response_model=HealthResponse, status_code=status.HTTP_200_OK)
async def health_check() -> HealthResponse:
    """
    Health check endpoint for monitoring and container orchestration.

    Returns:
        HealthResponse: Health check response with status, message, and version.
    """
    return HealthResponse(
        status="ok",
        message="Connection Manager service is running",
    )
