map $http_upgrade $connection_upgrade {
    default upgrade;
    '' close;
}

log_format proxy_combined '$remote_addr - $host [$time_local] '
                       '"$request" $status $body_bytes_sent '
                       '"$http_referer" "$http_user_agent" '
                       'upstream=$upstream_addr upstream_status=$upstream_status '
                       'req_time=$request_time up_resp_time=$upstream_response_time '
                       'req_id=$request_id';

server {
  listen 80;
  server_name  *.localhost localhost;
  access_log /var/log/nginx/access.log proxy_combined;
  error_log  /var/log/nginx/error.log info;

  # Forward any *.localhost to executor-proxy (HTTP + WebSocket)
  location / {
    proxy_set_header Host $host;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Request-Id $request_id;
    add_header X-Request-Id $request_id;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection $connection_upgrade;
    proxy_pass http://executor-proxy:8080;
  }
}
