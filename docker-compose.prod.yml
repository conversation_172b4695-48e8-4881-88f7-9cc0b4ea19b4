# Production docker-compose override for ECR images
# This file should be used in production deployments with ECR images

services:
  connection-manager:
    image: "${DOCKER_IMAGE_CONNECTION_MANAGER?Variable not set}:${TAG-latest}"
    build: null  # Disable build in production
  prestart:
    image: "${DOCKER_IMAGE_BACKEND?Variable not set}:${TAG-latest}"
    build: null  # Disable build in production

  backend:
    image: "${DOCKER_IMAGE_BACKEND?Variable not set}:${TAG-latest}"
    build: null  # Disable build in production

  scheduler:
    image: "${DOCKER_IMAGE_BACKEND?Variable not set}:${TAG-latest}"
    build: null  # Disable build in production

  worker:
    image: "${DOCKER_IMAGE_BACKEND?Variable not set}:${TAG-latest}"
    build: null  # Disable build in production

  executor:
    image: "${DOCKER_IMAGE_EXECUTOR?Variable not set}:${TAG-latest}"
    build: null  # Disable build in production

  frontend:
    image: "${DOCKER_IMAGE_FRONTEND?Variable not set}:${TAG-latest}"
    build: null  # Disable build in production

  payment:
    image: "${DOCKER_IMAGE_PAYMENT?Variable not set}:${TAG-latest}"
    build: null  # Disable build in production

  payment-prestart:
    image: "${DOCKER_IMAGE_PAYMENT?Variable not set}:${TAG-latest}"
    build: null  # Disable build in production

  slack-integration:
    image: "${DOCKER_IMAGE_SLACK_INTEGRATION:-slack-integration}:${TAG-latest}"
    build: null  # Disable build in production
