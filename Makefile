# CloudThinker Database Management Makefile

# Load environment variables from .env file
ifneq (,$(wildcard ./.env))
    include .env
    export
endif

# Database connection variables (loaded from .env or defaults)
DB_CONTAINER_NAME ?= cloud-cost-optimization-db-1
DB_USER ?= $(POSTGRES_USER)
DB_NAME ?= $(POSTGRES_DB)
DB_PASSWORD ?= $(POSTGRES_PASSWORD)

# Colors for output
GREEN = \033[0;32m
YELLOW = \033[1;33m
BLUE = \033[0;34m
RED = \033[0;31m
NC = \033[0m # No Color

# Check if formatting tools are available
PGCLI_AVAILABLE := $(shell command -v pgcli 2> /dev/null)
JQ_AVAILABLE := $(shell command -v jq 2> /dev/null)
COLUMN_AVAILABLE := $(shell command -v column 2> /dev/null)

.PHONY: run-sql db-logs

run-sql: ## Run SQL optimized for AI agents (clean JSON, no extra output)
	@if [ -z "$(SQL)" ]; then \
		echo "$(YELLOW)Usage: make run-sql SQL=\"YOUR_SQL_COMMAND_HERE\"$(NC)" >&2; \
		echo "$(YELLOW)Example: make run-sql SQL=\"SELECT * FROM users LIMIT 5\"$(NC)" >&2; \
		exit 1; \
	fi
	@CLEAN_SQL=$$(echo "$(SQL)" | sed 's/;$$//'); \
	docker exec -i $(DB_CONTAINER_NAME) psql -U $(DB_USER) -d $(DB_NAME) -t -A -c "SELECT COALESCE(json_agg(row_to_json(t)), '[]'::json) FROM ($$CLEAN_SQL) t;" 2>/dev/null | \
	if [ -n "$(JQ_AVAILABLE)" ]; then \
		jq -c '.'; \
	else \
		cat; \
	fi

db-logs: ## Show database container logs
	@echo "$(GREEN)Showing database logs...$(NC)"
	@docker logs $(DB_CONTAINER_NAME) --tail 50 -f
