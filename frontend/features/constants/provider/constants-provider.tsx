'use client';

import { PropsWithChildren, createContext, useContext } from 'react';

import { SchemaQuickStartTemplatesPublic } from '@/openapi-ts/gens';

type ConstantsContextValue = {
  constants: SchemaQuickStartTemplatesPublic;
};

const ConstantsContext = createContext<ConstantsContextValue | null>(null);

export const ConstantsProvider = ({
  children,
  constants,
}: PropsWithChildren<ConstantsContextValue>) => {
  return (
    <ConstantsContext.Provider value={{ constants }}>
      {children}
    </ConstantsContext.Provider>
  );
};

export const useConstantsContext = () => {
  const context = useContext(ConstantsContext);
  if (!context) {
    throw new Error(
      'useConstantsContext must be used within an ConstantsProvider',
    );
  }
  return context;
};
