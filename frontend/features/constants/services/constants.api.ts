import { ETagCache } from '@/constants/tag-cache';
import { api, fetchData } from '@/openapi-ts/openapi-fetch';
import { PathsRequestQueryDto } from '@/openapi-ts/utils.type';

export const constantsApi = {
  getStartTemplates: () =>
    fetchData(
      api.GET('/api/v1/utils/constants/quick_start_templates', {
        next: { tags: [ETagCache.CONSTANTS] },
      }),
    ),

  getExamplePrompts: (
    query: PathsRequestQueryDto<'/api/v1/utils/constants/example_prompts'>,
  ) =>
    fetchData(
      api.GET('/api/v1/utils/constants/example_prompts', {
        params: { query },
      }),
    ),
};
