'use client';

import { useEffect, useState } from 'react';

import { Button } from '@/components/ui/button';
import CronBuilder from '@/components/ui/cron-builder';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import cron from 'cron-validate';
import cronstrue from 'cronstrue';
import { AlertTriangle, CheckCircle, Clock, PencilIcon } from 'lucide-react';

interface CronExpressionInputProps {
  value?: string;
  onChange: (value: string) => void;
  onValidityChange?: (isValid: boolean) => void;
  disabled?: boolean;
  placeholder?: string;
}

export function CronExpressionInput({
  value,
  onChange,
  onValidityChange,
  disabled,
  placeholder,
}: CronExpressionInputProps) {
  const [open, setOpen] = useState(false);
  const [humanReadable, setHumanReadable] = useState<string>('');
  const [isValid, setIsValid] = useState<boolean>(true);
  const [validationError, setValidationError] = useState<string>('');

  const validateCronExpression = (expression: string) => {
    if (!expression) {
      setIsValid(false);
      setHumanReadable('');
      setValidationError('Cron expression is required');
      onValidityChange?.(false);
      return;
    }

    try {
      const cronResult = cron(expression);

      if (!cronResult.isValid()) {
        setIsValid(false);
        setHumanReadable('');
        setValidationError(cronResult.getError().join(', '));
        onValidityChange?.(false);
        return;
      }

      // Generate human readable text
      const readable = cronstrue.toString(expression, {
        verbose: true,
        use24HourTimeFormat: true,
      });

      setIsValid(true);
      setHumanReadable(readable);
      setValidationError('');
      onValidityChange?.(true);
    } catch {
      setIsValid(false);
      setHumanReadable('');
      setValidationError('Invalid cron expression format');
      onValidityChange?.(false);
    }
  };

  useEffect(() => {
    validateCronExpression(value || '');
  }, [value]);

  return (
    <div className="space-y-2">
      <div className="relative">
        <div className="relative flex items-center">
          <Input
            value={value}
            onChange={(e) => onChange(e.target.value)}
            disabled={disabled}
            placeholder={placeholder || '0 0 * * * (At 00:00)'}
            className={cn(
              'pr-20',
              !isValid && value && 'border-red-500 focus-visible:ring-red-500',
            )}
          />
          <div className="absolute right-0 flex items-center gap-1 px-2">
            {value && (
              <div className="flex items-center">
                {isValid ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <AlertTriangle className="h-4 w-4 text-red-600" />
                )}
              </div>
            )}
            <Button
              type="button"
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={() => setOpen(true)}
            >
              <PencilIcon className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Human readable display */}
      {value && (
        <div className="space-y-1">
          {isValid && humanReadable && (
            <div className="flex items-start gap-2 rounded-lg border border-green-200 bg-green-50 p-3 dark:border-green-800 dark:bg-green-950/20">
              <Clock className="mt-0.5 h-4 w-4 shrink-0 text-green-600" />
              <div>
                <p className="text-sm font-medium text-green-800 dark:text-green-200">
                  Schedule: {humanReadable}
                </p>
                <p className="mt-1 text-xs text-green-600 dark:text-green-400">
                  Cron expression: {value}
                </p>
              </div>
            </div>
          )}

          {!isValid && validationError && (
            <div className="flex items-start gap-2 rounded-lg border border-red-200 bg-red-50 p-3 dark:border-red-800 dark:bg-red-950/20">
              <AlertTriangle className="mt-0.5 h-4 w-4 shrink-0 text-red-600" />
              <div>
                <p className="text-sm font-medium text-red-800 dark:text-red-200">
                  Invalid cron expression
                </p>
                <p className="mt-1 text-xs text-red-600 dark:text-red-400">
                  {validationError}
                </p>
              </div>
            </div>
          )}
        </div>
      )}

      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Cron Expression Builder</DialogTitle>
            <DialogDescription>
              Create a schedule using cron syntax. Similar to{' '}
              <a
                href="https://crontab.guru"
                target="_blank"
                rel="noopener noreferrer"
                className="text-primary hover:underline"
              >
                crontab.guru
              </a>
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* Quick examples */}
            <div className="rounded-1xl bg-muted/50 p-4">
              <h4 className="mb-2 text-sm font-medium">Quick Examples:</h4>
              <div className="grid grid-cols-1 gap-2 text-xs md:grid-cols-2">
                <div className="flex justify-between">
                  <code className="bg-background rounded px-2 py-1">
                    0 0 * * *
                  </code>
                  <span className="text-muted-foreground">
                    Daily at midnight
                  </span>
                </div>
                <div className="flex justify-between">
                  <code className="bg-background rounded px-2 py-1">
                    0 12 * * *
                  </code>
                  <span className="text-muted-foreground">Daily at noon</span>
                </div>
                <div className="flex justify-between">
                  <code className="bg-background rounded px-2 py-1">
                    0 * * * *
                  </code>
                  <span className="text-muted-foreground">Every hour</span>
                </div>
                <div className="flex justify-between">
                  <code className="bg-background rounded px-2 py-1">
                    */30 * * * *
                  </code>
                  <span className="text-muted-foreground">
                    Every 30 minutes
                  </span>
                </div>
              </div>
            </div>

            <CronBuilder
              onSubmit={(val) => {
                onChange(val);
                setOpen(false);
              }}
              showResultText={true}
              showResultCron={true}
              options={{
                headers: ['MINUTES', 'HOURLY', 'DAILY', 'WEEKLY', 'MONTHLY'],
              }}
            />
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
