import {
  SchemaMcpConnectionCreate,
  SchemaMcpConnectionUpdate,
} from '@/openapi-ts/gens';
import { createQueryKeys } from '@lukemorales/query-key-factory';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

import { mcpConnectionApi } from '../services/mcp-connection.api';

const mcpConnectionQueryKeys = createQueryKeys('mcp-connections', {
  list: {
    queryKey: null,
    queryFn: () => mcpConnectionApi.list(),
  },
});

const useList = () => {
  return useQuery(mcpConnectionQueryKeys.list);
};

const useCreate = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (params: { mcp_conn_create: SchemaMcpConnectionCreate }) =>
      mcpConnectionApi.create(params.mcp_conn_create),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: mcpConnectionQueryKeys.list.queryKey,
      });
      toast.success('MCP connection created successfully');
    },
  });
};

const useUpdate = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (params: {
      mcp_conn_id: string;
      mcp_conn_update: SchemaMcpConnectionUpdate;
    }) => mcpConnectionApi.update(params.mcp_conn_id, params.mcp_conn_update),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: mcpConnectionQueryKeys.list.queryKey,
      });
      toast.success('MCP connection updated successfully');
    },
  });
};

const useDelete = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (params: { mcp_conn_id: string }) =>
      mcpConnectionApi.delete(params.mcp_conn_id),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: mcpConnectionQueryKeys.list.queryKey,
      });
      toast.success('MCP connection deleted successfully');
    },
  });
};

const useUpdateTool = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (params: {
      mcp_conn_id: string;
      tool_name: string;
      is_enabled: boolean;
      is_required_permission: boolean;
    }) =>
      mcpConnectionApi.updateTool(
        params.mcp_conn_id,
        params.tool_name,
        params.is_enabled,
        params.is_required_permission,
      ),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: mcpConnectionQueryKeys.list.queryKey,
      });
      toast.success('MCP connection tools updated successfully');
    },
  });
};

export const mcpConnectionQuery = {
  query: {
    useList,
  },
  mutation: {
    useCreate,
    useUpdate,
    useDelete,
    useUpdateTool,
  },
};
