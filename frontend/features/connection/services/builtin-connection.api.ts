import { api, fetchData } from '@/openapi-ts/openapi-fetch';
import { SchemaConnectionEnvConfig } from '@/openapi-ts/gens';

  export const builtinConnectionApi = {

    list: () => fetchData(api.GET('/api/v1/builtin-connection')),
    install: (builtin_id: string, env: SchemaConnectionEnvConfig[]) =>
      fetchData(
        api.POST('/api/v1/builtin-connection/{builtin_id}', {
          params: { path: { builtin_id } },
          body: { env },
        })
      ),
    update: (builtin_id: string, env: SchemaConnectionEnvConfig[]) =>
      fetchData(
        api.PATCH('/api/v1/builtin-connection/{builtin_id}', {
          params: { path: { builtin_id } },
          body: { env },
        })
      ),
    delete: (builtin_id: string) =>
      fetchData(
        api.DELETE('/api/v1/builtin-connection/{builtin_id}', {
          params: { path: { builtin_id } },
        })
      ),
    updateTool: (builtin_id: string, tool_name: string, is_enabled: boolean, is_required_permission: boolean) =>
      fetchData(
        api.PATCH('/api/v1/builtin-connection/{builtin_id}/tool', {
          params: { path: { builtin_id } },
          body: { tool_name, is_enabled, is_required_permission },
        })
      ),
  };
