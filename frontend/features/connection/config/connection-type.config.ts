import { ConnectionType } from '@/openapi-ts/gens';
import { createUtilityConfig } from '@/utils/option-config';

export const CONNECTION_TYPE_CONFIG = createUtilityConfig({
  [ConnectionType.builtin]: {
    label: 'Builtin Connections',
    description: 'System-provided connection that can be installed',
    color: 'blue',
  },
  [ConnectionType.mcp]: {
    label: 'MCP Connections',
    description: 'Model Context Protocol connections',
    color: 'green',
  },
} satisfies Record<
  ConnectionType,
  {
    label: string;
    description: string;
    color: string;
  }
>);
