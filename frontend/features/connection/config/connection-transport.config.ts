import { MCPTransportType } from '@/openapi-ts/gens';
import { createUtilityConfig } from '@/utils/option-config';

export const CONNECTION_TRANSPORT_CONFIG = createUtilityConfig({
  [MCPTransportType.streamable_http]: {
    label: 'Streamable HTTP',
    description: 'HTTP-based streaming connection',
    protocol: 'HTTP',
    supportsStreaming: true,
  },
  [MCPTransportType.sse]: {
    label: 'Server-Sent Events',
    description: 'Server-sent events connection',
    protocol: 'SSE',
    supportsStreaming: true,
  },
} satisfies Record<MCPTransportType, {
  label: string;
  description: string;
  protocol: string;
  supportsStreaming: boolean;
}>);
