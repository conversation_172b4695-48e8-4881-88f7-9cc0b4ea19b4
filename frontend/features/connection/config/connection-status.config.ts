import { BadgeProps } from '@/components/ui/badge';
import { ConnectionStatus } from '@/openapi-ts/gens';
import { createUtilityConfig } from '@/utils/option-config';

export const CONNECTION_STATUS_CONFIG = createUtilityConfig({
  [ConnectionStatus.connected]: {
    label: 'Connected',
    variant: 'success',
  },
  [ConnectionStatus.error]: {
    label: 'Error',
    variant: 'destructive',
  },
} satisfies Record<ConnectionStatus, { label: string; variant: BadgeProps['variant'] }>);