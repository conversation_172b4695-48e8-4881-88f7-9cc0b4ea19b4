'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Server,
  Edit,
  Trash2
} from 'lucide-react';
import { SchemaMcpConnectionPublic, SchemaMcpConnectionUpdate } from '@/openapi-ts/gens';
import { BaseConnectionCard } from './shared/base-connection-card';
import { ConnectionConfirmDialog } from './shared/connection-confirm-dialog';
import { ConnectionTools, type ConnectionTool } from './shared/connection-tools';
import { MCPConnectionConfigDialog } from './mcp-connection-config-dialog';

// Types for the UI component
export interface MCPConnectionCardUIProps {
  readonly connection: SchemaMcpConnectionPublic;
  readonly className?: string;
  readonly onUpdate: (connectionId: string, data: SchemaMcpConnectionUpdate) => Promise<void>;
  readonly onDelete: (connectionId: string) => Promise<void>;
  readonly onToolToggle?: (connectionId: string, toolName: string, enabled: boolean) => void;
  readonly onPermissionToggle?: (connectionId: string, toolName: string, requiresPermission: boolean) => void;
  readonly togglingTools?: Set<string>;
}

export function MCPConnectionCardUI({
  connection,
  className,
  onUpdate,
  onDelete,
  onToolToggle,
  onPermissionToggle,
  togglingTools = new Set(),
}: MCPConnectionCardUIProps) {
  // Header component
  const header = (
    <div className="flex items-center justify-between px-4 py-3">
      <div className="flex items-center gap-3">
        <div className="flex items-center justify-center w-10 h-10 rounded-lg bg-muted">
          <Server className="h-5 w-5 text-muted-foreground" />
        </div>
        <div className="flex flex-col">
          <h3 className="text-sm font-medium text-foreground">
            {connection.name}
          </h3>
          <Badge variant="secondary" className="w-fit text-xs mt-1 bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300">
            MCP Server
          </Badge>
        </div>
      </div>
    </div>
  );

  // Footer component - MCP connections are always "connected" when they exist
  const footer = (
    <div className="flex items-center gap-2">
      <MCPConnectionConfigDialog
        connection={connection}
        onUpdate={onUpdate}
        mode="edit"
      >
        <Button
          variant="outline"
          size="sm"
          className="flex items-center gap-2"
          aria-label={`Edit ${connection.name} connection`}
        >
          <Edit className="h-4 w-4" />
          Edit
        </Button>
      </MCPConnectionConfigDialog>

      <ConnectionConfirmDialog
        type="delete"
        connectionName={connection.name}
        onConfirm={() => onDelete(connection.id)}
      >
        <Button
          variant="outline"
          size="sm"
          className="flex items-center gap-2 text-destructive hover:text-destructive-foreground hover:bg-destructive"
          aria-label={`Delete ${connection.name} connection`}
        >
          <Trash2 className="h-4 w-4" />
          Delete
        </Button>
      </ConnectionConfirmDialog>
    </div>
  );

  // Convert connection tools to the expected format
  const tools: ConnectionTool[] = (connection.tools || []).map(tool => {
    return {
      name: tool.name as string,
      is_enabled: typeof tool.is_enabled === 'boolean' ? tool.is_enabled : false,
      is_required_permission: typeof tool.is_required_permission === 'boolean' ? tool.is_required_permission : false,
    };
  });

  // Main content - show tools since MCP connections are always active
  const content = (
    <div className="px-4 py-3">
      <ConnectionTools
        tools={tools}
        isConnected={true} // MCP connections are always "connected"
        onToolToggle={onToolToggle ? (toolName, enabled) => onToolToggle(connection.id, toolName, enabled) : undefined}
        onPermissionToggle={onPermissionToggle ? (toolName, requiresPermission) => onPermissionToggle(connection.id, toolName, requiresPermission) : undefined}
        isTogglingTool={(toolName) => togglingTools.has(toolName)}
        maxToolsDisplay={4}
        showExpandButton={tools.length > 4}
        connectionName={connection.name}
      />
    </div>
  );

  return (
    <BaseConnectionCard
      header={header}
      footer={footer}
      className={className}
    >
      {content}
    </BaseConnectionCard>
  );
}
