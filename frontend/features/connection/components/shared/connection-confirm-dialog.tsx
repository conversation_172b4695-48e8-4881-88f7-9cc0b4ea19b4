'use client';

import { useMemo } from 'react';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { SpinnerContainer } from '@/components/ui/spinner-container';
import { cn } from '@/lib/utils';
import { AlertTriangle, Loader2 } from 'lucide-react';
import { useToggle } from 'usehooks-ts';

/**
 * Configuration for different types of confirmation dialogs
 */
export type ConfirmationDialogType = 'delete' | 'disconnect';

type ConfirmationConfig = {
  icon: React.ComponentType<{ className?: string }>;
  title: string;
  description: string;
  confirmText: string;
  loadingText: string;
  variant: 'destructive' | 'default';
};

const CONFIRMATION_CONFIGS: Record<ConfirmationDialogType, ConfirmationConfig> =
  {
    delete: {
      icon: AlertTriangle,
      title: 'Delete Connection',
      description:
        'This action cannot be undone. This will permanently delete the connection and remove all associated data.',
      confirmText: 'Delete Connection',
      loadingText: 'Deleting...',
      variant: 'destructive',
    },
    disconnect: {
      icon: AlertTriangle,
      title: 'Disconnect Connection',
      description:
        'This will remove the connection from your workspace. You can reconnect it later if needed.',
      confirmText: 'Disconnect Connection',
      loadingText: 'Disconnecting...',
      variant: 'destructive',
    },
  };

/**
 * Props for the ConnectionConfirmDialog component
 */
interface ConnectionConfirmDialogProps {
  children: React.ReactNode | ((toggle: () => void) => React.ReactNode);
  type?: ConfirmationDialogType;
  connectionName?: string;
  title?: string;
  description?: string;
  confirmText?: string;
  loadingText?: string;
  onConfirm: (() => void) | ((toggle: () => void) => void);
  loading?: boolean;
  isLoading?: boolean;
  variant?: 'destructive' | 'default';
  showIcon?: boolean;
}

/**
 * A reusable confirmation dialog component for connection actions
 *
 * This component provides a consistent way to show confirmation dialogs for connection-related actions
 * like delete, disconnect, etc. It uses the useToggle hook internally to manage state without requiring
 * external state management.
 *
 * @example
 * ```tsx
 * // Delete confirmation with default config
 * <ConnectionConfirmDialog
 *   type="delete"
 *   connectionName="My Connection"
 *   onConfirm={(toggle) => handleDelete(toggle)}
 *   loading={isDeleting}
 * >
 *   <Button variant="destructive">Delete</Button>
 * </ConnectionConfirmDialog>
 *
 * // Custom confirmation
 * <ConnectionConfirmDialog
 *   title="Custom Action"
 *   description="Are you sure you want to do this?"
 *   confirmText="Do It"
 *   onConfirm={(toggle) => handleAction(toggle)}
 *   loading={isProcessing}
 * >
 *   {(toggle) => <Button onClick={toggle}>Custom Action</Button>}
 * </ConnectionConfirmDialog>
 * ```
 */
export function ConnectionConfirmDialog({
  children,
  type = 'delete',
  connectionName,
  title,
  description,
  confirmText,
  loadingText,
  onConfirm,
  loading,
  isLoading,
  variant,
  showIcon = true,
}: ConnectionConfirmDialogProps) {
  // Use the useToggle hook to manage the open/closed state of the dialog
  const [open, toggle] = useToggle(false);

  // Support both loading and isLoading props for compatibility
  const isProcessing = loading || isLoading || false;

  // Get the configuration for the dialog type
  const config = CONFIRMATION_CONFIGS[type];

  // Use custom props or fall back to config defaults
  const dialogTitle = title || config.title;
  const dialogConfirmText = confirmText || config.confirmText;
  const dialogLoadingText = loadingText || config.loadingText;
  const dialogVariant = variant || config.variant;
  const Icon = config.icon;

  // Use the provided description directly if it's custom, otherwise format with connection name
  const formattedDescription =
    description ||
    (connectionName
      ? `Are you sure you want to ${type} "${connectionName}"?\n\n${config.description}`
      : config.description);

  // Handle onConfirm function - support both patterns
  const handleConfirm = () => {
    if (onConfirm.length > 0) {
      // Function expects toggle parameter
      (onConfirm as (toggle: () => void) => void)(toggle);
    } else {
      // Function expects no parameters
      (onConfirm as () => void)();
      // Auto-close the dialog after action
      setTimeout(() => toggle(), 100);
    }
  };

  // Handle both direct children and render prop patterns
  const child = useMemo(
    () => (typeof children === 'function' ? children(toggle) : children),
    [children, toggle],
  );

  return (
    <AlertDialog open={open} onOpenChange={toggle}>
      <AlertDialogTrigger asChild>{child}</AlertDialogTrigger>

      <AlertDialogContent
        onEscapeKeyDown={(e) => isProcessing && e.preventDefault()}
      >
        <SpinnerContainer loading={isProcessing}>
          <AlertDialogHeader>
            <div className="flex items-center gap-2">
              {showIcon && <Icon className="text-destructive h-5 w-5" />}
              <AlertDialogTitle>{dialogTitle}</AlertDialogTitle>
            </div>
            <AlertDialogDescription className="text-muted-foreground whitespace-pre-line">
              {formattedDescription}
            </AlertDialogDescription>
          </AlertDialogHeader>

          <AlertDialogFooter>
            <AlertDialogCancel disabled={isProcessing}>
              Cancel
            </AlertDialogCancel>

            <AlertDialogAction
              type="button"
              onClick={handleConfirm}
              disabled={isProcessing}
              className={cn(
                dialogVariant === 'destructive' &&
                  'bg-destructive text-destructive-foreground hover:bg-destructive/90',
              )}
            >
              {isProcessing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {dialogLoadingText}
                </>
              ) : (
                dialogConfirmText
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </SpinnerContainer>
      </AlertDialogContent>
    </AlertDialog>
  );
}
