'use client';

import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { ReactNode } from 'react';

// Types for the base connection card
export interface BaseConnectionCardProps {
  readonly header: ReactNode;
  readonly footer: ReactNode;
  readonly children?: ReactNode;
  readonly className?: string;
}

export function BaseConnectionCard({
  header,
  footer,
  children,
  className,
}: BaseConnectionCardProps) {
  return (
    <Card className={cn("relative flex flex-col h-full", className)}>
      {/* Card Header - Customizable */}
      <CardHeader className="p-0 flex-shrink-0">
        {header}
      </CardHeader>

      <CardContent className="flex flex-col flex-grow px-4 pb-3 pt-0">
        {/* Main Content Area */}
        <div className="flex-grow">
          {children}
        </div>

        {/* Card Footer - Customizable */}
        <div className="flex items-center justify-end pt-3 mt-auto">
          <div className="flex items-center gap-2">
            {footer}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
