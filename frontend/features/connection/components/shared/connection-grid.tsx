'use client';

import { ReactNode } from 'react';

// Shared grid component that both builtin and MCP connection lists can use
export interface ConnectionGridProps {
  readonly children: ReactNode;
  readonly className?: string;
}

export function ConnectionGrid({ children, className = '' }: ConnectionGridProps) {
  return (
    <div className={`grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 auto-rows-min ${className}`}>
      {children}
    </div>
  );
}
