'use client';

import { useNavigationWarning } from '../hooks/use-navigation-warning';

interface ChatNavigationGuardProps {
  children: React.ReactNode;
}

/**
 * Component that provides navigation warning functionality for chat sessions.
 * Must be used inside a ChatProvider to access the streaming state.
 */
export function ChatNavigationGuard({ children }: ChatNavigationGuardProps) {
  // Navigation warning for active streams
  useNavigationWarning({
    enabled: true,
    beforeUnloadMessage:
      'You have an active conversation in progress. Are you sure you want to leave?',
    onNavigationAttempt: () => {
      console.log('User attempted to navigate away during streaming');
    },
    onNavigationConfirmed: () => {
      console.log('User confirmed navigation despite active streaming');
    },
  });

  return <>{children}</>;
}
