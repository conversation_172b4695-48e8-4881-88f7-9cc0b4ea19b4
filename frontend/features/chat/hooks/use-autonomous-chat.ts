import { useCallback, useEffect, useReducer, useRef, useState } from 'react';

import { useRouter, useSearchParams } from 'next/navigation';

import { conversationUrlUtils } from '@/features/conversation/utils/conversation-url.utils';
import { useMessageStream } from '@/hooks/use-autonomous-message-stream';

import { useStreamReconnection } from './use-stream-reconnection';

interface ChatState {
  selectedConversation: string | null;
  mounted: boolean;
  initialMessageSent: boolean;
}

type ChatAction =
  | { type: 'SET_MOUNTED'; payload: boolean }
  | { type: 'SET_SELECTED_CONVERSATION'; payload: string | null }
  | { type: 'SET_INITIAL_MESSAGE_SENT'; payload: boolean };

const initialState: ChatState = {
  selectedConversation: null,
  mounted: false,
  initialMessageSent: false,
};

const chatReducer = (state: ChatState, action: ChatAction): ChatState => {
  switch (action.type) {
    case 'SET_MOUNTED':
      return { ...state, mounted: action.payload };
    case 'SET_SELECTED_CONVERSATION':
      return { ...state, selectedConversation: action.payload };
    case 'SET_INITIAL_MESSAGE_SENT':
      return { ...state, initialMessageSent: action.payload };
    default:
      return state;
  }
};

interface AutonomousChatProps {
  conversationId?: string;
}

export const useAutonomousChat = ({ conversationId }: AutonomousChatProps) => {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [state, dispatch] = useReducer(chatReducer, {
    ...initialState,
    selectedConversation: conversationId || null,
  });

  // Handle conversation creation callback
  const handleConversationCreated = useCallback(
    (newConversationId: string) => {
      // Only update the URL, don't update selectedConversation state
      // This prevents triggering the useEffect that clears streaming messages
      const newUrl = conversationUrlUtils.updateUrlWithConversationId(
        window.location.pathname,
        searchParams,
        newConversationId,
      );
      router.replace(newUrl, { scroll: false });
    },
    [router, searchParams],
  );

  const messageStream = useMessageStream(state.selectedConversation, {
    onConversationCreated: handleConversationCreated,
  });

  const {
    isStreaming,
    streamingMessages,
    handleSendMessage,
    clearStreamingMessages,
    interruptConfirmation,
    setInterruptConfirmation,
    stopStream,
    currentReport,
    currentDashboard,
    currentRecommendations,
    thinkingContent,
    planningContent,
    // MessagePublicList fields
    conversationResourceId,
    hasReport,
    hasDashboard,
    isLoadingMessages,
  } = messageStream;

  // Stream reconnection functionality
  const streamReconnection = useStreamReconnection({
    enabled: true,
    onReconnectionAttempt: (conversationId, lastPosition) => {
      console.log(
        `Attempting to reconnect to stream for conversation ${conversationId} from position ${lastPosition}`,
      );
    },
    onReconnectionSuccess: (conversationId) => {
      console.log(
        `Successfully reconnected to stream for conversation ${conversationId}`,
      );
    },
    onReconnectionError: (conversationId, error) => {
      console.error(
        `Failed to reconnect to stream for conversation ${conversationId}:`,
        error,
      );
    },
  });

  // Selected resource state for immediate tab display
  const [selectedResourceId, setSelectedResourceId] = useState<string | null>(
    null,
  );

  const handleResourceSelect = useCallback((resourceId: string | null) => {
    setSelectedResourceId(resourceId);
  }, []);

  const setMounted = useCallback((mounted: boolean) => {
    dispatch({ type: 'SET_MOUNTED', payload: mounted });
  }, []);

  const setSelectedConversation = useCallback(
    (conversationId: string | null) => {
      dispatch({ type: 'SET_SELECTED_CONVERSATION', payload: conversationId });
    },
    [],
  );

  // Clear streaming messages when selected conversation changes to a different conversation
  // Use ref to track previous conversation ID to avoid clearing on streaming state changes
  const prevConversationRef = useRef<string | null>(null);

  useEffect(() => {
    const currentConversation = state.selectedConversation;
    const prevConversation = prevConversationRef.current;

    // Only clear messages if we're switching to a genuinely different conversation
    // (not just from null to a conversation during new chat creation)
    if (
      prevConversation !== null &&
      currentConversation !== null &&
      prevConversation !== currentConversation
    ) {
      clearStreamingMessages();
      // Clear selected resource when conversation changes
      setSelectedResourceId(null);
    }

    // Update the ref for next time
    prevConversationRef.current = currentConversation;
  }, [state.selectedConversation, clearStreamingMessages]);

  // Attempt stream reconnection when conversation is selected
  useEffect(() => {
    const attemptReconnection = async () => {
      if (state.selectedConversation && state.mounted && !isStreaming) {
        // Check if there's an active stream for this conversation
        const streamStatus = await streamReconnection.checkStreamStatus(
          state.selectedConversation,
        );

        if (streamStatus?.is_streaming_active) {
          console.log('Active stream detected, attempting reconnection...');
          // Note: We can't directly access processMessageStream from here,
          // so the reconnection will be handled by the useMessageStream hook
          // when it detects the stream status during message loading
        }
      }
    };

    attemptReconnection();
  }, [
    state.selectedConversation,
    state.mounted,
    isStreaming,
    streamReconnection,
  ]);

  const setInitialMessageSent = useCallback((sent: boolean) => {
    dispatch({ type: 'SET_INITIAL_MESSAGE_SENT', payload: sent });
  }, []);

  // Clear all state for new chat
  const clearAllState = useCallback(
    (options?: { preserveResource?: boolean }) => {
      // Preserve resource if requested
      const resourceToPreserve = options?.preserveResource
        ? selectedResourceId || conversationResourceId
        : null;

      // Clear selected conversation
      dispatch({ type: 'SET_SELECTED_CONVERSATION', payload: null });

      // Clear streaming messages and other state
      clearStreamingMessages();

      // Clear selected resource (unless preserving)
      setSelectedResourceId(resourceToPreserve);

      // Reset initial message sent flag
      dispatch({ type: 'SET_INITIAL_MESSAGE_SENT', payload: false });

      // Update URL to remove conversation ID
      const newUrl = conversationUrlUtils.removeUrlParam(
        window.location.pathname,
        searchParams,
        'conversationId',
      );
      router.replace(newUrl, { scroll: false });
    },
    [
      clearStreamingMessages,
      router,
      searchParams,
      selectedResourceId,
      conversationResourceId,
    ],
  );

  return {
    // State
    selectedConversation: state.selectedConversation,
    mounted: state.mounted,
    initialMessageSent: state.initialMessageSent,

    // Actions
    setMounted,
    setSelectedConversation,
    setInitialMessageSent,
    clearAllState,

    // Message stream
    isStreaming,
    streamingMessages,
    handleSendMessage,
    clearStreamingMessages,
    interruptConfirmation,
    setInterruptConfirmation,
    stopStream,
    currentReport,
    currentDashboard,
    currentRecommendations,
    thinkingContent,
    planningContent,

    // MessagePublicList fields
    conversationResourceId,
    hasReport,
    hasDashboard,

    // Selected resource for immediate tab display
    selectedResourceId,
    onResourceSelect: handleResourceSelect,

    // Loading state
    isLoadingMessages,
  };
};
