import { useEffect, useRef } from 'react';

import { useRouter, useSearchParams } from 'next/navigation';

import { conversationUrlUtils } from '@/features/conversation';

interface InitialMessageHandlerProps {
  mounted: boolean;
  initialMessage?: string;
  initialMessageSent: boolean;
  handleSendMessage: (content: string) => void;
  setInitialMessageSent: (sent: boolean) => void;
}

export const useInitialMessageHandler = ({
  mounted,
  initialMessage,
  initialMessageSent,
  handleSendMessage,
  setInitialMessageSent,
}: InitialMessageHandlerProps) => {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Use refs to store the latest callback functions
  const handleSendMessageRef = useRef(handleSendMessage);
  const setInitialMessageSentRef = useRef(setInitialMessageSent);

  // Update refs when the callbacks change
  useEffect(() => {
    handleSendMessageRef.current = handleSendMessage;
    setInitialMessageSentRef.current = setInitialMessageSent;
  }, [handleSendMessage, setInitialMessageSent]);

  // Cleanup initialMessage from URL when component unmounts (only if message wasn't sent)
  useEffect(() => {
    return () => {
      // Only cleanup if the initial message hasn't been sent yet
      if (
        initialMessage &&
        !initialMessageSent &&
        searchParams.has('initialMessage')
      ) {
        const newUrl = conversationUrlUtils.removeUrlParam(
          window.location.pathname,
          searchParams,
          'initialMessage',
        );
        // Use replace to avoid navigation issues during unmount
        window.history.replaceState({}, '', newUrl);
      }
    };
  }, [initialMessage, initialMessageSent, searchParams]);

  useEffect(() => {
    if (!mounted) return;

    const sendInitialMessage = () => {
      if (!initialMessage) return;

      const timer = setTimeout(() => {
        handleSendMessageRef.current(initialMessage);
        setInitialMessageSentRef.current(true);

        const newUrl = conversationUrlUtils.removeUrlParam(
          window.location.pathname,
          searchParams,
          'initialMessage',
        );
        router.replace(newUrl);
      }, 600);

      return () => clearTimeout(timer);
    };

    // Send initial message if present and not already sent
    // Conversation will be created implicitly by the backend when message is sent
    if (initialMessage && !initialMessageSent) {
      return sendInitialMessage();
    }
  }, [mounted, initialMessage, initialMessageSent, router, searchParams]);
};
