import { FC } from 'react';

import {
  AppRunnerIcon,
  BackupIcon,
  BatchIcon,
  CloudFormationIcon,
  CloudwatchIcon,
  DocumentdbIcon,
  DynamodbIcon,
  EbsIcon,
  Ec2AutoScalingIcon,
  Ec2Icon,
  EcsIcon,
  EfsIcon,
  EksIcon,
  ElasticBeanstalkIcon,
  ElasticacheIcon,
  ElbIcon,
  LambdaIcon,
  NeptuneIcon,
  OpensearchIcon,
  RdsIcon,
  RedshiftIcon,
  S3Icon,
  SnsIcon,
  SqsIcon,
  VpcIcon,
} from '@/components/aws-icons';
import { ResourceCategory } from '@/openapi-ts/gens';
import { createUtilityConfig } from '@/utils/option-config';
import { LayersIcon } from 'lucide-react';

// AWS-specific resource types (hardcoded based on backend enum)
export enum AWSResourceType {
  // Compute Services
  EC2 = 'EC2',
  LAMBDA = 'LAMBDA',
  ECS = 'ECS',
  EKS = 'EKS',
  BATCH = 'BATCH',
  EC2_AUTO_SCALING = 'EC2_AUTO_SCALING',
  ELASTIC_BEANSTALK = 'ELASTIC_BEANSTALK',
  APP_RUNNER = 'APP_RUNNER',

  // Database Services
  RDS = 'RDS',
  DYNAMODB = 'DYNAMODB',
  ELASTICACHE = 'ELASTICACHE',
  NEPTUNE = 'NEPTUNE',
  DOCUMENTDB = 'DOCUMENTDB',
  OPENSEARCH = 'OPENSEARCH',
  REDSHIFT = 'REDSHIFT',

  // Storage Services
  S3 = 'S3',
  EBS = 'EBS',
  EFS = 'EFS',
  BACKUP = 'BACKUP',

  // Networking & Content Delivery
  VPC = 'VPC',
  ELB = 'ELB',

  // Management & Governance
  CLOUDFORMATION = 'CLOUDFORMATION',
  CLOUDWATCH = 'CLOUDWATCH',
  SQS = 'SQS',
  SNS = 'SNS',

  // General/Unknown Resource Types
  GENERAL = 'General',
}

// GCP-specific resource types
export enum GCPResourceType {
  // Compute Services
  COMPUTE_ENGINE = 'COMPUTE_ENGINE',
  CLOUD_FUNCTIONS = 'CLOUD_FUNCTIONS',
  CLOUD_RUN = 'CLOUD_RUN',
  GKE = 'GKE',
  APP_ENGINE = 'APP_ENGINE',

  // Database Services
  CLOUD_SQL = 'CLOUD_SQL',
  FIRESTORE = 'FIRESTORE',
  BIGTABLE = 'BIGTABLE',
  SPANNER = 'SPANNER',
  MEMORYSTORE = 'MEMORYSTORE',

  // Storage Services
  CLOUD_STORAGE = 'CLOUD_STORAGE',
  PERSISTENT_DISK = 'PERSISTENT_DISK',
  FILESTORE = 'FILESTORE',

  // Networking
  VPC_NETWORK = 'VPC_NETWORK',
  LOAD_BALANCER = 'LOAD_BALANCER',

  // Management & Monitoring
  CLOUD_MONITORING = 'CLOUD_MONITORING',
  CLOUD_LOGGING = 'CLOUD_LOGGING',
  PUB_SUB = 'PUB_SUB',
}

// Azure-specific resource types
export enum AzureResourceType {
  // Compute Services
  VIRTUAL_MACHINES = 'VIRTUAL_MACHINES',
  AZURE_FUNCTIONS = 'AZURE_FUNCTIONS',
  CONTAINER_INSTANCES = 'CONTAINER_INSTANCES',
  AKS = 'AKS',
  APP_SERVICE = 'APP_SERVICE',

  // Database Services
  SQL_DATABASE = 'SQL_DATABASE',
  COSMOS_DB = 'COSMOS_DB',
  REDIS_CACHE = 'REDIS_CACHE',
  POSTGRESQL = 'POSTGRESQL',
  MYSQL = 'MYSQL',

  // Storage Services
  BLOB_STORAGE = 'BLOB_STORAGE',
  DISK_STORAGE = 'DISK_STORAGE',
  FILE_STORAGE = 'FILE_STORAGE',

  // Networking
  VIRTUAL_NETWORK = 'VIRTUAL_NETWORK',
  LOAD_BALANCER = 'LOAD_BALANCER',
  APPLICATION_GATEWAY = 'APPLICATION_GATEWAY',

  // Management & Monitoring
  MONITOR = 'MONITOR',
  LOG_ANALYTICS = 'LOG_ANALYTICS',
  SERVICE_BUS = 'SERVICE_BUS',
}

// Resource type mapping utilities
export const RESOURCE_TYPE_TO_CATEGORY = {
  // AWS mappings
  [AWSResourceType.EC2]: ResourceCategory.COMPUTE,
  [AWSResourceType.LAMBDA]: ResourceCategory.SERVERLESS,
  [AWSResourceType.ECS]: ResourceCategory.CONTAINER,
  [AWSResourceType.EKS]: ResourceCategory.CONTAINER,
  [AWSResourceType.BATCH]: ResourceCategory.COMPUTE,
  [AWSResourceType.EC2_AUTO_SCALING]: ResourceCategory.COMPUTE,
  [AWSResourceType.ELASTIC_BEANSTALK]: ResourceCategory.COMPUTE,
  [AWSResourceType.APP_RUNNER]: ResourceCategory.SERVERLESS,
  [AWSResourceType.RDS]: ResourceCategory.DATABASE,
  [AWSResourceType.DYNAMODB]: ResourceCategory.DATABASE,
  [AWSResourceType.ELASTICACHE]: ResourceCategory.DATABASE,
  [AWSResourceType.NEPTUNE]: ResourceCategory.DATABASE,
  [AWSResourceType.DOCUMENTDB]: ResourceCategory.DATABASE,
  [AWSResourceType.OPENSEARCH]: ResourceCategory.ANALYTICS,
  [AWSResourceType.REDSHIFT]: ResourceCategory.ANALYTICS,
  [AWSResourceType.S3]: ResourceCategory.STORAGE,
  [AWSResourceType.EBS]: ResourceCategory.STORAGE,
  [AWSResourceType.EFS]: ResourceCategory.STORAGE,
  [AWSResourceType.BACKUP]: ResourceCategory.STORAGE,
  [AWSResourceType.VPC]: ResourceCategory.NETWORKING,
  [AWSResourceType.ELB]: ResourceCategory.NETWORKING,
  [AWSResourceType.CLOUDFORMATION]: ResourceCategory.MANAGEMENT,
  [AWSResourceType.CLOUDWATCH]: ResourceCategory.MONITORING,
  [AWSResourceType.SQS]: ResourceCategory.MESSAGING,
  [AWSResourceType.SNS]: ResourceCategory.MESSAGING,
  [AWSResourceType.GENERAL]: ResourceCategory.MANAGEMENT,
};

export const RESOURCE_TYPE_CONFIG = createUtilityConfig({
  [AWSResourceType.EC2]: {
    label: 'EC2',
    icon: Ec2Icon,
  },
  [AWSResourceType.LAMBDA]: {
    label: 'Lambda',
    icon: LambdaIcon,
  },
  [AWSResourceType.ECS]: {
    label: 'ECS',
    icon: EcsIcon,
  },
  [AWSResourceType.EKS]: {
    label: 'EKS',
    icon: EksIcon,
  },
  [AWSResourceType.BATCH]: {
    label: 'Batch',
    icon: BatchIcon,
  },
  [AWSResourceType.EC2_AUTO_SCALING]: {
    label: 'EC2 Auto Scaling',
    icon: Ec2AutoScalingIcon,
  },
  [AWSResourceType.ELASTIC_BEANSTALK]: {
    label: 'Elastic Beanstalk',
    icon: ElasticBeanstalkIcon,
  },
  [AWSResourceType.APP_RUNNER]: {
    label: 'App Runner',
    icon: AppRunnerIcon,
  },
  [AWSResourceType.RDS]: {
    label: 'RDS',
    icon: RdsIcon,
  },
  [AWSResourceType.DYNAMODB]: {
    label: 'DynamoDB',
    icon: DynamodbIcon,
  },
  [AWSResourceType.ELASTICACHE]: {
    label: 'ElastiCache',
    icon: ElasticacheIcon,
  },
  [AWSResourceType.NEPTUNE]: {
    label: 'Neptune',
    icon: NeptuneIcon,
  },
  [AWSResourceType.DOCUMENTDB]: {
    label: 'DocumentDB',
    icon: DocumentdbIcon,
  },
  [AWSResourceType.OPENSEARCH]: {
    label: 'OpenSearch',
    icon: OpensearchIcon,
  },
  [AWSResourceType.REDSHIFT]: {
    label: 'Redshift',
    icon: RedshiftIcon,
  },
  [AWSResourceType.S3]: {
    label: 'S3',
    icon: S3Icon,
  },
  [AWSResourceType.EBS]: {
    label: 'EBS',
    icon: EbsIcon,
  },
  [AWSResourceType.EFS]: {
    label: 'EFS',
    icon: EfsIcon,
  },
  [AWSResourceType.BACKUP]: {
    label: 'Backup',
    icon: BackupIcon,
  },
  [AWSResourceType.VPC]: {
    label: 'VPC',
    icon: VpcIcon,
  },
  [AWSResourceType.ELB]: {
    label: 'ELB',
    icon: ElbIcon,
  },
  [AWSResourceType.CLOUDFORMATION]: {
    label: 'CloudFormation',
    icon: CloudFormationIcon,
  },
  [AWSResourceType.CLOUDWATCH]: {
    label: 'CloudWatch',
    icon: CloudwatchIcon,
  },
  [AWSResourceType.SQS]: {
    label: 'SQS',
    icon: SqsIcon,
  },
  [AWSResourceType.SNS]: {
    label: 'SNS',
    icon: SnsIcon,
  },
  [AWSResourceType.GENERAL]: {
    label: 'General',
    icon: LayersIcon,
  },
} satisfies Record<
  AWSResourceType,
  { label: string; icon: FC<{ className?: string }> }
>);
