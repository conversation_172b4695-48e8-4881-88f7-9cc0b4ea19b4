import { BadgeProps } from '@/components/ui/badge';
import { ResourceCategory } from '@/openapi-ts/gens';
import { createUtilityConfig } from '@/utils/option-config';

export const RESOURCE_CATEGORY_CONFIG = createUtilityConfig({
  [ResourceCategory.COMPUTE]: {
    label: 'Compute',
    variant: 'ghost-primary',
  },
  [ResourceCategory.DATABASE]: {
    label: 'Database',
    variant: 'ghost-primary',
  },
  [ResourceCategory.STORAGE]: {
    label: 'Storage',
    variant: 'ghost-primary',
  },
  [ResourceCategory.NETWORKING]: {
    label: 'Networking',
    variant: 'ghost-primary',
  },
  [ResourceCategory.SERVERLESS]: {
    label: 'Serverless',
    variant: 'ghost-primary',
  },
  [ResourceCategory.CONTAINER]: {
    label: 'Container',
    variant: 'ghost-primary',
  },
  [ResourceCategory.MESSAGING]: {
    label: 'Messaging',
    variant: 'ghost-primary',
  },
  [ResourceCategory.MONITORING]: {
    label: 'Monitoring',
    variant: 'ghost-primary',
  },
  [ResourceCategory.SECURITY]: {
    label: 'Security',
    variant: 'ghost-primary',
  },
  [ResourceCategory.MANAGEMENT]: {
    label: 'Management',
    variant: 'ghost-primary',
  },
  [ResourceCategory.ANALYTICS]: {
    label: 'Analytics',
    variant: 'ghost-primary',
  },
  [ResourceCategory.AI_ML]: {
    label: 'AI/ML',
    variant: 'ghost-primary',
  },
  [ResourceCategory.OTHER]: {
    label: 'Other',
    variant: 'ghost-primary',
  },
} satisfies Record<
  ResourceCategory,
  {
    label: string;
    variant: BadgeProps['variant'];
  }
>);
