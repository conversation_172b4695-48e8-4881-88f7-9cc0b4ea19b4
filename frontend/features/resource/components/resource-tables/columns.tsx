'use client';

import { createElement } from 'react';

import Link from 'next/link';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { If } from '@/components/ui/common/if';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { WithTooltip } from '@/components/ui/tooltip';
import pathsConfig from '@/config/paths.config';
import { useNavigateAgentDetail } from '@/features/agent/hooks/use-navigate-agent-detail';
import { RecommendationQueryParams } from '@/features/recommendation/models/recommendation.type';
import { formatUSD } from '@/lib/currency';
import { renderCellToFullDateTime } from '@/lib/date-utils';
import { SchemaResourcePublic } from '@/openapi-ts/gens';
import { ColumnDef } from '@tanstack/react-table';
import { flow } from 'lodash';
import { Activity, MoreHorizontal, ShieldCheck, Wallet } from 'lucide-react';

import { RESOURCE_CATEGORY_CONFIG } from '../../config/resource-category.config';
import { RESOURCE_PROVIDER_CONFIG } from '../../config/resource-provider.config';
import { RESOURCE_STATUS_CONFIG } from '../../config/resource-status.config';
import {
  AWSResourceType,
  RESOURCE_TYPE_CONFIG,
} from '../../config/resource-type.config';

export const resourceColumns: ColumnDef<SchemaResourcePublic>[] = [
  {
    accessorKey: 'name',
    header: 'Resource',
    cell: ({ row }) => {
      const { name, resource_id, type } = row.original;

      // Get the icon for the resource type
      const getResourceTypeIcon = () => {
        // Try to get icon from AWS resource config first
        const awsResourceType = type as AWSResourceType;
        if (RESOURCE_TYPE_CONFIG.CONFIG[awsResourceType]) {
          const iconComponent =
            RESOURCE_TYPE_CONFIG.CONFIG[awsResourceType].icon;
          return createElement(iconComponent, {
            className: 'size-6 text-primary shrink-0',
          });
        }

        // Fallback to a default icon if not found
        return (
          <div className="bg-muted flex size-6 shrink-0 items-center justify-center rounded text-xs font-medium">
            {type.charAt(0)}
          </div>
        );
      };

      return (
        <div className="flex gap-3">
          <div className="flex-shrink-0">{getResourceTypeIcon()}</div>
          <div className="-mt-0.5 min-w-0 flex-1 space-y-1">
            <div className="text-foreground leading-tight font-medium break-words">
              {name}
            </div>
            <div className="text-muted-foreground text-sm break-all">
              {resource_id}
            </div>
          </div>
        </div>
      );
    },
    minSize: 350,
    maxSize: 400,
    meta: {
      className: '',
    },
  },
  {
    accessorKey: 'provider',
    header: 'Provider',
    cell: ({ row }) => {
      const { provider } = row.original;
      const { label, variant } = RESOURCE_PROVIDER_CONFIG.CONFIG[provider];
      return <Badge variant={variant}>{label}</Badge>;
    },
    size: 100,
    meta: {
      className: 'text-center',
    },
  },
  {
    accessorKey: 'region',
    header: 'Region',
    cell: ({ row }) => {
      const { region } = row.original;
      return <span className="text-sm">{region}</span>;
    },
    size: 130,
  },
  {
    accessorKey: 'category',
    header: 'Category',
    cell: ({ row }) => {
      const { category } = row.original;
      const { label, variant } = RESOURCE_CATEGORY_CONFIG.CONFIG[category];
      return <Badge variant={variant}>{label}</Badge>;
    },
    size: 120,
    meta: {
      className: 'text-center',
    },
  },
  {
    accessorKey: 'type',
    header: 'Type',
    cell: ({ row }) => {
      const { type } = row.original;
      return <span className="text-sm font-medium">{type}</span>;
    },
    size: 100,
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const { status } = row.original;

      const { label, variant } = RESOURCE_STATUS_CONFIG.CONFIG[status];

      return <Badge variant={variant}>{label}</Badge>;
    },
    size: 110,
    meta: {
      className: 'text-center',
    },
  },
  {
    accessorKey: 'total_recommendation',
    header: 'Total Recommendations',
    cell: ({ row }) => {
      const { total_recommendation, id } = row.original;
      // Using lodash flow for functional composition:
      // 1. Takes resourceId and transforms it into RecommendationQueryParams object
      // 2. Transforms the params object into a URL string with query parameters
      // 3. Finally applies the composed function to data.id
      const allRecommendationsHref = flow(
        (resourceId: string): RecommendationQueryParams => ({
          resource_id: [resourceId],
        }),
        (params) =>
          `${pathsConfig.app.resourceRecommendations}?${new URLSearchParams(params as URLSearchParams).toString()}`,
      )(id);

      return (
        <If
          condition={total_recommendation}
          fallback={<p>{total_recommendation}</p>}
        >
          <WithTooltip tooltip="View all recommendations">
            <Button asChild variant="link" className="p-0">
              <Link href={allRecommendationsHref}>{total_recommendation}</Link>
            </Button>
          </WithTooltip>
        </If>
      );
    },
    meta: {
      className: 'text-right',
    },
  },
  {
    accessorKey: 'total_potential_saving',
    header: 'Potential Saving',
    cell: ({ row }) => {
      const value = row.original.total_potential_saving;
      if (typeof value !== 'number') {
        return 'N/A';
      }

      return <p>{formatUSD(value)}</p>;
    },
    meta: {
      className: 'text-right',
    },
  },
  {
    accessorKey: 'updated_at',
    header: 'Last Updated',
    cell: renderCellToFullDateTime,
    size: 230,
    meta: {
      className: 'text-center whitespace-nowrap',
    },
  },
  {
    id: 'actions',
    header: 'Actions',
    cell: ({ row }) => {
      return <RowActions resource={row.original} />;
    },
    size: 90,
    meta: {
      className: 'text-right',
      header: {
        className:
          'sticky right-0 z-20 bg-background text-right shadow-[inset_-1px_0_0_0_var(--border)]',
      },
      cell: {
        className:
          'sticky right-0 z-10 bg-background shadow-[inset_-1px_0_0_0_var(--border)]',
      },
    },
  },
];

function buildResourceXml(resource: SchemaResourcePublic): string {
  const lines: string[] = ['<resource>'];
  if (resource.name) lines.push(`  <name>${resource.name}</name>`);
  const idValue =
    (resource as { resource_id?: string }).resource_id ??
    (resource as { id?: string }).id;
  if (idValue) lines.push(`  <id>${idValue}</id>`);
  lines.push('</resource>');
  return lines.join('\n');
}

type RowActionKey = 'optimize_cost' | 'scan_security' | 'check_performance';

const ROW_ACTIONS: Array<{
  key: RowActionKey;
  label: string;
  prefix: string;
  icon: React.ComponentType<{ className?: string }>;
}> = [
  {
    key: 'optimize_cost',
    label: 'Optimize Cost',
    prefix: '@Alex please optimize the cost of this resource:',
    icon: Wallet,
  },
  {
    key: 'scan_security',
    label: 'Scan Security',
    prefix: '@Alex please perform a security scan on this resource:',
    icon: ShieldCheck,
  },
  {
    key: 'check_performance',
    label: 'Check Performance',
    prefix: '@Alex please check the performance of this resource:',
    icon: Activity,
  },
];

function RowActions({ resource }: { resource: SchemaResourcePublic }) {
  const navigateAgentDetail = useNavigateAgentDetail();

  const xml = buildResourceXml(resource);

  const handleSelect = (prefix: string) => {
    const message = `${prefix}\n${xml}`;
    navigateAgentDetail({
      initialMessage: message,
      resource_id: resource.id,
    });
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="hover:bg-muted h-8 w-8 rounded-full"
          aria-label="Actions"
        >
          <MoreHorizontal className="size-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        {ROW_ACTIONS.map(({ key, label, prefix, icon: Icon }) => (
          <DropdownMenuItem key={key} onClick={() => handleSelect(prefix)}>
            <Icon className="mr-2 size-4" />
            <span>{label}</span>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
