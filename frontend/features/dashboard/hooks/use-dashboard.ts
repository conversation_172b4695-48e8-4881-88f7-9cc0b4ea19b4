import { useQuery } from '@tanstack/react-query';

import { dashboardApi } from '../services/dashboard.api';

interface UseDashboardOptions {
  enabled?: boolean;
}

export const useDashboard = (
  conversationId: string | null,
  options: UseDashboardOptions = {},
) => {
  const {
    data: dashboard,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['dashboard', conversationId],
    queryFn: () =>
      dashboardApi.getDashboardByConversation({
        conversationId: conversationId!,
      }),
    enabled: <PERSON><PERSON><PERSON>(conversationId && options.enabled !== false),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

  // Async refresh function
  const refreshDashboard = () => refetch();

  return {
    dashboard,
    isLoading,
    error: error as Error | null,
    refreshDashboard,
  };
};
