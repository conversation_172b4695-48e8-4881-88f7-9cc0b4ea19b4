// Shared Types based on backend schema
// Backend source: backend/app/modules/multi_agents/tools/builtin/shared_schema.py
// Consolidates existing frontend types with backend-aligned types

// ----------------------------------------------------------------
// Chart Types
// ----------------------------------------------------------------

export type ChartType =
  | 'bar'
  | 'pie'
  | 'line'
  | 'area'
  | 'step_area'
  | 'radar'
  | 'sankey';

export interface ChartDataset {
  data: number[];
  label?: string;
}

export interface SankeyNode {
  id: string;
  label: string;
  color?: string;
}

export interface SankeyLink {
  source: string;
  target: string;
  value: number;
  label?: string;
}

export interface ChartAxis {
  title?: string;
  type?: 'time' | 'linear' | 'logarithmic' | 'category';
}

export interface ChartStructuredOutput {
  /** The title of the chart */
  title: string;
  /** The description of the chart */
  description?: string;
  /** The type of chart */
  chart_type: ChartType;
  /** The categories/labels for the chart */
  categories: string[];
  /** The datasets for the chart */
  datasets: ChartDataset[];
  /** Sankey chart specific nodes */
  sankey_nodes?: SankeyNode[];
  /** Sankey chart specific links */
  sankey_links?: SankeyLink[];
  /** X-axis configuration */
  x_axis: ChartAxis;
  /** Y-axis configuration */
  y_axis: ChartAxis;
  /** Whether to show the legend */
  show_legend: boolean;
  /** Whether to show the grid */
  show_grid: boolean;
  /** Whether to format as currency */
  currency_format: boolean;
  /** Whether to format as percentage */
  percentage_format: boolean;
  /** Position in the content */
  position?: number;
}

// ----------------------------------------------------------------
// Trend Types
// ----------------------------------------------------------------

export type TrendDirection = 'up' | 'down' | 'neutral';

export interface Trend {
  direction: TrendDirection;
  value: string;
  description?: string;
}

// Legacy TrendData type for backward compatibility
export interface TrendData {
  direction: TrendDirection;
  value: string;
  description?: string;
}

// ----------------------------------------------------------------
// KPI Card Types
// ----------------------------------------------------------------

export interface KPICard {
  /** The title of the card */
  title: string;
  /** The main data point or value of the card */
  value: string;
  /** Additional description */
  description?: string;
  /** Trend information */
  trend?: Trend;
  /** Name of a Lucide icon to display */
  icon?: string;
  /** Flag to indicate an alert state */
  alert?: boolean;
}

// ----------------------------------------------------------------
// Chart Display Options (for ChartRenderer compatibility)
// ----------------------------------------------------------------

export interface ChartDisplayOptions {
  show_legend?: boolean;
  show_grid?: boolean;
  currency_format?: boolean;
  percentage_format?: boolean;
}

// ----------------------------------------------------------------
// Type Guards
// ----------------------------------------------------------------

export const isValidTrend = (trend: unknown): trend is TrendData =>
  Boolean(
    trend &&
      typeof trend === 'object' &&
      'direction' in trend &&
      'value' in trend &&
      (trend as TrendData).direction in { up: true, down: true, neutral: true },
  );

export const isValidKPICard = (card: unknown): card is KPICard =>
  Boolean(
    card &&
      typeof card === 'object' &&
      'title' in card &&
      'value' in card &&
      typeof (card as KPICard).title === 'string' &&
      typeof (card as KPICard).value === 'string',
  );

export const isValidChartData = (
  chart: unknown,
): chart is ChartStructuredOutput =>
  Boolean(
    chart &&
      typeof chart === 'object' &&
      'chart_type' in chart &&
      'categories' in chart &&
      'datasets' in chart &&
      Array.isArray((chart as ChartStructuredOutput).categories) &&
      Array.isArray((chart as ChartStructuredOutput).datasets),
  );
