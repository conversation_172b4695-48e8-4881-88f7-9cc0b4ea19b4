// Dashboard Types based on backend schema
// Backend source: backend/app/modules/multi_agents/tools/builtin/dashboard/schema.py
import { ChartStructuredOutput, KPICard } from './shared.type';

// ----------------------------------------------------------------
// Layout Configuration
// ----------------------------------------------------------------

export interface LayoutConfig {
  /** The starting column of the widget (0-indexed) */
  x: number;
  /** The starting row of the widget (0-indexed) */
  y: number;
  /** The width of the widget in grid columns (column span) */
  w: number;
  /** The height of the widget in grid rows (row span) */
  h: number;
}

export interface GridConfig {
  /** The total number of columns in the dashboard grid */
  columns: number;
}

// ----------------------------------------------------------------
// Base Widget Interface
// ----------------------------------------------------------------

export interface BaseWidget {
  /** Unique identifier for the widget */
  id?: string;
  /** Layout configuration for the widget */
  layout: LayoutConfig;
  /** Type discriminator for the widget */
  type: string;
}

// ----------------------------------------------------------------
// Widget Types
// ----------------------------------------------------------------

export interface ChartWidget extends ChartStructuredOutput, BaseWidget {
  type: 'chart';
}

export interface KpiCardWidget extends KPICard, BaseWidget {
  type: 'kpi_card';
}

export interface GaugeWidget extends BaseWidget {
  type: 'gauge';
  /** The main title of the gauge */
  title: string;
  /** The primary value, typically a percentage from 0 to 100 */
  value: number;
  /** A secondary line of text below the gauge */
  description?: string;
}

export interface TableColumnConfig {
  /** The header text */
  header: string;
}

export interface TableWidget extends BaseWidget {
  type: 'table';
  /** The title of the table */
  title: string;
  /** The description of the table */
  description?: string;
  /** The columns of the table */
  columns: TableColumnConfig[];
  /** The rows of the table, each row is a list of strings */
  rows: string[][];
}

// ----------------------------------------------------------------
// Dashboard Structure
// ----------------------------------------------------------------

export type DashboardWidget =
  | KpiCardWidget
  | GaugeWidget
  | TableWidget
  | ChartWidget;

export interface Dashboard {
  /** Unique identifier for the dashboard */
  id?: string;
  /** Conversation ID this dashboard belongs to */
  conversation_id: string;
  /** Workspace ID this dashboard belongs to */
  workspace_id: string;
  /** Dashboard title */
  title: string;
  /** Dashboard description */
  description?: string;
  /** Grid configuration for the dashboard */
  grid_config: GridConfig;
  /** List of widgets in the dashboard */
  widgets: DashboardWidget[];
  /** Creation timestamp */
  created_at: string;
  /** Last update timestamp */
  updated_at: string;
}

// ----------------------------------------------------------------
// Dashboard Input Commands
// ----------------------------------------------------------------

export type DashboardCommand =
  | 'create_dashboard'
  | 'add_widgets'
  | 'update_widgets'
  | 'remove_widgets'
  | 'update_grid'
  | 'get_dashboard';

export interface DashboardInput {
  command: DashboardCommand;
  title?: string;
  description?: string;
  grid_config?: GridConfig;
  widgets?: DashboardWidget[];
  widget_positions?: LayoutConfig[];
}
