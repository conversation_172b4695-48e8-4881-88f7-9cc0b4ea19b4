import { TaskComplexity } from '@/openapi-ts/gens';
import { createUtilityConfig } from '@/utils/option-config';

export const TaskComplexityConfig = createUtilityConfig({
  [TaskComplexity.Easy]: {
    label: 'Easy',
    icon: '🟢',
    color: 'bg-green-100 text-green-800 border-green-200',
  },
  [TaskComplexity.Medium]: {
    label: 'Medium',
    icon: '🟡',
    color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  },
} satisfies Record<
  TaskComplexity,
  {
    label: string;
    icon: string;
    color: string;
  }
>);
