import { Alert, AlertTitle } from '@/components/ui/alert';
import { ShieldIcon } from 'lucide-react';

export function SecureInfoAlert() {
  return (
    <Alert>
      <AlertTitle className="flex items-center gap-2">
        <ShieldIcon className="size-4" />
        Your credentials are encrypted and stored securely. We recommend using
        read-only access keys when possible.
      </AlertTitle>
    </Alert>
  );
}
