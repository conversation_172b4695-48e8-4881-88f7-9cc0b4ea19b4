import { ReactNode } from 'react';

import { useStepContext } from '@/components/providers/step-provider';
import { Card, CardContent } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { zodResolver } from '@hookform/resolvers/zod';
import { SubmitHandler, useForm } from 'react-hook-form';

import {
  CLOUD_PROVIDER_CONFIG,
  CloudProvider,
} from '../../config/cloud-provider.config';
import { onboardingQuery } from '../../hooks/onboarding.query';
import {
  GcpConnectionSchema,
  gcpConnectionSchema,
} from '../../schema/gcp-connection.schema';
import { OnboardingConfigureConnectionCardHeader } from '../onboarding-configure-connection-card-header';

type Props = {
  ContinueButton: (props: { isPending: boolean }) => ReactNode;
};

export const GcpConfigConnection = ({ ContinueButton }: Props) => {
  const form = useForm<GcpConnectionSchema>({
    resolver: zodResolver(gcpConnectionSchema),
  });

  const { control, handleSubmit } = form;
  const { mutate, isPending } = onboardingQuery.mutation.useConnectGcp();
  const { goToNextStep } = useStepContext();

  const onSubmit: SubmitHandler<GcpConnectionSchema> = (data) => {
    mutate(data, {
      onSuccess: goToNextStep,
    });
  };

  return (
    <Form {...form}>
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <Card>
          <OnboardingConfigureConnectionCardHeader
            providerFullName={
              CLOUD_PROVIDER_CONFIG.CONFIG[CloudProvider.GCP].fullName
            }
          />
          <CardContent className="space-y-4">
            <FormField
              control={control}
              name="provider_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel required>Project ID</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="my-project-123456" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={control}
              name="google_service_account_key"
              render={({ field }) => (
                <FormItem>
                  <FormLabel required>Service Account Key (JSON)</FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="Paste your service account JSON key here"
                      className="min-h-40"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>
        {ContinueButton({ isPending })}
      </form>
    </Form>
  );
};
