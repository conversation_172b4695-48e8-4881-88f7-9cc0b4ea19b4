import { ReactNode } from 'react';

import { useStepContext } from '@/components/providers/step-provider';
import { Card, CardContent } from '@/components/ui/card';
import { CheckIcon, FlaskConicalIcon } from 'lucide-react';

import {
  CLOUD_PROVIDER_CONFIG,
  CloudProvider,
} from '../../config/cloud-provider.config';
import { onboardingQuery } from '../../hooks/onboarding.query';
import { OnboardingConfigureConnectionCardHeader } from '../onboarding-configure-connection-card-header';

type Props = {
  ContinueButton: (props: { isPending: boolean }) => ReactNode;
};

export const SandboxConfigConnection = ({ ContinueButton }: Props) => {
  const { goToNextStep } = useStepContext();
  const { mutate, isPending } = onboardingQuery.mutation.useConnectSandbox();

  const handleContinue = () => {
    mutate(undefined, {
      onSuccess: goToNextStep,
    });
  };

  const sandboxFeatures = [
    'Simulated AWS, Azure, and GCP resources',
    'Demo cost optimization scenarios',
    'Sample security incidents and alerts',
    'Kubernetes cluster simulation',
    'AI agent interactions with mock data',
  ];

  return (
    <div className="space-y-4">
      <Card>
        <OnboardingConfigureConnectionCardHeader
          providerFullName={
            CLOUD_PROVIDER_CONFIG.CONFIG[CloudProvider.SANDBOX].fullName
          }
          isSandbox={true}
        />
        <CardContent className="space-y-6">
          <div className="bg-info/10 border-info/20 flex items-start gap-3 rounded-lg border p-4">
            <FlaskConicalIcon className="text-info mt-0.5 size-5 shrink-0" />
            <div className="space-y-2">
              <p className="text-info-foreground font-medium">
                Sandbox mode provides a complete demonstration of our platform
                using simulated cloud resources, cost data, and security events.
                Perfect for exploring features before connecting real
                infrastructure.
              </p>
            </div>
          </div>

          <div className="space-y-4">
            <h4 className="text-lg font-semibold">{`What's included in Sandbox:`}</h4>
            <ul className="space-y-3">
              {sandboxFeatures.map((feature, index) => (
                <li key={index} className="flex items-start gap-3">
                  <CheckIcon className="text-success mt-0.5 size-5 shrink-0" />
                  <span className="text-foreground">{feature}</span>
                </li>
              ))}
            </ul>
          </div>
        </CardContent>
      </Card>

      <div onClick={handleContinue}>{ContinueButton({ isPending })}</div>
    </div>
  );
};
