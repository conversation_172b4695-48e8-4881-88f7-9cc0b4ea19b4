import { ReactNode } from 'react';

import { useStepContext } from '@/components/providers/step-provider';
import { Card, CardContent } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { zodResolver } from '@hookform/resolvers/zod';
import { SubmitHandler, useForm } from 'react-hook-form';

import {
  CLOUD_PROVIDER_CONFIG,
  CloudProvider,
} from '../../config/cloud-provider.config';
import { onboardingQuery } from '../../hooks/onboarding.query';
import {
  MicrosoftAzureConnectionSchema,
  microsoftAzureConnectionSchema,
} from '../../schema/microsoft-azure-connection.schema';
import { OnboardingConfigureConnectionCardHeader } from '../onboarding-configure-connection-card-header';

type Props = {
  ContinueButton: (props: { isPending: boolean }) => ReactNode;
};

export const MicrosoftAzureConfigConnection = ({ ContinueButton }: Props) => {
  const form = useForm<MicrosoftAzureConnectionSchema>({
    resolver: zodResolver(microsoftAzureConnectionSchema),
  });

  const { control, handleSubmit } = form;
  const { mutate, isPending } = onboardingQuery.mutation.useConnectAzure();
  const { goToNextStep } = useStepContext();

  const onSubmit: SubmitHandler<MicrosoftAzureConnectionSchema> = (data) => {
    mutate(data, {
      onSuccess: goToNextStep,
    });
  };

  return (
    <Form {...form}>
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <Card>
          <OnboardingConfigureConnectionCardHeader
            providerFullName={
              CLOUD_PROVIDER_CONFIG.CONFIG[CloudProvider.AZURE].fullName
            }
          />
          <CardContent className="space-y-4">
            {/* <FormField
              control={control}
              name="subscriptionId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel required>Subscription ID</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="xxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            /> */}

            <FormField
              control={control}
              name="tenant_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel required>Tenant ID</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="xxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={control}
              name="app_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel required>Client ID</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="Application (client) ID" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={control}
              name="client_secret"
              render={({ field }) => (
                <FormItem>
                  <FormLabel required>Client Secret</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="Enter client secret" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>
        {ContinueButton({ isPending })}
      </form>
    </Form>
  );
};
