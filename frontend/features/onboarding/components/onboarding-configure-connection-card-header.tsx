import { PropsWithChildren } from 'react';

import { CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

import { SecureInfoAlert } from './config-connection/secure-info-alert';
import { If } from '@/components/ui/common/if';

type Props = PropsWithChildren<{
  providerFullName: string;
  isSandbox?: boolean;
}>;

export const OnboardingConfigureConnectionCardHeader = ({
  providerFullName,
  isSandbox,
}: Props) => {
  return (
    <CardHeader>
      <CardTitle>Configure Connection</CardTitle>
      <CardDescription>
        Enter your {providerFullName} credentials
      </CardDescription>
      <If condition={!isSandbox}>
        <SecureInfoAlert />
      </If>
    </CardHeader>
  );
};
