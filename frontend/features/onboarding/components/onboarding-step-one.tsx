'use client';

import { useStepContext } from '@/components/providers/step-provider';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { UpdateWrapper } from '@/components/ui/common/update-wrapper';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { zodResolver } from '@hookform/resolvers/zod';
import { ArrowRightIcon } from 'lucide-react';
import { SubmitHandler, useForm } from 'react-hook-form';

import { onboardingQuery } from '../hooks/onboarding.query';
import {
  OnboardingWorkspaceSchema,
  onboardingWorkspaceSchema,
} from '../schema/onboarding-workspace.schema';

export function OnboardingStepOne() {
  const form = useForm<OnboardingWorkspaceSchema>({
    resolver: zodResolver(onboardingWorkspaceSchema),
  });

  const { mutate, isPending } = onboardingQuery.mutation.useCreateWorkspace();
  const { goToNextStep } = useStepContext();

  const { control, handleSubmit } = form;

  const onSubmit: SubmitHandler<OnboardingWorkspaceSchema> = (data) => {
    mutate(data, {
      onSuccess: goToNextStep,
    });
  };

  return (
    <Form {...form}>
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Workspace Details</CardTitle>
            <CardDescription>
              Basic information about your workspace and organization
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <FormField
              control={control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel required>Workspace Name</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="e.g. CloudThinker Operations"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={control}
              name="organization_name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel required>Organization</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="e.g. CloudThinker Inc." />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>
        <div className="flex justify-end">
          <Button type="submit" className="gap-2" disabled={isPending}>
            <UpdateWrapper isPending={isPending}>
              Create Workspace
            </UpdateWrapper>
            <ArrowRightIcon className="size-4" />
          </Button>
        </div>
      </form>
    </Form>
  );
}
