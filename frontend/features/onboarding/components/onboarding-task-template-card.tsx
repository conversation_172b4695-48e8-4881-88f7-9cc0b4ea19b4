import { Badge } from '@/components/ui/badge';
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card';
import { If } from '@/components/ui/common/if';
import { Heading } from '@/components/ui/heading';
import { cn } from '@/lib/utils';
import { SchemaTaskTemplate } from '@/openapi-ts/gens';
import { CheckIcon, ClockIcon, TargetIcon, Zap } from 'lucide-react';

import { TaskComplexityConfig } from '../config/task-complexity.config';

interface OnboardingTaskTemplateCardProps {
  taskTemplate: SchemaTaskTemplate;
  selected: boolean;
}

export function OnboardingTaskTemplateCard({
  taskTemplate,
  selected,
}: OnboardingTaskTemplateCardProps) {
  const taskComplexityConfig =
    TaskComplexityConfig.CONFIG[taskTemplate.complexity];

  return (
    <Card
      className={cn(
        'group relative transition-all duration-300',
        'bg-primary/5',
        'focus-visible:ring-primary cursor-pointer focus-visible:ring-2 focus-visible:outline-none',
      )}
    >
      <If condition={selected}>
        <CheckIcon className="text-primary ring-primary absolute top-2 right-2 size-4 rounded-full ring-1" />
      </If>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between gap-3">
          <div className="min-w-0 flex-1">
            <Heading level={5} className="line-clamp-2">
              {taskTemplate.title}
            </Heading>
            <Badge
              variant="outline"
              className={cn('text-xs font-medium', taskComplexityConfig.color)}
            >
              <span className="mr-1">{taskComplexityConfig.icon}</span>
              {taskTemplate.complexity}
            </Badge>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4 pt-0">
        {/* Description */}
        <p className="text-muted-foreground line-clamp-3 text-sm leading-relaxed">
          {taskTemplate.description}
        </p>

        {/* Metrics Row */}
        <div className="space-y-4 text-sm">
          <div className="text-muted-foreground flex items-center gap-1.5 whitespace-nowrap">
            <ClockIcon className="size-4" />
            <span className="font-medium">{taskTemplate.duration}</span>
          </div>

          <If condition={taskTemplate.impact}>
            {(impact) => (
              <div className="text-muted-foreground flex items-center gap-1.5 whitespace-nowrap">
                <TargetIcon className="size-4" />
                <span className="font-medium">{impact}</span>
              </div>
            )}
          </If>
        </div>

        {/* Impact Badge */}
        <If condition={taskTemplate.impact}>
          {(impact) => (
            <div className="flex justify-end">
              <Badge variant="secondary" className="text-xs">
                <Zap className="mr-1 h-3 w-3" />
                Impact: {impact}
              </Badge>
            </div>
          )}
        </If>
      </CardContent>

      {/* Hover Overlay */}
      <div
        className="from-primary/20 to-primary/0 pointer-events-none absolute inset-0 bg-gradient-to-br opacity-0 transition-all duration-300 group-hover:opacity-100"
        aria-hidden="true"
      />
    </Card>
  );
}
