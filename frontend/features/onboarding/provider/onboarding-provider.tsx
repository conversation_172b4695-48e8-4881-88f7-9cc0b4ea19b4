'use client';

import { PropsWithChildren, createContext, useContext } from 'react';

import { SchemaCloudRegionPublic } from '@/openapi-ts/gens';

type OnboardingContextValue = {
  cloudRegions: SchemaCloudRegionPublic;
};

const OnboardingContext = createContext<OnboardingContextValue | null>(null);

export const OnboardingProvider = ({
  children,
  cloudRegions: cloudRegions,
}: PropsWithChildren<OnboardingContextValue>) => {
  return (
    <OnboardingContext.Provider value={{ cloudRegions: cloudRegions }}>
      {children}
    </OnboardingContext.Provider>
  );
};

export const useOnboardingContext = () => {
  const context = useContext(OnboardingContext);
  if (!context) {
    throw new Error(
      'useOnboardingContext must be used within an OnboardingProvider',
    );
  }
  return context;
};
