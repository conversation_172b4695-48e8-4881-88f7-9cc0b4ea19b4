import { CloudProvider } from '@/openapi-ts/gens';
import { api, fetchData } from '@/openapi-ts/openapi-fetch';
import { PathsRequestBodyDto } from '@/openapi-ts/utils.type';

// Mock API that simulates successful workspace creation
export const onboardingApi = {
  getStatus: () =>
    fetchData(
      api.GET('/api/v1/onboarding/status', {
        next: {
          revalidate: 0,
        },
      }),
    ),
  createWorkspace: (
    body: PathsRequestBodyDto<'/api/v1/onboarding/workspace'>,
  ) => fetchData(api.POST('/api/v1/onboarding/workspace', { body })),

  connect: {
    aws: (body: PathsRequestBodyDto<'/api/v1/onboarding/connect-aws'>) =>
      api.POST('/api/v1/onboarding/connect-aws', { body }),
    gcp: (body: PathsRequestBodyDto<'/api/v1/onboarding/connect-gcp'>) =>
      api.POST('/api/v1/onboarding/connect-gcp', { body }),
    azure: (body: PathsRequestBodyDto<'/api/v1/onboarding/connect-azure'>) =>
      api.POST('/api/v1/onboarding/connect-azure', { body }),
    sandbox: () => api.POST('/api/v1/onboarding/connect-sandbox'),
  },

  getRegions: (provider: CloudProvider) =>
    fetchData(
      api.GET('/api/v1/utils/constants/cloud_regions', {
        params: {
          query: {
            provider,
          },
        },
        next: {
          revalidate: 60 * 60 * 24,
        },
      }),
    ),

  getTaskTemplates: () =>
    fetchData(api.GET('/api/v1/onboarding/task-template')),

  completeTaskTemplate: (
    body: PathsRequestBodyDto<'/api/v1/onboarding/complete-task-template'>,
  ) => api.POST('/api/v1/onboarding/complete-task-template', { body }),
};
