import cron from 'cron-validate';
import { z } from 'zod';

// Custom Zod validator for cron expressions
const cronValidator = z.string().superRefine((value, ctx) => {
  if (!value) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: 'Cron expression is required',
    });
    return;
  }

  try {
    const cronResult = cron(value);

    if (!cronResult.isValid()) {
      const errors = cronResult.getError();
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message:
          errors.length > 0 ? errors.join(', ') : 'Invalid cron expression',
      });
    }
  } catch {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: 'Invalid cron expression format',
    });
  }
});

export const taskSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().min(1, 'Description is required'),
  schedule: cronValidator,
});

export type TaskSchema = z.infer<typeof taskSchema>;
