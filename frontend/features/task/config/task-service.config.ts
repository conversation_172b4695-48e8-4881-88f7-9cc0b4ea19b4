import { TaskServiceEnum } from '@/openapi-ts/gens';
import { createUtilityConfig } from '@/utils/option-config';
import {
  Database,
  DollarSign,
  HardDrive,
  HelpCircle,
  LucideIcon,
  MessageCircle,
  Monitor,
  Network,
  Server,
  Settings,
  Shield,
  Video,
  Zap,
} from 'lucide-react';

export const taskServiceConfig = createUtilityConfig({
  [TaskServiceEnum.COMPUTE]: {
    label: 'Compute',
    icon: Server,
  },
  [TaskServiceEnum.STORAGE]: {
    label: 'Storage',
    icon: HardDrive,
  },
  [TaskServiceEnum.SERVERLESS]: {
    label: 'Serverless',
    icon: Zap,
  },
  [TaskServiceEnum.DATABASE]: {
    label: 'Database',
    icon: Database,
  },
  [TaskServiceEnum.NETWORK]: {
    label: 'Network',
    icon: Network,
  },
  [TaskServiceEnum.MESSAGING]: {
    label: 'Messaging',
    icon: MessageCircle,
  },
  [TaskServiceEnum.MANAGEMENT]: {
    label: 'Management',
    icon: Settings,
  },
  [TaskServiceEnum.BILLING]: {
    label: 'Billing',
    icon: DollarSign,
  },
  [TaskServiceEnum.CROSS_SERVICE]: {
    label: 'Cross Service',
    icon: Server,
  },
  [TaskServiceEnum.MONITORING]: {
    label: 'Monitoring',
    icon: Monitor,
  },
  [TaskServiceEnum.STREAMING]: {
    label: 'Streaming',
    icon: Video,
  },
  [TaskServiceEnum.SECURITY]: {
    label: 'Security',
    icon: Shield,
  },
  [TaskServiceEnum.ALL]: {
    label: 'All',
    icon: Server,
  },
  [TaskServiceEnum.OTHER]: {
    label: 'Other',
    icon: HelpCircle,
  },
} satisfies Record<
  TaskServiceEnum,
  {
    label: string;
    icon: LucideIcon;
  }
>);
