import { PropsWith<PERSON><PERSON>dren, useEffect, useMemo } from 'react';

import Link from 'next/link';
import { useRouter } from 'next/navigation';

import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { If } from '@/components/ui/common/if';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import pathsConfig from '@/config/paths.config';
import { agentDetailUrl } from '@/features/agent/hooks/use-navigate-agent-detail';
import { CronExpressionInput } from '@/features/cron/components/cron-expression-input';
import { taskTemplateQ<PERSON>y } from '@/features/task-template/hooks/task-template.query';
import { useUserContext } from '@/features/user/provider/user-provider';
import { cn } from '@/lib/utils';
import {
  RunModeEnum,
  SchemaAppSchemasTaskTemplateTaskTemplateResponse,
  TaskCategoryEnum,
  TaskCouldEnum,
  TaskServiceEnum,
} from '@/openapi-ts/gens';
import { zodResolver } from '@hookform/resolvers/zod';
import { CalendarIcon, Loader2Icon, PlayIcon, SaveIcon } from 'lucide-react';
import { SubmitHandler, useForm, useWatch } from 'react-hook-form';
import { useToggle } from 'usehooks-ts';

import { taskQuery } from '../hooks/task.query';
import { TaskSchema, taskSchema } from '../schema/task.schema';

type Props = PropsWithChildren<{
  taskTemplate: Pick<
    SchemaAppSchemasTaskTemplateTaskTemplateResponse,
    'task' | 'context' | 'schedule'
  >;
  hideStartNow?: boolean;
}>;

export function OperationHubDialog({ taskTemplate, children, hideStartNow = false }: Props) {
  const [open, toggle] = useToggle(false);
  const router = useRouter();
  const { agentId } = useUserContext();
  const defaultValues = useMemo(
    (): TaskSchema => ({
      title: taskTemplate.task,
      description: taskTemplate.context,
      schedule: taskTemplate.schedule ?? '*/30 * * * *',
    }),
    [taskTemplate],
  );

  const form = useForm<TaskSchema>({
    resolver: zodResolver(taskSchema),
    defaultValues,
  });

  const { control, handleSubmit, reset } = form;

  useEffect(() => {
    reset(defaultValues);
  }, [defaultValues, reset]);

  const createTaskTemplateMutation = taskTemplateQuery.mutation.useCreate();

  const createTaskMutation = taskQuery.mutation.useCreate();

  const watchDescription = useWatch({
    control,
    name: 'description',
  });

  const onCreateTaskTemplate: SubmitHandler<TaskSchema> = (values) => {
    createTaskTemplateMutation.mutate(
      {
        task: values.title,
        context: values.description.trim(),
        schedule: values.schedule,
        category: TaskCategoryEnum.OTHER,
        service: TaskServiceEnum.OTHER,
        service_name: 'cloudthinker',
        cloud: TaskCouldEnum.AWS,
        run_mode: RunModeEnum.agent,
      },
      {
        onSuccess: toggle,
      },
    );
  };

  const handleScheduleTask: SubmitHandler<TaskSchema> = (data) => {
    createTaskMutation.mutate(
      {
        title: data.title,
        description: data.description.trim(),
        schedule: data.schedule,
        agent_config: {
          agent_id: agentId,
          message: data.description.trim(),
          conversation_name: data.title,
          context_ids: [],
        },
      },
      {
        onSuccess: () => router.push(pathsConfig.app.tasks),
      },
    );
  };

  return (
    <AlertDialog open={open} onOpenChange={toggle}>
      <AlertDialogTrigger asChild>{children}</AlertDialogTrigger>
      <AlertDialogContent className="max-w-3xl">
        <AlertDialogHeader>
          <AlertDialogTitle>Operation Hub</AlertDialogTitle>
          <AlertDialogDescription>
            Create a new task or schedule an existing task.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <Form {...form}>
          <form className="space-y-8">
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <FormField
                control={control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel required>Title</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter title" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <FormField
              control={control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel required>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter description"
                      className="min-h-18"
                      {...field}
                      value={field.value ?? ''}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Schedule Configuration */}
            <FormField
              control={control}
              name="schedule"
              render={({ field }) => (
                <FormItem>
                  <FormLabel required>Schedule Configuration</FormLabel>
                  <FormControl>
                    <CronExpressionInput
                      value={field.value}
                      onChange={field.onChange}
                      placeholder="Enter cron expression (e.g., 0 9 * * 1-5)"
                    />
                  </FormControl>
                  <FormDescription>
                    Configure when this task should run automatically. Click the
                    edit button to use the visual cron builder, or enter a cron
                    expression directly. Leave empty for manual execution only.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div
              className={cn(
                'items-center justify-end gap-2',
                'grid grid-cols-2 sm:flex',
              )}
            >
              <AlertDialogCancel
                disabled={
                  createTaskTemplateMutation.isPending ||
                  createTaskMutation.isPending
                }
                className="mt-0"
              >
                Cancel
              </AlertDialogCancel>

              <Button
                disabled={
                  createTaskTemplateMutation.isPending ||
                  createTaskMutation.isPending
                }
                variant={hideStartNow ? "outlinePrimary" : "outlinePrimary"}
                type="button"
                className="gap-2"
                onClick={handleSubmit(onCreateTaskTemplate)}
              >
                <If condition={createTaskTemplateMutation.isPending}>
                  <Loader2Icon className="size-4 animate-spin" />
                </If>
                <SaveIcon className="size-4" />
                Save Task
              </Button>

              <Button
                disabled={
                  createTaskTemplateMutation.isPending ||
                  createTaskMutation.isPending
                }
                variant={hideStartNow ? "default" : "outlinePrimary"}
                type="button"
                className="gap-2"
                onClick={handleSubmit(handleScheduleTask)}
              >
                <If condition={createTaskMutation.isPending}>
                  <Loader2Icon className="size-4 animate-spin" />
                </If>
                <CalendarIcon className="size-4" />
                Schedule Task
              </Button>

              {!hideStartNow && (
                <Button
                  disabled={
                    createTaskTemplateMutation.isPending ||
                    createTaskMutation.isPending ||
                    !watchDescription?.trim()
                  }
                  type="button"
                  asChild
                >
                  <Link
                    href={agentDetailUrl(agentId, {
                      initialMessage: watchDescription?.trim(),
                    })}
                    className={cn('gap-2', {
                      'pointer-events-none opacity-50':
                        createTaskTemplateMutation.isPending ||
                        createTaskMutation.isPending ||
                        !watchDescription?.trim(),
                    })}
                  >
                    <PlayIcon className="size-4" />
                    Start Now
                  </Link>
                </Button>
              )}
            </div>
          </form>
        </Form>
      </AlertDialogContent>
    </AlertDialog>
  );
}
