import { useMemo } from 'react';

import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { If } from '@/components/ui/common/if';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { CronExpressionInput } from '@/features/cron/components/cron-expression-input';
import { SchemaTaskResponse } from '@/openapi-ts/gens';
import { differentObject } from '@/utils/object';
import { zodResolver } from '@hookform/resolvers/zod';
import { flow } from 'lodash';
import { Loader2Icon, SquarePenIcon } from 'lucide-react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, useForm } from 'react-hook-form';
import { useToggle } from 'usehooks-ts';

import { taskQuery } from '../hooks/task.query';
import { TaskSchema, taskSchema } from '../schema/task.schema';

type Props = {
  task: SchemaTaskResponse;
};

export function EditTaskDialog({ task }: Props) {
  const [open, toggle] = useToggle(false);
  const defaultValues = useMemo(
    (): TaskSchema => ({
      title: task.title,
      description: task.description ?? '',
      schedule: task.schedule,
    }),
    [task],
  );

  const form = useForm<TaskSchema>({
    resolver: zodResolver(taskSchema),
    defaultValues,
  });

  const { control, handleSubmit } = form;
  const { mutate, isPending } = taskQuery.mutation.useUpdateTask(task.id);

  const onSubmit: SubmitHandler<TaskSchema> = flow(
    differentObject(defaultValues),
    (changes) =>
      mutate(changes, {
        onSuccess: toggle,
      }),
  );

  return (
    <AlertDialog open={open} onOpenChange={toggle}>
      <AlertDialogTrigger asChild>
        <Button size="sm" className="gap-2">
          <SquarePenIcon className="size-4" />
          Edit
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent className="max-w-3xl">
        <AlertDialogHeader>
          <AlertDialogTitle>Edit Task</AlertDialogTitle>
          <AlertDialogDescription>
            Update task details and schedule configuration.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <Form {...form}>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <FormField
                control={control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel required>Task Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter task name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <FormField
              control={control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel required>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter item description"
                      className="resize-none"
                      {...field}
                      value={field.value ?? ''}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Schedule Configuration */}
            <FormField
              control={control}
              name="schedule"
              render={({ field }) => (
                <FormItem>
                  <FormLabel required>Schedule Configuration</FormLabel>
                  <FormControl>
                    <CronExpressionInput
                      value={field.value}
                      onChange={field.onChange}
                      placeholder="Enter cron expression (e.g., 0 9 * * 1-5)"
                    />
                  </FormControl>
                  <FormDescription>
                    Configure when this task should run automatically. Click the
                    edit button to use the visual cron builder, or enter a cron
                    expression directly. Leave empty for manual execution only.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex items-center justify-end gap-2">
              <AlertDialogCancel>Cancel</AlertDialogCancel>

              <Button disabled={isPending} type="submit" className="gap-2">
                <If condition={isPending}>
                  <Loader2Icon className="size-4 animate-spin" />
                </If>
                Save
              </Button>
            </div>
          </form>
        </Form>
      </AlertDialogContent>
    </AlertDialog>
  );
}
