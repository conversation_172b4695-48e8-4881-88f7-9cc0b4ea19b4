import { useState } from 'react';

import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Pagination } from '@/components/ui/pagination/pagination';
import { Skeleton } from '@/components/ui/skeleton';
import { SpinnerContainer } from '@/components/ui/spinner-container';
import { SchemaTaskResponse } from '@/openapi-ts/gens';
import { DEFAULT_LIMIT } from '@/utils/with-pagination-defaults';
import { HistoryIcon } from 'lucide-react';

import { taskQuery } from '../hooks/task.query';
import { TaskHistoryCard } from './task-history-card';

type Props = {
  task: SchemaTaskResponse;
};

export function TaskHistoriesDialog({ task }: Props) {
  const [page, setPage] = useState(1);

  const { data, isLoading, isRefetching } = taskQuery.query.useHistories(
    task.id,
    {
      page,
      limit: DEFAULT_LIMIT,
    },
  );

  if (data) {
    return (
      <AlertDialog>
        <AlertDialogTrigger asChild>
          <Button
            variant="outlinePrimary"
            size="sm"
            className="gap-2"
            disabled={!data.total}
          >
            <HistoryIcon className="size-4" />
            <span>History ({data.total})</span>
          </Button>
        </AlertDialogTrigger>
        <AlertDialogContent className="flex max-h-[90dvh] max-w-3xl flex-col">
          <AlertDialogHeader>
            <AlertDialogTitle>
              Execution History - {task.title}
            </AlertDialogTitle>
            <AlertDialogDescription>
              View past executions and their results
            </AlertDialogDescription>
          </AlertDialogHeader>

          <SpinnerContainer
            loading={isRefetching}
            className="overflow-thin-auto space-y-2"
          >
            {data.data.map((history) => (
              <TaskHistoryCard key={history.id} history={history} />
            ))}
          </SpinnerContainer>
          <Pagination
            currentPage={page}
            totalPages={Math.ceil(data.total / DEFAULT_LIMIT)}
            onPageChange={setPage}
            total={data.total}
            unit="history"
          />
          <AlertDialogFooter>
            <AlertDialogCancel>Done</AlertDialogCancel>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    );
  }

  if (isLoading) {
    return <Skeleton className="h-10 w-10" />;
  }
}
