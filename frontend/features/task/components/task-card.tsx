import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Heading } from '@/components/ui/heading';
import { formatCronReadable } from '@/features/cron/utils/cron';
import { SchemaTaskResponse } from '@/openapi-ts/gens';
import { CalendarIcon, ClockIcon, InfoIcon, Trash2Icon } from 'lucide-react';

import { ConfirmDeleteTask } from './confirm-delete-task';
import { EditTaskDialog } from './edit-task-dialog';
import { TaskDuration } from './task-duration';
import { TaskEnableSwitch } from './task-enable-switch';
import { TaskHistoriesDialog } from './task-histories-dialog';
import { TaskLastDuration } from './task-last-duration';
import { TaskStatusBadge } from './task-status-badge';

interface TaskCardProps {
  task: SchemaTaskResponse;
}

export function TaskCard({ task }: TaskCardProps) {
  // const priority = taskPriorityConfig.CONFIG[task.priority];

  return (
    <Card className="flex h-full flex-col space-y-2 p-3">
      <div className="space-y-1">
        <div className="flex justify-between gap-4">
          <div className="flex items-center gap-2 truncate">
            <Heading level={5} className="truncate">
              {task.title}
            </Heading>
            {/* <If
              condition={task.enable}
              fallback={<Badge variant="cancelled">Inactive</Badge>}
            >
              <Badge variant="success">Active</Badge>
            </If> */}
            {/* <Badge variant={priority.variant}>{priority.label}</Badge> */}
          </div>
          <div className="flex shrink-0 items-center gap-2">
            <TaskEnableSwitch task={task} />
          </div>
        </div>
        <p className="text-muted-foreground line-clamp-2 text-sm">
          {task.description}
        </p>
      </div>
      <div className="space-y-2">
        {[
          {
            label: 'Next Run',
            value: formatCronReadable(task.schedule),
            icon: CalendarIcon,
          },
          {
            label: 'Average Duration',
            value: <TaskDuration taskId={task.id} />,
            icon: ClockIcon,
          },
          {
            label: 'Last Duration',
            value: <TaskLastDuration taskId={task.id} />,
            icon: ClockIcon,
          },
          {
            label: 'Last Status',
            value: <TaskStatusBadge status={task.execution_status} />,
            icon: InfoIcon,
          },
        ].map((item) => (
          <div key={item.label} className="flex gap-1 text-sm">
            <p className="text-muted-foreground flex shrink-0 gap-1">
              <item.icon className="relative top-0.5 size-4" />
              {item.label}:
            </p>

            <div className="font-medium">{item.value}</div>
          </div>
        ))}
      </div>
      <div className="!mt-auto flex items-center justify-end gap-2">
        <ConfirmDeleteTask taskId={task.id}>
          <Button variant="outlineDestructive" size="sm" className="gap-2">
            <Trash2Icon className="size-4" />
            Delete
          </Button>
        </ConfirmDeleteTask>
        <TaskHistoriesDialog task={task} />
        <EditTaskDialog task={task} />
      </div>
    </Card>
  );
}
