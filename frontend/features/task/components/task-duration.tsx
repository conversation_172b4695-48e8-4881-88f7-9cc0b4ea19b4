import { Skeleton } from '@/components/ui/skeleton';
import { formatToMinutesAndSeconds } from '@/lib/date-utils';

import { taskQuery } from '../hooks/task.query';

export function TaskDuration({ taskId }: { taskId: string }) {
  const { data, isLoading } = taskQuery.query.useAverageRunTime(taskId);
  if (data) {
    return typeof data.data === 'number'
      ? formatToMinutesAndSeconds(Math.round(data.data))
      : 'N/A';
  }
  if (isLoading) {
    return <Skeleton className="h-4 w-16" />;
  }
  return 'N/A';
}
