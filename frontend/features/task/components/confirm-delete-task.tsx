import { PropsWithChildren } from 'react';

import { DeleteConfirmAlert } from '@/components/ui/common/delete-confirm-alert';

import { taskQuery } from '../hooks/task.query';

type Props = PropsWithChildren<{
  taskId: string;
}>;

export function ConfirmDeleteTask({ children, taskId }: Props) {
  const { mutate, isPending } = taskQuery.mutation.useDelete(taskId);

  const onConfirm = (toggle: () => void) => {
    mutate(undefined, {
      onSuccess: toggle,
    });
  };

  return (
    <DeleteConfirmAlert
      title="Delete Task"
      loading={isPending}
      onConfirm={onConfirm}
    >
      {children}
    </DeleteConfirmAlert>
  );
}
