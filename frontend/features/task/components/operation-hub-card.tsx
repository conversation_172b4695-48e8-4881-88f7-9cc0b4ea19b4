import { highlightMentions } from '@/components/chat/utils/mention-highlighting';
import { Badge } from '@/components/ui/badge';
import {
  Card,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { SchemaAppSchemasTaskTemplateTaskTemplateResponse } from '@/openapi-ts/gens';

import { taskCategoryConfig } from '../config/task-category.config';
import { taskServiceConfig } from '../config/task-service.config';
import { DisplayTaskCloud } from './display-task-cloud';
import { OperationHubDialog } from './operation-hub-dialog';

type Props = {
  taskTemplate: SchemaAppSchemasTaskTemplateTaskTemplateResponse;
};

export function OperationHubCard({ taskTemplate }: Props) {
  const { highlightedText } = highlightMentions(taskTemplate.context);
  const taskCategory = taskCategoryConfig.CONFIG[taskTemplate.category];
  const taskService = taskServiceConfig.CONFIG[taskTemplate.service];

  return (
    <OperationHubDialog taskTemplate={taskTemplate}>
      <Card
        className={cn(
          'bg-primary/5',
          'transition-all hover:scale-[0.98] hover:shadow-inner',
        )}
      >
        <CardHeader className="h-full p-4">
          <div className="flex items-center gap-2">
            <div
              className={cn(
                'shrink-0 rounded-md p-0.5',
                taskCategory.bgColor,
                taskCategory.color,
              )}
            >
              <taskCategory.icon className="size-4" />
            </div>
            <CardTitle className="line-clamp-1">{taskTemplate.task}</CardTitle>
          </div>
          <CardDescription className="line-clamp-4 grow">
            {highlightedText}
          </CardDescription>
          <div className="flex grow-0 items-center justify-between">
            <DisplayTaskCloud cloud={taskTemplate.cloud} />
            <Badge variant="ghost" className="gap-2">
              <taskService.icon className="size-4" />
              {taskService.label}
            </Badge>
          </div>
        </CardHeader>
      </Card>
    </OperationHubDialog>
  );
}
