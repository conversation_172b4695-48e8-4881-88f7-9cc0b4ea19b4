import { TaskCouldEnum } from '@/openapi-ts/gens';
import AwsLogo from '@/public/aws-logo.svg';
import AzureLogo from '@/public/azure-logo.svg';
import GcpLogo from '@/public/gcp-logo.svg';

const sizeClass = 'w-6';

export function DisplayTaskCloud({ cloud }: { cloud: TaskCouldEnum }) {
  if (cloud === TaskCouldEnum.ALL) {
    return (
      <div className="flex items-center gap-2">
        <AwsLogo className={sizeClass} />
        <AzureLogo className={sizeClass} />
        <GcpLogo className={sizeClass} />
      </div>
    );
  }

  if (cloud === TaskCouldEnum.AWS) {
    return <AwsLogo className={sizeClass} />;
  }

  if (cloud === TaskCouldEnum.AZURE) {
    return <AzureLogo className={sizeClass} />;
  }

  if (cloud === TaskCouldEnum.GCP) {
    return <GcpLogo className={sizeClass} />;
  }

  const neverHappen: never = cloud;
  return neverHappen;
}
