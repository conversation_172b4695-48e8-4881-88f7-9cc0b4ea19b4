import Link from 'next/link';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { If } from '@/components/ui/common/if';
import { WithTooltip } from '@/components/ui/tooltip';
import { agentDetailUrl } from '@/features/agent/hooks/use-navigate-agent-detail';
import { useUserContext } from '@/features/user/provider/user-provider';
import {
  formatFullDateTime,
  formatToMinutesAndSeconds,
} from '@/lib/date-utils';
import {
  SchemaTaskHistoryResponse,
  TaskExecutionStatus,
} from '@/openapi-ts/gens';
import { EyeIcon } from 'lucide-react';

import { TASK_STATUS_CONFIG } from '../config/task-status.config';

type Props = {
  history: SchemaTaskHistoryResponse;
};

export function TaskHistoryCard({ history }: Props) {
  const taskStatus = TASK_STATUS_CONFIG.OBJECT[history.status];
  const { agentId } = useUserContext();

  return (
    <Card>
      <CardHeader className="p-4">
        <div className="flex justify-between">
          <CardTitle>
            <Badge variant={taskStatus.variant}>{taskStatus.label}</Badge>
          </CardTitle>

          <div className="ml-auto flex w-fit items-center gap-2">
            <If
              condition={
                history.status === TaskExecutionStatus.required_approval
              }
            >
              <WithTooltip
                tooltip={
                  <div className="space-y-1">
                    <p className="text-sm font-medium">Continue Task</p>
                    <p className="text-xs">
                      Click to approve and continue the task from the execution
                      history
                    </p>
                  </div>
                }
              ></WithTooltip>
            </If>

            <WithTooltip tooltip="View agent details">
              <Button
                asChild
                variant="outlinePrimary"
                size="icon"
                className="gap-2"
              >
                <Link
                  href={agentDetailUrl(agentId, {
                    conversationId: history.conversation_id,
                  })}
                >
                  <EyeIcon className="size-4" />
                </Link>
              </Button>
            </WithTooltip>
          </div>
        </div>
        <CardDescription>
          {formatFullDateTime(history.start_time)}
        </CardDescription>
      </CardHeader>
      <CardContent className="p-4 pt-0">
        <div className="flex items-center gap-x-6 gap-y-2">
          {[
            //   {
            //     label: 'Next Run',
            //     value: formatCronReadable(task.schedule),
            //   },
            {
              label: 'Run Time',
              value: formatToMinutesAndSeconds(history.run_time ?? 0),
            },
          ].map((item) => (
            <div key={item.label} className="space-y-1">
              <p className="text-sm font-medium">{item.label.toUpperCase()}</p>
              <div className="text-muted-foreground text-xs">{item.value}</div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
