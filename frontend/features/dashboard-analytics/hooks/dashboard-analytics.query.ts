import { <PERSON><PERSON><PERSON><PERSON> } from '@/components/utils/cache-key';
import { createQueryKeys } from '@lukemorales/query-key-factory';
import { useQuery } from '@tanstack/react-query';

import { DashboardAnalyticsQueryParams } from '../models/dashboard-analytics.type';
import { dashboardAnalyticsApi } from '../services/dashboard-analytics.api';

const dashboardAnalyticsQueryKeys = createQueryKeys(
  CacheKey.DashboardAnalytics,
  {
    analytics: (params?: DashboardAnalyticsQueryParams) => ({
      queryKey: [params],
      queryFn: () => dashboardAnalyticsApi.getAnalytics(params),
    }),

    highConsumingServices: (params?: DashboardAnalyticsQueryParams) => ({
      queryKey: [params],
      queryFn: () => dashboardAnalyticsApi.getHighConsumingServices(params),
    }),

    topRecommendations: (params?: DashboardAnalyticsQueryParams) => ({
      queryKey: [params],
      queryFn: () => dashboardAnalyticsApi.getTopRecommendations(params),
    }),
  },
);

const useAnalytics = (params?: DashboardAnalyticsQueryParams) =>
  useQuery({
    ...dashboardAnalyticsQueryKeys.analytics(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

const useHighConsumingServices = (params?: DashboardAnalyticsQueryParams) =>
  useQuery({
    ...dashboardAnalyticsQueryKeys.highConsumingServices(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

const useTopRecommendations = (params?: DashboardAnalyticsQueryParams) =>
  useQuery({
    ...dashboardAnalyticsQueryKeys.topRecommendations(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

export const dashboardAnalyticsQuery = {
  query: {
    useAnalytics,
    useHighConsumingServices,
    useTopRecommendations,
  },
};