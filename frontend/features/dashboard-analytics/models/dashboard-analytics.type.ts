import { components } from '@/openapi-ts/gens';

// Extract types from OpenAPI generated types
export type DashboardAnalyticsResponse =
  components['schemas']['DashboardAnalyticsResponse'];

export type HighConsumingServicesResponse =
  components['schemas']['HighConsumingServicesResponse'];

export type TopRecommendationsResponse =
  components['schemas']['TopRecommendationsResponse'];

export type ServiceConsumptionData =
  components['schemas']['ServiceConsumptionData'];

export type TopRecommendationData = components['schemas']['TopRecommendationData'];

export type CloudProvider = components['schemas']['CloudProvider'];
export type ResourceCategory = components['schemas']['ResourceCategory'];

// Query parameters interface
export interface DashboardAnalyticsQueryParams {
  cloud_provider?: CloudProvider;
  service_category?: ResourceCategory;
  min_savings_amount?: number;
  top_services_limit?: number;
}