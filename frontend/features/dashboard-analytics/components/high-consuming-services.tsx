import * as React from 'react';

import { Bar<PERSON><PERSON> } from '@/components/chat/components/message/charts/bar-chart';
import { CHART_THEME_COLORS } from '@/components/chat/components/message/charts/common';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { dashboardAnalyticsQuery } from '@/features/dashboard-analytics';

interface HighConsumingServicesProps {
  topServicesLimit?: number;
}

export function HighConsumingServices({
  topServicesLimit = 10,
}: HighConsumingServicesProps) {
  const { data: servicesData, isLoading } =
    dashboardAnalyticsQuery.query.useHighConsumingServices({
      top_services_limit: topServicesLimit,
    });

  const chartData = React.useMemo(() => {
    if (!servicesData?.services) return [];

    // Filter out services with 0 savings
    const validServices = servicesData.services.filter(
      (service) => service.potential_monthly_savings > 0,
    );

    if (validServices.length === 0) return [];

    // Take top 10 services for bar chart (more readable than pie chart)
    const topServices = validServices.slice(0, 10);

    const data = topServices.map((service, index) => ({
      name: service.service_type,
      value: service.potential_monthly_savings,
      color: CHART_THEME_COLORS[index % CHART_THEME_COLORS.length],
      originalValue: service.potential_monthly_savings,
    }));

    return data;
  }, [servicesData]);

  const totalSavings = servicesData?.total_potential_savings || 0;
  const hasValidData = chartData.length > 0 && totalSavings > 0;

  // Dynamic title based on actual number of services
  const chartTitle =
    chartData.length > 0
      ? `Top ${chartData.length} High-Consuming Service${chartData.length !== 1 ? 's' : ''}`
      : 'High-Consuming Services';

  if (isLoading) {
    return (
      <Card className="h-full">
        <CardHeader>
          <CardTitle>{chartTitle}</CardTitle>
          <CardDescription>
            Monthly Savings Opportunities by Service
          </CardDescription>
        </CardHeader>
        <CardContent className="overflow-thin-auto h-[400px] pt-0">
          <div className="flex h-full items-center justify-center">
            <Skeleton className="h-full w-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!hasValidData) {
    return (
      <Card className="h-full">
        <CardHeader>
          <CardTitle>{chartTitle}</CardTitle>
          <CardDescription>
            Monthly Savings Opportunities by Service
          </CardDescription>
        </CardHeader>
        <CardContent className="overflow-thin-auto h-[400px] pt-0">
          <div className="flex h-full items-center justify-center">
            <p className="text-muted-foreground text-sm">No data available</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle>{chartTitle}</CardTitle>
        <CardDescription>
          Monthly Savings Opportunities by Service
        </CardDescription>
      </CardHeader>
      <CardContent className="overflow-thin-auto h-[400px] pt-0">
        <div className="h-full w-full">
          <BarChart
            data={chartData}
            chartData={{
              labels: chartData.map((item) => item.name),
              datasets: [
                {
                  data: chartData.map((item) => item.value),
                  label: 'Potential Savings',
                },
              ],
              x_axis: { title: 'Service Type' },
              y_axis: { title: 'Potential Savings ($)' },
              display_options: {
                show_grid: true,
                show_legend: false,
              },
            }}
            containerWidth={800}
          />
        </div>
      </CardContent>
    </Card>
  );
}
