import { SchemaCloudSyncConfigUpdate } from '@/openapi-ts/gens';
import { ApiError } from '@/openapi-ts/openapi-fetch';
import { createQueryKeys } from '@lukemorales/query-key-factory';
import {
  keepPreviousData,
  useMutation,
  useQuery,
  useQueryClient,
} from '@tanstack/react-query';
import { toast } from 'sonner';

import {
  CloudSyncConfigQueryParams,
  ResourceTypesQueryParams,
} from '../models/cloud-sync-config.type';
import { cloudSyncConfigApi } from '../services/cloud-sync-config.api';

const cloudSyncConfigQueryKeys = createQueryKeys('cloud-sync-configs', {
  list: (params?: CloudSyncConfigQueryParams) => ({
    queryKey: [params],
    queryFn: () => cloudSyncConfigApi.list(params),
  }),
  resourceTypes: (params: ResourceTypesQueryParams) => ({
    queryKey: ['resource-types', params],
    queryFn: () => cloudSyncConfigApi.getResourceTypes(params),
  }),
});

const useList = (params?: CloudSyncConfigQueryParams) => {
  return useQuery({
    ...cloudSyncConfigQueryKeys.list(params),
    placeholderData: keepPreviousData,
  });
};

const useResourceTypes = (params: ResourceTypesQueryParams) => {
  return useQuery({
    ...cloudSyncConfigQueryKeys.resourceTypes(params),
    enabled: !!params.cloud_provider,
  });
};

const useCreate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: cloudSyncConfigApi.create,
    onSuccess: () => {
      toast.success('Cloud sync configuration created successfully');
      queryClient.invalidateQueries({
        queryKey: cloudSyncConfigQueryKeys.list._def,
      });
    },
    onError: (error: any) => {
      // Rate limit errors are already handled in openapi-fetch with custom messages
      if (error instanceof ApiError && error.status === 429) {
        return; // Don't show duplicate toast
      }
      toast.error(
        error?.message || 'Failed to create cloud sync configuration',
      );
    },
  });
};

const useUpdate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      id,
      data,
    }: {
      id: string;
      data: SchemaCloudSyncConfigUpdate;
    }) => cloudSyncConfigApi.detail(id).update(data),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: cloudSyncConfigQueryKeys.list._def,
      });
    },
    onError: (error: any) => {
      // Rate limit errors are already handled in openapi-fetch with custom messages
      if (error instanceof ApiError && error.status === 429) {
        return; // Don't show duplicate toast
      }
      toast.error(
        error?.message || 'Failed to update cloud sync configuration',
      );
    },
  });
};

const useDelete = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (configId: string) =>
      cloudSyncConfigApi.detail(configId).delete(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: cloudSyncConfigQueryKeys.list._def,
      });
    },
    onError: (error: any) => {
      // Rate limit errors are already handled in openapi-fetch with custom messages
      if (error instanceof ApiError && error.status === 429) {
        return; // Don't show duplicate toast
      }
      toast.error(
        error?.message || 'Failed to delete cloud sync configuration',
      );
    },
  });
};

const useTriggerSync = () => {
  return useMutation({
    mutationFn: (configId: string) =>
      cloudSyncConfigApi.detail(configId).triggerSync(),
    onSuccess: () => {},
    onError: (error: any) => {
      // Rate limit errors are already handled in openapi-fetch with custom messages
      if (error instanceof ApiError && error.status === 429) {
        return; // Don't show duplicate toast
      }
      toast.error(error?.message || 'Failed to trigger manual sync');
    },
  });
};

export const cloudSyncConfigQuery = {
  query: {
    useList,
    useResourceTypes,
  },
  mutation: {
    useCreate,
    useUpdate,
    useDelete,
    useTriggerSync,
  },
};
