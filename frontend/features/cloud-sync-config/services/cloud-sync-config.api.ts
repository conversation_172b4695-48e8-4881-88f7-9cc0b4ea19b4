import {
  SchemaCloudSyncConfigCreate,
  SchemaCloudSyncConfigUpdate,
} from '@/openapi-ts/gens';
import { api, fetchData } from '@/openapi-ts/openapi-fetch';

import {
  CloudSyncConfigQueryParams,
  ResourceTypesQueryParams,
} from '../models/cloud-sync-config.type';

export const cloudSyncConfigApi = {
  list: (query?: CloudSyncConfigQueryParams) =>
    fetchData(api.GET('/api/v1/cloud-sync-config/', { params: { query } })),

  getResourceTypes: (query: ResourceTypesQueryParams) =>
    fetchData(
      api.GET('/api/v1/cloud-sync-config/resource-types', {
        params: { query },
      }),
    ),

  create: (body: SchemaCloudSyncConfigCreate) =>
    fetchData(api.POST('/api/v1/cloud-sync-config/', { body })),

  detail: (configId: string) => ({
    update: (body: SchemaCloudSyncConfigUpdate) =>
      fetchData(
        api.PUT('/api/v1/cloud-sync-config/{config_id}', {
          params: { path: { config_id: configId } },
          body,
        }),
      ),

    delete: () =>
      fetchData(
        api.DELETE('/api/v1/cloud-sync-config/{config_id}', {
          params: { path: { config_id: configId } },
        }),
      ),

    triggerSync: () =>
      fetchData(
        api.POST('/api/v1/cloud-sync-config/{config_id}/sync', {
          params: { path: { config_id: configId } },
        }),
      ),
  }),
};
