import { api, fetchData, publicApi } from '@/openapi-ts/openapi-fetch';

export const shareApi = {
  // Report sharing
  createReportShare: (reportId: string) =>
    fetchData(
      api.POST('/api/v1/reports/{report_id}/share', {
        params: { path: { report_id: reportId } },
      }),
    ),

  revokeReportShare: (reportId: string) =>
    fetchData(
      api.DELETE('/api/v1/reports/{report_id}/share', {
        params: { path: { report_id: reportId } },
      }),
    ),

  getSharedReport: (shareId: string) =>
    fetchData(
      publicApi.GET('/api/v1/public/reports/{share_id}', {
        params: { path: { share_id: shareId } },
      }),
    ),

  // Dashboard sharing
  createDashboardShare: (dashboardId: string) =>
    fetchData(
      api.POST('/api/v1/dashboards/{dashboard_id}/share', {
        params: { path: { dashboard_id: dashboardId } },
      }),
    ),

  revokeDashboardShare: (dashboardId: string) =>
    fetchData(
      api.DELETE('/api/v1/dashboards/{dashboard_id}/share', {
        params: { path: { dashboard_id: dashboardId } },
      }),
    ),

  getSharedDashboard: (shareId: string) =>
    fetchData(
      publicApi.GET('/api/v1/public/dashboards/{share_id}', {
        params: { path: { share_id: shareId } },
      }),
    ),
};
