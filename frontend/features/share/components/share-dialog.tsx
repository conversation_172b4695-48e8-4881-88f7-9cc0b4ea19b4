'use client';

import { PropsWithChildren, useEffect, useState } from 'react';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Check, Copy, ExternalLink, Trash2 } from 'lucide-react';
import { useToggle } from 'usehooks-ts';

import { shareQuery } from '../hooks/share.query';

interface ShareDialogProps extends PropsWithChildren {
  type: 'report' | 'dashboard';
  itemId: string;
  shareId?: string | null;
  isShared: boolean;
  title?: string;
}

export function ShareDialog({
  children,
  type,
  itemId,
  shareId,
  isShared,
  title = type,
}: ShareDialogProps) {
  const [isOpen, toggle] = useToggle(false);

  // Local state to track sharing status and share ID
  const [localIsShared, setLocalIsShared] = useState(isShared);
  const [localShareId, setLocalShareId] = useState(shareId);
  const [isCopied, setIsCopied] = useState(false);

  // Update local state when props change
  useEffect(() => {
    setLocalIsShared(isShared);
    setLocalShareId(shareId);
  }, [isShared, shareId]);

  // Choose the appropriate mutations based on type
  const createMutation =
    type === 'report'
      ? shareQuery.mutation.useCreateReportShare()
      : shareQuery.mutation.useCreateDashboardShare();

  const revokeMutation =
    type === 'report'
      ? shareQuery.mutation.useRevokeReportShare()
      : shareQuery.mutation.useRevokeDashboardShare();

  const shareUrl = localShareId
    ? `${window.location.origin}/share/${type}s/${localShareId}`
    : '';

  const handleCreateShare = async () => {
    createMutation.mutate(itemId, {
      onSuccess: (data) => {
        // Update local state immediately to show the share interface
        setLocalIsShared(true);
        setLocalShareId(data.share_id);
      },
    });
  };

  const handleRevokeShare = async () => {
    revokeMutation.mutate(itemId, {
      onSuccess: () => {
        // Update local state immediately to show the create button
        setLocalIsShared(false);
        setLocalShareId(null);
      },
    });
  };

  const handleCopyUrl = async () => {
    try {
      await navigator.clipboard.writeText(shareUrl);
      setIsCopied(true);
      setTimeout(() => setIsCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy URL:', err);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={toggle}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle>Share {title}</DialogTitle>
          <DialogDescription>
            Create a public link to share this {type} with others. Anyone with
            the link can view it without signing in.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {!localIsShared ? (
            <div className="text-center">
              <Button
                onClick={handleCreateShare}
                disabled={createMutation.isPending}
                className="w-full"
              >
                {createMutation.isPending ? 'Creating...' : 'Create share link'}
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="share-url">Share URL</Label>
                <div className="relative">
                  <Input
                    id="share-url"
                    value={shareUrl}
                    readOnly
                    className="pr-12 font-mono text-sm"
                  />
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleCopyUrl}
                    className="absolute top-1/2 right-1 h-8 w-8 -translate-y-1/2 p-0"
                  >
                    {isCopied ? (
                      <Check className="size-4 text-green-500" />
                    ) : (
                      <Copy className="size-4" />
                    )}
                  </Button>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.open(shareUrl, '_blank')}
                  className="flex items-center gap-2"
                >
                  <ExternalLink className="h-4 w-4" />
                  Open link
                </Button>

                <Button
                  variant="destructive"
                  size="sm"
                  onClick={handleRevokeShare}
                  disabled={revokeMutation.isPending}
                  className="flex items-center gap-2"
                >
                  <Trash2 className="h-4 w-4" />
                  {revokeMutation.isPending ? 'Revoking...' : 'Revoke'}
                </Button>
              </div>

              <Separator />

              <div className="text-muted-foreground text-sm">
                <p>
                  Anyone with this link can view this {type}. To stop sharing,
                  click &quot;Revoke&quot; above.
                </p>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
