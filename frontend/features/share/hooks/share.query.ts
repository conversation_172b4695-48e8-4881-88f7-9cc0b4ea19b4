import { createQueryKeys } from '@lukemorales/query-key-factory';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

import { shareApi } from '../services/share.api';

const shareQueryKeys = createQueryKeys('share', {
  sharedReport: (shareId: string) => ({
    queryKey: [shareId],
    queryFn: () => shareApi.getSharedReport(shareId),
  }),
  sharedDashboard: (shareId: string) => ({
    queryKey: [shareId],
    queryFn: () => shareApi.getSharedDashboard(shareId),
  }),
});

// Report sharing hooks
const useCreateReportShare = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: shareApi.createReportShare,
    onSuccess: (data) => {
      toast.success('Share link created successfully');
      // Invalidate queries that might contain this report
      queryClient.invalidateQueries({ queryKey: ['reports'] });
      queryClient.invalidateQueries({ queryKey: ['chat'] });
      return data;
    },
  });
};

const useRevokeReportShare = () => {
  return useMutation({
    mutationFn: shareApi.revokeReportShare,
    onSuccess: () => {
      toast.success('Share link revoked successfully');
      // Invalidate queries that might contain this report
    },
  });
};

// Dashboard sharing hooks
const useCreateDashboardShare = () => {
  return useMutation({
    mutationFn: shareApi.createDashboardShare,
    onSuccess: () => {
      toast.success('Share link created successfully');
    },
  });
};

const useRevokeDashboardShare = () => {
  return useMutation({
    mutationFn: shareApi.revokeDashboardShare,
    onSuccess: () => {
      toast.success('Share link revoked successfully');
    },
  });
};

// Public query hooks
const useSharedReport = (shareId: string) => {
  return useQuery({
    ...shareQueryKeys.sharedReport(shareId),
    enabled: Boolean(shareId),
  });
};

const useSharedDashboard = (shareId: string) => {
  return useQuery({
    ...shareQueryKeys.sharedDashboard(shareId),
    enabled: Boolean(shareId),
  });
};

export const shareQuery = {
  query: {
    useSharedReport,
    useSharedDashboard,
  },
  mutation: {
    useCreateReportShare,
    useRevokeReportShare,
    useCreateDashboardShare,
    useRevokeDashboardShare,
  },
};
