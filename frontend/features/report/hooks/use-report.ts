import { useQuery } from '@tanstack/react-query';

import { reportApi } from '../services/report.api';

interface UseReportOptions {
  enabled?: boolean;
}

export const useReport = (
  conversationId: string | null,
  options: UseReportOptions = {},
) => {
  const {
    data: report,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['report', conversationId],
    queryFn: () =>
      reportApi.getReportByConversation({
        conversationId: conversationId!,
      }),
    enabled: Bo<PERSON><PERSON>(conversationId && options.enabled !== false),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

  // Async refresh function
  const refreshReport = () => refetch();

  return {
    report,
    isLoading,
    error: error as Error | null,
    refreshReport,
  };
};
