// Report Types based on backend schema
// Backend source: backend/app/modules/multi_agents/tools/builtin/report/schema.py
// Import types from shared schema
import type { ChartStructuredOutput, KPICard } from './shared.type';

// ----------------------------------------------------------------
// Content Types
// ----------------------------------------------------------------

export interface TableColumnConfig {
  /** The header text */
  header: string;
}

export interface TableStructuredOutput {
  /** The title of the table */
  title: string;
  /** The description of the table */
  description?: string;
  /** The columns of the table */
  columns: TableColumnConfig[];
  /** The rows of the table, each row is a list of strings */
  rows: string[][];
}

export interface Content {
  /** The index of the content */
  index: number;
  /** The type of the content */
  type: 'paragraph' | 'chart' | 'table' | 'card';
  /** The data of the content */
  content:
    | string
    | ChartStructuredOutput
    | TableStructuredOutput
    | KPICard
    | KPICard[]
    | null;
}

// ----------------------------------------------------------------
// Executive Summary
// ----------------------------------------------------------------

export interface ExecutiveSummary {
  /** The key findings of the report */
  key_findings: string[];
  /** The business impact of the report */
  business_impact: string[];
  /** Key performance indicators and metrics cards for quick overview, max 3 cards */
  key_metrics?: KPICard[];
  /** Prioritized recommendations and next steps */
  recommendations?: string[];
}

// ----------------------------------------------------------------
// Report Section
// ----------------------------------------------------------------

export interface ReportSection {
  /** The index of the section */
  index: number;
  /** The header of the section */
  header?: string;
  /** The content of the section, each content has an index */
  content?: Content[];
}

// ----------------------------------------------------------------
// Report Input Commands
// ----------------------------------------------------------------

export type ReportCommand =
  | 'create_outline'
  | 'update_sections'
  | 'remove_sections'
  | 'create_or_update_executive_summary'
  | 'get_report';

export interface ReportInput {
  command: ReportCommand;
  title?: string;
  description?: string;
  sections?: ReportSection[];
  executive_summary?: ExecutiveSummary;
}
