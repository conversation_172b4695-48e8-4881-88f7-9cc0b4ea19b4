import { createQueryKeys } from '@lukemorales/query-key-factory';
import { useQuery } from '@tanstack/react-query';

import { UsageAnalyticsQueryParams } from '../models/usage-analytics.type';
import { usageAnalyticsApi } from '../services/usage-analytics.api';

const usageAnalyticsQueryKeys = createQueryKeys('usage-analytics', {
  usageStatistics: (user_id: string, params?: UsageAnalyticsQueryParams) => ({
    queryKey: [user_id, params],
    queryFn: () => usageAnalyticsApi.getUsageStatistics(user_id, params),
  }),

  quotaInfo: (user_id: string) => ({
    queryKey: [user_id],
    queryFn: () => usageAnalyticsApi.getQuotaInfo(user_id),
  }),
});

const useUsageStatistics = (
  user_id: string,
  params?: UsageAnalyticsQueryParams,
) =>
  useQuery({
    ...usageAnalyticsQueryKeys.usageStatistics(user_id, params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    enabled: !!user_id,
  });

const useQuotaInfo = (user_id: string) =>
  useQuery({
    ...usageAnalyticsQueryKeys.quotaInfo(user_id),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
    enabled: !!user_id,
  });

export const usageAnalyticsQuery = {
  query: {
    useUsageStatistics,
    useQuotaInfo,
  },
};
