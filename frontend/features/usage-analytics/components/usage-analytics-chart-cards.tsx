import { AreaChart } from '@/components/chat/components/message/charts';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

import type { UsageStatistics } from '../models/usage-analytics.type';

interface UsageAnalyticsChartCardsProps {
  stats: UsageStatistics;
}

export function UsageAnalyticsChartCards({
  stats,
}: UsageAnalyticsChartCardsProps) {
  // Transform daily token usage data for AreaChart
  const dailyUsageData = stats.daily_avg_messages.map((item) => ({
    name: new Date(item.date).toLocaleDateString(),
    value: item.total_tokens,
    color: '#3b82f6',
    originalValue: item.total_tokens,
  }));

  return (
    <div className="grid grid-cols-1 gap-6">
      {/* Daily Usage Trend */}
      <Card>
        <CardHeader>
          <CardTitle>Daily Token Usage Trend</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[300px]">
            <AreaChart
              data={dailyUsageData}
              height={300}
              title="Daily Token Usage"
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
