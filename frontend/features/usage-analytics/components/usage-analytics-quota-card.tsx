import { useRouter } from 'next/navigation';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { ArrowUpRight } from 'lucide-react';

import type { UsageStatistics } from '../models/usage-analytics.type';

interface UsageAnalyticsQuotaCardProps {
  stats: UsageStatistics;
}

export function UsageAnalyticsQuotaCard({
  stats,
}: UsageAnalyticsQuotaCardProps) {
  const router = useRouter();

  const handleUpgradeClick = () => {
    router.push('/purchase');
  };

  // Determine if user is approaching quota limit (>75%)
  const isApproachingLimit = stats.usage_percentage > 75;

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle>Quota Usage</CardTitle>
        <Button
          variant={isApproachingLimit ? 'default' : 'outlinePrimary'}
          size="sm"
          onClick={handleUpgradeClick}
          className="gap-2"
        >
          Upgrade Plan
          <ArrowUpRight className="size-3" />
        </Button>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <Progress
            value={stats.usage_percentage}
            className={`h-3 ${isApproachingLimit ? 'progress-warn' : ''}`}
          />
          <div className="text-muted-foreground flex justify-between text-sm">
            <span>{stats.quota_used.toLocaleString()} used</span>
            <span>{stats.quota_remaining.toLocaleString()} remaining</span>
            <span>{stats.quota_limit.toLocaleString()} total</span>
          </div>
          {isApproachingLimit && (
            <div className="text-sm font-medium text-amber-600">
              ⚠️ You&apos;re approaching your quota limit. Consider upgrading
              for uninterrupted service.
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
