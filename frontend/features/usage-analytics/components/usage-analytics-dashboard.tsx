import React from 'react';

import { Card, CardContent } from '@/components/ui/card';

import { usageAnalyticsQuery } from '../hooks/usage-analytics.query';
import type { UsageStatistics } from '../models/usage-analytics.type';
import {
  UsageAnalyticsChartCards,
  UsageAnalyticsHeroCards,
  UsageAnalyticsQuotaCard,
} from './index';

interface UsageAnalyticsDashboardProps {
  userId: string;
  dateRange?: {
    start_date?: string;
    end_date?: string;
  };
}

export const UsageAnalyticsDashboard: React.FC<
  UsageAnalyticsDashboardProps
> = ({ userId, dateRange }) => {
  const {
    data: usageStats,
    isLoading,
    error,
  } = usageAnalyticsQuery.query.useUsageStatistics(userId, dateRange);

  if (isLoading) {
    return (
      <div className="grid gap-6">
        <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="mb-2 h-4 rounded bg-gray-200" />
                <div className="h-8 rounded bg-gray-200" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error || !usageStats) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="mb-2 text-red-500">
            Failed to load usage analytics
          </div>
          <div className="text-gray-500">Please try refreshing the page</div>
        </div>
      </div>
    );
  }

  const stats = usageStats as UsageStatistics;

  return (
    <div className="space-y-6">
      {/* Hero Metrics */}
      <UsageAnalyticsHeroCards stats={stats} />

      {/* Quota Progress */}
      <UsageAnalyticsQuotaCard stats={stats} />

      {/* Charts */}
      <UsageAnalyticsChartCards stats={stats} />
    </div>
  );
};
