import DashboardCard from '@/components/dashboard-card';
import { ArrowDown, ArrowUp, MessageSquare } from 'lucide-react';

import type { UsageStatistics } from '../models/usage-analytics.type';

interface UsageAnalyticsHeroCardsProps {
  stats: UsageStatistics;
}

const formatTokens = (tokens: number): string => {
  if (tokens >= 1000000) {
    return `${(tokens / 1000000).toFixed(1)}M tokens`;
  }
  if (tokens >= 1000) {
    return `${(tokens / 1000).toFixed(1)}K tokens`;
  }
  return `${tokens} tokens`;
};

export function UsageAnalyticsHeroCards({
  stats,
}: UsageAnalyticsHeroCardsProps) {
  return (
    <div className="grid gap-4 md:grid-cols-3">
      <DashboardCard
        title="Total Messages"
        value={stats.total_messages.toLocaleString()}
        subtitle={`across ${stats.total_conversations} conversations`}
        icon={<MessageSquare className="text-muted-foreground size-4" />}
      />

      <DashboardCard
        title="Avg Input Tokens"
        value={stats.average_input_tokens_per_message.toFixed(1)}
        subtitle={`Total ${formatTokens(stats.total_input_tokens)}`}
        icon={<ArrowDown className="text-muted-foreground size-4" />}
      />

      <DashboardCard
        title="Avg Output Tokens"
        value={stats.average_output_tokens_per_message.toFixed(1)}
        subtitle={`Total ${formatTokens(stats.total_output_tokens)}`}
        icon={<ArrowUp className="text-muted-foreground size-4" />}
      />
    </div>
  );
}
