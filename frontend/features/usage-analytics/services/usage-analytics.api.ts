import { api, fetchData } from '@/openapi-ts/openapi-fetch';

export interface UsageAnalyticsQueryParams {
  start_date?: string;
  end_date?: string;
}

export const usageAnalyticsApi = {
  getUsageStatistics: (user_id: string, query?: UsageAnalyticsQueryParams) =>
    fetchData(
      api.GET('/api/v1/quotas/{user_id}/usage-statistics', {
        params: {
          path: {
            user_id,
          },
          query: query || {},
        },
      }),
    ),

  getQuotaInfo: (user_id: string) =>
    fetchData(
      api.GET('/api/v1/quotas/{user_id}/quota-info', {
        params: {
          path: {
            user_id,
          },
        },
      }),
    ),
};
