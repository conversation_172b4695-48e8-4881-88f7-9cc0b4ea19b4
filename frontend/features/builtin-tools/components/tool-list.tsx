'use client';

import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { PageSkeleton } from '@/components/ui/common/page';
import { InfoIcon } from 'lucide-react';

import { builtinToolsQuery } from '../hooks/builtin-tools.query';
import { BuiltinToolCard } from './builtin-tool-card';

export function ToolList() {
  const { data: builtinTools, isLoading } = builtinToolsQuery.query.useList();

  if (builtinTools) {
    return (
      <div className="flex h-full flex-col space-y-3 overflow-y-auto">
        <Badge variant="ghost-primary" className="w-fit">
          {builtinTools.length} Tools
        </Badge>
        <Alert variant="info" className="w-fit">
          <InfoIcon />
          <AlertTitle>Required permission</AlertTitle>
          <AlertDescription>
            Enable permission to use this tool during chat.
          </AlertDescription>
        </Alert>
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5">
          {builtinTools.map((tool) => (
            <BuiltinToolCard key={tool.id} tool={tool} />
          ))}
        </div>
      </div>
    );
  }

  if (isLoading) {
    return <PageSkeleton />;
  }
}
