import {
  <PERSON>,
  CardDescription,
  <PERSON><PERSON>ooter,
  <PERSON><PERSON>eader,
  <PERSON>Title,
} from '@/components/ui/card';
import { If } from '@/components/ui/common/if';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { WithTooltip } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { SchemaWorkspaceBuiltInToolResponse } from '@/openapi-ts/gens';

import { builtinToolsQuery } from '../hooks/builtin-tools.query';

type Props = {
  tool: SchemaWorkspaceBuiltInToolResponse;
};

export function BuiltinToolCard({ tool }: Props) {
  const { mutate, isPending } = builtinToolsQuery.mutation.useUpdatePermission(
    tool.id,
  );

  return (
    <Card className="flex h-full flex-col">
      <CardHeader className="grow p-4">
        <CardTitle>{tool.builtin_tool.display_name}</CardTitle>
        <WithTooltip tooltip={tool.builtin_tool.description}>
          <CardDescription className="line-clamp-1">
            {tool.builtin_tool.description}
          </CardDescription>
        </WithTooltip>
      </CardHeader>
      <CardFooter className="space-x-2 p-4">
        <Switch
          id={tool.id}
          checked={tool.required_permission}
          onCheckedChange={(value) =>
            mutate({
              required_permission: value,
            })
          }
          disabled={isPending}
        />
        <Label
          htmlFor={tool.id}
          className={cn(tool.required_permission && 'text-primary')}
        >
          <If
            condition={tool.required_permission}
            fallback={'No permission required'}
          >
            Require permission
          </If>
        </Label>
      </CardFooter>
    </Card>
  );
}
