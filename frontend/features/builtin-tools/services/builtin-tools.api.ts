import { api, fetchData } from '@/openapi-ts/openapi-fetch';
import { PathsPatchRequestBodyDto } from '@/openapi-ts/utils.type';

/**
 * API service for managing built-in tools
 */
export const builtinToolsApi = {
  /**
   * Get all workspace built-in tools
   */
  list: () => fetchData(api.GET('/api/v1/builtin-tools/')),

  /**
   * Update a specific built-in tool
   * @param id - ID of the built-in tool to update
   */
  detail: (id: string) => ({
    /**
     * Update built-in tool permission requirement
     * @param requiredPermission - Whether permission is required to use this tool
     */
    updatePermission: (
      body: PathsPatchRequestBodyDto<'/api/v1/builtin-tools/{workspace_builtin_tool_id}/'>,
    ) =>
      api.PATCH('/api/v1/builtin-tools/{workspace_builtin_tool_id}/', {
        params: {
          path: {
            workspace_builtin_tool_id: id,
          },
        },
        body,
      }),
  }),
};
