import { createQueryKeys } from '@lukemorales/query-key-factory';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

import { builtinToolsApi } from '../services/builtin-tools.api';

const builtinToolsQueryKeys = createQueryKeys('builtin-tools', {
  list: {
    queryKey: null,
    queryFn: builtinToolsApi.list,
  },
});

const useList = () => {
  return useQuery(builtinToolsQueryKeys.list);
};

const useUpdatePermission = (toolId: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: builtinToolsApi.detail(toolId).updatePermission,
    onSuccess: (_, { required_permission }) => {
      queryClient.invalidateQueries({
        queryKey: builtinToolsQueryKeys.list.queryKey,
      });
      toast.success(
        required_permission
          ? 'Permission requirement enabled'
          : 'Permission requirement disabled',
      );
    },
    onError: () => {
      toast.error('Failed to update permission requirement');
    },
  });
};

export const builtinToolsQuery = {
  query: {
    useList,
  },
  mutation: {
    useUpdatePermission,
  },
};
