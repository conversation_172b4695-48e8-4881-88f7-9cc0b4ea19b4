'use client';

import React, { useMemo } from 'react';

import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { If } from '@/components/ui/common/if';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { SchemaRecommendationPublic } from '@/openapi-ts/gens';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2Icon } from 'lucide-react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, useForm } from 'react-hook-form';
import { useToggle } from 'usehooks-ts';
import { z } from 'zod';

import { recommendationQuery } from '../hooks/recommendation.query';

const editRecommendationSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().min(1, 'Description is required'),
  potential_savings: z.number().min(0, 'Potential savings must be positive'),
  effort: z.enum(['low', 'medium', 'high']),
  risk: z.enum(['low', 'medium', 'high']),
  status: z.enum(['pending', 'implemented', 'ignored', 'in_progress']),
});

type EditRecommendationSchema = z.infer<typeof editRecommendationSchema>;

type Props = {
  recommendation: SchemaRecommendationPublic;
  children: React.ReactNode;
};

export function EditRecommendationDialog({ recommendation, children }: Props) {
  const [open, toggle] = useToggle(false);

  const defaultValues = useMemo(
    (): EditRecommendationSchema => ({
      title: recommendation.title,
      description: recommendation.description,
      potential_savings: recommendation.potential_savings,
      effort: recommendation.effort as 'low' | 'medium' | 'high',
      risk: recommendation.risk as 'low' | 'medium' | 'high',
      status: recommendation.status as
        | 'pending'
        | 'implemented'
        | 'ignored'
        | 'in_progress',
    }),
    [recommendation],
  );

  const form = useForm<EditRecommendationSchema>({
    resolver: zodResolver(editRecommendationSchema),
    defaultValues,
  });

  const { control, handleSubmit, reset } = form;
  const { mutate, isPending } = recommendationQuery.mutation.useUpdate();

  const onSubmit: SubmitHandler<EditRecommendationSchema> = (data) => {
    mutate(
      {
        id: recommendation.id,
        data,
      },
      {
        onSuccess: () => {
          toggle();
          reset();
        },
      },
    );
  };

  return (
    <AlertDialog open={open} onOpenChange={toggle}>
      <AlertDialogTrigger asChild>{children}</AlertDialogTrigger>
      <AlertDialogContent className="max-w-2xl">
        <AlertDialogHeader>
          <AlertDialogTitle>Edit Recommendation</AlertDialogTitle>
          <AlertDialogDescription>
            Update recommendation details and settings.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <Form {...form}>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 gap-4">
              <FormField
                control={control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Title</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter recommendation title"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter recommendation description"
                        className="min-h-[100px] resize-none"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={control}
                  name="potential_savings"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Potential Savings ($)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          step="0.01"
                          placeholder="0.00"
                          {...field}
                          onChange={(e) =>
                            field.onChange(parseFloat(e.target.value) || 0)
                          }
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="pending">Pending</SelectItem>
                          <SelectItem value="in_progress">
                            In Progress
                          </SelectItem>
                          <SelectItem value="implemented">
                            Implemented
                          </SelectItem>
                          <SelectItem value="ignored">Ignored</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={control}
                  name="effort"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Effort</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select effort level" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="low">Low</SelectItem>
                          <SelectItem value="medium">Medium</SelectItem>
                          <SelectItem value="high">High</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={control}
                  name="risk"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Risk</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select risk level" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="low">Low</SelectItem>
                          <SelectItem value="medium">Medium</SelectItem>
                          <SelectItem value="high">High</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <div className="flex items-center justify-end gap-2">
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <Button disabled={isPending} type="submit" className="gap-2">
                <If condition={isPending}>
                  <Loader2Icon className="size-4 animate-spin" />
                </If>
                Save Changes
              </Button>
            </div>
          </form>
        </Form>
      </AlertDialogContent>
    </AlertDialog>
  );
}
