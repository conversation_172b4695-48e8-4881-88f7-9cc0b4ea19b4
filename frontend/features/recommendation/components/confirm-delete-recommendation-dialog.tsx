'use client';

import React from 'react';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { If } from '@/components/ui/common/if';
import { SchemaRecommendationPublic } from '@/openapi-ts/gens';
import { Loader2Icon } from 'lucide-react';
import { useToggle } from 'usehooks-ts';

import { recommendationQuery } from '../hooks/recommendation.query';

type Props = {
  recommendation: SchemaRecommendationPublic;
  children: React.ReactNode;
};

export function ConfirmDeleteRecommendationDialog({
  recommendation,
  children,
}: Props) {
  const [open, toggle] = useToggle(false);
  const { mutate, isPending } = recommendationQuery.mutation.useDelete();

  const handleDelete = () => {
    mutate(recommendation.id, {
      onSuccess: () => {
        toggle();
      },
    });
  };

  return (
    <AlertDialog open={open} onOpenChange={toggle}>
      <AlertDialogTrigger asChild>{children}</AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Are you sure?</AlertDialogTitle>
          <AlertDialogDescription>
            This action cannot be undone. This will permanently delete the
            recommendation &quot;{recommendation.title}&quot;.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isPending}>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={isPending}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            <If condition={isPending}>
              <Loader2Icon className="mr-2 size-4 animate-spin" />
            </If>
            Delete
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
