import {
  BarChart3Icon,
  BarChartIcon,
  FileTextIcon,
  LightbulbIcon,
  LucideIcon,
  TriangleAlertIcon,
  UsersIcon,
} from 'lucide-react';

type QuickPrompt = {
  title: string;
  description: string;
  question: string;
  icon: LucideIcon;
};

export const QUICK_PROMPT_LIST: QuickPrompt[] = [
  {
    title: 'Smart Dashboard',
    description:
      'Multi-agent AI creates comprehensive executive dashboards automatically',
    question:
      '@anna create executive dashboard showing cost savings and performance metrics #dashboard. Use sample data.',
    icon: BarChart3Icon,
  },
  {
    title: 'AI Report Generator',
    description: 'Agents collaborate to generate detailed multi-format reports',
    question:
      '@anna generate comprehensive infrastructure report with agent collaboration #report. Use sample data.',
    icon: FileTextIcon,
  },
  {
    title: 'Smart Alert System',
    description:
      'AI team monitors and sends intelligent alerts before issues escalate',
    question:
      '@oliver setup proactive security alerts with multi-agent monitoring #alert. Use sample data.',
    icon: TriangleAlertIcon,
  },
  {
    title: 'AI Advisor',
    description:
      'Multiple agents analyze and provide optimization recommendations',
    question:
      '@alex analyze infrastructure and provide cost optimization recommendations #recommend. Use sample data.',
    icon: LightbulbIcon,
  },
  {
    title: 'Visual Intelligence',
    description:
      'AI creates stunning visualizations from complex data automatically',
    question:
      '@anna create visual insights showing system performance trends #visual. Use sample data.',
    icon: BarChartIcon,
  },
  {
    title: 'Multi-Agent AI Workforce',
    description:
      'Watch AI agents collaborate, delegate, and solve problems together',
    question:
      '@anna show multi-agent collaboration with task delegation to analyze and #report the system downtime event. Use sample data.',
    icon: UsersIcon,
  },
];
