import { useRouter } from 'next/navigation';

import pathsConfig from '@/config/paths.config';
import { useUserContext } from '@/features/user/provider/user-provider';
import { pickBy } from 'lodash';

type AgentDetailUrlParams = Partial<{
  initialMessage: string;
  conversationId: string;
  resource_id: string;
}>;

export const useNavigateAgentDetail = () => {
  const router = useRouter();
  const { agentId } = useUserContext();

  return (searchParams: AgentDetailUrlParams) => {
    router.push(agentDetailUrl(agentId, searchParams));
  };
};

export const agentDetailUrl = (
  agentId: string,
  searchParams: AgentDetailUrlParams,
) => {
  const params = new URLSearchParams(pickBy(searchParams));

  return `${pathsConfig.app.agentDetail(agentId)}?${params.toString()}`;
};
