import { api, fetchData } from '@/openapi-ts/openapi-fetch';

export const agentConnectionsApi = {
  list: () =>
    fetchData(
      api.GET('/api/v1/agents-connections/'),
    ),

  create: (agent_id: string, connection_id: string) =>
    fetchData(
      api.POST('/api/v1/agents-connections/{agent_id}/connections', {
        params: {
          path: {
            agent_id,
          },
        },
        body: {
          connection_id,
        },
      }),
    ),

  delete: (agent_id: string, connection_id: string) =>
    fetchData(
      api.DELETE('/api/v1/agents-connections/{agent_id}/connections/{connection_id}', {
        params: {
          path: {
            agent_id,
            connection_id,
          },
        },
      }),
    ),
};
