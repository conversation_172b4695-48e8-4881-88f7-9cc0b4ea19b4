import { api, fetchData } from '@/openapi-ts/openapi-fetch';
import { PathsPatchRequestBodyDto } from '@/openapi-ts/utils.type';

export const agentBuiltinToolsApi = {
  // list: () => fetchData(api.GET('/api/v1/agents-builtin-tools/')),

  detail: (agent_id: string) => ({
    getInfo: () =>
      fetchData(
        api.GET('/api/v1/agents-builtin-tools/{agent_id}', {
          params: {
            path: {
              agent_id,
            },
          },
        }),
      ),

    update: (
      body: PathsPatchRequestBodyDto<'/api/v1/agents-builtin-tools/{agent_id}'>,
    ) =>
      fetchData(
        api.PATCH('/api/v1/agents-builtin-tools/{agent_id}', {
          params: {
            path: {
              agent_id,
            },
          },
          body,
        }),
      ),
  }),
};
