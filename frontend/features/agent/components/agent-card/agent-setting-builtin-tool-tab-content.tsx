import { useEffect, useState } from 'react';

import { Button } from '@/components/ui/button';
import { CardDescription } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { UpdateWrapper } from '@/components/ui/common/update-wrapper';
import { DialogClose, DialogFooter } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import { SchemaAgentBuiltInToolPublic } from '@/openapi-ts/gens';

import { agentQuery } from '../../hooks/agent.query';

type Props = {
  agentId: string;
  toggle: () => void;
};

export function AgentSettingBuiltinToolTabContent({ agentId, toggle }: Props) {
  const { data: builtinTools } =
    agentQuery.query.useBuiltinToolsDetail(agentId);
  const [selectedTools, setSelectedTools] = useState<
    SchemaAgentBuiltInToolPublic[]
  >([]);

  const { mutate, isPending } =
    agentQuery.mutation.useUpdateAgentBuiltinTools(agentId);

  useEffect(() => {
    if (builtinTools) {
      setSelectedTools(builtinTools.tools);
    }
  }, [builtinTools]);

  const onCheckedChange = (toolId: string) => (checked: boolean) => {
    setSelectedTools((prev) => {
      return prev.map((tool) =>
        tool.id === toolId ? { ...tool, is_active: checked } : tool,
      );
    });
  };

  const onSave = () => {
    mutate(
      {
        agent_builtin_tools: selectedTools.map((tool) => ({
          workspace_builtin_tool_id: tool.id,
          is_active: tool.is_active,
        })),
      },
      {
        onSuccess: toggle,
      },
    );
  };

  if (selectedTools.length > 0) {
    return (
      <div className="flex flex-col space-y-4">
        <CardDescription>
          Select the builtin tools that the agent can use.
        </CardDescription>
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
          {selectedTools.map((tool) => (
            <div
              key={tool.id}
              className={cn('flex items-center gap-2 truncate')}
            >
              <Checkbox
                id={tool.id}
                checked={tool.is_active}
                onCheckedChange={onCheckedChange(tool.id)}
                className="shrink-0"
              />
              <Label htmlFor={tool.id}>{tool.display_name}</Label>
            </div>
          ))}
        </div>
        <DialogFooter className="!mt-auto">
          <DialogClose asChild>
            <Button variant="outline" disabled={isPending}>
              Cancel
            </Button>
          </DialogClose>
          <Button disabled={isPending} onClick={onSave}>
            <UpdateWrapper isPending={isPending}>Save</UpdateWrapper>
          </Button>
        </DialogFooter>
      </div>
    );
  }
}
