import { PropsWithChildren, useMemo } from 'react';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { SchemaAgentPublic } from '@/openapi-ts/gens';
import { useToggle } from 'usehooks-ts';

import { AgentConnectionsTab } from './agent-connections-tab';
import { AgentSettingBuiltinToolTabContent } from './agent-setting-builtin-tool-tab-content';
import { AgentSettingInstructionTabContent } from './agent-setting-instruction-tab-content';

type Props = PropsWithChildren<{
  agent: SchemaAgentPublic;
}>;

export function AgentSettingDetailDialog({ children, agent }: Props) {
  const [isOpen, toggle] = useToggle(false);

  const tabs = useMemo(
    () => [
      {
        id: 'tools',
        name: 'Builtin Tools',
        content: (
          <AgentSettingBuiltinToolTabContent
            agentId={agent.id}
            toggle={toggle}
          />
        ),
        description: "Manage agent's builtin tools.",
      },
      {
        id: 'connections',
        name: 'Connections',
        content: <AgentConnectionsTab agent={agent} />,
        description: "Manage agent's available connections.",
      },
      {
        id: 'instructions',
        name: 'Instructions',
        content: (
          <AgentSettingInstructionTabContent agent={agent} toggle={toggle} />
        ),
        description: "Define the agent's core behavior and guidelines.",
      },
    ],
    [agent, toggle],
  );

  return (
    <Dialog open={isOpen} onOpenChange={toggle}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="overflow-thin-auto flex h-[90dvh] max-w-3xl flex-col">
        <DialogHeader>
          <DialogTitle>Agent Settings - {agent.title}</DialogTitle>
          <DialogDescription>
            Configure the tools and instructions for this agent.
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue={tabs[0].id} className="flex grow flex-col">
          <TabsList>
            {tabs.map((tab) => (
              <TabsTrigger value={tab.id} key={tab.id}>
                <span>{tab.name}</span>
              </TabsTrigger>
            ))}
          </TabsList>
          {tabs.map((tab) => (
            <TabsContent
              value={tab.id}
              key={tab.id}
              className="overflow-thin-auto grow [&>div]:h-full"
            >
              {tab.content}
            </TabsContent>
          ))}
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
