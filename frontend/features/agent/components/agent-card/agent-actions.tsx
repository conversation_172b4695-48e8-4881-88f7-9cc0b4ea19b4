'use client';

import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { WithTooltip } from '@/components/ui/tooltip';
import { agentQuery } from '@/features/agent/hooks/agent.query';
import { cn } from '@/lib/utils';
import { SchemaAgentPublic } from '@/openapi-ts/gens';
import { Settings } from 'lucide-react';
import { toast } from 'sonner';

import { AgentSettingDetailDialog } from './agent-setting-detail-dialog';

interface AgentActionsProps {
  agent: SchemaAgentPublic;
}

export function AgentActions({ agent }: AgentActionsProps) {
  const toggleActiveMutation = agentQuery.mutation.useUpdateStatus(agent.id);

  const handleToggleActive = (isActive: boolean) => {
    toggleActiveMutation.mutate(
      {
        agent_status: isActive,
      },
      {
        onSuccess: () => {
          const status = isActive ? 'activated' : 'deactivated';
          toast.success(`Agent ${status} successfully`);
        },
        onError: () => {
          const action = isActive ? 'activate' : 'deactivate';
          toast.error(`Failed to ${action} agent`);
        },
      },
    );
  };

  return (
    <div className={cn('flex shrink-0 gap-2', heightClass)}>
      <Switch
        checked={agent.is_active ?? false}
        onCheckedChange={handleToggleActive}
        disabled={toggleActiveMutation.isPending}
        aria-label={agent.is_active ? 'Deactivate agent' : 'Activate agent'}
        className={heightClass}
      />

      <AgentSettingDetailDialog agent={agent}>
        <WithTooltip tooltip="Open settings for this agent">
          <Button
            variant="ghost"
            size="sm"
            className={heightClass}
            disabled={toggleActiveMutation.isPending}
          >
            <Settings className="size-4" />
          </Button>
        </WithTooltip>
      </AgentSettingDetailDialog>
    </div>
  );
}

const heightClass = 'h-5';
