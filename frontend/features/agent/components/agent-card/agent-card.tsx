import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { If } from '@/components/ui/common/if';
import { Skeleton } from '@/components/ui/skeleton';
import { useAgentContext } from '@/features/agent/provider/agent-provider';
import { cn } from '@/lib/utils';
import { ConnectionType, SchemaAgentPublic } from '@/openapi-ts/gens';
import pluralize from 'pluralize';

import { agentQuery } from '../../hooks/agent.query';
import { AgentActions } from './agent-actions';

interface AgentCardProps {
  agent: SchemaAgentPublic;
  className?: string;
}

export const AgentCard = ({ agent, className }: AgentCardProps) => {
  const { agentsConnections } = useAgentContext();
  const { data: builtinTool, isLoading: isLoadingBuiltinTools } =
    agentQuery.query.useBuiltinToolsDetail(agent.id);

  const agentConnections =
    agentsConnections?.agents_connections.find((ag) => ag.agent_id === agent.id)
      ?.connections || [];

  const agentName = agent.alias;
  const avatarPath = `/avatars/${agentName}.webp`;

  const activeAgentTools = builtinTool?.tools.filter((tool) => tool.is_active);

  return (
    <Card
      className={cn('flex flex-col transition-all duration-200', className)}
    >
      <CardHeader className="flex flex-row justify-between gap-1">
        <div className="flex items-center gap-1 truncate">
          <div className="shrink-0">
            <Avatar className="size-10">
              <AvatarImage src={avatarPath} />
              <AvatarFallback>{agent.alias}</AvatarFallback>
            </Avatar>
          </div>
          <div className="space-y-1 truncate">
            <div className="flex justify-between">
              <CardTitle>{agent.title}</CardTitle>
            </div>
            <CardDescription className="truncate">{agent.role}</CardDescription>
          </div>
        </div>

        <AgentActions agent={agent} />
      </CardHeader>

      <CardContent className="flex flex-col">
        <p className="text-muted-foreground line-clamp-3 text-sm">
          {agent.instructions}
        </p>

        <div className="space-y-4 pt-5">
          {/* Built-in Tools */}
          <If
            condition={activeAgentTools}
            fallback={
              <If condition={isLoadingBuiltinTools}>
                <Skeleton className="h-4 w-24" />
              </If>
            }
          >
            {(activeAgentTools) => (
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <p className="text-muted-foreground text-xs font-medium">
                    Built-in tools connected
                  </p>
                  <Badge variant="ghost-primary">
                    {pluralize('tool', activeAgentTools.length, true)}
                  </Badge>
                </div>
                <div className="flex flex-wrap gap-2">
                  {activeAgentTools.slice(0, 3).map((tool) => (
                    <Badge
                      variant="outline"
                      className="bg-primary/10 gap-2"
                      key={tool.id}
                    >
                      <div className="bg-success size-2 rounded-full" />
                      {tool.display_name}
                    </Badge>
                  ))}
                  <If condition={activeAgentTools.length > 3}>
                    <span className="text-muted-foreground text-sm italic">
                      +{activeAgentTools.length - 3} more
                    </span>
                  </If>
                </div>
              </div>
            )}
          </If>

          {/* Connections */}
          <If condition={agentConnections.length}>
            <div className="space-y-2">
              <p className="text-muted-foreground text-xs font-medium">
                Connections
              </p>
              <div className="flex flex-wrap gap-2">
                {agentConnections.map((connection) => (
                  <Badge
                    key={connection.id}
                    variant={
                      connection.connection_type === ConnectionType.builtin
                        ? 'ghost-info'
                        : 'ghost-success'
                    }
                    className="inline-flex items-center"
                  >
                    <p>{connection.name}</p>
                  </Badge>
                ))}
              </div>
            </div>
          </If>
        </div>
      </CardContent>
    </Card>
  );
};
