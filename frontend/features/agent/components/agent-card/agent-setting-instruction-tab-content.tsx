import { useState } from 'react';

import { Button } from '@/components/ui/button';
import { CardDescription } from '@/components/ui/card';
import { UpdateWrapper } from '@/components/ui/common/update-wrapper';
import { DialogClose, DialogFooter } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { SchemaAgentPublic } from '@/openapi-ts/gens';

import { agentQuery } from '../../hooks/agent.query';

type Props = {
  agent: SchemaAgentPublic;
  toggle: () => void;
};

export function AgentSettingInstructionTabContent({ agent, toggle }: Props) {
  const [value, setValue] = useState(agent.instructions);
  const { mutate, isPending } = agentQuery.mutation.useUpdateInstructions(
    agent.id,
  );

  const onSave = () => {
    mutate(
      {
        instructions: value.trim(),
      },
      {
        onSuccess: toggle,
      },
    );
  };
  return (
    <div className="flex flex-col space-y-4">
      <CardDescription>
        {`Configure the instructions that guide this agent's behavior.`}
      </CardDescription>
      <div className="grow">
        <Textarea
          value={value}
          onChange={(e) => setValue(e.target.value)}
          placeholder="Enter instructions for the agent..."
          className="min-h-full resize-none"
        />
      </div>
      <DialogFooter>
        <DialogClose asChild>
          <Button variant="outline" disabled={isPending}>
            Cancel
          </Button>
        </DialogClose>
        <Button
          disabled={
            !value.trim() || value.trim() === agent.instructions || isPending
          }
          onClick={onSave}
        >
          <UpdateWrapper isPending={isPending}>Save</UpdateWrapper>
        </Button>
      </DialogFooter>
    </div>
  );
}
