'use client';

import { useMemo } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { If } from '@/components/ui/common/if';
import { DialogClose, DialogFooter } from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import { agentQuery } from '@/features/agent/hooks/agent.query';
import { useAgentContext } from '@/features/agent/provider/agent-provider';
import { builtinConnectionQuery } from '@/features/connection-v2/hooks/builtin-connection.query';
import { mcpConnectionQuery } from '@/features/connection/hooks/mcp-connection.query';
import { SchemaAgentPublic } from '@/openapi-ts/gens';
import { Link, Minus, Plus, Settings } from 'lucide-react';

interface AgentConnectionsTabProps {
  agent: SchemaAgentPublic;
}

export function AgentConnectionsTab({ agent }: AgentConnectionsTabProps) {
  const { agentsConnections } = useAgentContext();

  // Get current agent connections (exclude CLOUD type)
  const currentConnections = useMemo(() => {
    const agentConnections =
      agentsConnections?.agents_connections?.find(
        (agentConn) => agentConn.agent_id === agent.id,
      )?.connections || [];
    return agentConnections;
  }, [agentsConnections, agent.id]);

  // Get available connections
  const { data: builtinConnections, isLoading: isLoadingBuiltin } =
    builtinConnectionQuery.query.useList();

  const { data: mcpConnections, isLoading: isLoadingMcp } =
    mcpConnectionQuery.query.useList();

  // Mutation hooks
  const { mutate: createConnection, isPending: isCreating } =
    agentQuery.mutation.useCreateConnection(agent.id);
  const { mutate: deleteConnection, isPending: isDeleting } =
    agentQuery.mutation.useDeleteConnection(agent.id);

  // Available connections (not currently assigned to agent)
  const availableBuiltinConnections = builtinConnections?.data.filter(
    (conn) =>
      conn.is_connected &&
      !currentConnections.some((current) => current.id === conn.id),
  );
  const availableMcpConnections = mcpConnections?.data.filter(
    (conn) => !currentConnections.some((current) => current.id === conn.id),
  );

  const handleAddConnection = (connectionId: string) => {
    createConnection(connectionId);
  };

  const handleRemoveConnection = (connectionId: string) => {
    deleteConnection(connectionId);
  };

  const isLoading =
    isLoadingBuiltin || isLoadingMcp || isCreating || isDeleting;

  return (
    <div className="flex flex-col space-y-4">
      <div className="flex-1 space-y-6 overflow-y-auto p-1">
        {/* Current Connections */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="text-foreground text-sm font-medium">
              Current Connections
            </h4>
            <Badge variant="secondary" size="sm">
              {currentConnections.length}
            </Badge>
          </div>

          <If
            condition={currentConnections.length > 0}
            fallback={
              <div className="bg-muted/20 rounded-lg border border-dashed p-6 text-center">
                <Settings className="text-muted-foreground/50 mx-auto h-8 w-8" />
                <p className="text-muted-foreground mt-2 text-sm">
                  No connections assigned
                </p>
              </div>
            }
          >
            <div className="grid grid-cols-1 gap-3 sm:grid-cols-2">
              {currentConnections.map((connection) => (
                <div
                  key={connection.id}
                  className="bg-card relative rounded-lg border p-4 shadow-sm transition-all hover:shadow-md"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex min-w-0 flex-1 items-center gap-3">
                      <div className="min-w-0 flex-1">
                        <p className="truncate text-sm font-medium">
                          {connection.name}
                        </p>
                        <div className="mt-1 flex items-center gap-2">
                          <Badge variant="ghost-info" size="sm">
                            {connection.connection_type.toUpperCase()}
                          </Badge>
                        </div>
                      </div>
                    </div>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => handleRemoveConnection(connection.id)}
                      disabled={isLoading}
                      className="text-destructive hover:text-destructive hover:bg-destructive/10 shrink-0"
                    >
                      <Minus className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </If>
        </div>

        <Separator />

        {/* Available Connections */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="text-foreground text-sm font-medium">
              Available Connections
            </h4>
            <Badge variant="outline" size="sm">
              {(availableBuiltinConnections?.length || 0) +
                (availableMcpConnections?.length || 0)}
            </Badge>
          </div>

          <If
            condition={
              (availableBuiltinConnections?.length || 0) +
                (availableMcpConnections?.length || 0) >
              0
            }
            fallback={
              <div className="bg-muted/20 rounded-lg border border-dashed p-6 text-center">
                <Link className="text-muted-foreground/50 mx-auto h-8 w-8" />
                <p className="text-muted-foreground mt-2 text-sm">
                  All connections are assigned
                </p>
              </div>
            }
          >
            <div className="grid grid-cols-1 gap-3 sm:grid-cols-2">
              {[
                ...(availableBuiltinConnections || []),
                ...(availableMcpConnections || []),
              ].map((connection) => (
                <div
                  key={connection.id}
                  className="bg-card/50 hover:bg-card relative rounded-lg border border-dashed p-4 transition-all hover:border-solid hover:shadow-md"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex min-w-0 flex-1 items-center gap-3">
                      <div className="min-w-0 flex-1">
                        <p className="truncate text-sm font-medium">
                          {connection.name}
                        </p>
                        <div className="mt-1 flex items-center gap-2">
                          <Badge variant="outline" size="sm">
                            {'builtin_connection_type' in connection
                              ? 'BUILTIN'
                              : 'MCP'}
                          </Badge>
                        </div>
                      </div>
                    </div>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => handleAddConnection(connection.id)}
                      disabled={isLoading}
                      className="text-primary hover:text-primary hover:bg-primary/10 shrink-0"
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </If>
        </div>
      </div>
      <DialogFooter className="!mt-auto">
        <DialogClose asChild>
          <Button variant="outline">Cancel</Button>
        </DialogClose>
      </DialogFooter>
    </div>
  );
}
