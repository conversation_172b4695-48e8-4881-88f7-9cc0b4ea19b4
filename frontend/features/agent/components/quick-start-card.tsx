import { highlightMentions } from '@/components/chat/utils/mention-highlighting';
import { Badge } from '@/components/ui/badge';
import {
  Card,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { taskServiceConfig } from '@/features/task/config/task-service.config';
import { cn } from '@/lib/utils';
import { SchemaQuickStartTemplate } from '@/openapi-ts/gens';

type Props = {
  template: SchemaQuickStartTemplate;
  onClick: (question: string) => void;
};

export function QuickStartCard({ template, onClick }: Props) {
  const service = taskServiceConfig.CONFIG[template.service];

  return (
    <button
      className="contents text-left"
      onClick={() => onClick(template.context)}
    >
      <Card
        className={cn(
          'bg-primary/5 transition-all hover:scale-[0.98] hover:shadow-inner',
        )}
      >
        <CardHeader className="h-full p-4">
          <CardTitle className="line-clamp-2">{template.title}</CardTitle>
          <CardDescription className="line-clamp-4 grow">
            {highlightMentions(template.context).highlightedText}
          </CardDescription>
          <div className="flex items-center justify-end">
            <Badge variant="ghost" className="gap-2">
              <service.icon className="size-4" />
              {service.label}
            </Badge>
          </div>
        </CardHeader>
      </Card>
    </button>
  );
}
