import { BuiltinConnectionType } from '@/openapi-ts/gens';
import { createUtilityConfig } from '@/utils/option-config';

export const builtinConnectionTypeConfig = createUtilityConfig({
  [BuiltinConnectionType.cloud]: {
    label: 'Cloud',
  },
  [BuiltinConnectionType.db]: {
    label: 'Database',
  },
  [BuiltinConnectionType.orchestration]: {
    label: 'Orchestration',
  },
  [BuiltinConnectionType.observability]: {
    label: 'Observability',
  },
  [BuiltinConnectionType.cli]: {
    label: 'CLI',
  },
} satisfies Record<
  BuiltinConnectionType,
  {
    label: string;
  }
>);
