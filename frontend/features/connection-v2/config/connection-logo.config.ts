/**
 * Configuration for connection logos
 * Maps connection prefixes to their corresponding logo paths in the public directory
 */
export const CONNECTION_LOGO_CONFIG = {
  aws: '/aws-logo.svg',
  gcp: '/gcp-logo.svg',
  azure: '/azure-logo.svg',
  k8s: '/k8s-logo.svg',
  grafana: '/grafana-logo.svg',
} as const;

/**
 * Get the logo path for a given connection prefix
 * @param prefix - The connection prefix (e.g., 'aws', 'gcp', etc.)
 * @returns The logo path or null if not found
 */
export const getConnectionLogo = (prefix: string): string | null => {
  return (
    CONNECTION_LOGO_CONFIG[prefix as keyof typeof CONNECTION_LOGO_CONFIG] ||
    null
  );
};
