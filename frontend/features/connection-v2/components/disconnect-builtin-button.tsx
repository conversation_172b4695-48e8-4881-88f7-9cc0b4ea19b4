'use client';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button, buttonVariants } from '@/components/ui/button';
import { If } from '@/components/ui/common/if';
import { SpinnerContainer } from '@/components/ui/spinner-container';
import { Loader2Icon, UnplugIcon } from 'lucide-react';
import { useToggle } from 'usehooks-ts';

import { builtinConnectionQuery } from '../hooks/builtin-connection.query';

type Props = {
  builtinId: string;
  builtinName: string;
};

export function DisconnectBuiltinButton({ builtinId, builtinName }: Props) {
  const [open, toggle] = useToggle(false);

  const { mutate, isPending } =
    builtinConnectionQuery.mutation.useDisconnect(builtinId);

  return (
    <AlertDialog open={open} onOpenChange={toggle}>
      <AlertDialogTrigger asChild>
        <Button
          variant="destructive"
          className="gap-2"
          size="sm"
          disabled={isPending}
        >
          <If
            condition={isPending}
            fallback={<UnplugIcon className="size-4" />}
          >
            <Loader2Icon className="size-4 animate-spin" />
          </If>
          Disconnect
        </Button>
      </AlertDialogTrigger>

      <AlertDialogContent onEscapeKeyDown={(e) => e.preventDefault()}>
        <SpinnerContainer loading={isPending}>
          <AlertDialogHeader>
            <AlertDialogTitle>Disconnect {builtinName}</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to disconnect {builtinName}? This action
              cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>

          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>

            <AlertDialogAction
              type={'button'}
              onClick={() => mutate(undefined, { onSuccess: toggle })}
              className={buttonVariants({ variant: 'destructive' })}
            >
              Disconnect
            </AlertDialogAction>
          </AlertDialogFooter>
        </SpinnerContainer>
      </AlertDialogContent>
    </AlertDialog>
  );
}
