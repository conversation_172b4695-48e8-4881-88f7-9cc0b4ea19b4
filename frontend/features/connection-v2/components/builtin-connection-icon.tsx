import Image from 'next/image';

import { SchemaBuiltinConnectionPublic } from '@/openapi-ts/gens';
import { PlugIcon } from 'lucide-react';

import { getConnectionLogo } from '../config';

type Props = {
  connection: SchemaBuiltinConnectionPublic;
};

export function BuiltinConnectionIcon({ connection }: Props) {
  const logoPath = getConnectionLogo(connection.prefix);

  if (logoPath) {
    return (
      <Image
        src={logoPath}
        alt={connection.name}
        width={24}
        height={24}
        priority
        className="size-6 h-auto"
      />
    );
  }

  return <PlugIcon className="size-6" />;
}
