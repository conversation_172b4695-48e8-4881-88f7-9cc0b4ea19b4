import { Badge } from '@/components/ui/badge';
import { If } from '@/components/ui/common/if';
import { Heading } from '@/components/ui/heading';
import { Skeleton } from '@/components/ui/skeleton';
import { ZapIcon } from 'lucide-react';

import { builtinConnectionQuery } from '../hooks/builtin-connection.query';
import { HomeSandboxConnectionCard } from './home-sandbox-connection-card';
import { QuickConnectionCard } from './quick-connection-card';

// Simple skeleton component for all connection cards
function ConnectionCardSkeleton() {
  return (
    <div className="bg-primary/5 space-y-2 rounded-lg border p-4">
      <div className="space-y-1">
        <div className="flex items-center justify-between gap-4">
          <div className="flex items-center gap-2 truncate">
            <Skeleton className="size-6 rounded" />
            <Skeleton className="h-5 w-32" />
          </div>
          <div className="flex items-center gap-2">
            <Skeleton className="h-8 w-20 rounded" />
          </div>
        </div>
        <Skeleton className="h-4 w-full" />
      </div>
    </div>
  );
}

export function HomeQuickConnectionSection() {
  const { data, isLoading } = builtinConnectionQuery.query.useList();

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between gap-2 max-sm:flex-col-reverse">
        <Heading level={2}>Quick Connections</Heading>
        <Badge variant="outline-primary" className="w-fit gap-2">
          <ZapIcon className="size-4" />
          <span>Get started in minutes</span>
        </Badge>
      </div>

      <If condition={data}>
        {(data) => (
          <div className="grid grid-cols-1 gap-4 @2xl:grid-cols-2">
            {/* Sandbox Environment Card */}
            <HomeSandboxConnectionCard connections={data.data} />

            {data.data.map((connection) => (
              <QuickConnectionCard
                key={connection.id}
                connection={connection}
              />
            ))}
          </div>
        )}
      </If>

      <If condition={isLoading}>
        <div className="grid grid-cols-1 gap-4 @2xl:grid-cols-2">
          {/* Show 5 skeleton cards to match typical layout */}
          {Array.from({ length: 6 }).map((_, index) => (
            <ConnectionCardSkeleton key={index} />
          ))}
        </div>
      </If>
    </div>
  );
}
