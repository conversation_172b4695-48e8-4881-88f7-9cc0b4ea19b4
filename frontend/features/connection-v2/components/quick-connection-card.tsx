import Image from 'next/image';

import { Card } from '@/components/ui/card';
import { If } from '@/components/ui/common/if';
import { Heading } from '@/components/ui/heading';
import { SchemaBuiltinConnectionPublic } from '@/openapi-ts/gens';
import { PlugIcon } from 'lucide-react';

import { getConnectionLogo } from '../config';
import { ConnectBuiltinDialog } from './connect-builtin-dialog';
import { DisconnectBuiltinButton } from './disconnect-builtin-button';

type Props = {
  connection: SchemaBuiltinConnectionPublic;
};

export function QuickConnectionCard({ connection }: Props) {
  const logoPath = getConnectionLogo(connection.prefix);
  return (
    <Card key={connection.id} className="bg-primary/5 space-y-2 p-4">
      <div className="space-y-1">
        <div className="flex items-center justify-between gap-4">
          <div className="flex items-center gap-2 truncate">
            <If condition={logoPath} fallback={<PlugIcon className="size-6" />}>
              {(logoPath) => (
                <Image
                  src={logoPath}
                  alt={connection.name}
                  width={24}
                  height={24}
                  className="h-auto w-6"
                />
              )}
            </If>
            <Heading level={4} className="truncate">
              {connection.name}
            </Heading>
          </div>

          <div className="flex items-center gap-2">
            <If
              condition={connection.is_connected}
              fallback={<ConnectBuiltinDialog builtinConnection={connection} />}
            >
              <DisconnectBuiltinButton
                builtinId={connection.id}
                builtinName={connection.name}
              />
            </If>
          </div>
        </div>
        <p className="text-muted-foreground text-sm">
          {connection.description}
        </p>
      </div>
    </Card>
  );
}
