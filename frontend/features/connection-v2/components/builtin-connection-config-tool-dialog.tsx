'use client';

import { PropsWithChildren } from 'react';

import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { If } from '@/components/ui/common/if';
import { Spinner } from '@/components/ui/spinner';
import { Switch } from '@/components/ui/switch';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { cn } from '@/lib/utils';
import { SchemaBuiltinConnectionPublic } from '@/openapi-ts/gens';
import { filter, map } from 'lodash';
import { Loader2Icon } from 'lucide-react';

import { builtinConnectionQuery } from '../hooks/builtin-connection.query';

type Props = PropsWithChildren<{
  builtinConnection: SchemaBuiltinConnectionPublic;
}>;

export function BuiltinConnectionConfigToolDialog({
  children,
  builtinConnection,
}: Props) {
  const { mutate, isPending } = builtinConnectionQuery.mutation.useUpdateTool(
    builtinConnection.id,
  );

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>{children}</AlertDialogTrigger>

      <AlertDialogContent className="flex max-h-[90dvh] w-full max-w-3xl flex-col">
        <AlertDialogHeader>
          <AlertDialogTitle>
            Configure Tools for {builtinConnection.name}
          </AlertDialogTitle>
          <AlertDialogDescription>
            Enable or disable tools for {builtinConnection.name}. Only enabled
            tools will be available to agents.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <If
          condition={builtinConnection.tools.length}
          fallback={
            <p className="text-muted-foreground text-sm">No tools available</p>
          }
        >
          <div className="flex items-center justify-between">
            <p className="font-medium">Available Tools</p>
            <p className="text-muted-foreground text-sm">
              {filter(builtinConnection.tools, 'is_enabled').length} of{' '}
              {builtinConnection.tools.length} enabled
            </p>
          </div>
          <Table
            containerClassName={cn(
              'overflow-thin-auto relative grow',
              isPending && 'overflow-hidden',
            )}
          >
            <If condition={isPending}>
              <div className="bg-foreground/5 absolute inset-0 z-10 flex items-center justify-center">
                <Spinner />
              </div>
            </If>
            <TableHeader>
              <TableRow>
                <TableHead className="max-w-[300px] truncate">
                  Tool name
                </TableHead>
                <TableHead className="text-center">Enabled</TableHead>
                <TableHead className="text-center">
                  Required Permission
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {map(builtinConnection.tools, (tool) => (
                <TableRow key={tool.name}>
                  <TableCell className="font-medium">{tool.name}</TableCell>
                  <TableCell className="text-center">
                    <Switch
                      checked={tool.is_enabled}
                      onCheckedChange={(checked) => {
                        mutate({
                          tool_name: tool.name,
                          is_enabled: checked,
                          is_required_permission: tool.is_required_permission,
                        });
                      }}
                    />
                  </TableCell>
                  <TableCell className="text-center">
                    <Switch
                      checked={tool.is_required_permission}
                      onCheckedChange={(checked) => {
                        mutate({
                          tool_name: tool.name,
                          is_enabled: tool.is_enabled,
                          is_required_permission: checked,
                        });
                      }}
                    />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </If>

        <AlertDialogFooter>
          <AlertDialogCancel disabled={isPending}>
            <If condition={isPending}>
              <Loader2Icon className="mr-2 size-4 animate-spin" />
            </If>
            Done
          </AlertDialogCancel>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
