'use client';

import { Badge } from '@/components/ui/badge';
import { PageSkeleton } from '@/components/ui/common/page';

import { builtinConnectionQuery } from '../hooks/builtin-connection.query';
import { BuiltinConnectionCard } from './builtin-connection-card';
import { SandboxConnectionCard } from './sandbox-connection-card';

export function BuiltinConnectionPage() {
  const { data, isLoading } = builtinConnectionQuery.query.useList();

  if (data) {
    return (
      <div className="space-y-2">
        <Badge variant="ghost-primary" className="px-3 py-1">
          {data.data.length} Builtin Connections
        </Badge>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4">
          <SandboxConnectionCard connections={data.data} />
          {data.data.map((connection) => (
            <BuiltinConnectionCard
              key={connection.id}
              connection={connection}
            />
          ))}
        </div>
      </div>
    );
  }

  if (isLoading) {
    return <PageSkeleton />;
  }
}
