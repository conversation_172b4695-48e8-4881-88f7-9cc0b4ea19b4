'use client';

import { Card } from '@/components/ui/card';
import { Heading } from '@/components/ui/heading';
import { SchemaBuiltinConnectionPublic } from '@/openapi-ts/gens';
import { FlaskConicalIcon } from 'lucide-react';

import { SandboxConnectionDialog } from './sandbox-connection-dialog';

type Props = {
  connections: SchemaBuiltinConnectionPublic[];
};

export function HomeSandboxConnectionCard({ connections }: Props) {
  return (
    <Card className="bg-primary/5 space-y-2 p-4">
      <div className="space-y-1">
        <div className="flex items-center justify-between gap-4">
          <div className="flex items-center gap-2 truncate">
            <FlaskConicalIcon className="h-6 w-6" />
            <Heading level={4} className="truncate">
              Setup Sandbox Connections
            </Heading>
          </div>

          <div className="flex items-center gap-2">
            <SandboxConnectionDialog connections={connections} />
          </div>
        </div>
        <p className="text-muted-foreground text-sm">
          Choose demo connections to explore CloudThinker safely
        </p>
      </div>
    </Card>
  );
}
