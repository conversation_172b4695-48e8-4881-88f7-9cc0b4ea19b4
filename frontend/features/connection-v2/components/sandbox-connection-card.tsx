import { Card } from '@/components/ui/card';
import { Heading } from '@/components/ui/heading';
import { SchemaBuiltinConnectionPublic } from '@/openapi-ts/gens';
import { FlaskConicalIcon } from 'lucide-react';

import { SandboxConnectionDialog } from './sandbox-connection-dialog';

type Props = {
  connections: SchemaBuiltinConnectionPublic[];
};

export function SandboxConnectionCard({ connections }: Props) {
  return (
    <Card className="flex flex-col space-y-2 p-4">
      <div className="grow space-y-1">
        <div className="flex items-center gap-2">
          <FlaskConicalIcon className="h-6 w-6" />
          <Heading level={4}>Setup Sandbox Connections</Heading>
        </div>

        <p className="text-muted-foreground text-sm">
          Choose demo connections to explore CloudThinker safely
        </p>
      </div>
      <div className="flex items-center justify-end">
        <SandboxConnectionDialog connections={connections} />
      </div>
    </Card>
  );
}
