import { If } from '@/components/ui/common/if';
import { SchemaBuiltinConnectionPublic } from '@/openapi-ts/gens';

import { ConnectBuiltinDialog } from './connect-builtin-dialog';
import { DisconnectBuiltinButton } from './disconnect-builtin-button';

type Props = {
  connection: SchemaBuiltinConnectionPublic;
};

export function BuiltinConnectedActions({ connection }: Props) {
  return (
    <If
      condition={connection.is_connected}
      fallback={<ConnectBuiltinDialog builtinConnection={connection} />}
    >
      <DisconnectBuiltinButton
        builtinId={connection.id}
        builtinName={connection.name}
      />
    </If>
  );
}
