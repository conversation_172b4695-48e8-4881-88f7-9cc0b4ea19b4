import { useRouter } from 'next/navigation';

import { ETagCache } from '@/constants/tag-cache';
import { clearTagCache } from '@/utils/server/server-action';
import { createQueryKeys } from '@lukemorales/query-key-factory';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

import { builtinConnectionApi } from '../services/builtin-connection.api';

const builtinConnectionQueryKeys = createQueryKeys('builtin-connections', {
  list: {
    queryKey: null,
    queryFn: () => builtinConnectionApi.list(),
  },
});

const useList = () => {
  return useQuery(builtinConnectionQueryKeys.list);
};

const useDisconnect = (builtinId: string) => {
  const queryClient = useQueryClient();
  const router = useRouter();

  return useMutation({
    mutationFn: () => builtinConnectionApi.detail(builtinId).disconnect(),
    onSuccess: async () => {
      toast.success('Builtin connection disconnected successfully');
      queryClient.invalidateQueries({
        queryKey: builtinConnectionQueryKeys.list.queryKey,
      });
      await clearTagCache(ETagCache.CONSTANTS);
      router.refresh();
    },
  });
};

const useConnect = (builtinId: string) => {
  const queryClient = useQueryClient();
  const router = useRouter();

  return useMutation({
    mutationFn: builtinConnectionApi.detail(builtinId).connect,
    onSuccess: async () => {
      toast.success('Builtin connection connected successfully');
      queryClient.invalidateQueries({
        queryKey: builtinConnectionQueryKeys.list.queryKey,
      });
      await clearTagCache(ETagCache.CONSTANTS);
      router.refresh();
    },
  });
};

const useUpdateTool = (builtinId: string) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: builtinConnectionApi.detail(builtinId).updateTool,
    onSuccess: () => {
      toast.success('Builtin connection tools updated successfully');
      queryClient.invalidateQueries({
        queryKey: builtinConnectionQueryKeys.list.queryKey,
      });
    },
  });
};

const useInstallSandbox = () => {
  const queryClient = useQueryClient();
  const router = useRouter();

  return useMutation({
    mutationFn: builtinConnectionApi.sandbox.install,
    onSuccess: async () => {
      toast.success('Sandbox connection installed successfully');
      queryClient.invalidateQueries({
        queryKey: builtinConnectionQueryKeys.list.queryKey,
      });
      await clearTagCache(ETagCache.CONSTANTS);
      router.refresh();
    },
  });
};
// const useInstall = () => {
//   const queryClient = useQueryClient();
//   return useMutation({
//     mutationFn: (params: { builtin_id: string; env: Record<string, string>[] }) =>
//       builtinConnectionApi.install(params.builtin_id, params.env),
//     onSuccess: () => {
//       queryClient.invalidateQueries({ queryKey: builtinConnectionQueryKeys.list.queryKey });
//       toast.success('Builtin connection installed successfully');
//     },
//     onError: (error: any) => {
//       toast.error(error?.message || 'Failed to install builtin connection');
//     },
//   });
// };

// const useUpdate = () => {
//   const queryClient = useQueryClient();
//   return useMutation({
//     mutationFn: (params: { builtin_id: string; env: Record<string, string>[] }) =>
//       builtinConnectionApi.update(params.builtin_id, params.env),
//     onSuccess: () => {
//       queryClient.invalidateQueries({ queryKey: builtinConnectionQueryKeys.list.queryKey });
//       toast.success('Builtin connection updated successfully');
//     },
//     onError: (error: any) => {
//       toast.error(error?.message || 'Failed to update builtin connection');
//     },
//   });
// };

// const useDelete = () => {
//   const queryClient = useQueryClient();
//   return useMutation({
//     mutationFn: (params: { builtin_id: string }) =>
//       builtinConnectionApi.delete(params.builtin_id),
//     onSuccess: () => {
//       queryClient.invalidateQueries({ queryKey: builtinConnectionQueryKeys.list.queryKey });
//       toast.success('Builtin connection deleted successfully');
//     },
//     onError: (error: any) => {
//       toast.error(error?.message || 'Failed to delete builtin connection');
//     },
//   });
// };

// const useUpdateTool = () => {
//   const queryClient = useQueryClient();
//   return useMutation({
//     mutationFn: (params: { builtin_id: string; tool_name: string; is_enabled: boolean; is_required_permission: boolean }) =>
//       builtinConnectionApi.updateTool(params.builtin_id, params.tool_name, params.is_enabled, params.is_required_permission),
//     onSuccess: () => {
//       queryClient.invalidateQueries({ queryKey: builtinConnectionQueryKeys.list.queryKey });
//       toast.success('Builtin connection tools updated successfully');
//     },
//     onError: (error: any) => {
//       toast.error(error?.message || 'Failed to update builtin connection tools');
//     },
//   });
// };

export const builtinConnectionQuery = {
  query: {
    useList,
  },
  mutation: {
    useDisconnect,
    useConnect,
    useInstallSandbox,
    // useInstall,
    // useUpdate,
    // useDelete,
    useUpdateTool,
  },
};
