import { PathsRequestQueryDto } from '@/openapi-ts/utils.type';

export type ConversationQueryParams =
  PathsRequestQueryDto<'/api/v1/autonomous-agents/conversations'>;

// Background task status structure
interface BackgroundTaskStatus {
  exists: boolean;
  status: string;
  cancelled: boolean;
  exception: unknown;
}

// Redis stream status structure
interface RedisStreamStatus {
  is_streaming_active: boolean;
  stream_status: 'completed' | 'in_progress' | 'paused' | 'error';
  last_stream_position: number;
  current_message_id?: string;
  current_message_start_position?: number;
  message_boundaries?: Record<
    string,
    {
      start: number;
      end: number;
      status: string;
    }
  >;
  started_at?: string;
}
export interface StreamStatusResponse {
  conversation_id: string;
  redis_stream_status: RedisStreamStatus | null;
  background_task_status: BackgroundTaskStatus | null;
  current_message_events_count: number;
}
