export const CONVERSATION_CONFIG = {
  CACHE: {
    STALE_TIME: 5 * 60 * 1000, // 5 minutes
    GC_TIME: 10 * 60 * 1000,   // 10 minutes
  },
  PAGINATION: {
    DEFAULT_LIMIT: 10,
    MAX_LIMIT: 100,
  },
  NAVIGATION: {
    AGENTS_BASE_PATH: '/agents',
  },
} as const;

export const CONVERSATION_MESSAGES = {
  SUCCESS: {
    CREATED: 'Conversation created successfully',
    RENAMED: 'Conversation renamed successfully',
    DELETED: 'Conversation deleted successfully',
  },
  ERROR: {
    CREATE_FAILED: 'Failed to create conversation',
    RENAME_FAILED: 'Failed to rename conversation',
    DELETE_FAILED: 'Failed to delete conversation',
    NO_AGENTS: 'No autonomous agents available',
    AGENT_DISCOVERY_FAILED: 'Failed to find available agents',
  },
  LOADING: {
    CREATING: 'Preparing your conversation...',
  },
} as const;
