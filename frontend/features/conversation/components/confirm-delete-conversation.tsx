'use client';

import { PropsWithChildren } from 'react';

import { DeleteConfirmAlert } from '@/components/ui/common/delete-confirm-alert';

import { conversationQuery } from '../hooks/conversation.query';

type Props = PropsWithChildren<{
  conversationId: string;
}>;

export function ConfirmDeleteConversation({ children, conversationId }: Props) {
  const { mutate, isPending } =
    conversationQuery.mutation.useDelete(conversationId);

  const onConfirm = (toggle: () => void) => {
    mutate(undefined, {
      onSuccess: toggle,
    });
  };

  return (
    <DeleteConfirmAlert
      title="Delete Conversation"
      loading={isPending}
      onConfirm={onConfirm}
    >
      {children}
    </DeleteConfirmAlert>
  );
}
