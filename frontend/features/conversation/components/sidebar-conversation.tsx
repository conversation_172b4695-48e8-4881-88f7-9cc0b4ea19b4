'use client';

import Link from 'next/link';

import { Button } from '@/components/ui/button';
import {
  SidebarGroupLabel,
  SidebarMenuButton,
  useSidebar,
} from '@/components/ui/sidebar';
import pathsConfig from '@/config/paths.config';
import { useUserContext } from '@/features/user/provider/user-provider';
import { cn } from '@/lib/utils';
import { HistoryIcon, MessageSquarePlusIcon } from 'lucide-react';

import { HistoriesPopup } from './histories-popup';
import { RecentConversationList } from './recent-conversation-list';

export function SidebarConversation() {
  const { open } = useSidebar();
  const { agentId } = useUserContext();

  return (
    <>
      <SidebarMenuButton tooltip="Start a new chat" asChild>
        <Button
          variant="ghost"
          size="sm"
          className="flex w-full items-center justify-start gap-2"
          asChild
        >
          <Link href={pathsConfig.app.agentDetail(agentId)}>
            <MessageSquarePlusIcon className="size-4" />
            <span>New chat</span>
          </Link>
        </Button>
      </SidebarMenuButton>
      <SidebarGroupLabel className={cn({ hidden: !open })}>
        Recent
      </SidebarGroupLabel>
      <RecentConversationList className="group-data-[minimized=true]:hidden" />
      <HistoriesPopup>
        <SidebarMenuButton tooltip="View All History" asChild>
          <Button
            variant="ghost"
            size="sm"
            className="relative w-full justify-start gap-2 px-2"
          >
            <HistoryIcon className="size-4" />
            <span>View All History</span>
          </Button>
        </SidebarMenuButton>
      </HistoriesPopup>
    </>
  );
}
