'use client';

import { PropsWithChildren, useState } from 'react';

import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';

import { ConversationList } from './conversation-list';

export function HistoriesDialog({
  children,
  onCloseDropdown,
}: PropsWithChildren<{
  onCloseDropdown: () => void;
}>) {
  const [open, setOpen] = useState(false);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent>
        <ConversationList
          onClose={() => {
            setOpen(false);
            onCloseDropdown();
          }}
        />
      </DialogContent>
    </Dialog>
  );
}
