'use client';

import { PropsWithChildren, useState } from 'react';

import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

import { ConversationList } from './conversation-list';

export function HistoriesPopup({ children }: PropsWithChildren) {
  const [open, setOpen] = useState(false);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>{children}</PopoverTrigger>

      <PopoverContent
        align="start"
        side="right"
        sideOffset={8}
        className="space-y-2 sm:w-96"
      >
        <ConversationList onClose={() => setOpen(false)} />
      </PopoverContent>
    </Popover>
  );
}
