import { PropsWithChildren } from 'react';

import { Button } from '@/components/ui/button';
import { If } from '@/components/ui/common/if';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { SchemaConversationRenameRequest } from '@/openapi-ts/gens';
import { Loader2 } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { useToggle } from 'usehooks-ts';

import { conversationQuery } from '../hooks/conversation.query';

type Props = PropsWithChildren<{
  conversationId: string;
  defaultName: string;
}>;

export function RenameConversationDialog({
  children,
  conversationId,
  defaultName,
}: Props) {
  const [open, toggle] = useToggle(false);
  const { mutate, isPending } =
    conversationQuery.mutation.useRename(conversationId);

  const form = useForm<SchemaConversationRenameRequest>({
    defaultValues: {
      name: defaultName,
    },
  });

  const onSubmit = (data: SchemaConversationRenameRequest) => {
    mutate(data, {
      onSuccess: toggle,
    });
  };

  return (
    <Dialog open={open} onOpenChange={toggle}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <DialogHeader>
              <DialogTitle>Rename Conversation</DialogTitle>
              <DialogDescription>
                Rename the conversation to a new name.
              </DialogDescription>
            </DialogHeader>

            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Name</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      disabled={isPending}
                      placeholder="Enter new name"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <DialogClose asChild>
                <Button variant="outline">Cancel</Button>
              </DialogClose>
              <Button type="submit" disabled={isPending} className="gap-2">
                <If condition={isPending}>
                  <Loader2 className="size-4 animate-spin" />
                </If>
                Save
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
