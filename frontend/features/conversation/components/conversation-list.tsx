'use client';

import { useEffect, useState } from 'react';

import Link from 'next/link';

import { Button } from '@/components/ui/button';
import { If } from '@/components/ui/common/if';
import { PageSkeleton } from '@/components/ui/common/page';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Heading } from '@/components/ui/heading';
import { InputIconPrefix } from '@/components/ui/input-icon-prefix';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { agentDetailUrl } from '@/features/agent/hooks/use-navigate-agent-detail';
import { ConfirmDeleteConversation } from '@/features/conversation/components/confirm-delete-conversation';
import { RenameConversationDialog } from '@/features/conversation/components/rename-conversation-dialog';
import { useUserContext } from '@/features/user/provider/user-provider';
import { Loader2Icon, MoreHorizontalIcon, SearchIcon } from 'lucide-react';
import { useInView } from 'react-intersection-observer';
import { useDebounceValue } from 'usehooks-ts';

import { conversationQuery } from '../hooks/conversation.query';

type Props = {
  onClose: () => void;
};

export function ConversationList({ onClose }: Props) {
  const { agentId } = useUserContext();
  const [valueSearch, setValueSearch] = useState('');
  const [debouncedValueSearch] = useDebounceValue(valueSearch, 300);

  const { data, isFetchingNextPage, fetchNextPage, hasNextPage, isLoading } =
    conversationQuery.query.useInfiniteList({
      agent_id: agentId,
      search: debouncedValueSearch,
    });

  const { ref: loadMoreRef, inView } = useInView({
    threshold: 0.1,
    rootMargin: '100px',
  });

  useEffect(() => {
    if (inView && hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [inView, hasNextPage, isFetchingNextPage, fetchNextPage]);

  return (
    <>
      <If condition={data}>
        {(data) => {
          const histories = data.pages.flatMap((page) => page.data);

          return (
            <>
              <div className="space-y-1">
                <Heading level={5}>Recent Conversations</Heading>
                <InputIconPrefix
                  Icon={SearchIcon}
                  placeholder="Search conversations..."
                  value={valueSearch}
                  onChange={(e) => setValueSearch(e.target.value)}
                />
              </div>
              <Separator />
              <ScrollArea className="h-[400px]">
                {histories.map((history) => (
                  <Link
                    key={history.id}
                    href={agentDetailUrl(agentId, {
                      conversationId: history.id,
                    })}
                    className="hover:bg-secondary flex items-center justify-between space-y-1 rounded-md px-2 py-1 transition-colors"
                    onClick={onClose}
                  >
                    <p className="max-w-40 truncate text-sm sm:max-w-64">
                      {history.name}
                    </p>

                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontalIcon className="size-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent onClick={(e) => e.stopPropagation()}>
                        <RenameConversationDialog
                          conversationId={history.id}
                          defaultName={history.name}
                        >
                          <DropdownMenuItem
                            onSelect={(e) => e.preventDefault()}
                          >
                            Rename
                          </DropdownMenuItem>
                        </RenameConversationDialog>
                        <ConfirmDeleteConversation conversationId={history.id}>
                          <DropdownMenuItem
                            onSelect={(e) => e.preventDefault()}
                            className="text-destructive/80 hover:!text-destructive flex h-full w-full items-center gap-2"
                          >
                            Delete
                          </DropdownMenuItem>
                        </ConfirmDeleteConversation>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </Link>
                ))}

                <If condition={hasNextPage}>
                  <div ref={loadMoreRef} className="flex justify-center py-4">
                    <If
                      condition={isFetchingNextPage}
                      fallback={
                        <div className="text-muted-foreground text-sm">
                          Scroll to load more...
                        </div>
                      }
                    >
                      <Loader2Icon className="text-primary size-4 animate-spin" />
                    </If>
                  </div>
                </If>
              </ScrollArea>
            </>
          );
        }}
      </If>
      <If condition={isLoading}>
        <PageSkeleton />
      </If>
    </>
  );
}
