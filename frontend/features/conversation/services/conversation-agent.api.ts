import { agentApi } from '@/features/agent/services/agent.api';
import { AgentType } from '@/openapi-ts/gens';

export const conversationAgentApi = {
  /**
   * Get the default autonomous agent for conversation creation
   */
  getDefaultAutonomousAgent: async () => {
    const agents = await agentApi.list();
    const autonomousAgent = agents.data.find(
      (agent) => agent.type === AgentType.autonomous_agent,
    );

    if (!autonomousAgent) {
      throw new Error('No autonomous agents available');
    }

    return autonomousAgent;
  },

  /**
   * Check if autonomous agents are available
   */
  isAutonomousAgentAvailable: async () => {
    try {
      await conversationAgentApi.getDefaultAutonomousAgent();
      return true;
    } catch {
      return false;
    }
  },
};
