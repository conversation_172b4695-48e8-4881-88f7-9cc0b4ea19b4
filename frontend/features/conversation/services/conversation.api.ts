import { urlConfig } from '@/config/url.config';
import { SchemaConversationRenameRequest } from '@/openapi-ts/gens';
import { getAccessToken } from '@/openapi-ts/get-access-token';
import { api, fetchData, interpolatePath } from '@/openapi-ts/openapi-fetch';

import {
  ConversationQueryParams,
  StreamStatusResponse,
} from '../models/conversation.type';

export const conversationApi = {
  // Basic CRUD operations
  list: (query: ConversationQueryParams) =>
    fetchData(
      api.GET('/api/v1/autonomous-agents/conversations', {
        params: {
          query,
        },
      }),
    ),

  detail: (conversationId: string) => ({
    rename: (body: SchemaConversationRenameRequest) =>
      fetchData(
        api.PUT(
          '/api/v1/autonomous-agents/conversations/{conversation_id}/name',
          {
            params: { path: { conversation_id: conversationId } },
            body,
          },
        ),
      ),

    delete: () =>
      fetchData(
        api.DELETE(
          '/api/v1/autonomous-agents/conversations/{conversation_id}',
          {
            params: { path: { conversation_id: conversationId } },
          },
        ),
      ),

    getMessagesHistory: () =>
      fetchData(
        api.GET('/api/v1/autonomous-agents/messages/{conversation_id}', {
          params: { path: { conversation_id: conversationId } },
        }),
      ),

    getMessagePlans: () =>
      fetchData(
        api.GET('/api/v1/autonomous-agents/messages/{conversation_id}/plans', {
          params: { path: { conversation_id: conversationId } },
        }),
      ),

    getStreamStatus: () => getStreamStatus(conversationId),
  }),

  // Agent-specific operations
  getWithAgent: (agentId: string, resourceId?: string) =>
    fetchData(
      api.GET('/api/v1/autonomous-agents/conversations', {
        params: {
          query: {
            agent_id: agentId,
            ...(resourceId && { resource_id: resourceId }),
          },
        },
      }),
    ),
};

const getStreamStatus = async (
  conversationId: string,
): Promise<StreamStatusResponse | null> => {
  try {
    const url = new URL(
      interpolatePath(
        '/api/v1/autonomous-agents/chat/{conversation_id}/stream-status',
        {
          conversation_id: conversationId,
        },
      ),
      urlConfig.apiUrl,
    );
    const accessToken = await getAccessToken();

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${accessToken}`,
      },
    });

    if (!response.ok) {
      if (response.status === 404) {
        // No active stream found
        return null;
      }
      throw new Error(`Failed to fetch stream status: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error checking stream status:', error);
    return null;
  }
};
