import { useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { conversationUrlUtils } from '../utils/conversation-url.utils';

export const useConversationNavigation = () => {
  const router = useRouter();

  const navigateToConversation = useCallback(
    (agentId: string, conversationId: string, initialMessage?: string) => {
      conversationUrlUtils.navigateToConversation(
        router,
        agentId,
        conversationId,
        initialMessage
      );
    },
    [router]
  );

  const buildConversationUrl = useCallback(
    (agentId: string, conversationId: string, initialMessage?: string) => {
      return conversationUrlUtils.buildAgentConversationUrl(
        agentId,
        conversationId,
        initialMessage
      );
    },
    []
  );

  return {
    navigateToConversation,
    buildConversationUrl,
  };
};
