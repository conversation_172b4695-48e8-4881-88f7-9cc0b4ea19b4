import { createQueryKeys } from '@lukemorales/query-key-factory';
import {
  keepPreviousData,
  useInfiniteQuery,
  useMutation,
  useQuery,
  useQueryClient,
} from '@tanstack/react-query';
import { toast } from 'sonner';

import {
  CONVERSATION_CONFIG,
  CONVERSATION_MESSAGES,
} from '../config/conversation.config';
import { ConversationQueryParams } from '../models/conversation.type';
import { conversationApi } from '../services/conversation.api';

const conversationQueryKeys = createQueryKeys('conversations', {
  list: (params: ConversationQueryParams) => ({
    queryKey: [params],
    queryFn: () => conversationApi.list(params),
  }),

  byAgent: (agentId: string, resourceId?: string) => ({
    queryKey: [{ agentId, resourceId }],
    queryFn: () => conversationApi.getWithAgent(agentId, resourceId),
  }),

  infinite: (params: ConversationQueryParams) => ({
    queryKey: [params],
  }),
});

// Query hooks
const useList = (params: ConversationQueryParams) => {
  return useQuery({
    ...conversationQueryKeys.list(params),
    staleTime: CONVERSATION_CONFIG.CACHE.STALE_TIME,
    gcTime: CONVERSATION_CONFIG.CACHE.GC_TIME,
    placeholderData: keepPreviousData,
  });
};

const useByAgent = (agentId: string, resourceId?: string) => {
  return useQuery({
    ...conversationQueryKeys.byAgent(agentId, resourceId),
    enabled: !!agentId,
    staleTime: CONVERSATION_CONFIG.CACHE.STALE_TIME,
    gcTime: CONVERSATION_CONFIG.CACHE.GC_TIME,
  });
};

const useInfiniteList = (params: ConversationQueryParams) => {
  return useInfiniteQuery({
    queryKey: conversationQueryKeys.infinite(params).queryKey,
    queryFn: ({ pageParam = 0 }) =>
      conversationApi.list({
        ...params,
        skip: pageParam * 20,
        limit: 100,
      }),
    initialPageParam: 0,
    getNextPageParam: (lastPage, _, lastPageParam) => {
      return lastPage.data.length === 20 ? lastPageParam + 1 : undefined;
    },
    placeholderData: keepPreviousData,
  });
};

const useRename = (conversationId: string) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: conversationApi.detail(conversationId).rename,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: conversationQueryKeys._def });
      toast.success(CONVERSATION_MESSAGES.SUCCESS.RENAMED);
    },
    onError: () => {
      toast.error(CONVERSATION_MESSAGES.ERROR.RENAME_FAILED);
    },
  });
};

const useDelete = (conversationId: string) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: conversationApi.detail(conversationId).delete,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: conversationQueryKeys._def });
      toast.success(CONVERSATION_MESSAGES.SUCCESS.DELETED);
    },
    onError: () => {
      toast.error(CONVERSATION_MESSAGES.ERROR.DELETE_FAILED);
    },
  });
};

export const conversationQuery = {
  query: {
    useList,
    useByAgent,
    useInfiniteList,
  },
  mutation: {
    useRename,
    useDelete,
  },
};
