{"name": "frontend", "version": "1.0.0", "private": true, "scripts": {"build": "next build", "clean": "git clean -xdf node_modules dist .next", "dev": "next dev --turbopack -p 5173", "format": "prettier --check \"**/*.{js,cjs,mjs,ts,tsx,md,json}\"", "format:fix": "prettier --write \"**/*.{js,cjs,mjs,ts,tsx,md,json}\"", "gen": "openapi-typescript https://cloud-c2-dev.builder-studio.com/api/v1/openapi.json --enum --dedupe-enums --output openapi-ts/gens.ts --root-types", "gen:local": "openapi-typescript http://localhost:8000/api/v1/openapi.json --enum --dedupe-enums --output openapi-ts/gens.ts --root-types", "generate-client": "openapi-ts", "preinstall": "npx only-allow pnpm", "postinstall": "sort-package-json", "lint": "eslint .", "lint:fix": "next lint --fix", "start": "next start", "typecheck": "tsc --noEmit"}, "lint-staged": {"**/*": ["pnpm format:fix"]}, "prettier": "./prettier/base.mjs", "dependencies": {"@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^5.2.1", "@lukemorales/query-key-factory": "^1.3.4", "@next/eslint-plugin-next": "15.3.3", "@radix-ui/react-icons": "^1.3.2", "@tailwindcss/postcss": "^4.1.10", "@tanstack/react-query": "5.84.1", "@tanstack/react-table": "^8.21.3", "@tanstack/react-virtual": "^3.13.6", "@types/d3": "^7.4.3", "@types/d3-sankey": "^0.12.4", "@types/eslint": "9.6.1", "@types/lodash": "^4.17.16", "@types/react-dom": "19.1.6", "@uiw/react-markdown-preview": "^5.1.3", "@xterm/addon-fit": "^0.10.0", "@xterm/addon-web-links": "^0.11.0", "@xterm/xterm": "^5.5.0", "axios": "^1.7.9", "caniuse-lite": "^1.0.30001723", "class-variance-authority": "^0.7.0", "classnames": "^2.5.1", "clsx": "^2.1.1", "cmdk": "1.1.1", "cron-validate": "^1.4.5", "cronstrue": "^2.54.0", "d3": "^7.9.0", "d3-sankey": "^0.12.3", "date-fns": "^4.1.0", "embla-carousel-react": "^8.5.2", "es-toolkit": "^1.30.1", "eslint-config-next": "15.3.3", "eslint-config-turbo": "^2.5.4", "framer-motion": "^12.0.0", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "lucide-react": "^0.536.0", "match-sorter": "^6.3.4", "mermaid": "^11.4.1", "motion": "^12.4.5", "next": "15.4.5", "next-auth": "^5.0.0-beta.18", "next-themes": "0.4.6", "nextjs-toploader": "^1.6.12", "nuqs": "^1.19.1", "openapi-fetch": "^0.14.0", "pluralize": "^8.0.0", "postcss": "^8.5.1", "radix-ui": "1.4.2", "react": "19.1.1", "react-collapsed": "^4.2.0", "react-dom": "19.1.1", "react-dropzone": "^14.2.3", "react-hook-form": "^7.62.0", "react-icons": "^5.4.0", "react-intersection-observer": "^9.16.0", "react-markdown": "^9.1.0", "react-resizable-panels": "^2.1.7", "react-top-loading-bar": "3.0.2", "recharts": "2.15.3", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "shiki": "^3.8.1", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "tailwindcss": "4.1.11", "tailwindcss-animate": "^1.0.7", "typescript-eslint": "8.34.1", "usehooks-ts": "^3.1.1", "uuid": "^9.0.1", "zod": "^3.25.74", "zustand": "^4.4.6"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.29.0", "@faker-js/faker": "^9.0.3", "@hey-api/client-axios": "^0.3.1", "@hey-api/openapi-ts": "^0.59.2", "@svgr/webpack": "^8.1.0", "@tailwindcss/typography": "^0.5.16", "@trivago/prettier-plugin-sort-imports": "5.2.2", "@types/js-cookie": "^3.0.6", "@types/node": "^24.0.4", "@types/pluralize": "^0.0.33", "@types/react": "19.1.8", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^8.34.1", "eslint": "^9.30.1", "husky": "^9.0.11", "lint-staged": "^15.2.7", "null-loader": "^4.0.1", "openapi-typescript": "^7.4.4", "openapi-typescript-codegen": "^0.29.0", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.13", "react-scan": "^0.3.4", "sort-package-json": "^3.2.1", "tailwind-scrollbar": "^4.0.2", "typescript": "^5.9.2"}, "packageManager": "pnpm@9.12.0", "pnpm": {"overrides": {"@types/react": "19.1.8", "@types/react-dom": "19.1.6"}}}