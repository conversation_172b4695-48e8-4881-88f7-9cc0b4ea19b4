import React from 'react';

import { Minus, TrendingDown, TrendingUp } from 'lucide-react';

type TrendDirection = 'up' | 'down' | 'neutral';

export interface TrendData {
  direction: TrendDirection;
  value: string;
  description?: string;
}

/**
 * Maps trend direction to corresponding Lucide icon component
 */
const TREND_ICONS = {
  up: TrendingUp,
  down: TrendingDown,
  neutral: Minus,
} as const;

/**
 * Maps trend direction to CSS color classes
 */
const TREND_COLORS = {
  up: 'text-green-600',
  down: 'text-red-600',
  neutral: 'text-muted-foreground',
} as const;

/**
 * Maps trend direction to arrow symbols
 */
const TREND_SYMBOLS = {
  up: '↑',
  down: '↓',
  neutral: '→',
} as const;

/**
 * Gets the icon component for a trend direction
 */
export const getTrendIcon = (direction: TrendDirection) =>
  TREND_ICONS[direction] || TREND_ICONS.neutral;

/**
 * Gets the color class for a trend direction
 */
export const getTrendColor = (direction: TrendDirection): string =>
  TREND_COLORS[direction] || TREND_COLORS.neutral;

/**
 * Gets the symbol for a trend direction
 */
export const getTrendSymbol = (direction: TrendDirection): string =>
  TREND_SYMBOLS[direction] || TREND_SYMBOLS.neutral;

/**
 * Functional component to render trend indicator with icon
 */
export const renderTrendWithIcon = (trend: TrendData) => {
  const TrendIcon = getTrendIcon(trend.direction);
  const colorClass = getTrendColor(trend.direction);

  return React.createElement(
    'div',
    { className: `flex items-center gap-1 text-sm ${colorClass}` },
    React.createElement(TrendIcon, { className: 'h-4 w-4' }),
    React.createElement('span', { className: 'font-medium' }, trend.value),
    trend.description &&
      React.createElement(
        'span',
        { className: 'text-muted-foreground text-xs' },
        trend.description,
      ),
  );
};

/**
 * Functional component to render trend indicator with symbol
 */
export const renderTrendWithSymbol = (trend: TrendData) => {
  const symbol = getTrendSymbol(trend.direction);
  const colorClass = getTrendColor(trend.direction);

  return React.createElement(
    'div',
    { className: `flex items-center gap-1 text-sm ${colorClass}` },
    React.createElement(
      'span',
      { className: 'font-medium' },
      `${symbol} ${trend.value}`,
    ),
    trend.description &&
      React.createElement(
        'span',
        { className: 'text-muted-foreground text-xs' },
        trend.description,
      ),
  );
};
