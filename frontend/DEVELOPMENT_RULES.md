# Frontend Development Rules

This document outlines the conventions and architecture for developing frontend features for the Cloud Cost Optimization platform.

## Project Architecture

Our project follows a feature-based architecture with Next.js App Router, emphasizing a clear separation of concerns:

### Directory Structure

```
frontend/
├── app/                       # Next.js App Router
│   └── (home)/                # Main application routes
│       └── [feature]/         # Feature-specific pages
│           ├── _components/   # Page-specific components
│           └── page.tsx       # Route definition
│
├── features/                  # Feature modules (business logic)
│   └── [feature]/             # Specific feature
│       ├── components/        # Feature-specific reusable components
│       ├── config/            # Feature configuration
│       ├── hooks/             # Feature-specific hooks
│       ├── models/            # TypeScript types and interfaces
│       └── services/          # API interactions and data fetching
│
└── components/                # Global shared components
```

## Feature Development Rules

### 1. Feature Module Structure

Each feature should have the following structure in `features/[feature-name]/`:

- **components/**: UI components specific to this feature
- **config/**: Configuration constants, enums, and maps
- **hooks/**: React Query hooks and other custom hooks
- **models/**: TypeScript types and interfaces
- **services/**: API interaction functions

### 2. API Services Pattern

For API interactions (`services/`):

```typescript
// services/[feature].api.ts
import { api, fetchData } from '@/openapi-ts/openapi-fetch';

export const featureApi = {
  list: (query: QueryParams) =>
    fetchData(api.GET('/api/v1/resources/', { params: { query } })),

  detail: (id: string) => ({
    getInfo: () =>
      fetchData(api.GET(`/api/v1/resources/{id}`, {
        params: { path: { id } }
      })),

    update: (body: UpdateBody) =>
      api.PUT(`/api/v1/resources/{id}`, {
        params: { path: { id } },
        body
      }),

    // Other operations on the resource
  })
};
```

### 3. React Query Pattern

For data fetching and mutations (`hooks/`):

```typescript
// hooks/[feature].query.ts
import { createQueryKeys } from '@lukemorales/query-key-factory';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

// Define query keys
const featureQueryKeys = createQueryKeys('[feature]', {
  list: (params) => ({
    queryKey: [params],
    queryFn: () => featureApi.list(params),
  }),
  detail: (id) => ({
    queryKey: [id],
    queryFn: () => featureApi.detail(id).getInfo(),
  }),
});

// Query hooks
const useList = (params) => useQuery({
  ...featureQueryKeys.list(params),
  placeholderData: keepPreviousData,
});

// Mutation hooks
const useUpdate = (id) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: featureApi.detail(id).update,
    onSuccess: () => {
      toast.success('Updated successfully');
      queryClient.invalidateQueries({
        queryKey: featureQueryKeys.detail._def,
      });
    },
  });
};

// Export hooks as a single object
export const featureQuery = {
  query: {
    useList,
    useDetail,
  },
  mutation: {
    useUpdate,
  },
};
```

### 4. Component Structure

Each component should:

- Be in its own file with kebab-case naming
- Use TypeScript for props definition
- Follow the 'use client' directive when using hooks or browser APIs
- Have clear, focused responsibility

```typescript
// components/feature-table.tsx
'use client';

import { useFeatureQuery } from '../hooks/feature.query';

type FeatureTableProps = {
  searchParams: QueryParams;
};

export function FeatureTable({ searchParams }: FeatureTableProps) {
  const { data, isLoading, isError } = featureQuery.query.useList(searchParams);

  if (isLoading) return <LoadingSkeleton />;
  if (isError) return <ErrorDisplay />;

  return (
    <Table data={data.data} columns={columns} />
  );
}
```

### 5. Page Component Structure

Page components should:
- Import from feature modules
- Handle routing/URL params
- Use containers for data fetching when necessary

```typescript
// app/(home)/features/page.tsx
export default function Page({ searchParams }) {
  // Process search params
  const processedParams = withPaginationDefaults(searchParams);

  return (
    <PageContainer>
      <PageHeader title="Feature" description="Feature Description" />
      <FeatureFilters defaultValues={processedParams} />
      <FeatureTable searchParams={processedParams} />
    </PageContainer>
  );
}
```

## Specific Patterns

### Data Fetching

1. Use React Query for client-side data fetching
2. Create typed hooks for all API interactions
3. Handle loading, error, and success states
4. Use placeholderData for smoother pagination

### State Management

1. Keep state as local as possible
2. Use context only when state needs to be shared across many components
3. Prefer React Query for server state
4. Use Zustand for global client state that isn't server data

### Type Safety

1. Always use TypeScript interfaces or types for component props
2. Generate API types from OpenAPI specs
3. Use discriminated unions for complex state
4. Avoid `any` type

## Code Style Guidelines

1. Use functional components with hooks
2. Use the Container/Presenter pattern for complex components
3. Use named exports rather than default exports
4. Add 'use client' directive at the top of client components
5. Follow ESLint and Prettier configurations

By following these guidelines, we maintain a consistent, maintainable, and scalable frontend architecture.
