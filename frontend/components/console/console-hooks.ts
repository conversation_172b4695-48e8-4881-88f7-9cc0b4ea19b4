import { useEffect, useMemo, useState } from 'react';

import { ToolCall as OriginalToolCall } from '@/components/chat/types';

import {
  ProcessedToolCall,
  calculateProgressFromMouse,
  processScriptToolCalls,
  scrollToBottom,
} from './console-utils';

/**
 * Hook for responsive line length calculation
 */
export const useResponsiveLineLength = (
  containerRef: React.RefObject<HTMLDivElement | null>,
) => {
  const [lineLength, setLineLength] = useState(80);

  useEffect(() => {
    const calculateLineLength = () => {
      if (containerRef.current) {
        const containerWidth = containerRef.current.offsetWidth;
        // Estimate characters per line based on container width
        // Assuming monospace font with ~8px character width and accounting for padding
        const estimatedCharsPerLine = Math.floor((containerWidth - 32) / 8); // 32px for padding
        setLineLength(Math.max(40, Math.min(120, estimatedCharsPerLine))); // Min 40, max 120
      }
    };

    calculateLineLength();
    window.addEventListener('resize', calculateLineLength);

    return () => window.removeEventListener('resize', calculateLineLength);
  }, [containerRef]);

  return lineLength;
};

/**
 * Hook for console state management and navigation
 */
export const useConsoleState = (
  toolCalls: OriginalToolCall[],
  scrollAreaRef: React.RefObject<HTMLDivElement | null>,
) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isDragging, setIsDragging] = useState(false);

  // Process and filter tool calls
  const scriptToolCalls = useMemo(
    () => processScriptToolCalls(toolCalls),
    [toolCalls],
  );

  // Handle auto-scroll and index updates
  useEffect(() => {
    // Jump to latest when new tool calls are added
    if (scriptToolCalls.length > 0) {
      setCurrentIndex(scriptToolCalls.length - 1);
    }

    // Auto-scroll to bottom for any content changes
    scrollToBottom(scrollAreaRef);
  }, [scriptToolCalls.length, scrollAreaRef]);

  const currentToolCall = scriptToolCalls[currentIndex];

  return {
    currentIndex,
    setCurrentIndex,
    isDragging,
    setIsDragging,
    scriptToolCalls,
    currentToolCall,
  };
};

/**
 * Hook for progress bar drag functionality
 */
export const useProgressBarDrag = (
  isDragging: boolean,
  setIsDragging: (dragging: boolean) => void,
  setCurrentIndex: (index: number) => void,
  progressBarRef: React.RefObject<HTMLDivElement | null>,
  scriptToolCalls: ProcessedToolCall[],
) => {
  const handleProgressBarClick = (event: React.MouseEvent<HTMLDivElement>) => {
    const newIndex = calculateProgressFromMouse(
      event.clientX,
      progressBarRef,
      scriptToolCalls.length,
    );
    if (newIndex !== null) {
      setCurrentIndex(newIndex);
    }
  };

  const handleMouseDown = () => setIsDragging(true);
  const handleMouseUp = () => setIsDragging(false);

  const handleMouseMove = (event: MouseEvent) => {
    if (!isDragging) return;
    const newIndex = calculateProgressFromMouse(
      event.clientX,
      progressBarRef,
      scriptToolCalls.length,
    );
    if (newIndex !== null) setCurrentIndex(newIndex);
  };

  // Add global mouse event listeners for dragging
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, progressBarRef, scriptToolCalls.length, setCurrentIndex]);

  return {
    handleProgressBarClick,
    handleMouseDown,
    handleMouseUp,
  };
};
