import React, { memo } from 'react';

import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { ChevronDown, ChevronLeft, ChevronRight } from 'lucide-react';

interface EmptyStateProps {
  containerRef: React.RefObject<HTMLDivElement | null>;
}

export const EmptyState = memo(({ containerRef }: EmptyStateProps) => (
  <div
    ref={containerRef}
    className="bg-secondary flex h-full items-center justify-center rounded-lg font-mono text-sm text-green-400"
  >
    <div className="space-y-2 text-center">
      <div className="text-gray-500">No bash scripts executed</div>
      <div className="flex items-center gap-1">
        <span className="text-green-400">sandbox$</span>
        <span className="animate-pulse">_</span>
      </div>
    </div>
  </div>
));

EmptyState.displayName = 'EmptyState';

interface NavigationControlsProps {
  currentIndex: number;
  totalItems: number;
  onPrevious: () => void;
  onNext: () => void;
}

export const NavigationControls = memo(
  ({
    currentIndex,
    totalItems,
    onPrevious,
    onNext,
  }: NavigationControlsProps) => (
    <div className="flex items-center gap-1">
      <span className="text-muted-foreground">
        {currentIndex + 1}/{totalItems}
      </span>
      <Button
        variant="ghost"
        size="sm"
        onClick={onPrevious}
        disabled={currentIndex === 0}
        className="text-muted-foreground hover:text-foreground size-6 p-1 disabled:opacity-30"
      >
        <ChevronLeft className="size-4" />
      </Button>
      <Button
        variant="ghost"
        size="sm"
        onClick={onNext}
        disabled={currentIndex === totalItems - 1}
        className="text-muted-foreground hover:text-foreground size-6 p-1 disabled:opacity-30"
      >
        <ChevronRight className="size-4" />
      </Button>
    </div>
  ),
);

NavigationControls.displayName = 'NavigationControls';

interface ProgressBarProps {
  currentIndex: number;
  totalItems: number;
  progressBarRef: React.RefObject<HTMLDivElement | null>;
  onClick: (event: React.MouseEvent<HTMLDivElement>) => void;
  onMouseDown: () => void;
}

export const ProgressBar = memo(
  ({
    currentIndex,
    totalItems,
    progressBarRef,
    onClick,
    onMouseDown,
  }: ProgressBarProps) => (
    <div className="mx-4 flex-1">
      <div
        ref={progressBarRef}
        className="bg-muted/30 relative h-1 cursor-pointer rounded-full"
        onClick={onClick}
      >
        <div
          className="bg-primary h-1 rounded-full"
          style={{
            width: `${totalItems > 1 ? (currentIndex / (totalItems - 1)) * 100 : 100}%`,
          }}
        ></div>
        {/* Draggable dot */}
        <div
          className="bg-primary border-background absolute top-1/2 -translate-x-1/2 -translate-y-1/2 cursor-grab rounded-full border-2 active:cursor-grabbing"
          style={{
            left: `${totalItems > 1 ? (currentIndex / (totalItems - 1)) * 100 : 100}%`,
            width: '16px',
            height: '16px',
          }}
          onMouseDown={onMouseDown}
        ></div>
      </div>
    </div>
  ),
);

ProgressBar.displayName = 'ProgressBar';

export const LiveIndicator = memo(() => (
  <div className="flex items-center gap-2">
    <div className="bg-success h-2 w-2 rounded-full"></div>
    <span className="text-success font-medium">live</span>
  </div>
));

LiveIndicator.displayName = 'LiveIndicator';

interface UnifiedStatusBarProps {
  currentIndex: number;
  totalItems: number;
  progressBarRef: React.RefObject<HTMLDivElement | null>;
  onPrevious: () => void;
  onNext: () => void;
  onProgressBarClick: (event: React.MouseEvent<HTMLDivElement>) => void;
  onMouseDown: () => void;
  reasoning?: string;
}

export const UnifiedStatusBar = memo(
  ({
    currentIndex,
    totalItems,
    progressBarRef,
    onPrevious,
    onNext,
    onProgressBarClick,
    onMouseDown,
    reasoning,
  }: UnifiedStatusBarProps) => (
    <div className="bg-primary/5 rounded-lg">
      {/* Top row: Progress controls */}
      <div className="border-border/20 flex items-center border px-3 py-2 text-xs">
        <NavigationControls
          currentIndex={currentIndex}
          totalItems={totalItems}
          onPrevious={onPrevious}
          onNext={onNext}
        />

        <ProgressBar
          currentIndex={currentIndex}
          totalItems={totalItems}
          progressBarRef={progressBarRef}
          onClick={onProgressBarClick}
          onMouseDown={onMouseDown}
        />

        <LiveIndicator />
      </div>

      {/* Bottom row: Reasoning */}
      <div className="px-3 py-2">
        <div className="text-muted-foreground max-h-12 overflow-hidden text-xs leading-relaxed break-words">
          {reasoning || (
            <span className="italic opacity-50">No reasoning available</span>
          )}
        </div>
      </div>
    </div>
  ),
);

UnifiedStatusBar.displayName = 'UnifiedStatusBar';

// Legacy components for backward compatibility
interface StatusBarProps {
  currentIndex: number;
  totalItems: number;
  progressBarRef: React.RefObject<HTMLDivElement | null>;
  onPrevious: () => void;
  onNext: () => void;
  onProgressBarClick: (event: React.MouseEvent<HTMLDivElement>) => void;
  onMouseDown: () => void;
}

export const StatusBar = memo(
  ({
    currentIndex,
    totalItems,
    progressBarRef,
    onPrevious,
    onNext,
    onProgressBarClick,
    onMouseDown,
  }: StatusBarProps) => (
    <div className="bg-primary/5 rounded-lg">
      <div className="flex items-center px-3 py-2 text-xs">
        {/* Left side: Navigation controls */}
        <NavigationControls
          currentIndex={currentIndex}
          totalItems={totalItems}
          onPrevious={onPrevious}
          onNext={onNext}
        />

        {/* Center: Progress bar */}
        <ProgressBar
          currentIndex={currentIndex}
          totalItems={totalItems}
          progressBarRef={progressBarRef}
          onClick={onProgressBarClick}
          onMouseDown={onMouseDown}
        />

        {/* Right side: Live indicator */}
        <LiveIndicator />
      </div>
    </div>
  ),
);

StatusBar.displayName = 'StatusBar';

interface ReasoningPanelProps {
  reasoning?: string;
}

export const ReasoningPanel = memo(({ reasoning }: ReasoningPanelProps) => (
  <div className="border-border bg-muted/30 border-none">
    <div className="h-14 overflow-hidden p-3">
      <ScrollArea className="h-16">
        <div className="text-muted-foreground pr-2 text-xs leading-relaxed break-words">
          {reasoning || (
            <span className="italic opacity-50">No reasoning available</span>
          )}
        </div>
      </ScrollArea>
    </div>
  </div>
));

ReasoningPanel.displayName = 'ReasoningPanel';

interface JumpToLiveButtonProps {
  currentIndex: number;
  totalItems: number;
  onJumpToLive: () => void;
}

export const JumpToLiveButton = memo(
  ({ currentIndex, totalItems, onJumpToLive }: JumpToLiveButtonProps) => {
    if (currentIndex >= totalItems - 1) return null;

    return (
      <Button
        type="button"
        variant="default"
        size="sm"
        className="bg-primary hover:bg-primary/90 text-primary-foreground absolute bottom-23 left-1/2 z-10 -translate-x-1/2 max-md:w-full"
        onClick={onJumpToLive}
      >
        <ChevronDown className="mr-2 size-4" />
        Jump to Live
      </Button>
    );
  },
);

JumpToLiveButton.displayName = 'JumpToLiveButton';

interface ConsoleContentProps {
  scrollAreaRef: React.RefObject<HTMLDivElement | null>;
  children: React.ReactNode;
}

export const ConsoleContent = memo(
  ({ scrollAreaRef, children }: ConsoleContentProps) => (
    <ScrollArea ref={scrollAreaRef} className="flex-1 p-4 py-2">
      <div className="space-y-0 pb-16">{children}</div>
    </ScrollArea>
  ),
);

ConsoleContent.displayName = 'ConsoleContent';

export const ProcessingIndicator = memo(() => (
  <div className="flex items-center gap-1">
    <span className="animate-pulse text-yellow-400">Processing...</span>
  </div>
));

ProcessingIndicator.displayName = 'ProcessingIndicator';
