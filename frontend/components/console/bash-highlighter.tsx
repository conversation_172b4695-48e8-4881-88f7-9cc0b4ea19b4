import React from 'react';

/**
 * Bash syntax highlighting utility for terminal commands
 * Tokenizes bash commands and applies appropriate styling
 */

interface BashToken {
  type:
    | 'command'
    | 'option'
    | 'argument'
    | 'string'
    | 'variable'
    | 'operator'
    | 'comment'
    | 'pipe'
    | 'redirect'
    | 'number'
    | 'path';
  value: string;
  className: string;
}

/**
 * Tokenize a bash command line into syntax-highlighted parts
 */
export const tokenizeBashCommand = (command: string): BashToken[] => {
  const tokens: BashToken[] = [];

  // Preserve leading whitespace (tabs/spaces) for indentation
  const leadingWhitespace = command.match(/^(\s*)/)?.[1] || '';
  const trimmedCommand = command.trim();

  if (!trimmedCommand) return tokens;

  // Add leading whitespace as a separate token if it exists
  if (leadingWhitespace) {
    tokens.push({
      type: 'argument', // Use a neutral type that doesn't affect styling
      value: leadingWhitespace,
      className: '', // No special styling for whitespace
    });
  }

  // Handle comments
  if (trimmedCommand.startsWith('#')) {
    // For comments, include the original command with leading whitespace preserved
    tokens.push({
      type: 'comment',
      value: command, // Use original command instead of trimmed to preserve indentation
      className: 'text-gray-500 italic',
    });
    return tokens;
  }

  // Split command while preserving quoted strings
  const parts = splitBashCommand(trimmedCommand);
  let isFirstToken = true;

  for (let i = 0; i < parts.length; i++) {
    const part = parts[i];

    if (!part.trim()) {
      // Preserve whitespace
      tokens.push({
        type: 'argument',
        value: part,
        className: '',
      });
      continue;
    }

    // Variables ($VAR, ${VAR})
    if (part.match(/^\$\{?[\w_]+\}?$/)) {
      tokens.push({
        type: 'variable',
        value: part,
        className: 'text-yellow-400',
      });
    }
    // Quoted strings
    else if (part.match(/^["'].*["']$/)) {
      tokens.push({
        type: 'string',
        value: part,
        className: 'text-green-300',
      });
    }
    // Options (starts with -)
    else if (part.match(/^-{1,2}[\w-]+$/)) {
      tokens.push({
        type: 'option',
        value: part,
        className: 'text-blue-400',
      });
    }
    // Pipes and redirects
    else if (
      ['|', '||', '&&', '>', '>>', '<', '<<', '2>', '&>', '2>&1'].includes(part)
    ) {
      tokens.push({
        type: 'pipe',
        value: part,
        className: 'text-purple-400 font-bold',
      });
    }
    // Operators
    else if (
      ['=', '==', '!=', '-eq', '-ne', '-lt', '-gt', '-le', '-ge'].includes(part)
    ) {
      tokens.push({
        type: 'operator',
        value: part,
        className: 'text-purple-400',
      });
    }
    // Numbers
    else if (part.match(/^\d+$/)) {
      tokens.push({
        type: 'number',
        value: part,
        className: 'text-cyan-400',
      });
    }
    // Paths (contains / or ~)
    else if (part.match(/[/~]/)) {
      tokens.push({
        type: 'path',
        value: part,
        className: 'text-cyan-300',
      });
    }
    // First token is typically the command
    else if (isFirstToken) {
      tokens.push({
        type: 'command',
        value: part,
        className: 'text-white font-semibold',
      });
      isFirstToken = false;
    }
    // Everything else is an argument
    else {
      tokens.push({
        type: 'argument',
        value: part,
        className: 'text-gray-300',
      });
    }
  }

  return tokens;
};

/**
 * Split bash command while preserving quoted strings and special characters
 */
function splitBashCommand(command: string): string[] {
  const parts: string[] = [];
  let current = '';
  let inQuotes = false;
  let quoteChar = '';
  let i = 0;

  while (i < command.length) {
    const char = command[i];
    const nextChar = command[i + 1];

    // Handle quotes
    if ((char === '"' || char === "'") && !inQuotes) {
      inQuotes = true;
      quoteChar = char;
      current += char;
    } else if (char === quoteChar && inQuotes) {
      inQuotes = false;
      quoteChar = '';
      current += char;
    }
    // Handle escaped characters
    else if (char === '\\' && nextChar) {
      current += char + nextChar;
      i++; // Skip next character
    }
    // Handle multi-character operators
    else if (
      !inQuotes &&
      ((char === '|' && nextChar === '|') ||
        (char === '&' && nextChar === '&') ||
        (char === '>' && nextChar === '>') ||
        (char === '<' && nextChar === '<') ||
        (char === '2' && nextChar === '>') ||
        (char === '&' && nextChar === '>'))
    ) {
      if (current.trim()) {
        parts.push(current);
        current = '';
      }
      parts.push(char + nextChar);
      i++; // Skip next character
    }
    // Handle single-character operators and pipes
    else if (!inQuotes && ['|', '>', '<', '&', ';'].includes(char)) {
      if (current.trim()) {
        parts.push(current);
        current = '';
      }
      parts.push(char);
    }
    // Handle whitespace
    else if (!inQuotes && /\s/.test(char)) {
      if (current.trim()) {
        parts.push(current);
        current = '';
      }
      // Add whitespace as separate token to preserve formatting
      let whitespace = char;
      while (i + 1 < command.length && /\s/.test(command[i + 1])) {
        i++;
        whitespace += command[i];
      }
      parts.push(whitespace);
    }
    // Regular characters
    else {
      current += char;
    }

    i++;
  }

  if (current) {
    parts.push(current);
  }

  return parts;
}

/**
 * React component to render highlighted bash command
 */
interface HighlightedBashCommandProps {
  command: string;
  className?: string;
}

export const HighlightedBashCommand: React.FC<HighlightedBashCommandProps> = ({
  command,
  className = '',
}) => {
  const tokens = tokenizeBashCommand(command);

  return (
    <span className={className}>
      {tokens.map((token, index) => (
        <span key={index} className={token.className}>
          {token.value}
        </span>
      ))}
    </span>
  );
};

/**
 * JSON syntax highlighting component
 */
interface HighlightedJSONProps {
  text: string;
  className?: string;
}

export const HighlightedJSON: React.FC<HighlightedJSONProps> = ({
  text,
  className = '',
}) => {
  // Simple JSON syntax highlighting
  const highlightJSON = (jsonText: string) => {
    return jsonText
      .replace(/("[\w_-]+")\s*:/g, '<span class="text-blue-300">$1</span>:') // Keys
      .replace(/:\s*(".*?")/g, ': <span class="text-green-300">$1</span>') // String values
      .replace(/:\s*(\d+\.?\d*)/g, ': <span class="text-yellow-400">$1</span>') // Numbers
      .replace(
        /:\s*(true|false|null)/g,
        ': <span class="text-purple-400">$1</span>',
      ) // Booleans/null
      .replace(/([{}[\]])/g, '<span class="text-gray-400">$1</span>') // Brackets
      .replace(
        /(===.*===|---.*---)/g,
        '<span class="text-cyan-400 font-bold">$1</span>',
      ); // Headers
  };

  return (
    <span
      className={`font-mono ${className}`}
      dangerouslySetInnerHTML={{ __html: highlightJSON(text) }}
    />
  );
};

/**
 * Error highlighting component for displaying errors in red
 */
interface HighlightedErrorProps {
  text: string;
  className?: string;
}

export const HighlightedError: React.FC<HighlightedErrorProps> = ({
  text,
  className = '',
}) => {
  // Format error text with highlighting
  const highlightError = (errorText: string) => {
    // IMPORTANT: Highlight numbers first to avoid matching digits inside injected class names (e.g., text-red-400)
    return errorText
      .replace(/(\b\d{3}\b)/g, '<span class="text-yellow-400">$1</span>') // HTTP status codes
      .replace(
        /(stderr:|error:|Error:|ERROR:)/gi,
        '<span class="text-red-400 font-bold">$1</span>',
      ) // Error labels
      .replace(
        /(timeout|timed out)/gi,
        '<span class="text-orange-400 font-bold">$1</span>',
      ) // Timeouts
      .replace(
        /(failed|failure|fail)/gi,
        '<span class="text-red-400 font-bold">$1</span>',
      ) // Failures
      .replace(/(\d+ seconds?)/g, '<span class="text-yellow-400">$1</span>') // Time durations
      .replace(/(API request)/gi, '<span class="text-blue-300">$1</span>'); // API mentions
  };

  return (
    <div className={`font-mono ${className}`}>
      <span
        className="text-red-300"
        dangerouslySetInnerHTML={{ __html: highlightError(text) }}
      />
    </div>
  );
};

/**
 * Utility function to check if a string contains bash-like syntax
 */
export const isBashCommand = (text: string): boolean => {
  const bashIndicators = [
    /^\s*[\w./]+/, // Starts with command-like pattern
    /\s+-[\w-]+/, // Contains options
    /[|&><]/, // Contains pipes or redirects
    /\$\w+/, // Contains variables
    /["'].*["']/, // Contains quoted strings
  ];

  return bashIndicators.some((pattern) => pattern.test(text));
};

/**
 * Utility function to detect if a string is JSON or contains JSON
 */
export const isJSONOutput = (text: string): boolean => {
  const trimmed = text.trim();

  // Try to parse the entire text as JSON first
  try {
    JSON.parse(trimmed);
    return true;
  } catch {
    // Continue to other checks
  }

  // Quick pattern checks for JSON-like structures
  const jsonIndicators = [
    /^\s*[\[\{]/, // Starts with [ or {
    /[\]\}]\s*$/, // Ends with ] or }
    /"\w+":\s*["\d\[\{]/, // Contains "key": value pattern
    /^\s*===.*===/m, // Contains section headers like "=== Instance Count by Region ==="
  ];

  // Check if it looks like JSON
  if (jsonIndicators.some((pattern) => pattern.test(trimmed))) {
    return true;
  }

  // Look for embedded JSON structures
  if (containsEmbeddedJSON(trimmed)) {
    return true;
  }

  return false;
};

/**
 * Check if text contains embedded JSON structures
 */
const containsEmbeddedJSON = (text: string): boolean => {
  // Look for JSON object or array patterns
  const jsonPatterns = [
    /\{[\s\S]*?"[^"]*":\s*[\s\S]*?\}/g, // JSON objects with key-value pairs
    /\[[\s\S]*?\{[\s\S]*?"[^"]*":\s*[\s\S]*?\}[\s\S]*?\]/g, // Arrays containing objects
  ];

  for (const pattern of jsonPatterns) {
    const matches = text.match(pattern);
    if (matches) {
      // Verify at least one match is valid JSON
      for (const match of matches) {
        try {
          JSON.parse(match);
          return true;
        } catch {
          // Continue checking other matches
        }
      }
    }
  }

  return false;
};

/**
 * Utility function to detect if output represents an error
 */
export const isErrorOutput = (output: any): boolean => {
  if (typeof output === 'string') {
    try {
      const parsed = JSON.parse(output);
      return parsed.return_code !== 0 || parsed.stderr || parsed.error;
    } catch {
      return false;
    }
  }

  if (typeof output === 'object' && output !== null) {
    return output.return_code !== 0 || output.stderr || output.error;
  }

  return false;
};

/**
 * Extract error information from tool output
 */
export const extractErrorInfo = (
  output: any,
): { hasError: boolean; errorText: string; returnCode?: number } => {
  let parsedOutput = output;

  if (typeof output === 'string') {
    try {
      parsedOutput = JSON.parse(output);
    } catch {
      return { hasError: false, errorText: '' };
    }
  }

  if (typeof parsedOutput === 'object' && parsedOutput !== null) {
    const hasError =
      parsedOutput.return_code !== 0 ||
      parsedOutput.stderr ||
      parsedOutput.error;

    if (hasError) {
      let errorText = '';

      // Only include stderr and error messages in errorText
      if (parsedOutput.stderr) {
        errorText += `${parsedOutput.stderr}`;
      }

      if (parsedOutput.error) {
        errorText += errorText ? `\n${parsedOutput.error}` : parsedOutput.error;
      }


      return {
        hasError: true,
        errorText: errorText || 'Unknown error occurred',
        returnCode: parsedOutput.return_code,
      };
    }
  }

  return { hasError: false, errorText: '' };
};

/**
 * Format JSON output with proper indentation and structure
 */
export const formatJSONOutput = (
  text: string,
  maxLineLength: number = 80,
): string[] => {
  const trimmed = text.trim();

  // First, try to extract and format pure JSON
  const jsonResult = extractAndFormatJSON(trimmed);
  if (jsonResult.length > 0) {
    return jsonResult;
  }

  // If not valid JSON, check for structured output with headers
  if (trimmed.includes('===') || trimmed.includes('---')) {
    return formatStructuredOutput(trimmed, maxLineLength);
  }

  // Fall back to line-by-line formatting
  return trimmed
    .split('\n')
    .map((line) => line.trim())
    .filter((line) => line);
};

/**
 * Extract JSON from mixed content and format it properly
 */
const extractAndFormatJSON = (text: string): string[] => {
  const trimmed = text.trim();

  // Try to parse the entire text as JSON first
  try {
    const parsed = JSON.parse(trimmed);
    const formatted = JSON.stringify(parsed, null, 2);
    return formatted.split('\n');
  } catch {
    // If that fails, look for JSON patterns within the text
  }

  // Process line by line to extract JSON structures

  const lines = trimmed.split('\n');
  const formattedLines: string[] = [];
  let currentJsonBlock = '';
  let inJsonBlock = false;
  let braceCount = 0;
  let bracketCount = 0;

  for (const line of lines) {
    const trimmedLine = line.trim();

    // Check if this line starts a JSON structure
    if (
      !inJsonBlock &&
      (trimmedLine.startsWith('{') || trimmedLine.startsWith('['))
    ) {
      inJsonBlock = true;
      currentJsonBlock = trimmedLine;
      braceCount =
        (trimmedLine.match(/\{/g) || []).length -
        (trimmedLine.match(/\}/g) || []).length;
      bracketCount =
        (trimmedLine.match(/\[/g) || []).length -
        (trimmedLine.match(/\]/g) || []).length;

      // If it's a complete JSON on one line, process it immediately
      if (braceCount === 0 && bracketCount === 0) {
        try {
          const parsed = JSON.parse(currentJsonBlock);
          const formatted = JSON.stringify(parsed, null, 2);
          formattedLines.push(...formatted.split('\n'));
        } catch {
          formattedLines.push(currentJsonBlock);
        }
        inJsonBlock = false;
        currentJsonBlock = '';
      }
    } else if (inJsonBlock) {
      // Continue building the JSON block
      currentJsonBlock += '\n' + trimmedLine;
      braceCount +=
        (trimmedLine.match(/\{/g) || []).length -
        (trimmedLine.match(/\}/g) || []).length;
      bracketCount +=
        (trimmedLine.match(/\[/g) || []).length -
        (trimmedLine.match(/\]/g) || []).length;

      // Check if we've completed the JSON structure
      if (braceCount === 0 && bracketCount === 0) {
        try {
          const parsed = JSON.parse(currentJsonBlock);
          const formatted = JSON.stringify(parsed, null, 2);
          formattedLines.push(...formatted.split('\n'));
        } catch {
          // If parsing fails, add the block as-is but with better indentation
          const indentedBlock = indentJSONLikeBlock(currentJsonBlock);
          formattedLines.push(...indentedBlock.split('\n'));
        }
        inJsonBlock = false;
        currentJsonBlock = '';
        braceCount = 0;
        bracketCount = 0;
      }
    } else {
      // Regular line - check if it contains inline JSON
      const inlineJson = extractInlineJSON(trimmedLine);
      if (inlineJson) {
        formattedLines.push(...inlineJson);
      } else {
        formattedLines.push(trimmedLine);
      }
    }
  }

  // Handle any remaining JSON block
  if (inJsonBlock && currentJsonBlock) {
    try {
      const parsed = JSON.parse(currentJsonBlock);
      const formatted = JSON.stringify(parsed, null, 2);
      formattedLines.push(...formatted.split('\n'));
    } catch {
      const indentedBlock = indentJSONLikeBlock(currentJsonBlock);
      formattedLines.push(...indentedBlock.split('\n'));
    }
  }

  return formattedLines.filter((line) => line !== null);
};

/**
 * Extract inline JSON from a line of text
 */
const extractInlineJSON = (line: string): string[] | null => {
  // Look for JSON patterns in the line
  const jsonMatches = line.match(/\{.*\}|\[.*\]/);
  if (jsonMatches) {
    for (const match of jsonMatches) {
      try {
        const parsed = JSON.parse(match);
        const formatted = JSON.stringify(parsed, null, 2);
        // Replace the JSON in the original line with formatted version
        return formatted.split('\n');
      } catch {
        // Continue to next match
      }
    }
  }
  return null;
};

/**
 * Indent JSON-like blocks even if they're not valid JSON
 */
const indentJSONLikeBlock = (block: string): string => {
  const lines = block.split('\n');
  const indentedLines: string[] = [];
  let indentLevel = 0;

  for (const line of lines) {
    const trimmed = line.trim();

    if (!trimmed) {
      indentedLines.push('');
      continue;
    }

    // Decrease indent for closing brackets/braces
    if (trimmed.startsWith('}') || trimmed.startsWith(']')) {
      indentLevel = Math.max(0, indentLevel - 1);
    }

    // Add the line with proper indentation
    const indent = '  '.repeat(indentLevel);
    indentedLines.push(indent + trimmed);

    // Increase indent for opening brackets/braces
    if (trimmed.endsWith('{') || trimmed.endsWith('[')) {
      indentLevel++;
    }

    // Handle lines that both open and close (like "key": { ... })
    const openCount = (trimmed.match(/[\{\[]/g) || []).length;
    const closeCount = (trimmed.match(/[\}\]]/g) || []).length;
    indentLevel += openCount - closeCount;
    indentLevel = Math.max(0, indentLevel);
  }

  return indentedLines.join('\n');
};

/**
 * Format structured output (like AWS CLI output with headers)
 */
const formatStructuredOutput = (
  text: string,
  maxLineLength: number,
): string[] => {
  const lines = text.split('\n');
  const formatted: string[] = [];

  for (const line of lines) {
    const trimmed = line.trim();

    // Empty lines
    if (!trimmed) {
      formatted.push('');
      continue;
    }

    // Headers (=== or ---)
    if (trimmed.startsWith('===') || trimmed.startsWith('---')) {
      formatted.push(trimmed);
      continue;
    }

    // Try to parse individual lines as JSON
    if (trimmed.startsWith('[') || trimmed.startsWith('{')) {
      try {
        const parsed = JSON.parse(trimmed);
        const jsonFormatted = JSON.stringify(parsed, null, 2);
        formatted.push(...jsonFormatted.split('\n'));
        continue;
      } catch {
        // Fall through to regular formatting
      }
    }

    // Regular line formatting
    if (trimmed.length <= maxLineLength) {
      formatted.push(trimmed);
    } else {
      // Break long lines
      const words = trimmed.split(' ');
      let currentLine = '';

      for (const word of words) {
        const testLine = currentLine ? `${currentLine} ${word}` : word;

        if (testLine.length <= maxLineLength) {
          currentLine = testLine;
        } else {
          if (currentLine) {
            formatted.push(currentLine);
            currentLine = word;
          } else {
            formatted.push(word);
          }
        }
      }

      if (currentLine) {
        formatted.push(currentLine);
      }
    }
  }

  return formatted;
};

/**
 * Enhanced tokenizer that handles more complex bash constructs
 */
export const tokenizeComplexBashCommand = (command: string): BashToken[] => {
  // Handle special cases first
  if (command.trim().startsWith('#')) {
    return [
      {
        type: 'comment',
        value: command,
        className: 'text-gray-500 italic',
      },
    ];
  }

  // Handle command substitution $(...)
  if (command.includes('$(') || command.includes('`')) {
    return tokenizeWithCommandSubstitution(command);
  }

  // Handle heredoc
  if (command.includes('<<')) {
    return tokenizeWithHeredoc(command);
  }

  // Fall back to simple tokenizer
  return tokenizeBashCommand(command);
};

function tokenizeWithCommandSubstitution(command: string): BashToken[] {
  const tokens: BashToken[] = [];
  let current = '';
  let inSubstitution = false;
  let parenCount = 0;
  let i = 0;

  while (i < command.length) {
    const char = command[i];
    const nextChar = command[i + 1];

    if (char === '$' && nextChar === '(' && !inSubstitution) {
      if (current) {
        tokens.push(...tokenizeBashCommand(current));
        current = '';
      }
      inSubstitution = true;
      parenCount = 1;
      current = '$(';
      i++; // Skip next char
    } else if (inSubstitution) {
      current += char;
      if (char === '(') parenCount++;
      if (char === ')') {
        parenCount--;
        if (parenCount === 0) {
          tokens.push({
            type: 'variable',
            value: current,
            className: 'text-orange-400 bg-gray-800 px-1 rounded',
          });
          current = '';
          inSubstitution = false;
        }
      }
    } else {
      current += char;
    }

    i++;
  }

  if (current) {
    tokens.push(...tokenizeBashCommand(current));
  }

  return tokens;
}

function tokenizeWithHeredoc(command: string): BashToken[] {
  // Simplified heredoc handling
  const parts = command.split('<<');
  const tokens: BashToken[] = [];

  if (parts.length > 1) {
    tokens.push(...tokenizeBashCommand(parts[0]));
    tokens.push({
      type: 'redirect',
      value: '<<',
      className: 'text-purple-400 font-bold',
    });
    tokens.push({
      type: 'string',
      value: parts[1],
      className: 'text-green-300',
    });
  } else {
    return tokenizeBashCommand(command);
  }

  return tokens;
}
