import React, { memo } from 'react';
import {
  HighlightedBashCommand,
  HighlightedError,
  HighlightedJSON,
} from './bash-highlighter';

// ===== RENDERING UI COMPONENTS =====

interface LineRenderConfig {
  keyPrefix: string;
  promptColor: string;
  showPrompt: boolean;
  renderContent: (line: string, index: number, hasPrompt: boolean) => React.ReactNode;
}

interface GenericLineRendererProps {
  lines: string[];
  config: LineRenderConfig;
}

export const GenericLineRenderer = memo(({ lines, config }: GenericLineRendererProps) => {
  const firstNonEmptyIndex = lines.findIndex((line) => line.trim() !== '');

  return (
    <>
      {lines.map((line, index) => {
        if (line.trim() === '') {
          return (
            <div
              key={`${config.keyPrefix}-${index}`}
              className={config.keyPrefix === 'output' ? 'h-4' : ''}>
            </div>
          );
        }

        const hasPrompt = config.showPrompt && index === firstNonEmptyIndex;

        return (
          <div key={`${config.keyPrefix}-${index}`} className="flex items-start gap-1">
            {hasPrompt && (
              <span className={`shrink-0 ${config.promptColor}`}>sandbox$</span>
            )}
            <div className="break-all whitespace-pre-wrap">
              {config.renderContent(line, index, hasPrompt)}
            </div>
          </div>
        );
      })}
    </>
  );
});

GenericLineRenderer.displayName = 'GenericLineRenderer';

interface CommandRendererProps {
  lines: string[];
}

export const CommandRenderer = memo(({ lines }: CommandRendererProps) => (
  <GenericLineRenderer
    lines={lines}
    config={{
      keyPrefix: 'command',
      promptColor: 'text-green-400',
      showPrompt: true,
      renderContent: (line) => (
        <HighlightedBashCommand command={line} className="font-mono" />
      ),
    }}
  />
));

CommandRenderer.displayName = 'CommandRenderer';

interface OutputRendererProps {
  lines: string[];
  isJSON?: boolean;
  isError?: boolean;
  errorInfo?: {
    hasError: boolean;
    errorText: string;
    returnCode?: number;
  } | null;
}

export const OutputRenderer = memo(({
  lines,
  isJSON = false,
  isError = false,
  errorInfo = null,
}: OutputRendererProps) => {
  // If error exists, show a single block with one prompt, stdout in green and error in red
  if (isError && errorInfo) {
    return (
      <div className="flex items-start gap-1">
        <span className="shrink-0 text-green-400">sandbox$</span>
        <div className="break-all whitespace-pre-wrap">
          {lines && lines.length > 0 && (
            <GenericLineRenderer
              lines={lines}
              config={{
                keyPrefix: 'output',
                promptColor: 'text-green-400',
                showPrompt: false,
                renderContent: (line) =>
                  isJSON ? (
                    <HighlightedJSON text={line} className="text-green-300" />
                  ) : (
                    <HighlightedBashCommand
                      command={line}
                      className="font-mono text-green-300"
                    />
                  ),
              }}
            />
          )}
          <HighlightedError
            text={errorInfo.errorText}
            className="mt-1"
          />
        </div>
      </div>
    );
  }

  // Normal output path
  return (
    <GenericLineRenderer
      lines={lines}
      config={{
        keyPrefix: 'output',
        promptColor: 'text-green-400',
        showPrompt: true,
        renderContent: (line, _index, hasPrompt) =>
          isJSON ? (
            <HighlightedJSON text={line} className="text-green-300" />
          ) : hasPrompt ? (
            <span className="font-mono text-green-300">{line}</span>
          ) : (
            <HighlightedBashCommand
              command={line}
              className="font-mono text-green-300"
            />
          ),
      }}
    />
  );
});

OutputRenderer.displayName = 'OutputRenderer';
