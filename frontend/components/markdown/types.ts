export interface MarkdownRendererProps {
  content: string;
  enableStreaming?: boolean;
  enableMentions?: boolean;
  enableMermaid?: boolean;
  isStreaming?: boolean;
  className?: string;
  theme?: 'default' | 'compact' | 'enhanced';
}

export interface MarkdownCodeBlockProps {
  className?: string;
  children: React.ReactNode;
  inline?: boolean;
  enableMermaid?: boolean;
  isStreaming?: boolean;
}

export interface MarkdownMermaidProps {
  content: string;
  isStreaming?: boolean;
  className?: string;
}

export interface MarkdownStreamingProps {
  content: string;
  isStreaming: boolean;
  revealDelay?: number;
  className?: string;
}

// Simplified theme type - just CSS classes for prose styling
export type MarkdownTheme = string;

export interface MentionHighlightResult {
  highlightedText: React.ReactNode;
  mentions: string[];
}

export interface StreamingState {
  visibleLength: number;
  isComplete: boolean;
}
