'use client';

import {
  ComponentType,
  type PropsWithChildren,
  createContext,
  useContext,
} from 'react';

import { useStep } from 'usehooks-ts';

type UseStepActions = ReturnType<typeof useStep>[1];

type StepContextValue = {
  currentStep: number;
  steps: number;
} & UseStepActions;

const StepContext = createContext<StepContextValue | undefined>(undefined);

const StepProvider = ({
  children,
  steps,
}: PropsWithChildren<{ steps: number }>) => {
  const [currentStep, helpers] = useStep(steps);

  const value = {
    currentStep,
    ...helpers,
    steps,
  };

  return <StepContext.Provider value={value}>{children}</StepContext.Provider>;
};

export const useStepContext = (): StepContextValue => {
  const context = useContext(StepContext);

  if (context === undefined) {
    throw new Error('useStepContext must be used within a StepProvider');
  }

  return context;
};

// Higher-Order Component (HOC)
export function withStepProvider<T extends object>(
  Component: ComponentType<T>,
  steps: number,
) {
  const WithStepProvider = (props: T) => (
    <StepProvider steps={steps}>
      <Component {...props} />
    </StepProvider>
  );

  // Set display name for debugging
  const displayName = Component.displayName || Component.name || 'Component';
  WithStepProvider.displayName = `withStepProvider(${displayName})`;

  return WithStepProvider;
}
