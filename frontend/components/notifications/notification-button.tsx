'use client';

import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

import { usePathname } from 'next/navigation';

import type { Alert } from '@/app/(home)/alerts/_components/types';
import { AlertsService } from '@/client/sdk.gen';
import type { NotificationType } from '@/client/types.gen';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import pathsConfig from '@/config/paths.config';
import { agentDetailUrl } from '@/features/agent/hooks/use-navigate-agent-detail';
import { useUserContext } from '@/features/user/provider/user-provider';
import { Notification, useNotifications } from '@/hooks/use-notifications';
import { formatFullDateTime, parseTimestamp } from '@/lib/date-utils';
import { cn } from '@/lib/utils';
import {
  AlertCircle,
  AlertOctagon,
  AlertOctagonIcon,
  AlertTriangle,
  Bell,
  BellIcon,
  Check,
  CheckCheck,
  CheckCircle2,
  Info,
  Loader2,
  MessageSquare,
} from 'lucide-react';
import pluralize from 'pluralize';
import { toast } from 'sonner';

// Get icon based on notification type
const getNotificationIcon = (type: NotificationType | undefined) => {
  switch (type) {
    case 'error':
      return <AlertCircle className="text-destructive h-3 w-3" />;
    case 'warning':
      return <AlertTriangle className="h-3 w-3 text-amber-500" />;
    case 'interrupt':
      return <MessageSquare className="text-primary h-3 w-3" />;
    case 'info':
      return <Info className="h-3 w-3 text-blue-500" />;
    default:
      return <Info className="h-3 w-3 text-blue-500" />;
  }
};

// Add alert severity icons
const getAlertSeverityIcon = (
  severity: 'critical' | 'high' | 'medium' | 'low',
) => {
  switch (severity) {
    case 'critical':
      return <AlertOctagon className="text-destructive h-3 w-3" />;
    case 'high':
      return <AlertTriangle className="h-3 w-3 text-amber-500" />;
    case 'medium':
      return <AlertCircle className="h-3 w-3 text-blue-500" />;
    case 'low':
      return <Info className="text-muted-foreground h-3 w-3" />;
    default:
      return <Info className="h-3 w-3" />;
  }
};

// Redesigned notification card component with better visual hierarchy
const NotificationCard = React.memo(
  ({
    notification,
    onMarkAsRead,
  }: {
    notification: Notification;
    onMarkAsRead: (id: string) => void;
  }) => {
    const isUnread = notification.status === 'unread';
    const isArchived = notification.status === 'archived';
    const { agentId } = useUserContext();

    const handleClick = useCallback(() => {
      if (isUnread) {
        onMarkAsRead(notification.id);
      }
      if (notification.notification_metadata?.conversation_id) {
        window.open(
          agentDetailUrl(agentId, {
            conversationId: notification.notification_metadata
              .conversation_id as string,
          }),
          '_blank',
        );
      }
    }, [
      agentId,
      isUnread,
      notification.id,
      notification.notification_metadata?.conversation_id,
      onMarkAsRead,
    ]);

    return (
      <div
        className={cn(
          'group flex cursor-pointer items-start gap-3 rounded-lg p-4 transition-all duration-200',
          'hover:bg-muted/60 border border-transparent hover:shadow-xs',
          isUnread
            ? 'border-primary/10 bg-primary/5'
            : 'hover:bg-muted/40 bg-transparent',
          isArchived && 'opacity-60',
        )}
        onClick={handleClick}
      >
        {/* Status indicator */}
        <div className="mt-1 shrink-0">
          {isUnread ? (
            <div className="bg-primary h-2 w-2 animate-pulse rounded-full" />
          ) : (
            <div className="bg-muted-foreground/30 h-2 w-2 rounded-full" />
          )}
        </div>

        {/* Content */}
        <div className="min-w-0 flex-1 space-y-1">
          <div className="flex items-start justify-between gap-3">
            <h4
              className={cn(
                'text-sm leading-tight',
                isUnread
                  ? 'text-foreground font-semibold'
                  : 'text-muted-foreground font-medium',
                isArchived && 'line-through',
              )}
            >
              {notification.title}
            </h4>
            <span className="text-muted-foreground/70 mt-0.5 shrink-0 text-xs font-medium">
              {formatFullDateTime(notification.created_at)}
            </span>
          </div>

          {/* Type indicator */}
          <div className="flex items-center gap-1.5">
            {getNotificationIcon(notification.type)}
            <span className="text-muted-foreground/60 text-xs capitalize">
              {notification.type || 'info'}
            </span>
          </div>
        </div>
      </div>
    );
  },
);

// Redesigned alert card component with better visual hierarchy
const AlertCard = ({ alert }: { alert: Alert }) => {
  const handleClick = useCallback(() => {
    window.open(pathsConfig.app.alertDetail(alert.id), '_blank');
  }, [alert.id]);

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-50/80 dark:bg-red-950/20 border-red-200/50 dark:border-red-800/30';
      case 'high':
        return 'bg-amber-50/80 dark:bg-amber-950/20 border-amber-200/50 dark:border-amber-800/30';
      case 'medium':
        return 'bg-blue-50/80 dark:bg-blue-950/20 border-blue-200/50 dark:border-blue-800/30';
      case 'low':
        return 'bg-gray-50/80 dark:bg-gray-950/20 border-gray-200/50 dark:border-gray-800/30';
      default:
        return 'bg-gray-50/80 dark:bg-gray-950/20 border-gray-200/50 dark:border-gray-800/30';
    }
  };

  const getStatusIndicator = () => {
    if (alert.status === 'triggered') {
      return (
        <div className="bg-destructive h-2 w-2 animate-pulse rounded-full" />
      );
    }
    return <CheckCircle2 className="h-3 w-3 text-emerald-500" />;
  };

  return (
    <div
      className={cn(
        'group flex cursor-pointer items-start gap-3 rounded-lg p-4 transition-all duration-200',
        'border hover:shadow-xs',
        alert.status === 'triggered'
          ? cn('hover:bg-opacity-90', getSeverityColor(alert.severity))
          : 'hover:bg-muted/40 border-transparent bg-transparent',
      )}
      onClick={handleClick}
    >
      {/* Status indicator */}
      <div className="mt-1 shrink-0">{getStatusIndicator()}</div>

      {/* Content */}
      <div className="min-w-0 flex-1 space-y-1">
        <div className="flex items-start justify-between gap-3">
          <h4
            className={cn(
              'line-clamp-2 text-sm leading-tight',
              alert.status === 'triggered'
                ? 'text-foreground font-semibold'
                : 'text-muted-foreground font-medium',
            )}
          >
            {alert.title}
          </h4>
          <span className="text-muted-foreground/70 mt-0.5 shrink-0 text-xs font-medium">
            {formatFullDateTime(alert.startTime)}
          </span>
        </div>

        {/* Severity indicator */}
        <div className="flex items-center gap-1.5">
          {getAlertSeverityIcon(alert.severity)}
          <span
            className={cn(
              'text-xs font-medium capitalize',
              alert.severity === 'critical'
                ? 'text-red-600 dark:text-red-400'
                : alert.severity === 'high'
                  ? 'text-amber-600 dark:text-amber-400'
                  : alert.severity === 'medium'
                    ? 'text-blue-600 dark:text-blue-400'
                    : 'text-muted-foreground/60',
            )}
          >
            {alert.severity} severity
          </span>
        </div>
      </div>
    </div>
  );
};

export function NotificationButton({
  side = 'right',
}: {
  side?: 'right' | 'bottom';
}) {
  const [open, setOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('notifications');
  const [markingAllAsRead, setMarkingAllAsRead] = useState(false);
  const [markingAllAcknowledged, setMarkingAllAcknowledged] = useState(false);
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  const {
    notifications,
    totalCount,
    unreadCount,
    loading,
    isFetching,
    markAsRead,
    markAllAsRead,
    loadMore,
    refetch,
    refetchAlerts,
    alerts,
    isAlertsLoading,
  } = useNotifications();

  // Reset active tab when popover closes
  useEffect(() => {
    if (!open) {
      setActiveTab('notifications');
    }
  }, [open]);

  // Get current pathname for detecting navigation
  const pathname = usePathname();

  // Refresh notifications when pathname changes
  useEffect(() => {
    if (refetch) {
      refetch();
    }
  }, [pathname, refetch]);

  // Refresh notifications when the page becomes visible again
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && refetch) {
        refetch();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [refetch]);

  // Handle mark all as read
  const handleMarkAllAsRead = useCallback(async () => {
    setMarkingAllAsRead(true);
    try {
      await markAllAsRead.mutateAsync();
      toast.success('All notifications marked as read');
    } catch {
      toast.error('Failed to mark notifications as read');
    } finally {
      setMarkingAllAsRead(false);
    }
  }, [markAllAsRead]);

  // Handle mark all alerts as acknowledged
  const handleMarkAllAcknowledged = useCallback(async () => {
    setMarkingAllAcknowledged(true);
    try {
      await AlertsService.markAllAlertsAcknowledged();
      toast.success('All alerts marked as acknowledged');
      // Refresh alerts after marking as acknowledged
      if (refetchAlerts) {
        refetchAlerts();
      }
    } catch {
      toast.error('Failed to mark alerts as acknowledged');
    } finally {
      setMarkingAllAcknowledged(false);
    }
  }, [refetchAlerts]);

  // Handle mark notification as read
  const handleMarkAsRead = useCallback(
    (notificationId: string) => {
      markAsRead.mutate(notificationId);
    },
    [markAsRead],
  );

  // Infinite scroll for notifications
  const loadMoreRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!loadMoreRef.current || activeTab !== 'notifications') return;

    const observer = new IntersectionObserver(
      (entries) => {
        if (
          entries[0].isIntersecting &&
          !loading &&
          !isFetching &&
          notifications.length < totalCount
        ) {
          loadMore();
        }
      },
      { threshold: 0.5 },
    );

    observer.observe(loadMoreRef.current);

    return () => observer.disconnect();
  }, [
    activeTab,
    loading,
    isFetching,
    notifications.length,
    totalCount,
    loadMore,
  ]);

  // Calculate badge count - only triggered alerts count toward badge
  const triggeredAlertsCount =
    alerts?.filter((alert) => alert.status === 'triggered').length || 0;
  const totalBadgeCount = unreadCount + triggeredAlertsCount;

  // Sort notifications: unread first, then by date (show ALL notifications)
  const sortedNotifications = useMemo(() => {
    return [...notifications].sort((a, b) => {
      // First sort by read status (unread first)
      if (a.status !== b.status) {
        return a.status === 'unread' ? -1 : 1;
      }
      // Then sort by date (newest first)
      const dateA = parseTimestamp(a.created_at);
      const dateB = parseTimestamp(b.created_at);
      return (dateB?.getTime() || 0) - (dateA?.getTime() || 0);
    });
  }, [notifications]);

  // Filter alerts to only show triggered ones (hide acknowledged/resolved)
  const visibleAlerts = useMemo(() => {
    return alerts?.filter((alert) => alert.status === 'triggered') || [];
  }, [alerts]);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button variant="ghost" size="icon" className="relative h-8 w-8">
          <Bell className="h-4 w-4" />
          {totalBadgeCount > 0 && (
            <Badge
              variant="destructive"
              className="absolute -top-1 -right-1 flex h-5 min-w-5 animate-pulse items-center justify-center rounded-full p-0.5 text-[10px] font-medium"
            >
              {totalBadgeCount > 99 ? '99+' : totalBadgeCount}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent
        side={side}
        align="start"
        className="h-[80dvh] w-[90dvw] rounded-lg p-0 md:w-2xl"
        sideOffset={8}
      >
        <div className="flex items-center justify-between border-b p-4">
          <h4 className="text-base font-semibold">Updates</h4>
          {totalBadgeCount > 0 && (
            <span className="text-muted-foreground text-sm">
              {pluralize('new', totalBadgeCount, true)}
            </span>
          )}
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <div className="px-2 pt-2">
            <TabsList className="w-full">
              <TabsTrigger value="notifications" className="flex-1 gap-1">
                <BellIcon className="size-4" />
                Notifications
                {unreadCount > 0 && (
                  <Badge variant="destructive" size="sm">
                    {unreadCount}
                  </Badge>
                )}
              </TabsTrigger>
              <TabsTrigger value="alerts" className="flex-1 gap-1">
                <AlertOctagonIcon className="size-4" />
                Alerts
                {triggeredAlertsCount > 0 && (
                  <Badge variant="destructive" size="sm">
                    {triggeredAlertsCount}
                  </Badge>
                )}
              </TabsTrigger>
            </TabsList>
          </div>

          {/* Mark all buttons - consistent padding */}
          <div className="px-2 py-2">
            {activeTab === 'notifications' && unreadCount > 0 && (
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-fit justify-end px-2 text-sm"
                onClick={handleMarkAllAsRead}
                disabled={markingAllAsRead}
              >
                {markingAllAsRead ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <CheckCheck className="mr-2 h-4 w-4" />
                )}
                Mark all as read
              </Button>
            )}

            {activeTab === 'alerts' && triggeredAlertsCount > 0 && (
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-fit justify-end px-2 text-sm"
                onClick={handleMarkAllAcknowledged}
                disabled={markingAllAcknowledged}
              >
                {markingAllAcknowledged ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Check className="mr-2 h-4 w-4" />
                )}
                Mark all as acknowledged
              </Button>
            )}
          </div>

          <TabsContent value="notifications" className="m-0">
            <ScrollArea
              ref={scrollAreaRef}
              className="max-h-[60vh] overflow-y-auto"
              scrollHideDelay={0}
            >
              <div className="px-2 pb-2">
                {loading && notifications.length === 0 ? (
                  <div className="flex items-center justify-center py-12">
                    <Loader2 className="text-muted-foreground h-5 w-5 animate-spin" />
                    <span className="text-muted-foreground ml-3 text-sm">
                      Loading...
                    </span>
                  </div>
                ) : sortedNotifications.length > 0 ? (
                  <div className="space-y-1">
                    {sortedNotifications.map((notification) => (
                      <NotificationCard
                        key={notification.id}
                        notification={notification}
                        onMarkAsRead={handleMarkAsRead}
                      />
                    ))}

                    {/* Infinite scroll trigger */}
                    {notifications.length < totalCount && (
                      <div
                        ref={loadMoreRef}
                        className="flex justify-center py-4"
                      >
                        {(loading || isFetching) && (
                          <div className="flex items-center">
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            <span className="text-muted-foreground text-sm">
                              Loading more...
                            </span>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="flex items-center justify-center py-12">
                    <span className="text-muted-foreground text-sm">
                      No notifications
                    </span>
                  </div>
                )}
              </div>
            </ScrollArea>
          </TabsContent>

          <TabsContent value="alerts" className="m-0">
            <ScrollArea
              className="max-h-[60vh] overflow-y-auto"
              scrollHideDelay={0}
            >
              <div className="px-2 pb-2">
                {isAlertsLoading ? (
                  <div className="flex items-center justify-center py-12">
                    <Loader2 className="text-muted-foreground h-5 w-5 animate-spin" />
                    <span className="text-muted-foreground ml-3 text-sm">
                      Loading...
                    </span>
                  </div>
                ) : visibleAlerts.length > 0 ? (
                  <div className="space-y-1">
                    {visibleAlerts.map((alert) => (
                      <AlertCard key={alert.id} alert={alert} />
                    ))}
                  </div>
                ) : (
                  <div className="flex items-center justify-center py-12">
                    <span className="text-muted-foreground text-sm">
                      No active alerts
                    </span>
                  </div>
                )}
              </div>
            </ScrollArea>
          </TabsContent>
        </Tabs>
      </PopoverContent>
    </Popover>
  );
}

NotificationCard.displayName = 'NotificationCard';
