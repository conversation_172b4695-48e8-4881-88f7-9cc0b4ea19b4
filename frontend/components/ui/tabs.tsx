'use client';

import * as React from 'react';

import { cn } from '@/lib/utils';
import { Tabs as TabsPrimitive } from 'radix-ui';

const Tabs = TabsPrimitive.Root;

const TabsList: React.FC<
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>
> = ({ className, ...props }) => (
  <TabsPrimitive.List
    className={cn(
      'bg-muted text-muted-foreground inline-flex h-9 w-fit items-center gap-2 rounded-lg p-1',
      className,
    )}
    {...props}
  />
);
TabsList.displayName = TabsPrimitive.List.displayName;

const TabsTrigger: React.FC<
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>
> = ({ className, ...props }) => (
  <TabsPrimitive.Trigger
    className={cn(
      // Layout and Alignment
      'inline-flex items-center justify-center whitespace-nowrap',

      // Shape and Spacing
      'rounded-lg px-3 py-1',

      // Typography
      'text-sm font-medium',

      // Transitions and Ring Offset Background
      'ring-offset-background transition-all',

      // Focus Styles
      'focus-visible:ring-ring focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-hidden',

      // Disabled State
      'disabled:pointer-events-none disabled:opacity-50',

      // Active State Styles
      'data-[state=active]:bg-primary data-[state=active]:text-primary-foreground shadow-primary/20 data-[state=active]:shadow-sm',

      // Hover Styles
      'hover:bg-accent hover:text-accent-foreground',

      // Disabled Styles

      className,
    )}
    {...props}
  />
);
TabsTrigger.displayName = TabsPrimitive.Trigger.displayName;

const TabsContent: React.FC<
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>
> = ({ className, ...props }) => (
  <TabsPrimitive.Content
    className={cn(
      'ring-offset-background focus-visible:ring-ring mt-2 focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-hidden',
      className,
    )}
    {...props}
  />
);
TabsContent.displayName = TabsPrimitive.Content.displayName;

export { Tabs, TabsList, TabsTrigger, TabsContent };
