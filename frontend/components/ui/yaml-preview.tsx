import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { FileTextIcon } from 'lucide-react';

type YamlPreviewProps = {
  content: string;
  title?: string;
  className?: string;
};

export function YamlPreview({
  content,
  title = 'YAML Configuration',
  className,
}: YamlPreviewProps) {
  // const formatAsYaml = (obj: unknown, indent = 0): string => {
  //   const spaces = '  '.repeat(indent);

  //   if (obj === null || obj === undefined) {
  //     return 'null';
  //   }

  //   if (typeof obj === 'boolean' || typeof obj === 'number') {
  //     return String(obj);
  //   }

  //   if (typeof obj === 'string') {
  //     // Quote strings that contain special characters or start with special chars
  //     if (
  //       obj.includes(':') ||
  //       obj.includes('#') ||
  //       obj.includes('\n') ||
  //       obj.match(/^[0-9]/)
  //     ) {
  //       return `"${obj}"`;
  //     }
  //     return obj;
  //   }

  //   if (Array.isArray(obj)) {
  //     if (obj.length === 0) {
  //       return '[]';
  //     }
  //     return obj
  //       .map(
  //         (item) =>
  //           `${spaces}- ${formatAsYaml(item, indent + 1).replace(/^\s+/, '')}`,
  //       )
  //       .join('\n');
  //   }

  //   if (typeof obj === 'object') {
  //     const entries = Object.entries(obj);
  //     if (entries.length === 0) {
  //       return '{}';
  //     }

  //     return entries
  //       .map(([key, value]) => {
  //         const formattedValue = formatAsYaml(value, indent + 1);
  //         if (
  //           typeof value === 'object' &&
  //           value !== null &&
  //           !Array.isArray(value) &&
  //           Object.keys(value).length > 0
  //         ) {
  //           return `${spaces}${key}:\n${formattedValue}`;
  //         }
  //         if (Array.isArray(value) && value.length > 0) {
  //           return `${spaces}${key}:\n${formattedValue}`;
  //         }
  //         return `${spaces}${key}: ${formattedValue}`;
  //       })
  //       .join('\n');
  //   }

  //   return String(obj);
  // };

  return (
    <Card className={cn('w-full', className)}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-base">
          <FileTextIcon className="size-4" />
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <code className="bg-muted block rounded-md p-4 font-mono text-sm break-words whitespace-pre-wrap">
          {content}
        </code>
      </CardContent>
    </Card>
  );
}
