import { cn } from '@/lib/utils';
import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
} from 'lucide-react';
import pluralize from 'pluralize';

import { Badge } from '../badge';
import { Button, ButtonProps } from '../button';
import { generatePages } from './generate-pages';

type Props = {
  currentPage: number;
  totalPages: number;
  onPageChange: (pageIndex: number) => void;
  total: number;
  unit: string;
};

export function Pagination({
  currentPage,
  totalPages,
  onPageChange,
  total,
  unit,
}: Props) {
  const defaultVariant: ButtonProps['variant'] = 'ghost';
  const items = generatePages(currentPage, totalPages, 1);

  return (
    <div className="relative flex items-center justify-center space-x-4 py-2">
      <div className="absolute left-0 flex h-full items-center">
        <Badge variant="ghost-primary" className="text-sm">
          {pluralize(unit, total, true)}
        </Badge>
      </div>
      <div className="flex items-center space-x-1">
        <Button
          size={'icon'}
          variant={defaultVariant}
          onClick={() => onPageChange(1)}
          disabled={currentPage === 1}
        >
          <ChevronsLeft className="size-4" />
        </Button>

        <Button
          size={'icon'}
          variant={defaultVariant}
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage === 1}
        >
          <ChevronLeft className="size-4" />
        </Button>

        <p className="mx-2 text-sm font-medium md:hidden">
          {currentPage} / {totalPages}
        </p>

        <div className="hidden items-center gap-1 md:flex">
          {items.map((pageNumber, index) => {
            if (typeof pageNumber === 'string') {
              return (
                <Button
                  key={`${pageNumber}-${index}`}
                  size="sm"
                  variant={'ghost'}
                  className={cn(
                    'aspect-square rounded-xs',
                    'pointer-events-none',
                  )}
                >
                  {pageNumber}
                </Button>
              );
            }

            return (
              <Button
                key={pageNumber}
                variant={defaultVariant}
                className={cn('size-9 rounded-lg', {
                  'border-primary pointer-events-none border':
                    pageNumber === currentPage,
                })}
                onClick={() => {
                  onPageChange(pageNumber);
                }}
              >
                {pageNumber}
              </Button>
            );
          })}
        </div>

        <Button
          size={'icon'}
          variant={defaultVariant}
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
        >
          <ChevronRight className="size-4" />
        </Button>

        <Button
          size={'icon'}
          variant={defaultVariant}
          onClick={() => onPageChange(totalPages)}
          disabled={currentPage === totalPages}
        >
          <ChevronsRight className="size-4" />
        </Button>
      </div>
    </div>
  );
}
