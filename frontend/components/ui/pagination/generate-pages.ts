import { range } from 'lodash';

const ELLIPSES = '...';

/**
 * Generates a list of page numbers for pagination, including ellipses (`...`) if necessary.
 * @param current - The current active page.
 * @param total - The total number of pages.
 * @param siblings - The number of pages to display on each side of the current page.
 * @returns An array of page numbers, possibly including `...` as placeholders.
 */
export const generatePages = (
  current: number,
  total: number,
  siblings: number,
) => {
  const totalNumbers = siblings * 2 + 3;
  const totalBlocks = totalNumbers + 2;

  if (total <= totalBlocks) {
    return range(1, total + 1);
  }

  const startPage = Math.max(current - siblings, 1);
  const endPage = Math.min(current + siblings, total);

  const hasLeftEllipsis = startPage > 2;
  const hasRightEllipsis = endPage < total - 1;

  // Use concatenation and spread operator instead of push
  const leftPages = hasLeftEllipsis ? [1, ELLIPSES] : range(1, startPage);

  const middlePages = range(startPage, endPage + 1);

  const rightPages = hasRightEllipsis
    ? [ELLIPSES, total]
    : range(endPage + 1, total + 1);

  // Combine all sections
  return [...leftPages, ...middlePages, ...rightPages];
};
