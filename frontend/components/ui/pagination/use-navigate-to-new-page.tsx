import { useCallback } from 'react';

import { useRouter } from 'next/navigation';

/**
 * Navigates to a new page using the provided page index and optional page parameter.
 */
export function useNavigateToNewPage(
  props: { pageParam?: string } = {
    pageParam: 'page',
  },
) {
  const router = useRouter();
  const param = props.pageParam ?? 'page';

  return useCallback(
    (pageIndex: number) => {
      const url = new URL(window.location.href);
      url.searchParams.set(param, String(pageIndex + 1));
      // Use replace to avoid adding to browser history and disable scroll
      router.replace(url.pathname + url.search, { scroll: false });
    },
    [param, router],
  );
}
