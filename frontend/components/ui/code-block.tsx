import { useEffect, useState } from 'react';

import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { Check, Copy } from 'lucide-react';
import { type Highlighter, createHighlighter } from 'shiki';

interface CodeBlockProps {
  code: string;
  language?: string;
  className?: string;
  showCopyButton?: boolean;
  title?: string;
  // wrapLongLines?: boolean;
}

export function CodeBlock({
  code,
  language = 'text',
  className,
  showCopyButton = true,
  title,
  // wrapLongLines = true,
}: CodeBlockProps) {
  const [highlighter, setHighlighter] = useState<Highlighter | null>(null);
  const [highlightedCode, setHighlightedCode] = useState<string>('');
  const [copied, setCopied] = useState(false);

  useEffect(() => {
    const initHighlighter = async () => {
      try {
        const hl = await createHighlighter({
          themes: ['github-dark'],
          langs: [
            'javascript',
            'typescript',
            'python',
            'bash',
            'json',
            'yaml',
            'sql',
            'html',
            'css',
            'markdown',
            'text',
          ],
        });
        setHighlighter(hl);
      } catch (error) {
        console.error('Failed to initialize highlighter:', error);
      }
    };

    initHighlighter();
  }, []);

  useEffect(() => {
    if (highlighter && code) {
      try {
        const highlighted = highlighter.codeToHtml(code, {
          lang: language,
          theme: 'github-dark',
        });
        setHighlightedCode(highlighted);
      } catch (error) {
        console.error('Failed to highlight code:', error);
        // Fallback to plain text
        setHighlightedCode(`<pre><code>${code}</code></pre>`);
      }
    }
  }, [highlighter, code, language]);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(code);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy code:', error);
    }
  };

  if (!highlightedCode) {
    return (
      <div className={cn('relative max-w-full', className)}>
        <pre className="bg-muted/30 max-w-full overflow-x-auto rounded-lg p-4 break-words whitespace-pre-wrap">
          <code className="break-words">{code}</code>
        </pre>
      </div>
    );
  }

  // const codeLines = code.split('\n');
  // const lineCount = codeLines.length;

  // Determine responsive classes based on wrapLongLines
  // const getResponsiveClasses = () => {
  //   if (wrapLongLines) {
  //     return {
  //       container: 'max-w-full',
  //       pre: '[&>pre]:max-w-full! [&>pre]:whitespace-pre-wrap! [&>pre]:break-words! [&>pre]:!overflow-wrap-anywhere',
  //       code: '[&_code]:break-words! [&_code]:whitespace-pre-wrap! [&_code]:!overflow-wrap-anywhere [&_code]:!word-break-break-all',
  //     };
  //   }
  //   return {
  //     container: '',
  //     pre: '[&>pre]:whitespace-pre!',
  //     code: '',
  //   };
  // };

  // const responsiveClasses = getResponsiveClasses();

  return (
    <div
      className={cn(
        'group relative max-w-full overflow-hidden rounded-lg border',
        // 'border-muted-foreground/10 bg-muted/20 shadow-sm',
        className,
      )}
    >
      {title && (
        <div className="bg-muted/50 border-muted-foreground/10 min-w-0 border-b px-3 py-1.5 text-xs font-medium">
          <span className="block truncate">{title}</span>
        </div>
      )}

      {showCopyButton && (
        <Button
          variant="ghost"
          size="sm"
          className="absolute top-2 right-2 z-10 opacity-0 transition-opacity group-hover:opacity-100"
          onClick={handleCopy}
        >
          {copied ? (
            <Check className="h-4 w-4 text-green-500" />
          ) : (
            <Copy className="h-4 w-4" />
          )}
        </Button>
      )}

      <div
        className={cn(
          'overflow-wrap-anywhere overflow-x-auto break-words whitespace-pre-wrap',
          '[&_pre]:m-0 [&_pre]:p-3',
        )}
        dangerouslySetInnerHTML={{ __html: highlightedCode }}
      />
    </div>
  );
}
