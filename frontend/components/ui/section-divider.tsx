import { cn } from '@/lib/utils';

type SectionDividerProps = {
  children: React.ReactNode;
  className?: string;
};

export function SectionDivider({ children, className }: SectionDividerProps) {
  return (
    <div className={cn('flex items-center gap-4 py-6', className)}>
      <div className="bg-border h-px flex-1" />
      <div className="text-muted-foreground text-sm font-medium tracking-wide">
        {children}
      </div>
      <div className="bg-border h-px flex-1" />
    </div>
  );
}
