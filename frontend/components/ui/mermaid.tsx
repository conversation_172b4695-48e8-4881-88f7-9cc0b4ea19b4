'use client';

import { useCallback, useEffect, useRef, useState } from 'react';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Expand, RotateCcw, ZoomIn, ZoomOut } from 'lucide-react';
import mermaid from 'mermaid';
import { useTheme } from 'next-themes';

interface MermaidProps {
  content: string;
  className?: string;
}

export function Mermaid({ content, className = '' }: MermaidProps) {
  const { theme } = useTheme();
  const [svg, setSvg] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [dialogZoom, setDialogZoom] = useState(1);
  const [isRendering, setIsRendering] = useState(false);
  const elementRef = useRef<HTMLDivElement>(null);
  const dialogElementRef = useRef<HTMLDivElement>(null);
  const renderAttempts = useRef(0);
  const diagramIdRef = useRef(0);

  // Mermaid configuration
  useEffect(() => {
    mermaid.initialize({
      startOnLoad: true,
      theme: theme === 'dark' ? 'dark' : 'default',
      securityLevel: 'loose',
      fontFamily: 'inherit',
      themeVariables: {
        fontSize: '14px',
        primaryColor: theme === 'dark' ? '#7C3AED' : '#6D28D9',
        primaryTextColor: theme === 'dark' ? '#E2E8F0' : '#1E293B',
        primaryBorderColor: theme === 'dark' ? '#4C1D95' : '#7C3AED',
        lineColor: theme === 'dark' ? '#FFFFFF' : '#94A3B8',
        secondaryColor: theme === 'dark' ? '#1E293B' : '#F8FAFC',
        tertiaryColor: theme === 'dark' ? '#0F172A' : '#FFFFFF',
        backgroundColor: 'transparent',
      },
      gantt: {
        fontSize: 14,
        numberSectionStyles: 3,
      },
      flowchart: {
        curve: 'basis',
        padding: 20,
        useMaxWidth: true,
        htmlLabels: true,
        diagramPadding: 20,
        nodeSpacing: 60,
        rankSpacing: 80,
      },
    });
  }, [theme]);

  // Apply SVG styling - separated from rendering
  const applySvgStyling = useCallback(() => {
    // Style main diagram
    if (elementRef.current) {
      const svgElement = elementRef.current.querySelector('svg');
      if (svgElement) {
        svgElement.style.maxWidth = '100%';
        svgElement.style.minHeight = '400px';
        svgElement.style.height = 'auto';
        svgElement.style.display = 'block';
        svgElement.style.width = '100%';

        if (!svgElement.getAttribute('viewBox')) {
          try {
            const bbox = svgElement.getBBox();
            const expandedHeight = bbox.height * 1.2;
            svgElement.setAttribute(
              'viewBox',
              `0 0 ${bbox.width} ${expandedHeight}`,
            );
          } catch (e) {
            // Fallback if getBBox fails
            console.warn('Could not get SVG bbox:', e);
          }
        }
      }
    }

    // Style dialog diagram
    if (dialogElementRef.current && isDialogOpen) {
      const dialogSvgElement = dialogElementRef.current.querySelector('svg');
      if (dialogSvgElement) {
        dialogSvgElement.style.maxWidth = '100%';
        dialogSvgElement.style.height = 'auto';
        dialogSvgElement.style.minHeight = '60vh';
        dialogSvgElement.style.display = 'block';
        dialogSvgElement.style.width = '100%';
      }
    }
  }, [isDialogOpen]);

  // Main diagram rendering - separate from dialog state
  useEffect(() => {
    const renderDiagram = async () => {
      if (isRendering) return;

      try {
        setIsRendering(true);
        setError(null);

        // Clean and validate the content
        const cleanContent = content
          .replace(/^```mermaid\n?/, '')
          .replace(/```$/, '')
          .trim();

        if (!cleanContent) {
          setSvg('');
          return;
        }

        // Use unique ID for each render to avoid conflicts
        diagramIdRef.current += 1;
        const diagramId = `mermaid-diagram-${diagramIdRef.current}`;

        const { svg: renderedSvg } = await mermaid.render(
          diagramId,
          cleanContent,
        );
        setSvg(renderedSvg);
        renderAttempts.current = 0;

        // Apply styling after a short delay to ensure DOM is ready
        setTimeout(applySvgStyling, 100);
      } catch (err) {
        console.error('Error rendering Mermaid diagram:', err);
        renderAttempts.current++;

        if (renderAttempts.current >= 3) {
          setError('Failed to render diagram. Please check your syntax.');
        }
      } finally {
        setIsRendering(false);
      }
    };

    if (content) {
      renderDiagram();
    } else {
      setSvg('');
      setError(null);
    }

    return () => {
      renderAttempts.current = 0;
    };
  }, [content, theme]); // Removed isDialogOpen from dependencies

  // Apply styling when SVG content changes or dialog opens
  useEffect(() => {
    if (svg && !isRendering) {
      applySvgStyling();
    }
  }, [svg, applySvgStyling, isRendering]);

  // Dialog zoom controls
  const handleDialogZoomIn = () => {
    setDialogZoom((prev) => Math.min(prev + 0.25, 3));
  };

  const handleDialogZoomOut = () => {
    setDialogZoom((prev) => Math.max(prev - 0.25, 0.5));
  };

  const handleDialogZoomReset = () => {
    setDialogZoom(1);
  };

  // Reset zoom when dialog closes
  useEffect(() => {
    if (!isDialogOpen) {
      setDialogZoom(1);
    }
  }, [isDialogOpen]);

  if (error) {
    return (
      <div className="rounded-md bg-red-50 p-4 text-sm text-red-500 dark:bg-red-950/50">
        {error}
        <pre className="mt-2 text-xs">{content}</pre>
      </div>
    );
  }

  return (
    <>
      <div
        className={`mermaid-container custom-scrollbar relative min-h-[500px] max-w-full overflow-hidden rounded-lg border border-slate-200 bg-transparent p-6 dark:border-slate-800 ${className} flex items-center justify-center`}
      >
        {/* Expand button */}
        <div className="absolute top-2 right-2 z-10">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                  <DialogTrigger asChild>
                    <Button
                      size="sm"
                      variant="ghost"
                      className="border-border bg-background/80 hover:bg-background h-8 w-8 border p-0 backdrop-blur-xs"
                    >
                      <Expand className="h-4 w-4" />
                    </Button>
                  </DialogTrigger>
                </Dialog>
              </TooltipTrigger>
              <TooltipContent side="left">
                <p>View in full screen</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>

        {/* Loading state */}
        {isRendering && (
          <div className="bg-background/50 absolute inset-0 flex items-center justify-center rounded-lg backdrop-blur-xs">
            <div className="flex items-center gap-1.5">
              <div className="bg-primary/60 h-2 w-2 animate-pulse rounded-full"></div>
              <div className="bg-primary/60 h-2 w-2 animate-pulse rounded-full [animation-delay:0.2s]"></div>
              <div className="bg-primary/60 h-2 w-2 animate-pulse rounded-full [animation-delay:0.4s]"></div>
            </div>
          </div>
        )}

        {/* Main diagram */}
        <div
          ref={elementRef}
          className="mermaid-diagram flex min-h-[400px] w-full max-w-full min-w-[300px] items-center justify-center overflow-hidden"
          style={{
            opacity: isRendering ? 0.3 : 1,
            transition: 'opacity 0.2s ease',
          }}
          dangerouslySetInnerHTML={{ __html: svg }}
        />
      </div>

      {/* Full screen dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="h-full max-h-[95vh] w-full max-w-[95vw] overflow-hidden p-0">
          <DialogHeader className="border-border border-b px-6 py-4">
            <div className="flex items-center justify-between">
              <DialogTitle>Mermaid Diagram - Full View</DialogTitle>
              <div className="flex items-center gap-1">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-8 w-8 p-0"
                        onClick={handleDialogZoomIn}
                        disabled={dialogZoom >= 3}
                      >
                        <ZoomIn className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent side="bottom">
                      <p>Zoom in</p>
                    </TooltipContent>
                  </Tooltip>

                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-8 w-8 p-0"
                        onClick={handleDialogZoomOut}
                        disabled={dialogZoom <= 0.5}
                      >
                        <ZoomOut className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent side="bottom">
                      <p>Zoom out</p>
                    </TooltipContent>
                  </Tooltip>

                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-8 w-8 p-0"
                        onClick={handleDialogZoomReset}
                      >
                        <RotateCcw className="h-3.5 w-3.5" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent side="bottom">
                      <p>Reset zoom</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>
          </DialogHeader>
          <div className="custom-scrollbar overflow-thin-auto flex min-h-[70vh] max-w-full flex-1 items-center justify-center p-6">
            <div
              ref={dialogElementRef}
              className="mermaid-dialog-diagram flex w-full max-w-full items-center justify-center"
              style={{
                transform: `scale(${dialogZoom})`,
                transformOrigin: 'center center',
                transition: 'transform 0.2s ease',
              }}
              dangerouslySetInnerHTML={{ __html: svg }}
            />
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
