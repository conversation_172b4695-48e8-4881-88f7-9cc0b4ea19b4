'use client';

import { Component, ReactNode } from 'react';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { AlertCircle, RefreshCw } from 'lucide-react';

interface Props {
  children: ReactNode;
  fallbackComponent?: ReactNode;
  errorTitle?: string;
  errorDescription?: string;
  resetLabel?: string;
  hideReset?: boolean;
  reportError?: (error: Error, componentStack: string) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: React.ErrorInfo | null;
}

export class EnhancedErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    this.setState({ errorInfo });

    // Report error to monitoring service if provided
    if (this.props.reportError) {
      this.props.reportError(error, errorInfo.componentStack || '');
    }

    console.error('Error caught by boundary:', error, errorInfo);
  }

  resetError = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
  };

  render() {
    const {
      children,
      fallbackComponent,
      errorTitle = 'Something went wrong',
      errorDescription = 'An unexpected error occurred while rendering this component.',
      resetLabel = 'Try again',
      hideReset = false,
    } = this.props;

    if (this.state.hasError) {
      // Custom fallback component if provided
      if (fallbackComponent) {
        return fallbackComponent;
      }

      // Default error UI
      return (
        <Card className="border-destructive/50 bg-destructive/5">
          <CardHeader>
            <div className="flex items-center gap-2">
              <AlertCircle className="text-destructive h-5 w-5" />
              <CardTitle className="text-destructive">{errorTitle}</CardTitle>
            </div>
            <CardDescription>{errorDescription}</CardDescription>
          </CardHeader>
          <CardContent>
            {this.state.error && (
              <div className="bg-muted/50 text-muted-foreground overflow-thin-auto max-h-40 rounded-lg p-2 text-sm">
                <p className="font-mono">{this.state.error.toString()}</p>
              </div>
            )}
          </CardContent>
          {!hideReset && (
            <CardFooter>
              <Button
                variant="outline"
                className="gap-1"
                onClick={this.resetError}
              >
                <RefreshCw className="h-4 w-4" />
                {resetLabel}
              </Button>
            </CardFooter>
          )}
        </Card>
      );
    }

    return children;
  }
}
