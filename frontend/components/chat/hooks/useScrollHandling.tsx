import { useCallback, useEffect, useRef, useState } from 'react';

// Constants for scroll behavior configuration - OPTIMIZED
const SCROLL_CONSTANTS = {
  DEBOUNCE_TIMEOUT: 16, // Reduced to ~60fps for smoother updates
  BOTTOM_THRESHOLD: 50, // Reduced threshold for better detection
  SCROLL_UP_DETECTION: 5, // Increased sensitivity for better detection
};

interface UseScrollHandlingProps {
  isLoading?: boolean;
  messageCount: number;
}

/**
 * Custom hook to handle scroll behavior in chat interfaces
 */
export function useScrollHandling({
  isLoading,
  messageCount,
}: UseScrollHandlingProps) {
  // Refs
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const scrollContainerRef = useRef<HTMLElement | null>(null); // Cache container reference
  const lastScrollPositionRef = useRef<number>(0);
  const lastProcessedMessageIndexRef = useRef<number>(0);
  const isInitializedRef = useRef<boolean>(false);
  const userHasScrolledRef = useRef<boolean>(false);
  const userScrollTimeoutRef = useRef<NodeJS.Timeout>(undefined); // Track user scroll timeout
  const rafRef = useRef<number>(undefined); // Animation frame reference

  // State
  const [showScrollButton, setShowScrollButton] = useState(false);
  const [isNearBottom, setIsNearBottom] = useState(true);
  const [autoScroll, setAutoScroll] = useState(true);
  const [unreadCount, setUnreadCount] = useState(0);

  // Cache scroll container to avoid repeated DOM queries
  const getScrollContainer = useCallback(() => {
    if (!scrollContainerRef.current && scrollAreaRef.current) {
      scrollContainerRef.current = scrollAreaRef.current.querySelector(
        '[data-radix-scroll-area-viewport]',
      ) as HTMLElement;
    }
    return scrollContainerRef.current;
  }, []);

  // Check if scroll container is at bottom
  const isAtBottom = useCallback(() => {
    const scrollContainer = getScrollContainer();
    if (!scrollContainer) return true;

    const { scrollTop, scrollHeight, clientHeight } = scrollContainer;
    const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

    return distanceFromBottom <= SCROLL_CONSTANTS.BOTTOM_THRESHOLD;
  }, [getScrollContainer]);

  // Scroll to bottom function with interruption protection
  const scrollToBottom = useCallback(
    (behavior: ScrollBehavior = 'smooth', force: boolean = false) => {
      const scrollContainer = getScrollContainer();
      if (!scrollContainer) return;

      // Don't interrupt user scrolling unless forced (auto behavior)
      if (!force && userHasScrolledRef.current && behavior === 'smooth') {
        return;
      }

      const targetScroll =
        scrollContainer.scrollHeight - scrollContainer.clientHeight;

      // For instant scrolling during initial load or streaming
      if (behavior === 'auto') {
        // Use RAF for smoother instant scrolling
        if (rafRef.current) {
          cancelAnimationFrame(rafRef.current);
        }

        rafRef.current = requestAnimationFrame(() => {
          scrollContainer.scrollTop = targetScroll;
          lastScrollPositionRef.current = targetScroll;
        });
      } else {
        // For smooth scrolling
        scrollContainer.scrollTo({
          top: targetScroll,
          behavior: behavior,
        });
      }

      // Reset UI state
      setShowScrollButton(false);
      setUnreadCount(0);
      setAutoScroll(true);
      userHasScrolledRef.current = false;
    },
    [getScrollContainer],
  );

  // Handle scroll events with improved sensitivity
  const handleScroll = useCallback((event: Event) => {
    const target = event.target as HTMLDivElement;
    // Get current scroll position
    const currentScrollTop = target.scrollTop;
    const scrollHeight = target.scrollHeight;
    const clientHeight = target.clientHeight;
    const distanceFromBottom = scrollHeight - currentScrollTop - clientHeight;

    // Check if near bottom using threshold
    const isNear = distanceFromBottom < SCROLL_CONSTANTS.BOTTOM_THRESHOLD;

    // User has manually scrolled
    if (
      Math.abs(currentScrollTop - lastScrollPositionRef.current) >
      SCROLL_CONSTANTS.SCROLL_UP_DETECTION
    ) {
      // If scrolled up, disable auto-scroll temporarily
      if (
        currentScrollTop <
        lastScrollPositionRef.current - SCROLL_CONSTANTS.SCROLL_UP_DETECTION
      ) {
        userHasScrolledRef.current = true;
        setAutoScroll(false);

        // Clear existing timeout and set new one
        clearTimeout(userScrollTimeoutRef.current);
        userScrollTimeoutRef.current = setTimeout(() => {
          if (isAtBottom()) {
            userHasScrolledRef.current = false;
            setAutoScroll(true);
          }
        }, 1000); // Resume auto-scroll after 1 second of no scrolling
      }
      // If scrolled to bottom, enable auto-scroll immediately
      else if (isNear) {
        clearTimeout(userScrollTimeoutRef.current);
        setAutoScroll(true);
        setUnreadCount(0);
        userHasScrolledRef.current = false; // Reset the flag when user scrolls to bottom
      }
    }

    // Update UI state
    setIsNearBottom(isNear);
    setShowScrollButton(!isNear);

    // Update reference scroll position
    lastScrollPositionRef.current = currentScrollTop;
  }, []);

  // Setup scroll event listener with RAF-based optimization
  useEffect(() => {
    const scrollContainer = getScrollContainer();
    if (scrollContainer) {
      let ticking = false;
      const optimizedScrollHandler = (event: Event) => {
        if (!ticking) {
          requestAnimationFrame(() => {
            handleScroll(event);
            ticking = false;
          });
          ticking = true;
        }
      };

      scrollContainer.addEventListener('scroll', optimizedScrollHandler, {
        passive: true,
      });

      // Initial scroll to bottom when first loaded
      if (!isInitializedRef.current) {
        isInitializedRef.current = true;
        requestAnimationFrame(() => {
          scrollToBottom('auto');
          userHasScrolledRef.current = false; // Ensure initial state is clean
        });
      }

      return () => {
        scrollContainer.removeEventListener('scroll', optimizedScrollHandler);
        clearTimeout(userScrollTimeoutRef.current);
        if (rafRef.current) {
          cancelAnimationFrame(rafRef.current);
        }
      };
    }
  }, [handleScroll, scrollToBottom, getScrollContainer]);

  // Track message count changes and handle auto-scrolling
  useEffect(() => {
    if (messageCount > lastProcessedMessageIndexRef.current) {
      // If auto-scroll is enabled or user is at bottom, scroll to bottom
      if (autoScroll || isAtBottom()) {
        // Use RAF for better performance
        requestAnimationFrame(() => {
          scrollToBottom('smooth');
        });
      } else {
        // Track new messages when scrolled up
        const newMessages = messageCount - lastProcessedMessageIndexRef.current;
        setUnreadCount((prev) => prev + newMessages);
        setShowScrollButton(true);
      }
    }

    // Update last processed count
    lastProcessedMessageIndexRef.current = messageCount;
  }, [messageCount, scrollToBottom, autoScroll, isAtBottom]);

  // Handle scrolling during streaming - optimized
  useEffect(() => {
    if (isLoading && autoScroll && !userHasScrolledRef.current) {
      // Reduced frequency for better performance
      const scrollInterval = setInterval(() => {
        if (!userHasScrolledRef.current && (autoScroll || isAtBottom())) {
          scrollToBottom('auto');
        }
      }, 100); // Reduced from 300ms to 100ms for more responsive updates

      return () => clearInterval(scrollInterval);
    }
  }, [isLoading, scrollToBottom, autoScroll, isAtBottom]);

  return {
    scrollAreaRef,
    showScrollButton,
    isNearBottom,
    unreadCount,
    scrollToBottom,
  };
}
