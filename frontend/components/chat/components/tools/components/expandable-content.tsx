import { useCallback, useLayoutEffect, useState } from 'react';

import { Button } from '@/components/ui/button';
import { If } from '@/components/ui/common/if';
import { cn } from '@/lib/utils';
import { AnimatePresence, motion } from 'framer-motion';
import { ChevronDown, ChevronUp } from 'lucide-react';

import { ANIMATION_CONFIG, UI_TIMEOUTS } from '../constants/ui-constants';

interface ExpandableContentProps {
  children: React.ReactNode;
  maxHeight?: string;
  useAnimations?: boolean;
  className?: string;
  label?: string;
  initialExpanded?: boolean;
  onExpandChange?: (expanded: boolean) => void;
}

export function ExpandableContent({
  children,
  maxHeight = 'max-h-[300px]',
  useAnimations = true,
  className = '',
  label = 'Content',
  initialExpanded = false,
  onExpandChange,
}: ExpandableContentProps) {
  const [isExpanded, setIsExpanded] = useState(initialExpanded);
  const [showExpandButton, setShowExpandButton] = useState(false);
  const [contentNode, setContentNode] = useState<HTMLDivElement | null>(null);

  const contentRef = useCallback((node: HTMLDivElement | null) => {
    setContentNode(node);
  }, []);

  useLayoutEffect(() => {
    const checkOverflow = () => {
      if (contentNode) {
        setShowExpandButton(
          contentNode.scrollHeight > contentNode.clientHeight,
        );
      }
    };

    const timerId = setTimeout(checkOverflow, UI_TIMEOUTS.OVERFLOW_CHECK);
    window.addEventListener('resize', checkOverflow);

    return () => {
      clearTimeout(timerId);
      window.removeEventListener('resize', checkOverflow);
    };
  }, [contentNode, isExpanded]);

  const toggleExpanded = () => {
    const newExpanded = !isExpanded;
    setIsExpanded(newExpanded);
    onExpandChange?.(newExpanded);
  };

  const renderContent = () => (
    <div className={cn('space-y-1 md:space-y-1.5', className)}>
      <div className="text-muted-foreground flex items-center gap-1 text-xs font-medium">
        {label}
        <If condition={showExpandButton}>
          <Button
            type="button"
            size="sm"
            variant="outline"
            className="ml-1 flex items-center gap-1 px-1.5 py-0.5 text-xs md:ml-2 md:px-2 md:py-1"
            onClick={toggleExpanded}
            aria-label={isExpanded ? `Collapse ${label}` : `Expand ${label}`}
          >
            <If
              condition={isExpanded}
              fallback={<ChevronDown className="h-3.5 w-3.5 md:h-4 md:w-4" />}
            >
              <ChevronUp className="h-3.5 w-3.5 md:h-4 md:w-4" />
            </If>
            <span className="hidden sm:inline">
              {isExpanded ? 'Collapse' : 'Expand'}
            </span>
          </Button>
        </If>
      </div>

      <div className="border-border/60 overflow-hidden rounded-lg border md:rounded-lg">
        <div
          ref={contentRef}
          className={cn(
            'custom-scrollbar overflow-thin-auto',
            isExpanded ? 'max-h-none' : maxHeight,
          )}
        >
          {children}
        </div>
      </div>
    </div>
  );

  if (!useAnimations) {
    return renderContent();
  }

  return (
    <AnimatePresence mode="wait">
      <motion.div
        key={isExpanded ? 'expanded' : 'collapsed'}
        initial={{ opacity: 0, height: 0 }}
        animate={{ opacity: 1, height: 'auto' }}
        exit={{ opacity: 0, height: 0 }}
        transition={{ duration: ANIMATION_CONFIG.DURATION }}
      >
        {renderContent()}
      </motion.div>
    </AnimatePresence>
  );
}
