/**
 * Replace @Customer with the full name of the customer
 * @param content - The content to replace
 * @param fullName - The full name of the customer
 * @returns The content with the full name replaced
 */
export const replaceCustomerFullname = ({
  content,
  fullName,
}: {
  content: string;
  fullName: string;
}) => {
  return content.replace(/@Customer/g, `@${fullName.replace(/\s+/g, '')}`);
};
