'use client';

import { useMemo } from 'react';

import { motion } from 'framer-motion';
import { SparkleIcon } from 'lucide-react';

type Props = {
  label?: string;
};

const THINKING_WORDS = [
  'Analyzing...',
  'Processing...',
  'Thinking...',
  'Working...',
  'Computing...',
];

export const ThinkingAnimation = ({ label }: Props) => {
  // Randomly pick one word once if no custom label is provided
  const randomWord = useMemo(() => {
    if (label) return null;
    return THINKING_WORDS[Math.floor(Math.random() * THINKING_WORDS.length)];
  }, [label]);

  // Use custom label if provided, otherwise use the randomly selected word
  const displayText = label || randomWord;

  return (
    <motion.div
      className="text-primary"
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{
        opacity: 1,
        scale: 1,
      }}
      transition={{
        opacity: { duration: 0.3 },
        scale: { duration: 0.3, type: 'spring', stiffness: 300 },
      }}
    >
      <motion.div
        className="flex items-center gap-2 text-sm font-medium"
        animate={{ opacity: [0.7, 1, 0.7] }}
        transition={{ duration: 1, repeat: Infinity, ease: 'easeInOut' }}
      >
        <SparkleIcon className="size-4 animate-spin [animation-duration:2s]" />
        {displayText}
      </motion.div>
    </motion.div>
  );
};
