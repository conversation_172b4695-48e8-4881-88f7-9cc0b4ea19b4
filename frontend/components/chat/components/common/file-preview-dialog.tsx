'use client';

import React from 'react';

import { SimplePDFViewer } from '@/app/(home)/kb/components/SimplePDFViewer';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

interface FilePreviewDialogProps {
  fileCategory: 'image' | 'pdf' | 'text' | 'document' | 'other';
  filename: string;
  imagePreviewUrl: string | null;
  pdfUrl: string | null;
  textContent: string | null;
  previewOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

export function FilePreviewDialog({
  fileCategory,
  filename,
  imagePreviewUrl,
  pdfUrl,
  textContent,
  previewOpen,
  onOpenChange,
}: FilePreviewDialogProps) {
  return (
    <Dialog open={previewOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] w-full max-w-[95vw] sm:max-w-4xl">
        <DialogHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
          <DialogTitle className="truncate pr-4">{filename}</DialogTitle>
        </DialogHeader>

        <div className="overflow-thin-auto flex-1">
          {/* Image Preview */}
          {fileCategory === 'image' && imagePreviewUrl && (
            <div className="flex justify-center p-2">
              <img
                src={imagePreviewUrl}
                alt={filename}
                className="max-h-[70vh] max-w-full rounded object-contain"
              />
            </div>
          )}

          {/* PDF Preview */}
          {fileCategory === 'pdf' && pdfUrl && (
            <div className="h-[70vh] w-full">
              <SimplePDFViewer url={pdfUrl} className="h-full w-full" />
            </div>
          )}

          {/* Text Preview */}
          {fileCategory === 'text' && textContent && (
            <div className="bg-muted rounded-lg p-4">
              <pre className="overflow-thin-auto max-h-[60vh] font-mono text-sm break-words whitespace-pre-wrap">
                {textContent}
              </pre>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
