'use client';

import { PropsWithChildren } from 'react';

import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { If } from '@/components/ui/common/if';
import { agentQuery } from '@/features/agent/hooks/agent.query';
import { Loader2Icon, UserCheckIcon } from 'lucide-react';

interface AgentActivationDialogProps extends PropsWithChildren {
  agentId: string;
  agentName: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm?: () => void;
}

export function AgentActivationDialog({
  children,
  agentId,
  agentName,
  open,
  onOpenChange,
  onConfirm,
}: AgentActivationDialogProps) {
  const { mutate: updateStatus, isPending } =
    agentQuery.mutation.useUpdateStatus(agentId);

  const handleConfirm = () => {
    updateStatus(
      {
        agent_status: true,
      },
      {
        onSuccess: () => {
          onOpenChange(false);

          // Small delay to ensure the invalidated queries have time to refetch
          setTimeout(() => {
            onConfirm?.();
          }, 50);
        },
      },
    );
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogTrigger asChild>{children}</AlertDialogTrigger>
      <AlertDialogContent className="max-w-md">
        <AlertDialogHeader>
          <AlertDialogTitle>Activate Agent</AlertDialogTitle>
          <AlertDialogDescription>
            <span className="text-foreground font-semibold">@{agentName}</span>{' '}
            is currently inactive. Would you like to activate this agent so it
            can assist with your conversation?
          </AlertDialogDescription>
        </AlertDialogHeader>

        <div className="flex justify-end gap-2">
          <AlertDialogCancel disabled={isPending} className="mt-0">
            Cancel
          </AlertDialogCancel>
          <Button
            onClick={handleConfirm}
            disabled={isPending}
            className="gap-2"
          >
            <If condition={isPending}>
              <Loader2Icon className="size-4 animate-spin" />
            </If>
            <UserCheckIcon className="size-4" />
            {isPending ? 'Activating...' : 'Activate Agent'}
          </Button>
        </div>
      </AlertDialogContent>
    </AlertDialog>
  );
}
