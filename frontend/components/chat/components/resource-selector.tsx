'use client';

import { createElement, useEffect, useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { If } from '@/components/ui/common/if';
import { InputIconPrefix } from '@/components/ui/input-icon-prefix';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { ScrollArea } from '@/components/ui/scroll-area';
import { RESOURCE_STATUS_CONFIG } from '@/features/resource/config/resource-status.config';
import { RESOURCE_TYPE_CONFIG } from '@/features/resource/config/resource-type.config';
import { resourceQuery } from '@/features/resource/hooks/resource.query';
import { cn } from '@/lib/utils';
import { SchemaResourcePublic } from '@/openapi-ts/gens';
import { Loader2, SearchIcon } from 'lucide-react';
import pluralize from 'pluralize';
import { useInView } from 'react-intersection-observer';
import { useDebounceValue } from 'usehooks-ts';

interface ResourceSelectorProps {
  onResourceSelect: (resourceId: string | null) => void;
  selectedResourceId: string | null;
}

export function ResourceSelector({
  onResourceSelect,
  selectedResourceId,
}: ResourceSelectorProps) {
  const [searchInput, setSearchInput] = useState('');
  const [debouncedSearchQuery] = useDebounceValue(searchInput, 200);

  const { data, isLoading, fetchNextPage, hasNextPage, isFetchingNextPage } =
    resourceQuery.query.useInfiniteList({
      name: debouncedSearchQuery,
    });

  const { ref: loadMoreRef, inView } = useInView({
    threshold: 0.1,
    rootMargin: '100px',
  });

  // Load more when in view
  useEffect(() => {
    if (inView && hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [inView, hasNextPage, isFetchingNextPage, fetchNextPage]);

  if (data) {
    const allResources = data?.pages.flatMap((page) => page.data);

    return (
      <div className="flex h-full flex-col">
        {/* Search Header */}
        <div className="border-b p-2">
          <div className="flex items-center gap-2">
            <InputIconPrefix
              Icon={SearchIcon}
              placeholder="Search resources..."
              value={searchInput}
              onChange={(e) => setSearchInput(e.target.value)}
              containerClassName="grow"
            />
            <Badge variant="ghost-primary" size="sm">
              {pluralize('resource', allResources.length, true)}
            </Badge>
          </div>
        </div>

        {/* Resource List */}
        <ScrollArea className="flex-1">
          <If
            condition={allResources.length > 0}
            fallback={
              <div className="flex flex-col items-center justify-center py-8 text-center">
                <div className="bg-muted/50 mb-2 flex h-10 w-10 items-center justify-center rounded-full">
                  <SearchIcon className="text-muted-foreground/70 h-5 w-5" />
                </div>
                <p className="text-muted-foreground text-sm">
                  {isLoading
                    ? 'Loading resources...'
                    : debouncedSearchQuery
                      ? 'No resources found'
                      : 'No resources available'}
                </p>
                {debouncedSearchQuery && !isLoading && (
                  <p className="text-muted-foreground/70 mt-1 max-w-[80%] text-xs">
                    Try a different search term
                  </p>
                )}
              </div>
            }
          >
            <RadioGroup
              value={selectedResourceId}
              onValueChange={onResourceSelect}
            >
              {allResources.map((resource) => (
                <ResourceItem
                  key={resource.id}
                  resource={resource}
                  isSelected={selectedResourceId === resource.id}
                />
              ))}
            </RadioGroup>

            {/* Load More Trigger */}
            {hasNextPage && (
              <div
                ref={loadMoreRef}
                className="flex min-h-[40px] items-center justify-center py-4"
              >
                {isFetchingNextPage ? (
                  <div className="text-muted-foreground flex items-center text-sm">
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Loading more resources...
                  </div>
                ) : (
                  <div className="h-4" />
                )}
              </div>
            )}

            {!hasNextPage && allResources.length > 0 && (
              <div className="text-muted-foreground my-4 h-4 text-center text-sm">
                No more resources
              </div>
            )}
          </If>
        </ScrollArea>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-6 w-6 animate-spin" />
        <span className="text-muted-foreground ml-2 text-sm">
          Loading resources...
        </span>
      </div>
    );
  }
}

interface ResourceItemProps {
  resource: SchemaResourcePublic;
  isSelected: boolean;
}

function ResourceItem({ resource, isSelected }: ResourceItemProps) {
  const statusConfig = RESOURCE_STATUS_CONFIG.CONFIG[resource.status];
  const { icon } = RESOURCE_TYPE_CONFIG.CONFIG[resource.type];

  return (
    <Label
      className={cn(
        'hover:bg-card/50 flex items-center gap-3 rounded-lg p-2 transition-colors',
        isSelected && 'bg-card pointer-events-none',
      )}
    >
      <RadioGroupItem value={resource.id} />

      <div className="grow space-y-1">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {createElement(icon, { className: 'h-4 w-4 text-foreground' })}
            <h4 className="text-foreground line-clamp-1 text-sm leading-none font-medium">
              {resource.name}
            </h4>
          </div>
          <Badge variant={statusConfig.variant} size="sm" className="shrink-0">
            {statusConfig.label}
          </Badge>
        </div>

        <div className="text-muted-foreground flex min-w-0 items-center space-x-2 text-xs">
          <span className="truncate break-all">{resource.type}</span>
          {resource.region && (
            <>
              <span>•</span>
              <span className="truncate break-all">{resource.region}</span>
            </>
          )}
        </div>
      </div>
    </Label>
  );
}
