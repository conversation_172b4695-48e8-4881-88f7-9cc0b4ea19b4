'use client';

import { useRef } from 'react';

import { MessageDisplayComponentPublic } from '@/client/types.gen';

import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  process<PERSON>hartD<PERSON>,
} from './charts';

interface DisplayComponentProps {
  component: MessageDisplayComponentPublic;
}

interface ChartDataType {
  labels: string[];
  datasets: {
    data: number[];
    backgroundColor?: string[];
    borderWidth?: number;
    label?: string;
  }[];
  x_axis?: {
    title?: string;
    type?: string;
  };
  y_axis?: {
    title?: string;
    type?: string;
  };
  display_options?: {
    show_legend?: boolean;
    show_grid?: boolean;
    currency_format?: boolean;
    percentage_format?: boolean;
  };
  // Sankey-specific data
  sankey_nodes?: {
    id: string;
    label: string;
    color?: string;
  }[];
  sankey_links?: {
    source: string;
    target: string;
    value: number;
    label?: string;
  }[];
}

// Extended chart types (including those not yet in the auto-generated types)
type ExtendedChartType =
  | 'line'
  | 'bar'
  | 'pie'
  | 'doughnut'
  | 'area'
  | 'step_area'
  | 'scatter'
  | 'radar'
  | 'sankey';

export function DisplayComponent({ component }: DisplayComponentProps) {
  const componentRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  // Check if we're in a resource context

  const isChartData = (data: unknown): data is ChartDataType => {
    const d = data as ChartDataType;
    return (
      Array.isArray(d?.labels) &&
      Array.isArray(d?.datasets) &&
      d.datasets.length > 0 &&
      Array.isArray(d.datasets[0].data)
    );
  };

  const isSankeyData = (data: unknown): data is ChartDataType => {
    const d = data as ChartDataType;
    return (
      Array.isArray(d?.sankey_nodes) &&
      Array.isArray(d?.sankey_links) &&
      d.sankey_nodes.length > 0 &&
      d.sankey_links.length > 0
    );
  };

  const renderChart = () => {
    if (!component.data) return null;

    const chartType = (component.chart_type as ExtendedChartType) || 'bar';
    const chartTitle = component.title || '';

    // Handle Sankey charts first (they have different data structure)
    if (chartType === 'sankey') {
      if (!isSankeyData(component.data)) {
        return (
          <div className="text-muted-foreground flex h-64 items-center justify-center">
            Invalid Sankey data: missing nodes or links
          </div>
        );
      }

      const sankeyData = {
        sankey_nodes: component.data.sankey_nodes!,
        sankey_links: component.data.sankey_links!,
      };

      const enhancedChartData = {
        ...component.data,
        x_axis: component.data.x_axis,
        y_axis: component.data.y_axis,
        display_options: component.data.display_options,
      };

      return (
        <div className="w-full" style={{ height: '400px' }}>
          <SankeyChart
            data={sankeyData}
            width="100%"
            height="500"
            title={chartTitle}
            chartData={enhancedChartData}
          />
        </div>
      );
    }

    // For all other chart types, validate standard chart data
    if (!isChartData(component.data)) {
      return (
        <div className="text-muted-foreground flex h-64 items-center justify-center">
          No chart data available
        </div>
      );
    }

    // For all other chart types, use the existing logic
    const chartData = processChartData(component.data);

    // Create enhanced chart data with axis and display options
    const enhancedChartData = {
      ...component.data,
      x_axis: component.data.x_axis,
      y_axis: component.data.y_axis,
      display_options: component.data.display_options,
    };

    // Default height for different chart types
    let chartHeight = 300;

    if (chartType === 'pie' || chartType === 'radar') {
      chartHeight = 400; // Pie and radar charts need more height
    } else if (
      chartType === 'area' ||
      chartType === 'step_area' ||
      chartType === 'line' ||
      chartType === 'bar'
    ) {
      chartHeight = 350; // Area, line, and bar charts need a bit more height
    }

    // Return the appropriate chart component based on type
    return (
      <div className="w-full" style={{ height: `${chartHeight}px` }}>
        {chartType === 'pie' ? (
          <PieChart
            data={chartData}
            width="100%"
            height="100%"
            title={chartTitle}
            chartData={enhancedChartData}
          />
        ) : chartType === 'line' ? (
          <LineChart
            data={chartData}
            width="100%"
            height="100%"
            title={chartTitle}
            chartData={enhancedChartData}
          />
        ) : chartType === 'area' ? (
          <AreaChart
            data={chartData}
            width="100%"
            height="100%"
            title={chartTitle}
            chartData={enhancedChartData}
          />
        ) : chartType === 'step_area' ? (
          <StepAreaChart
            data={chartData}
            width="100%"
            height="100%"
            title={chartTitle}
            chartData={enhancedChartData}
          />
        ) : chartType === 'radar' ? (
          <RadarChart
            data={chartData}
            width="100%"
            height="100%"
            title={chartTitle}
            chartData={enhancedChartData}
          />
        ) : (
          <BarChart
            data={chartData}
            width="100%"
            height="100%"
            title={chartTitle}
            chartData={enhancedChartData}
          />
        )}
      </div>
    );
  };

  if (!component || !component.type) return null;

  return (
    <div className="relative my-4 w-full" ref={componentRef}>
      {/* Content with different layouts for tables vs charts */}
      <div ref={contentRef}>
        {/* {component.type === 'table' && (
          <div className="w-full overflow-hidden rounded-lg border">
            {renderTable()}
          </div>
        )} */}
        {component.type === 'chart' && (
          <div className="bg-card rounded-lg border p-4">
            {/* Header section for charts (inside card) */}
            <div className="mb-6">
              <div className="flex items-start justify-between">
                <div>
                  <h3 className="text-foreground text-lg font-semibold">
                    {component.title}
                  </h3>
                  {component.description && (
                    <p className="text-muted-foreground mt-1 text-sm">
                      {component.description}
                    </p>
                  )}
                </div>
              </div>
            </div>
            {renderChart()}
          </div>
        )}
      </div>
    </div>
  );
}
