'use client';

import { ReactNode } from 'react';

import { AnimatePresence, motion } from 'framer-motion';

interface StreamingMessageContainerProps {
  isStreaming: boolean;
  children: ReactNode;
}

export function StreamingMessageContainer({
  isStreaming,
  children,
}: StreamingMessageContainerProps) {
  if (!isStreaming) {
    return <>{children}</>;
  }

  return (
    <AnimatePresence mode="wait">
      <motion.div
        initial={{
          opacity: 0,
          x: -40,
          scale: 0.96,
          rotateY: -8,
          filter: 'blur(2px)',
        }}
        animate={{
          opacity: 1,
          x: 0,
          scale: 1,
          rotateY: 0,
          filter: 'blur(0px)',
        }}
        exit={{
          opacity: 0,
          x: 15,
          scale: 0.98,
          rotateY: 3,
          filter: 'blur(1px)',
        }}
        transition={{
          type: 'spring',
          stiffness: 260,
          damping: 25,
          mass: 0.8,
          opacity: {
            duration: 0.4,
            ease: 'easeOut',
          },
          scale: {
            duration: 0.35,
            ease: [0.34, 1.56, 0.64, 1],
          },
          rotateY: {
            duration: 0.6,
            ease: [0.25, 0.46, 0.45, 0.94],
          },
          filter: {
            duration: 0.3,
            ease: 'easeOut',
          },
        }}
        className="will-change-transform"
        style={{
          transformPerspective: 1200,
          transformStyle: 'preserve-3d',
        }}
      >
        {children}
      </motion.div>
    </AnimatePresence>
  );
}
