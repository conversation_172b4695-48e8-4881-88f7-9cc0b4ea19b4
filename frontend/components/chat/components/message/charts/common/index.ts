export * from './types';
export * from './base-chart';
export * from './chart-legend';
export * from './hooks/use-chart-performance';
export * from './hooks/use-resize-observer';
export * from './functional-utils';

// Re-export key utilities for easy access
export {
  CHART_COLORS,
  CHART_THEME_COLORS,
  formatCurrency,
  createValueFormatter,
  calculateTicks,
  getXAxisProps,
  calculateOptimalXAxisConfig,
  smartTruncateLabel,
  isDateTimeLabel,
  processChartData,
  calculateTextWidth,
  detectTextOverlap,
} from './functional-utils';
