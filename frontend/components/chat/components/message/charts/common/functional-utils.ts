// Functional composition utilities for chart components
import {
  ChartData,
  ChartDataset,
  ChartLabelFormatter,
  ChartValueFormatter,
  EnhancedChartData,
  ProcessedChartData,
  XAxisConfig,
} from './types';

// ============================================================================
// FUNCTIONAL COMPOSITION UTILITIES
// ============================================================================

// Functional array operations

// ============================================================================
// CHART THEME COLORS (from theme-colors.ts)
// ============================================================================

export const CHART_THEME_COLORS = [
  'var(--color-brand-teal)',
  'var(--color-brand-blue)',
  'var(--color-brand-green)',
  'hsl(210, 85%, 65%)', // Light blue
  'hsl(160, 85%, 65%)', // Light teal
  'hsl(130, 85%, 65%)', // Light green
] as const;

// ============================================================================
// CHART UTILITIES (from utils.ts - refactored functionally)
// ============================================================================

// Enhanced color palette that works well in both light and dark modes
export const CHART_COLORS = [
  'hsl(4, 74%, 65%)', // Warm Red/Coral
  'hsl(156, 45%, 45%)', // Teal
  'hsl(211, 45%, 40%)', // Dark Blue
  'hsl(45, 80%, 65%)', // Gold/Yellow
  'hsl(26, 75%, 65%)', // Orange
  'hsl(271, 40%, 65%)', // Lavender
];

// Functional color selection
const flattenColors = (colors: (string | string[])[]) =>
  colors
    .flatMap((color) => (Array.isArray(color) ? color : [color]))
    .filter(Boolean);

const getColor = (index: number, backgroundColor?: (string | string[])[]) => {
  const validColors = backgroundColor ? flattenColors(backgroundColor) : [];
  return validColors[index] || CHART_COLORS[index % CHART_COLORS.length];
};

// Functional data processing strategies
const dataProcessors = {
  multiDataset: (labels: string[], datasets: ChartDataset[]) =>
    labels.map((label, index) => {
      const value = datasets.reduce(
        (sum, dataset) => sum + (dataset.data[index] || 0),
        0,
      );
      const colors = datasets
        .map((d) =>
          Array.isArray(d.backgroundColor)
            ? d.backgroundColor[0]
            : d.backgroundColor || '',
        )
        .filter(Boolean);

      return {
        name: label || 'Unnamed',
        value,
        color: getColor(index, colors),
        originalValue: value,
        datasets: datasets.map((dataset) => ({
          label: dataset.label || '',
          value: dataset.data[index] || 0,
          color: Array.isArray(dataset.backgroundColor)
            ? dataset.backgroundColor[0]
            : dataset.backgroundColor || getColor(datasets.indexOf(dataset)),
        })),
      };
    }),

  singleDataset: (labels: string[], datasets: ChartDataset[]) =>
    labels.map((label, index) => {
      const firstDataset = datasets[0];
      if (!firstDataset || !Array.isArray(firstDataset.data)) {
        return {
          name: label || 'Unnamed',
          value: 0,
          color: getColor(index),
          originalValue: 0,
          datasets: undefined,
        };
      }

      const value = firstDataset.data[index];
      const processedValue =
        value === null || value === undefined || isNaN(Number(value))
          ? 0
          : Number(value);
      const color = Array.isArray(firstDataset.backgroundColor)
        ? firstDataset.backgroundColor[
            index % (firstDataset.backgroundColor.length || 1)
          ]
        : firstDataset.backgroundColor || getColor(index);

      return {
        name: label || 'Unnamed',
        value: processedValue,
        color,
        originalValue: value,
        datasets: firstDataset?.label
          ? [
              {
                label: firstDataset.label,
                value: processedValue,
                color,
              },
            ]
          : undefined,
      };
    }),
};

export const processChartData = (data: ChartData): ProcessedChartData[] => {
  const { labels, datasets } = data;

  // Validate inputs
  if (!Array.isArray(labels) || labels.length === 0) {
    return [];
  }

  if (!Array.isArray(datasets) || datasets.length === 0) {
    return [];
  }

  const processor =
    datasets.length > 1
      ? dataProcessors.multiDataset
      : dataProcessors.singleDataset;

  return processor(labels, datasets).filter((item) => item.name);
};

// Functional formatting strategies
const formatStrategies = {
  currency: (value: number): string => {
    // Handle zero and very small values explicitly
    if (value === 0) return '$0';
    if (Math.abs(value) < 0.000001) return '$0';

    const formatMap = [
      {
        condition: (v: number) => Math.abs(v) < 0.1,
        format: (v: number) => {
          const formatted = v.toFixed(6);
          const cleaned = formatted.replace(/\.?0+$/, '');
          return `$${cleaned}`;
        },
      },
      {
        condition: (v: number) => Math.abs(v) < 1,
        format: (v: number) => `$${v.toFixed(3)}`,
      },
      {
        condition: (v: number) => Math.abs(v) < 10,
        format: (v: number) => `$${v.toFixed(2)}`,
      },
      {
        condition: () => true,
        format: (v: number) => `$${Math.abs(v).toLocaleString()}`,
      },
    ];

    return formatMap
      .find(({ condition }) => condition(Math.abs(value)))!
      .format(value);
  },

  percentage: (value: number): string => `${value}%`,

  number: (value: number): string => {
    // Handle zero and very small values explicitly
    if (value === 0) return '0';
    if (Math.abs(value) < 0.000001) return '0';

    const formatMap = [
      {
        condition: (v: number) => Math.abs(v) < 0.01,
        format: (v: number) => {
          // For very small numbers, show more precision but clean up trailing zeros
          const formatted = v.toFixed(6);
          return formatted.replace(/\.?0+$/, '');
        },
      },
      {
        condition: (v: number) => Math.abs(v) < 1,
        format: (v: number) => v.toFixed(2),
      },
      {
        condition: (v: number) => Number.isInteger(v),
        format: (v: number) => v.toString(),
      },
      {
        condition: () => true,
        format: (v: number) => {
          const formatted = v.toFixed(1);
          // Remove unnecessary .0 for whole numbers
          return formatted.endsWith('.0') ? formatted.slice(0, -2) : formatted;
        },
      },
    ];

    return formatMap
      .find(({ condition }) => condition(Math.abs(value)))!
      .format(value);
  },
};

export const formatCurrency: ChartValueFormatter = formatStrategies.currency;

// Functional value formatter factory
const formatterMap = {
  currency: formatStrategies.currency,
  percentage: formatStrategies.percentage,
  number: formatStrategies.number,
};

export const createValueFormatter = (
  isCurrency: boolean,
  isPercentage: boolean,
): ChartValueFormatter => {
  const formatterKey = isCurrency
    ? 'currency'
    : isPercentage
      ? 'percentage'
      : 'number';
  return formatterMap[formatterKey];
};

// Functional tick calculation strategy
const tickIntervalMap = [
  { condition: (v: number) => v < 0.01, interval: 0.002 },
  { condition: (v: number) => v < 0.1, interval: 0.02 },
  { condition: (v: number) => v < 1, interval: 0.2 },
  { condition: (v: number) => v < 10, interval: 2 },
  { condition: (v: number) => v < 100, interval: 20 },
  { condition: () => true, interval: 100 },
];

export const calculateTicks = (maxValue: number) => {
  const tickInterval = tickIntervalMap.find(({ condition }) =>
    condition(maxValue),
  )!.interval;
  const maxTick = Math.ceil(maxValue / tickInterval) * tickInterval * 1.05;

  return {
    maxTick,
    ticks: Array.from(
      { length: Math.ceil(maxTick / tickInterval) + 1 },
      (_, i) => {
        const value = i * tickInterval;
        // Clean up decimal places - remove unnecessary trailing zeros
        return Number(value.toFixed(6));
      },
    ),
  };
};

// Functional date/time detection
const dateTimePatterns = [
  /^\d{1,2}:\d{2}(:\d{2})?$/, // HH:MM or HH:MM:SS
  /^\d{4}-\d{2}-\d{2}/, // YYYY-MM-DD
  /^\d{2}\/\d{2}\/\d{4}/, // MM/DD/YYYY
  /^\d{1,2}\/\d{1,2}\/\d{2,4}/, // M/D/YY or MM/DD/YYYY
  /^(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)/i, // Month names
  /^\d{1,2}\s+(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)/i, // Day Month
];

export const isDateTimeLabel = (label: string): boolean => {
  if (!label || typeof label !== 'string') return false;
  return dateTimePatterns.some((pattern) => pattern.test(label));
};

// Functional label truncation strategies
const labelTruncationStrategies = {
  dateTime: (label: string) => {
    if (label.includes(':') && label.length <= 8) return label;
    if (label.includes(':')) return label.substring(0, 5);
    return label;
  },

  multiWord: (label: string, maxLength: number) => {
    const words = label.split(/[\s-_]/);
    const [firstWord, secondWord] = words;

    const strategies = [
      () =>
        firstWord.length >= Math.min(8, maxLength * 0.6) ? firstWord : null,
      () => {
        const combined = `${firstWord} ${secondWord}`;
        return combined.length <= maxLength ? combined : null;
      },
      () =>
        secondWord.length <= 6 && firstWord.length > 8
          ? `${firstWord.substring(0, maxLength - secondWord.length - 1)} ${secondWord}`
          : null,
      () => firstWord,
    ];

    return strategies.find((strategy) => strategy())?.() || firstWord;
  },

  singleWord: (label: string, maxLength: number) =>
    label.length > maxLength + 3
      ? label.substring(0, maxLength - 3) + '...'
      : label,
};

export const smartTruncateLabel: ChartLabelFormatter = (
  label: string,
  maxLength: number = 15,
): string => {
  if (!label || typeof label !== 'string') return '';
  if (isDateTimeLabel(label)) return labelTruncationStrategies.dateTime(label);
  if (label.length <= maxLength) return label;

  const words = label.split(/[\s-_]/);
  return words.length > 1
    ? labelTruncationStrategies.multiWord(label, maxLength)
    : labelTruncationStrategies.singleWord(label, maxLength);
};

// More accurate text width calculation
export const calculateTextWidth = (
  text: string,
  fontSize: number = 12,
): number => {
  const characterWidths: { [key: string]: number } = {
    i: 0.3,
    l: 0.3,
    I: 0.3,
    t: 0.4,
    f: 0.4,
    j: 0.4,
    r: 0.5,
    c: 0.5,
    s: 0.5,
    a: 0.6,
    e: 0.6,
    g: 0.6,
    n: 0.6,
    o: 0.6,
    p: 0.6,
    u: 0.6,
    v: 0.6,
    x: 0.6,
    y: 0.6,
    z: 0.6,
    h: 0.6,
    k: 0.6,
    b: 0.6,
    d: 0.6,
    q: 0.6,
    A: 0.7,
    B: 0.7,
    C: 0.7,
    D: 0.7,
    E: 0.7,
    F: 0.7,
    G: 0.7,
    H: 0.7,
    J: 0.7,
    K: 0.7,
    L: 0.7,
    N: 0.7,
    O: 0.7,
    P: 0.7,
    Q: 0.7,
    R: 0.7,
    S: 0.7,
    T: 0.7,
    U: 0.7,
    V: 0.7,
    X: 0.7,
    Y: 0.7,
    Z: 0.7,
    w: 0.8,
    W: 0.9,
    M: 0.9,
    m: 0.9,
    ' ': 0.3,
    '-': 0.4,
    _: 0.6,
    '.': 0.3,
    ',': 0.3,
    ':': 0.3,
    ';': 0.3,
    '!': 0.3,
    '?': 0.5,
    '(': 0.4,
    ')': 0.4,
    '[': 0.4,
    ']': 0.4,
    '{': 0.4,
    '}': 0.4,
    '|': 0.3,
    '/': 0.4,
    '\\': 0.4,
    '0': 0.6,
    '1': 0.4,
    '2': 0.6,
    '3': 0.6,
    '4': 0.6,
    '5': 0.6,
    '6': 0.6,
    '7': 0.6,
    '8': 0.6,
    '9': 0.6,
  };

  const averageWidth = 0.6;
  const textWidth = text.split('').reduce((width, char) => {
    return width + (characterWidths[char] || averageWidth);
  }, 0);

  return textWidth * fontSize;
};

// Functional overlap detection strategy
const overlapAngleMap = [
  { condition: (ratio: number) => ratio > 2.5, angle: -90 },
  { condition: (ratio: number) => ratio > 1.3, angle: -45 },
  { condition: (ratio: number) => ratio > 1, angle: -30 },
  { condition: () => true, angle: 0 },
];

const calculateOverlapMetrics = (
  labels: string[],
  containerWidth: number,
  fontSize: number,
  padding: number,
) => {
  const availableWidth = containerWidth - 80;
  const spaceBetweenLabels = availableWidth / labels.length;
  const maxTextWidth = Math.max(
    ...labels.map((label) => calculateTextWidth(label, fontSize)),
  );
  const requiredSpacePerLabel = maxTextWidth + padding;
  const overlapRatio = requiredSpacePerLabel / spaceBetweenLabels;

  return { overlapRatio, maxTextWidth, spaceBetweenLabels };
};

export const detectTextOverlap = (
  labels: string[],
  containerWidth: number,
  fontSize: number = 12,
  padding: number = 10,
): { hasOverlap: boolean; overlapRatio: number; recommendedAngle: number } => {
  if (labels.length === 0) {
    return { hasOverlap: false, overlapRatio: 0, recommendedAngle: 0 };
  }

  const { overlapRatio, maxTextWidth, spaceBetweenLabels } =
    calculateOverlapMetrics(labels, containerWidth, fontSize, padding);
  const hasOverlap = overlapRatio > 1;
  let recommendedAngle = overlapAngleMap.find(({ condition }) =>
    condition(overlapRatio),
  )!.angle;

  // Special case for 45-degree optimization
  if (recommendedAngle === -45) {
    const rotatedWidth = maxTextWidth * 0.7;
    if (rotatedWidth + padding > spaceBetweenLabels) {
      recommendedAngle = -45;
    }
  }

  return { hasOverlap, overlapRatio, recommendedAngle };
};

// Functional X-axis configuration strategies
const angleConfigMap = {
  '-30': {
    height: (maxLength: number) => Math.max(45, Math.min(60, maxLength * 2)),
    textAnchor: 'end' as const,
    truncationLength: 30,
  },
  '-45': {
    height: (maxLength: number) => Math.max(55, Math.min(80, maxLength * 2.5)),
    textAnchor: 'end' as const,
    truncationLength: 25,
  },
  '-90': {
    height: (maxLength: number) => Math.max(70, Math.min(120, maxLength * 6)),
    textAnchor: 'middle' as const,
    truncationLength: 20,
  },
};

const intervalStrategies = {
  tooManyLabels: (labelCount: number) =>
    labelCount > 15 ? Math.ceil(labelCount / 10) : 0,
  overlapReduction: (labelCount: number, hasOverlap: boolean) =>
    labelCount > 10 && hasOverlap ? Math.ceil(labelCount / 8) : 0,
};

const formatterStrategies = {
  dateTimeDominant: (value: string) =>
    isDateTimeLabel(value) ? value : smartTruncateLabel(value, 15),
  longLabels: (value: string) => smartTruncateLabel(value, 15),
  rotated: (maxLength: number) => (value: string) =>
    smartTruncateLabel(value, maxLength),
  default: (value: string) => value,
};

const createBaseConfig = (): XAxisConfig => ({
  angle: 0,
  height: 40,
  interval: 0,
  textAnchor: 'middle',
  tickFormatter: formatterStrategies.default,
  labelOffset: 0,
});

export const calculateOptimalXAxisConfig = (
  data: ProcessedChartData[],
  containerWidth: number = 800,
): XAxisConfig => {
  if (!data || data.length === 0) return createBaseConfig();

  const labels = data.map((d) => d.name);
  const maxLabelLength = Math.max(...labels.map((label) => label.length));
  const avgLabelLength =
    labels.reduce((sum, label) => sum + label.length, 0) / labels.length;
  const labelCount = labels.length;
  const isDateTimeDominant =
    labels.filter(isDateTimeLabel).length > labelCount * 0.5;
  const overlapAnalysis = detectTextOverlap(labels, containerWidth);

  const config = createBaseConfig();

  config.interval =
    intervalStrategies.tooManyLabels(labelCount) ||
    intervalStrategies.overlapReduction(labelCount, overlapAnalysis.hasOverlap);

  if (overlapAnalysis.hasOverlap && config.interval === 0) {
    const angleStr =
      overlapAnalysis.recommendedAngle.toString() as keyof typeof angleConfigMap;
    const angleConfig = angleConfigMap[angleStr];

    if (angleConfig) {
      config.angle = overlapAnalysis.recommendedAngle;
      config.height = angleConfig.height(maxLabelLength);
      config.textAnchor = angleConfig.textAnchor;
      config.tickFormatter = formatterStrategies.rotated(
        angleConfig.truncationLength,
      );
    }
  } else if (isDateTimeDominant) {
    config.tickFormatter = formatterStrategies.dateTimeDominant;
  } else if (avgLabelLength > 15 && !overlapAnalysis.hasOverlap) {
    config.tickFormatter = formatterStrategies.longLabels;
  }

  return config;
};

// Functional label offset mapping
const labelOffsetMap = {
  0: -5,
  '-30': -45,
  '-45': -55,
  '-90': -75,
};

export const getXAxisProps = (
  data: ProcessedChartData[],
  chartData?: EnhancedChartData,
  containerWidth?: number,
) => {
  const config = calculateOptimalXAxisConfig(data, containerWidth);
  const labelOffset =
    labelOffsetMap[config.angle as keyof typeof labelOffsetMap] || -5;

  return {
    dataKey: 'name',
    height: config.height,
    interval: config.interval,
    angle: config.angle,
    textAnchor: config.textAnchor,
    tick: { fontSize: 12 },
    tickLine: false,
    axisLine: false,
    padding: { left: 15, right: 15 },
    className: 'text-muted-foreground',
    tickFormatter: config.tickFormatter,
    label: chartData?.x_axis?.title
      ? {
          value: chartData.x_axis.title,
          position: 'insideBottom',
          offset: labelOffset,
          style: { textAnchor: 'middle', fill: 'currentColor' },
        }
      : undefined,
  };
};
