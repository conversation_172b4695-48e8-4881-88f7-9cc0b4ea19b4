'use client';

import {
  <PERSON>,
  CartesianGrid,
  Legend,
  AreaChart as Re<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from 'recharts';

import { useChartPerformance } from './common/hooks/use-chart-performance';
import { BaseChart, CHART_THEME_COLORS, ChartLegend } from './common/index';
import { BaseChartProps } from './common/types';

export const AreaChart: React.FC<BaseChartProps> = (props) => {
  const { data: originalData, chartData } = props;

  // Use the performance hook for consistent formatting
  const { sortedData, formatValue, hasMultipleDatasets } = useChartPerformance(
    originalData,
    chartData,
  );

  // Cast to array to ensure it's the right type for RechartsAreaChart
  const data = Array.isArray(sortedData) ? sortedData : [];

  if (!originalData || originalData.length === 0) {
    return null;
  }

  // Remove custom X-axis configuration logic

  return (
    <BaseChart {...props}>
      <RechartsAreaChart
        data={data}
        margin={{
          top: 10,
          right: 20,
          bottom: 20,
          left: 20,
        }}
      >
        {hasMultipleDatasets ? (
          // Create a gradient for each dataset
          <defs>
            {originalData[0].datasets?.map((dataset, index) => (
              <linearGradient
                key={dataset.label}
                id={`color${index}`}
                x1="0"
                y1="0"
                x2="0"
                y2="1"
              >
                <stop
                  offset="5%"
                  stopColor={
                    CHART_THEME_COLORS[index % CHART_THEME_COLORS.length]
                  }
                  stopOpacity={0.4}
                />
                <stop
                  offset="95%"
                  stopColor={
                    CHART_THEME_COLORS[index % CHART_THEME_COLORS.length]
                  }
                  stopOpacity={0.1}
                />
              </linearGradient>
            ))}
          </defs>
        ) : (
          <defs>
            <linearGradient id="colorValue" x1="0" y1="0" x2="0" y2="1">
              <stop
                offset="5%"
                stopColor="var(--color-brand-teal)"
                stopOpacity={0.4}
              />
              <stop
                offset="95%"
                stopColor="var(--color-brand-teal)"
                stopOpacity={0.1}
              />
            </linearGradient>
            {/* Base gradient that transitions between brand colors */}
            <linearGradient id="areaGradient" x1="0" y1="0" x2="1" y2="0">
              <stop offset="0%" stopColor="var(--color-brand-teal)" />
              <stop offset="50%" stopColor="var(--color-brand-blue)" />
              <stop offset="100%" stopColor="var(--color-brand-green)" />
            </linearGradient>
          </defs>
        )}
        {chartData?.display_options?.show_grid !== false && (
          <CartesianGrid strokeDasharray="3 3" className="stroke-border" />
        )}
        <XAxis
          dataKey="name"
          tick={{ fontSize: 12 }}
          tickLine={false}
          axisLine={false}
          className="text-muted-foreground"
          padding={{ left: 0, right: 0 }}
          label={
            chartData?.x_axis?.title
              ? {
                  value: chartData.x_axis.title,
                  position: 'insideBottom',
                  offset: -5,
                  style: { textAnchor: 'middle', fill: 'currentColor' },
                }
              : undefined
          }
        />
        <YAxis
          tickFormatter={formatValue}
          tick={{ fontSize: 12 }}
          domain={['auto', 'auto']}
          allowDataOverflow={false}
          width={80}
          tickLine={false}
          axisLine={false}
          className="text-muted-foreground"
          label={
            chartData?.y_axis?.title
              ? {
                  value: chartData.y_axis.title,
                  angle: -90,
                  position: 'insideLeft',
                  style: { textAnchor: 'middle', fill: 'currentColor' },
                }
              : undefined
          }
        />
        <Tooltip
          content={({ active, payload }) => {
            if (active && payload && payload.length) {
              return (
                <div className="bg-background rounded-lg border p-2 shadow-lg">
                  <div className="mb-1 font-medium">
                    {payload[0].payload.name}
                  </div>
                  {payload.map((entry, index) => (
                    <div
                      key={index}
                      className="text-muted-foreground flex items-center gap-2 text-sm"
                    >
                      <span
                        className="h-2.5 w-2.5 shrink-0 rounded-full"
                        style={{
                          background: hasMultipleDatasets
                            ? CHART_THEME_COLORS[
                                index % CHART_THEME_COLORS.length
                              ]
                            : 'linear-gradient(90deg, var(--color-brand-teal), var(--color-brand-blue), var(--color-brand-green))',
                        }}
                      />
                      <span className="font-medium">{entry.name}:</span>
                      <span>{formatValue(Number(entry.value) || 0)}</span>
                    </div>
                  ))}
                </div>
              );
            }
            return null;
          }}
        />
        {hasMultipleDatasets ? (
          // Render an Area component for each dataset
          originalData[0].datasets?.map((dataset, index) => (
            <Area
              key={dataset.label}
              type="monotone"
              dataKey={dataset.label}
              stroke={CHART_THEME_COLORS[index % CHART_THEME_COLORS.length]}
              strokeWidth={2}
              fill={`url(#color${index})`}
              dot={false}
              activeDot={{ r: 4, strokeWidth: 2 }}
            />
          ))
        ) : (
          <Area
            type="monotone"
            dataKey="value"
            stroke="url(#areaGradient)"
            strokeWidth={2}
            fill="url(#colorValue)"
            dot={false}
            activeDot={{ r: 4, strokeWidth: 2 }}
          />
        )}
        {hasMultipleDatasets &&
          chartData?.display_options?.show_legend !== false && (
            <Legend
              verticalAlign="top"
              height={24}
              content={(props) => {
                // Map the payload to include correct colors
                const enhancedPayload = props.payload?.map((entry, index) => ({
                  ...entry,
                  color: CHART_THEME_COLORS[index % CHART_THEME_COLORS.length],
                }));
                return (
                  <ChartLegend
                    payload={enhancedPayload}
                    className="mb-2 justify-center px-2"
                  />
                );
              }}
            />
          )}
      </RechartsAreaChart>
    </BaseChart>
  );
};
