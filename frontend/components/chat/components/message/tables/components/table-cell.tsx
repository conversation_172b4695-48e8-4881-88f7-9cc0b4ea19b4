import { TableCell } from '@/components/ui/table';
import { cn } from '@/lib/utils';

import { TableCellProps } from '../common/types';
import { formatCellValue } from '../common/utils';

export const TableCellComponent: React.FC<TableCellProps> = ({
  value,
  columnConfig,
}) => {
  // Handle technical guidances array
  if (Array.isArray(value) && columnConfig.header === 'Technical Guidances') {
    return (
      <TableCell className="dark:border-border p-4 align-middle *:bg-transparent dark:bg-transparent">
        <div className="pr-2">
          {value.length > 0 ? (
            <ul className="list-inside list-disc space-y-1.5">
              {value.map((guidance: string, i: number) => (
                <li key={i} className="dark:text-foreground/90 text-sm">
                  {guidance}
                </li>
              ))}
            </ul>
          ) : (
            <span className="text-muted-foreground italic">None</span>
          )}
        </div>
      </TableCell>
    );
  }

  // Handle document URL
  if (columnConfig.header === 'Document URL') {
    return (
      <TableCell
        className={cn(
          'dark:border-border p-4 align-middle *:bg-transparent dark:bg-transparent',
        )}
      >
        {value ? (
          <a
            href={value}
            target="_blank"
            rel="noopener noreferrer"
            className="text-primary hover:underline"
          >
            View Document
          </a>
        ) : (
          <span className="text-muted-foreground italic">N/A</span>
        )}
      </TableCell>
    );
  }

  // Handle description with longer text
  if (columnConfig.header === 'Description') {
    return (
      <TableCell className="dark:border-border p-4 align-middle *:bg-transparent dark:bg-transparent">
        <div className="dark:text-foreground/90 max-w-[500px]">
          {value || <span className="text-muted-foreground italic">N/A</span>}
        </div>
      </TableCell>
    );
  }

  const formattedValue = formatCellValue(value);

  return (
    <TableCell
      className={cn(
        'dark:border-border p-4 align-middle *:bg-transparent dark:bg-transparent',
      )}
    >
      <span
        className={cn(
          'dark:text-foreground/90 text-sm',
          typeof value === 'number' && 'font-medium',
          (value === null || value === '') && 'text-muted-foreground italic',
        )}
      >
        {formattedValue}
      </span>
    </TableCell>
  );
};
