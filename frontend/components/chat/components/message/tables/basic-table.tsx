import {
  Table,
  TableBody,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { cn } from '@/lib/utils';

import { BaseTableProps } from './common/types';
import { TableCellComponent } from './components/table-cell';

export const BasicTable: React.FC<BaseTableProps> = ({
  data,
  showFullView = false,
}) => {
  const { headers, rows } = data;

  return (
    <div
      className={cn(
        'relative w-full border-none',
        showFullView && 'overflow-thin-auto',
      )}
    >
      <Table className="dark:[&_tbody]:bg-transparent">
        <TableHeader>
          <TableRow className="dark:border-borde bg-primary/5">
            {headers.map((columnConfig, index) => (
              <TableHead key={index}>{columnConfig.header}</TableHead>
            ))}
          </TableRow>
        </TableHeader>
        <TableBody className="dark:bg-transparent">
          {rows.map((row, rowIndex) => (
            <TableRow
              key={rowIndex}
              className={cn(
                // Align hover color with table header's default muted tone
                'data-[state=selected]:bg-muted/60 hover:bg-muted/50 dark:data-[state=selected]:bg-muted/40 dark:hover:bg-muted/30 transition-colors dark:bg-transparent',
                rowIndex !== rows.length - 1 && 'dark:border-border border-b',
              )}
            >
              {row.map((cell, cellIndex) => (
                <TableCellComponent
                  key={cellIndex}
                  value={cell}
                  columnConfig={headers[cellIndex]}
                  rowIndex={rowIndex}
                />
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};
