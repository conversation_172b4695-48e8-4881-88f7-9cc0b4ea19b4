export interface TableColumnConfig {
  header: string;
}

export interface TableData {
  headers: TableColumnConfig[];
  rows: any[][];
}

export interface BaseTableProps {
  data: TableData;
  showFullView?: boolean;
}

export interface RecommendationData {
  headers: TableColumnConfig[];
  rows: any[][];
  recommendation?: {
    [key: string]: any;
  };
}

export interface RecommendationTableProps {
  data: RecommendationData;
  onFullViewClick?: () => void;
}

export interface EnhancedTableProps extends BaseTableProps {
  onFullViewClick?: () => void;
}

export interface TableCellProps {
  value: any;
  columnConfig: TableColumnConfig;
  rowIndex: number;
}

export interface StatusIndicatorProps {
  value: string;
  type?: 'status' | 'risk' | 'effort';
}
