'use client';

import { useState } from 'react';

import { Button } from '@/components/ui/button';
import { CodeBlock } from '@/components/ui/code-block';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { WithTooltip } from '@/components/ui/tooltip';
import { Bot, Command, Hash, HelpCircleIcon, Rocket } from 'lucide-react';

import { MentionsTabContent } from './mentions-tab-content';

type TabId = 'overview' | 'mentions' | 'commands' | 'integrations';

export function ChatHelpDialog() {
  const [activeTab, setActiveTab] = useState<TabId>('overview');

  const currentTab = TABS.find((tab) => tab.id === activeTab)!;

  return (
    <Dialog>
      <DialogTrigger asChild>
        <WithTooltip tooltip="Help & usage tips">
          <Button
            size="icon"
            variant="ghost"
            className="text-muted-foreground"
            type="button"
          >
            <HelpCircleIcon className="size-4" />
            <span className="sr-only">Open Help</span>
          </Button>
        </WithTooltip>
      </DialogTrigger>
      <DialogContent className="flex h-[600px] max-w-4xl flex-col">
        <DialogHeader>
          <DialogTitle>{currentTab.name}</DialogTitle>
          <DialogDescription>{currentTab.description}</DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={(v) => setActiveTab(v as TabId)}>
          {/* Desktop tabs - hidden on mobile */}
          <TabsList className="max-md:hidden">
            {TABS.map((tab) => (
              <TabsTrigger key={tab.id} value={tab.id} className="gap-2">
                <tab.icon className="size-4" />
                {tab.name}
              </TabsTrigger>
            ))}
          </TabsList>

          {/* Mobile select dropdown - hidden on desktop */}
          <div className="w-fit md:hidden">
            <Select
              value={activeTab}
              onValueChange={(v) => setActiveTab(v as TabId)}
            >
              <SelectTrigger className="gap-2">
                <SelectValue>
                  <div className="flex items-center gap-2">
                    <currentTab.icon className="size-4" />
                    {currentTab.name}
                  </div>
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                {TABS.map((tab) => (
                  <SelectItem key={tab.id} value={tab.id}>
                    <div className="flex items-center gap-2">
                      <tab.icon className="size-4" />
                      {tab.name}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {TABS.map((tab) => (
            <TabsContent key={tab.id} value={tab.id}>
              <tab.content />
            </TabsContent>
          ))}
        </Tabs>

        <DialogFooter className="!mt-auto">
          <DialogClose asChild>
            <Button variant="outline">Close</Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

type Tab = {
  id: TabId;
  name: string;
  icon: React.ElementType;
  description?: string;
  content: () => React.ReactNode;
};

const TABS: Tab[] = [
  {
    id: 'overview',
    name: 'Chat Interface Help',
    icon: Bot,
    description: 'Learn how to interact efficiently with your AI agent team',
    content: () => (
      <ul className="list-disc space-y-2 pl-5">
        <li>Type naturally to communicate with your AI team</li>
        <li>
          Use <span className="text-foreground font-medium">@</span> to mention
          a specific agent
        </li>
        <li>
          Use <span className="text-foreground font-medium">#</span> to attach
          tools or documents
        </li>
      </ul>
    ),
  },
  {
    id: 'mentions',
    name: 'Agent Mentions (@)',
    icon: Hash,
    description: 'Direct your message to specific agents using mentions',
    content: MentionsTabContent,
  },
  {
    id: 'commands',
    name: 'Quick Commands',
    icon: Command,
    description: 'Declarative commands for instant actions',
    content: () => (
      <div className="space-y-4">
        <CodeBlock
          title="Commands"
          code={`/help\n/analyze costs\n/security scan\n/scale cluster\n/report status`}
          className="prose rounded-none border-none"
        />
        <CodeBlock
          title="Agent mentions"
          code={`@alex show me cost trends\n@maya audit security for S3`}
          className="prose rounded-none border-none"
        />
      </div>
    ),
  },
  {
    id: 'integrations',
    name: 'Tool Integration',
    icon: Rocket,
    description: 'What tools your agents can use automatically',
    content: () => (
      <ul className="list-disc space-y-2 pl-5">
        <li>AWS CLI commands for cloud management</li>
        <li>Kubernetes kubectl for container ops</li>
        <li>Database queries for data analysis</li>
        <li>Monitoring tools for system health</li>
        <li>Custom webhooks for integrations</li>
      </ul>
    ),
  },
];
