import { agentQuery } from '@/features/agent/hooks/agent.query';
import { AgentType } from '@/openapi-ts/gens';
import { filter } from 'lodash';

import { MentionHighlight } from '../../utils/mention-highlighting';

export const MentionsTabContent = () => {
  const { data } = agentQuery.query.useList();
  const conversationalAgents = filter(data?.data, {
    type: AgentType.conversation_agent,
  });
  return (
    <div className="space-y-4">
      <p className="text-foreground">
        Direct your messages to specific agents:
      </p>
      <ul className="mt-2 list-disc space-y-2 pl-5">
        {conversationalAgents?.map((agent) => (
          <li key={agent.id}>
            <MentionHighlight type="agent">{agent.title}</MentionHighlight>–{' '}
            {agent.role}
          </li>
        ))}
      </ul>
    </div>
  );
};
