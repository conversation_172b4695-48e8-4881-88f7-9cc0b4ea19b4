'use client';

import { useEffect, useMemo, useState } from 'react';

import { Button } from '@/components/ui/button';
import { If } from '@/components/ui/common/if';
import { Progress } from '@/components/ui/progress';
import { useChatContext } from '@/features/chat/context/chat-context';
import { cn } from '@/lib/utils';
import { AnimatePresence, motion } from 'framer-motion';
import {
  CheckCircle2,
  ChevronDown,
  ChevronUp,
  CircleX,
  Clock,
  Loader2,
} from 'lucide-react';

import { BotAvatar } from './common/bot-icon';

export function PlanningSession() {
  const [isExpanded, setIsExpanded] = useState(false);
  const { planningContent } = useChatContext();

  type AgentPlan = {
    agent_id: string;
    tasks: any[];
    updated_at?: string;
  };

  const agentPlans = useMemo<AgentPlan[]>(() => {
    // Handle both cases where planningContent may be raw plans array or nested under .plans
    const plansArray = Array.isArray(planningContent)
      ? planningContent
      : planningContent?.plans;

    if (!Array.isArray(plansArray)) return [];

    // Only show agents that have tasks
    return plansArray.filter(
      (ap) => Array.isArray(ap.tasks) && ap.tasks.length > 0,
    );
  }, [planningContent]);

  // Sort agents by most recent updated_at (desc). Undefined dates go last
  const sortedAgentPlans = useMemo(() => {
    return [...agentPlans].sort((a, b) => {
      const aTime = a.updated_at ? Date.parse(a.updated_at) : 0;
      const bTime = b.updated_at ? Date.parse(b.updated_at) : 0;
      return bTime - aTime;
    });
  }, [agentPlans]);

  // Selected agent (default: latest)
  const [selectedAgentId, setSelectedAgentId] = useState<string | null>(null);

  // Keep selected in sync when plans change
  useEffect(() => {
    if (!selectedAgentId) {
      setSelectedAgentId(sortedAgentPlans[0]?.agent_id ?? null);
      return;
    }

    // If the previously selected agent no longer exists, switch to latest
    const exists = sortedAgentPlans.some((p) => p.agent_id === selectedAgentId);
    if (!exists) {
      setSelectedAgentId(sortedAgentPlans[0]?.agent_id ?? null);
    }
  }, [sortedAgentPlans, selectedAgentId]);

  const selectedPlan = useMemo(() => {
    if (!selectedAgentId) return null;
    return sortedAgentPlans.find((p) => p.agent_id === selectedAgentId) ?? null;
  }, [sortedAgentPlans, selectedAgentId]);

  const hasActivePlanning = useMemo(() => agentPlans.length > 0, [agentPlans]);

  const agentCompleted = useMemo(
    () => (selectedPlan?.tasks || []).filter((t) => t?.status === 'completed'),
    [selectedPlan],
  );
  const agentTotal = selectedPlan?.tasks?.length ?? 0;
  const agentProgress =
    agentTotal > 0 ? (agentCompleted.length / agentTotal) * 100 : 0;

  const currentItem = useMemo(
    () => (selectedPlan?.tasks || []).find((t) => t?.status !== 'completed'),
    [selectedPlan],
  );

  const isCompleted = agentTotal > 0 && agentCompleted.length === agentTotal;
  const hideWhenCompleted = isCompleted && agentProgress === 100;

  return (
    <If condition={hasActivePlanning && !hideWhenCompleted}>
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -10 }}
        className={cn(
          'border-muted-foreground/10 bg-background/50 rounded-lg border p-3 backdrop-blur-sm transition-all duration-300',
          isExpanded && 'max-h-[40vh] overflow-y-auto',
        )}
      >
        <div className="space-y-2">
          <div className="flex items-center justify-between gap-2">
            <div
              className="hover:bg-muted/40 -m-1 flex min-w-0 flex-1 cursor-pointer items-center gap-2 overflow-hidden rounded-md p-1"
              role="button"
              aria-expanded={isExpanded}
              tabIndex={0}
              onClick={() => setIsExpanded(!isExpanded)}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  setIsExpanded((v) => !v);
                }
              }}
            >
              <div className="flex items-center gap-2">
                {sortedAgentPlans.map((ap) => (
                  <button
                    key={ap.agent_id}
                    type="button"
                    className={cn(
                      'rounded-full p-0 transition-colors focus-visible:outline-none',
                      ap.agent_id !== selectedAgentId && 'hover:bg-muted/60',
                    )}
                    onClick={(e) => {
                      e.stopPropagation();
                      setSelectedAgentId(ap.agent_id);
                    }}
                    aria-label={`Select agent ${ap.agent_id}`}
                    aria-pressed={ap.agent_id === selectedAgentId}
                  >
                    <div
                      className={cn(
                        'rounded-full',
                        ap.agent_id === selectedAgentId &&
                          'ring-primary/60 ring-2',
                      )}
                    >
                      <BotAvatar
                        role={ap.agent_id}
                        variant="compact"
                        hideRoleText={true}
                        className="mb-0"
                      />
                    </div>
                  </button>
                ))}
              </div>

              <div className="min-w-0 flex-1">
                <span className="text-foreground truncate text-sm font-medium">
                  {currentItem?.content || 'Planning Session'}
                </span>
                {selectedPlan?.agent_id && (
                  <span className="text-muted-foreground ml-2 truncate text-xs">
                    {selectedPlan.agent_id}
                  </span>
                )}
              </div>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsExpanded(!isExpanded)}
              className="h-6 w-6 shrink-0"
            >
              {isExpanded ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </Button>
          </div>

          <If condition={isExpanded}>
            <div className="space-y-1">
              <div className="flex justify-between text-xs">
                <span className="text-muted-foreground">
                  {agentCompleted.length}/{agentTotal} tasks
                </span>
                <span
                  className={cn('font-medium', isCompleted && 'text-green-500')}
                >
                  {isCompleted ? 'Completed' : `${Math.round(agentProgress)}%`}
                </span>
              </div>
              <Progress
                value={agentProgress}
                className={cn('h-1.5', isCompleted && '[&>div]:bg-green-500')}
              />
            </div>
          </If>

          <AnimatePresence>
            <If condition={isExpanded}>
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: 'auto', opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                transition={{ duration: 0.2 }}
                className="overflow-hidden pt-2"
              >
                <div className="border-muted-foreground/10 space-y-2 border-t pt-2">
                  {(selectedPlan?.tasks || []).map((item, index) => (
                    <div
                      key={item?.id || `${selectedPlan?.agent_id}-${index}`}
                      className="flex items-start gap-3 text-sm"
                    >
                      <div
                        className={cn(
                          'mt-0.5 shrink-0',
                          item?.status === 'completed' && 'text-green-500',
                          item?.status === 'in_progress' && 'text-blue-500',
                          item?.status === 'blocked' && 'text-red-500',
                          item?.status === 'pending' && 'text-yellow-500',
                        )}
                      >
                        {item?.status === 'pending' && (
                          <Clock className="h-4 w-4" />
                        )}
                        {item?.status === 'in_progress' && (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        )}
                        {item?.status === 'completed' && (
                          <CheckCircle2 className="h-4 w-4" />
                        )}
                        {item?.status === 'blocked' && (
                          <CircleX className="h-4 w-4" />
                        )}
                      </div>
                      <div className="min-w-0 flex-1">
                        <p className="text-foreground">{item?.content}</p>
                        <If condition={item?.notes}>
                          {(notes) => (
                            <p className="text-muted-foreground mt-1 text-xs">
                              {notes}
                            </p>
                          )}
                        </If>
                      </div>
                    </div>
                  ))}
                </div>
              </motion.div>
            </If>
          </AnimatePresence>
        </div>
      </motion.div>
    </If>
  );
}
