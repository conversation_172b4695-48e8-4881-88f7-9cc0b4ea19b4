'use client';

import { CSSProperties, useCallback, useEffect, useRef, useState } from 'react';

import { flushSync } from 'react-dom';

import Link from 'next/link';

import { ActionButton } from '@/components/chat/components/action-button';
import { AgentActivationDialog } from '@/components/chat/components/agent-activation-dialog';
import { AgentMentionDropdown } from '@/components/chat/components/agent-mention';
import { ChatHelpDialog } from '@/components/chat/components/chat-help-dialog/chat-help-dialog';
import { resourceCategories } from '@/components/chat/components/data';
import { PlusButtonDropdown } from '@/components/chat/components/plus-button-dropdown';
import { ResourceMentionDropdown } from '@/components/chat/components/resource-mention';
import { FilePreviewList } from '@/components/chat/file-preview';
import { useAgentActivation } from '@/components/chat/hooks/use-agent-activation';
import {
  getIncompleteAtCursor,
  highlightMentions,
} from '@/components/chat/utils/mention-highlighting';
import { Button } from '@/components/ui/button';
import { Heading } from '@/components/ui/heading';
import { Textarea } from '@/components/ui/textarea';
import { WithTooltip } from '@/components/ui/tooltip';
import pathsConfig from '@/config/paths.config';
import { QuickStartCard } from '@/features/agent/components/quick-start-card';
import { builtinToolsQuery } from '@/features/builtin-tools/hooks/builtin-tools.query';
import { useChatContext } from '@/features/chat/context/chat-context';
import { useConstantsContext } from '@/features/constants/provider/constants-provider';
import { resourceQuery } from '@/features/resource/hooks/resource.query';
import { OperationHubDialog } from '@/features/task/components/operation-hub-dialog';
import { useFileUpload } from '@/hooks/use-file-upload';
import { useKnowledgeBases } from '@/hooks/use-knowledge-bases';
import { cn } from '@/lib/utils';
import { AlertTriangle, CalendarIcon, Settings } from 'lucide-react';
import { useDebounceCallback } from 'usehooks-ts';

import { ResourceAttachmentIndicator } from '../resource-attachment-indicator';
import { If } from '../ui/common/if';
import { PageSkeleton } from '../ui/common/page';
import { ChatInputPlaceholder } from './chat-input-placeholder';

type MessageInputProps = {
  onSendMessage: (
    content: string,
    attachmentIds?: string[],
    resourceId?: string,
  ) => void;
  disabled?: boolean;
  value?: string;
  onChange: (value: string) => void;
};

export function MessageInput({
  onSendMessage,
  disabled,
  value: externalValue,
  onChange,
}: MessageInputProps) {
  // File upload hook
  const {
    files,
    addFiles,
    removeFile,
    clearAll: clearAllFiles,
    isUploading,
    hasValidationErrors,
    completedAttachmentIds,
  } = useFileUpload();

  const {
    messages,
    isStreaming,
    quotaInfo,
    onStopStreaming,
    isLoadingMessages,
  } = useChatContext();

  // State
  const [localValue, setLocalValue] = useState(externalValue || '');

  // Helper function to update context
  const handleResourceSelection = (resourceId: string | null) => {
    onResourceSelect?.(resourceId);
  };

  // # Mention feature states
  const [showResourceMention, setShowResourceMention] = useState(false);
  const [resourceMentionPosition, setResourceMentionPosition] = useState({
    top: 0,
    left: 0,
  });
  const [resourceMentionFilter, setResourceMentionFilter] = useState('');

  // @ Mention feature states
  const [showAgentMention, setShowAgentMention] = useState(false);
  const [agentMentionPosition, setAgentMentionPosition] = useState({
    top: 0,
    left: 0,
  });
  const [agentMentionFilter, setAgentMentionFilter] = useState('');

  // Animation states for text limit feedback
  const [isShaking, setIsShaking] = useState(false);

  // Agent activation hook
  const {
    showAgentActivationDialog,
    setShowAgentActivationDialog,
    inactiveAgentInfo,
    checkForInactiveAgents,
    handleActivationConfirm,
    showActivationDialog,
  } = useAgentActivation();

  // Get knowledge bases
  const { kbs, isLoading: kbsLoading } = useKnowledgeBases();

  const { constants: startTemplate } = useConstantsContext();

  // Refs
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const isSubmittingRef = useRef(false);
  const overlayRef = useRef<HTMLDivElement>(null);

  // Derived state
  const hasContent = Boolean(localValue?.trim());

  // Fetch builtin tools
  const { data: builtinTools, isPending: isBuiltinToolsLoading } =
    builtinToolsQuery.query.useList();

  // Get resource IDs from chat context
  const { conversationResourceId, selectedResourceId, onResourceSelect } =
    useChatContext();

  // Fetch resources to find selected resource details
  const { data: resourcesData } = resourceQuery.query.useInfiniteList({});
  const allResources = resourcesData?.pages.flatMap((page) => page.data) || [];

  // Find selected resource (for current message input)
  const selectedResource = selectedResourceId
    ? allResources.find((resource) => resource.id === selectedResourceId)
    : null;

  // Fetch conversation resource directly by ID
  const { data: conversationResource } = resourceQuery.query.useById(
    conversationResourceId || '',
    { enabled: !!conversationResourceId },
  );

  // Prepare dynamic resource categories by merging static data with dynamic KB collections and builtin tools
  const dynamicResourceCategories = [
    {
      id: 'tools',
      name: 'Tools',
      icon: <Settings className="h-5 w-5 text-purple-500" />,
      items: isBuiltinToolsLoading
        ? [
            {
              id: 'loading',
              title: 'Loading tools...',
              description: 'Please wait',
            },
          ]
        : builtinTools?.map((tool) => ({
            id: tool.id,
            title: tool.builtin_tool.display_name,
            description: tool.builtin_tool.description,
          })) || [],
    },
    ...resourceCategories.map((category) => {
      if (category.isDynamic && category.source === 'kb_collections') {
        return {
          ...category,
          items: kbsLoading
            ? [
                {
                  id: 'loading',
                  title: 'Loading collections...',
                  description: 'Please wait',
                },
              ]
            : kbs,
        };
      }
      return category;
    }),
  ];

  // Effects
  // Sync with external value when it changes
  useEffect(() => {
    if (externalValue !== undefined) {
      setLocalValue(externalValue);
    }
  }, [externalValue]);

  const debouncedOnChange = useDebounceCallback(onChange, 100);

  // Auto-resize textarea with debounce
  const debouncedResize = useDebounceCallback(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      const maxHeight = window.innerWidth < 640 ? 200 : 250; // Responsive max height
      const newHeight = Math.min(textareaRef.current.scrollHeight, maxHeight);
      textareaRef.current.style.height = `${newHeight}px`;
    }
  }, 50);

  useEffect(() => {
    debouncedResize();
    return () => {
      debouncedResize.cancel();
    };
  }, [localValue, debouncedResize]);

  // Add this function to sync scrolling
  const syncScroll = () => {
    if (overlayRef.current && textareaRef.current) {
      overlayRef.current.scrollTop = textareaRef.current.scrollTop;
      overlayRef.current.scrollLeft = textareaRef.current.scrollLeft;
    }
  };

  // Event handlers
  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    // Don't update if we're in the middle of submitting
    if (isSubmittingRef.current) return;

    const newValue = e.target.value;
    const maxLength = 10000; // Set reasonable text limit

    // Check for text length limit
    if (newValue.length > maxLength) {
      // Trigger shake animation
      setIsShaking(true);
      setTimeout(() => setIsShaking(false), 600);
      return; // Don't update if over limit
    }

    setLocalValue(newValue);
    debouncedOnChange(newValue);

    // Check for mention triggers
    checkForMentions(newValue, e.target.selectionStart);

    // Ensure textarea resizes properly after content changes
    debouncedResize();
  };

  // Handle paste events with animation and file support
  const handlePaste = useCallback(
    (e: React.ClipboardEvent<HTMLTextAreaElement>) => {
      // First check for files
      const items = Array.from(e.clipboardData.items);
      const files = items
        .filter((item) => item.kind === 'file')
        .map((item) => item.getAsFile())
        .filter((file): file is File => file !== null);

      if (files.length > 0) {
        e.preventDefault();
        addFiles(files);
        return;
      }

      // Handle text paste
      const pastedText = e.clipboardData.getData('text');
      const currentLength = localValue.length;
      const newLength = currentLength + pastedText.length;
      const maxLength = 10000;

      // If pasting would exceed limit, show shake animation
      if (newLength > maxLength) {
        e.preventDefault();
        setIsShaking(true);
        setTimeout(() => setIsShaking(false), 600);
        return;
      }

      // If pasting a lot of text, trigger scroll animation
      if (pastedText.length > 100) {
        setTimeout(() => {
          if (textareaRef.current) {
            // Smooth scroll to show new content
            textareaRef.current.scrollTo({
              top: textareaRef.current.scrollHeight,
              behavior: 'smooth',
            });
          }
        }, 100);
      }
    },
    [localValue, addFiles],
  );

  // Check for # and @ mentions in the text
  const checkForMentions = (text: string, cursorPos: number) => {
    const mentionInfo = getIncompleteAtCursor(text, cursorPos);

    if (!mentionInfo) {
      // If no mentions, hide both dropdowns
      setShowResourceMention(false);
      setShowAgentMention(false);
      return;
    }

    if (mentionInfo.type === 'resource') {
      // Show resource dropdown
      setResourceMentionFilter(mentionInfo.filter.toLowerCase());

      if (textareaRef.current) {
        const textareaRect = textareaRef.current.getBoundingClientRect();
        const cursorCoords = getCaretCoordinates(
          textareaRef.current,
          cursorPos,
        );

        setResourceMentionPosition({
          top: window.scrollY + textareaRect.top + cursorCoords.top - 180,
          left: Math.max(
            16,
            Math.min(
              window.scrollX + textareaRect.left + cursorCoords.left,
              window.innerWidth - 320,
            ),
          ),
        });
        setShowResourceMention(true);
      }

      // Hide agent mention
      setShowAgentMention(false);
    } else if (mentionInfo.type === 'agent') {
      // Show agent dropdown
      setAgentMentionFilter(mentionInfo.filter.toLowerCase());

      if (textareaRef.current) {
        const textareaRect = textareaRef.current.getBoundingClientRect();
        const cursorCoords = getCaretCoordinates(
          textareaRef.current,
          cursorPos,
        );

        setAgentMentionPosition({
          top: window.scrollY + textareaRect.top + cursorCoords.top - 180,
          left: Math.max(
            16,
            Math.min(
              window.scrollX + textareaRect.left + cursorCoords.left,
              window.innerWidth - 320,
            ),
          ),
        });
        setShowAgentMention(true);
      }

      // Hide resource mention
      setShowResourceMention(false);
    }
  };

  // Helper function to get caret coordinates
  const getCaretCoordinates = (
    element: HTMLTextAreaElement,
    position: number,
  ) => {
    // Create a temporary div to measure text
    const div = document.createElement('div');
    const style = window.getComputedStyle(element);
    const properties = [
      'fontFamily',
      'fontSize',
      'fontWeight',
      'wordWrap',
      'whiteSpace',
      'borderLeftWidth',
      'borderTopWidth',
      'borderRightWidth',
      'borderBottomWidth',
      'paddingLeft',
      'paddingTop',
      'paddingRight',
      'paddingBottom',
      'lineHeight',
    ] as const satisfies (keyof CSSProperties)[];

    // Copy styles from textarea to div
    properties.forEach((prop) => {
      div.style[prop] = style[prop];
    });

    // Set div content and styles
    div.textContent = element.value.substring(0, position);
    div.style.position = 'absolute';
    div.style.visibility = 'hidden';
    div.style.whiteSpace = 'pre-wrap';
    div.style.wordWrap = 'break-word';
    div.style.overflow = 'hidden';
    div.style.width = element.offsetWidth + 'px';

    // Add a span to mark cursor position
    const span = document.createElement('span');
    span.textContent = element.value.charAt(position) || '.';
    div.appendChild(span);

    // Add to document, measure, then remove
    document.body.appendChild(div);
    const coordinates = {
      top:
        span.offsetTop +
        parseInt(style.borderTopWidth) +
        parseInt(style.paddingTop),
      left:
        span.offsetLeft +
        parseInt(style.borderLeftWidth) +
        parseInt(style.paddingLeft),
    };
    document.body.removeChild(div);

    return coordinates;
  };

  const handleSubmit = (e?: React.FormEvent) => {
    e?.preventDefault();

    // Block submission if files are still uploading/validating or have errors
    if (isUploading || hasValidationErrors) {
      return;
    }

    if (!hasContent || disabled || isSubmittingRef.current) return;

    // Check for inactive agent mentions before submitting
    const inactiveAgent = checkForInactiveAgents(localValue);
    if (inactiveAgent) {
      showActivationDialog(inactiveAgent);
      return;
    }

    // Set the submitting flag to prevent race conditions
    isSubmittingRef.current = true;

    // Capture message before clearing
    const messageContent = localValue.trim();

    try {
      // Use flushSync to ensure state updates are processed immediately
      flushSync(() => {
        setLocalValue('');
        // Call onChange directly instead of using the debounced version
        onChange('');
      });

      // Send the message with attachment IDs and resource ID
      onSendMessage(
        messageContent,
        completedAttachmentIds,
        selectedResourceId || undefined,
      );

      // Clear uploaded files and selected resource after sending
      clearAllFiles();
      handleResourceSelection(null);

      // Reset textarea height
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto';
      }
    } finally {
      // Reset the submitting flag
      isSubmittingRef.current = false;
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // Let the dropdowns handle their own keyboard events
    if (showResourceMention || showAgentMention) {
      return;
    }

    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();

      if (!isStreaming) {
        handleSubmit();
      }
    }
  };

  // Generic mention handler to reduce code duplication
  const handleMentionSelect = (
    rawValue: string,
    mentionType: 'resource' | 'agent',
    prefix: string,
    setShowMention: (show: boolean) => void,
    valueTransformer?: (value: string) => string,
  ) => {
    if (!textareaRef.current) return;

    const cursorPos = textareaRef.current.selectionStart;
    const textBeforeCursor = localValue.substring(0, cursorPos);
    const textAfterCursor = localValue.substring(cursorPos);

    // Get mention being typed using the utility function
    const mentionInfo = getIncompleteAtCursor(localValue, cursorPos);

    if (mentionInfo && mentionInfo.type === mentionType) {
      // Apply value transformation if provided
      const processedValue = valueTransformer
        ? valueTransformer(rawValue)
        : rawValue;

      // Construct new text with the replacement
      const mentionText = `${prefix}${processedValue}`;
      const newText =
        textBeforeCursor.substring(0, mentionInfo.startIndex) +
        mentionText +
        ' ' + // Add a space AFTER the entire mention
        textAfterCursor;

      // Update the input value
      setLocalValue(newText);
      onChange(newText);
      setShowMention(false);

      // Position the cursor after the mention and space
      setTimeout(() => {
        if (textareaRef.current) {
          const newCursorPos =
            mentionInfo.startIndex + `${mentionText} `.length;
          textareaRef.current.focus();
          textareaRef.current.setSelectionRange(newCursorPos, newCursorPos);
        }
      }, 0);
    }
  };

  // Handle resource mention selection
  const handleResourceSelect = (fullPath: string) => {
    handleMentionSelect(
      fullPath,
      'resource',
      '#',
      setShowResourceMention,
      (path) => path.replace(/\//g, '/').toLowerCase(),
    );
  };

  // Handle agent mention selection
  const handleAgentSelect = (agentName: string) => {
    handleMentionSelect(
      agentName,
      'agent',
      '@',
      setShowAgentMention,
      (name) => name.split(' (')[0], // Remove parenthetical information
    );
  };

  // File upload handlers
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = () => {
    fileInputRef.current?.click();
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length > 0) {
      addFiles(files);
    }
    // Reset input to allow selecting the same file again
    e.target.value = '';
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      addFiles(files);
    }
  };

  // Add autofocus functionality
  useEffect(() => {
    // Auto focus the textarea when component mounts
    if (textareaRef.current && !disabled) {
      // Small delay to ensure focus works properly after page load/navigation
      const timer = setTimeout(() => {
        textareaRef.current?.focus();
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [disabled]);

  const { highlightedText, hasMentions } = highlightMentions(localValue);

  // Check if user is out of quota
  const isOutOfQuota = quotaInfo?.quota_remaining === 0;

  return (
    <div className="flex grow flex-col justify-end overflow-y-auto pt-2">
      {/* Show quick start templates when conversation is empty */}
      <If condition={!isLoadingMessages} fallback={<PageSkeleton />}>
        {messages.length === 0 && !isStreaming && !localValue && (
          <div className="mb-4 flex w-full grow items-center justify-center overflow-y-auto">
            <div className="size-full py-6">
              <div className="flex flex-col items-center px-4 md:px-8 lg:px-12">
                <Heading level={2} className="mb-3 text-center">
                  Quick Start Templates
                </Heading>

                <div className="grid grid-cols-1 gap-4 @2xl:grid-cols-2">
                  {startTemplate.data.map((template) => {
                    const handleClick = (question: string) => {
                      setLocalValue(question);
                      if (onChange) onChange(question);
                      // Focus on the textarea after selecting a question
                      setTimeout(() => {
                        if (textareaRef.current) {
                          textareaRef.current.focus();
                          // Move cursor to the end of the text
                          const length = question.length;
                          textareaRef.current.setSelectionRange(length, length);
                        }
                      }, 100);
                    };

                    return (
                      <QuickStartCard
                        key={template.title}
                        onClick={handleClick}
                        template={template}
                      />
                    );
                  })}
                </div>
              </div>
            </div>
          </div>
        )}
      </If>

      {/* Quota Warning Banner - Always at bottom, outside flex grow */}
      {isOutOfQuota && (
        <div className="mb-4 shrink-0">
          <Link
            href={pathsConfig.app.dashboard + '?tab=usage'}
            className="block"
          >
            <div className="bg-destructive/10 border-destructive/20 text-destructive hover:bg-destructive/20 flex items-center gap-2 rounded-lg border px-3 py-2 text-sm font-medium transition-colors">
              <AlertTriangle className="size-4 flex-shrink-0" />
              <span>
                You&apos;re out of quota. Upgrade to continue using the service.
              </span>
            </div>
          </Link>
        </div>
      )}
      <div
        className={cn(
          'border-muted-foreground/10 bg-muted/30 hover:border-muted-foreground/20 dark:border-muted-foreground/20 dark:bg-muted/30 dark:hover:border-muted-foreground/30 shrink-0 overflow-hidden rounded-lg border shadow-md transition-all duration-200 hover:shadow-lg sm:rounded-lg',
          isOutOfQuota && 'pointer-events-none opacity-50',
        )}
      >
        {/* File Upload Area */}
        {files.length > 0 && (
          <div className="border-muted-foreground/10 border-b p-3">
            <FilePreviewList
              files={files}
              showPreview={true}
              onRemove={removeFile}
            />
          </div>
        )}

        {/* Message Input Row */}
        <form
          onSubmit={handleSubmit}
          className="relative"
          onDragOver={handleDragOver}
          onDrop={handleDrop}
        >
          {/* Resource Mention Dropdown */}
          <ResourceMentionDropdown
            isVisible={showResourceMention}
            position={resourceMentionPosition}
            filter={resourceMentionFilter}
            categories={dynamicResourceCategories}
            onSelect={handleResourceSelect}
            onClose={() => setShowResourceMention(false)}
          />

          {/* Agent Mention Dropdown */}
          <AgentMentionDropdown
            isVisible={showAgentMention}
            position={agentMentionPosition}
            filter={agentMentionFilter}
            onSelect={handleAgentSelect}
            onClose={() => setShowAgentMention(false)}
          />

          {/* Agent Activation Dialog */}
          {inactiveAgentInfo && (
            <AgentActivationDialog
              agentId={inactiveAgentInfo.id}
              agentName={inactiveAgentInfo.name}
              open={showAgentActivationDialog}
              onOpenChange={setShowAgentActivationDialog}
              onConfirm={() => handleActivationConfirm(handleSubmit)}
            >
              <Button
                variant="ghost"
                size="sm"
                className="hidden"
                data-test="agent-activation-trigger"
              >
                Activate Agent
              </Button>
            </AgentActivationDialog>
          )}

          {/* Message Input Container */}
          <div
            className={cn(
              'relative overflow-hidden transition-transform duration-150',
              isShaking && 'animate-shake',
            )}
          >
            {/* Display the textarea for input */}
            <Textarea
              ref={textareaRef}
              value={localValue}
              onChange={handleChange}
              onKeyDown={handleKeyDown}
              onPaste={handlePaste}
              onScroll={syncScroll}
              placeholder=""
              disabled={isStreaming ? false : disabled || isOutOfQuota}
              autoFocus={!disabled && !isOutOfQuota}
              className={cn(
                'w-full',
                'max-h-[200px] min-h-[60px] sm:max-h-[250px] sm:min-h-[80px]',
                'custom-scrollbar resize-none overflow-y-auto',
                'px-3 py-3 sm:px-4 sm:py-4',
                'border-none focus-visible:ring-0',
                'bg-transparent',
                'leading-relaxed',
                'text-base md:text-sm',
                // Only make text transparent when there are mentions, otherwise use normal text color
                hasMentions
                  ? 'caret-foreground selection:bg-primary/20 text-transparent'
                  : 'text-foreground caret-foreground',
                // Don't hide when empty to ensure cursor is visible
                'placeholder:opacity-0', // Use placeholder opacity instead
              )}
              rows={1}
            />

            {/* Display highlighted text overlay when there are mentions */}
            {hasMentions && (
              <div
                ref={overlayRef}
                className={cn(
                  'text-foreground overflow-thin-auto pointer-events-none absolute inset-0 h-full w-full px-3 py-3 text-sm leading-relaxed break-words whitespace-pre-wrap sm:px-4 sm:py-4',
                  'text-base md:text-sm',
                )}
              >
                {highlightedText}
              </div>
            )}

            {!localValue && files.length === 0 && (
              <ChatInputPlaceholder className="p-3 text-base sm:p-4 md:text-sm" />
            )}

            {!localValue && files.length > 0 && (
              <div className="pointer-events-none absolute inset-0 px-3 py-3 text-gray-400 sm:px-4 sm:py-4 dark:text-gray-500">
                <div className="text-base leading-relaxed md:text-sm">
                  {isUploading
                    ? 'Processing files...'
                    : hasValidationErrors
                      ? 'Fix file errors before sending'
                      : 'Type your message...'}
                </div>
              </div>
            )}
          </div>
        </form>

        {/* Model and Tools Row */}
        <div className="flex items-center gap-1 bg-transparent p-2 sm:gap-2 sm:p-3">
          {/* Plus Button Dropdown for File Upload and Resource Selection */}
          <PlusButtonDropdown
            onFileSelect={handleFileSelect}
            onResourceSelect={handleResourceSelection}
            selectedResourceId={selectedResourceId || null}
            disabled={disabled || isOutOfQuota}
            isStreaming={isStreaming}
          />

          {/* Resource Attachment Indicator */}
          {(selectedResource || conversationResource) && (
            <ResourceAttachmentIndicator
              selectedResource={selectedResource}
              conversationResource={conversationResource}
              onRemove={() => handleResourceSelection(null)}
            />
          )}

          {/* Hidden File Input */}
          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept="image/*,.pdf,.txt,.csv,.json,.docx,.xlsx"
            onChange={handleFileInputChange}
            className="hidden"
          />

          <div className="flex-1" />
          <ChatHelpDialog />

          {/* Scheduler Button */}
          <OperationHubDialog
            taskTemplate={{
              task: localValue.split('\n')[0] || 'New Task',
              context: localValue.trim(),
              schedule: '',
            }}
            hideStartNow={true}
          >
            <WithTooltip tooltip="Create a scheduled task from this message">
              <Button
                size="icon"
                variant="ghost"
                disabled={!localValue.trim() || disabled || isOutOfQuota}
                className="text-muted-foreground hover:bg-muted/70 hover:text-foreground dark:hover:bg-muted/30 h-8 w-8 rounded-full transition-all duration-200 active:scale-95"
              >
                <CalendarIcon className="h-4 w-4" />
                <span className="sr-only">Schedule Task</span>
              </Button>
            </WithTooltip>
          </OperationHubDialog>

          {/* Send/Stop Button */}
          <ActionButton
            isStreaming={isStreaming}
            hasContent={hasContent}
            disabled={
              disabled || isUploading || hasValidationErrors || isOutOfQuota
            }
            onSubmit={handleSubmit}
            onStop={onStopStreaming}
          />
        </div>
      </div>
    </div>
  );
}
