import React from 'react';

import { cn } from '@/lib/utils';

// MentionHighlight component - simplified to just use gradient text
export const MentionHighlight = ({
  type,
  children,
  className,
}: {
  type: 'resource' | 'agent';
  children: React.ReactNode;
  className?: string;
}) => {
  return (
    <span
      className={cn(
        type === 'resource'
          ? 'mention-resource-gradient'
          : 'mention-agent-gradient',
        className,
      )}
    >
      {children}
    </span>
  );
};

// Function to process text and highlight mentions more efficiently
export function highlightMentions(text: string): {
  highlightedText: React.ReactNode[];
  hasMentions: boolean;
} {
  if (!text) {
    return {
      highlightedText: [],
      hasMentions: false,
    };
  }

  // For very large texts, chunk processing to avoid performance issues
  if (text.length > 10000) {
    const chunks = [];
    const chunkSize = 5000;

    for (let i = 0; i < text.length; i += chunkSize) {
      chunks.push(text.substring(i, i + chunkSize));
    }

    let allHighlightedText: React.ReactNode[] = [];
    let anyMentionsFound = false;

    chunks.forEach((chunk, chunkIndex) => {
      const result = processTextChunk(chunk, chunkIndex * chunkSize);
      allHighlightedText = [...allHighlightedText, ...result.highlightedText];
      if (result.hasMentions) {
        anyMentionsFound = true;
      }
    });

    return {
      highlightedText: allHighlightedText,
      hasMentions: anyMentionsFound,
    };
  }

  // For normal sized texts, process directly
  return processTextChunk(text, 0);
}

// Helper function to process a chunk of text
function processTextChunk(
  text: string,
  startIndex: number,
): {
  highlightedText: React.ReactNode[];
  hasMentions: boolean;
} {
  // Improved regex for more precise matching
  // @mentions: Stop at whitespace or special characters
  // #mentions: Include the resource path but stop at whitespace
  const regex =
    /(@[^\s@#:;,.!?()[\]{}]+)|(#[^@#\s:;,.!?()[\]{}]+(?:\/[^@#\s:;,.!?()[\]{}]+)*)/g;

  let lastIndex = 0;
  const formattedText: React.ReactNode[] = [];
  let mentionsFound = false;
  let match;

  // Find all mentions in the text
  while ((match = regex.exec(text)) !== null) {
    // Add any text before this mention
    if (match.index > lastIndex) {
      formattedText.push(text.substring(lastIndex, match.index));
    }

    const mention = match[0];

    // Determine mention type (@agent or #resource)
    if (mention.startsWith('@')) {
      mentionsFound = true;
      formattedText.push(
        <MentionHighlight
          key={`agent-${startIndex + match.index}`}
          type="agent"
        >
          {mention}
        </MentionHighlight>,
      );
    } else if (mention.startsWith('#')) {
      mentionsFound = true;
      formattedText.push(
        <MentionHighlight
          key={`resource-${startIndex + match.index}`}
          type="resource"
        >
          {mention}
        </MentionHighlight>,
      );
    }

    lastIndex = match.index + mention.length;
  }

  // Add remaining text after last mention
  if (lastIndex < text.length) {
    formattedText.push(text.substring(lastIndex));
  }

  return {
    highlightedText: formattedText,
    hasMentions: mentionsFound,
  };
}

// Helper function to get precise cursor position data
export function getMentionAtPosition(
  text: string,
  cursorPosition: number,
): {
  type: 'resource' | 'agent' | null;
  startIndex: number;
  endIndex: number;
  value: string;
} | null {
  if (!text || cursorPosition < 0 || cursorPosition > text.length) {
    return null;
  }

  // Check if cursor is within a mention
  const regex =
    /(@[^\s@#:;,.!?()[\]{}]+)|(#[^@#\s:;,.!?()[\]{}]+(?:\/[^@#\s:;,.!?()[\]{}]+)*)/g;
  let match;

  while ((match = regex.exec(text)) !== null) {
    const startIndex = match.index;
    const endIndex = startIndex + match[0].length;

    // If cursor position is within this mention
    if (cursorPosition >= startIndex && cursorPosition <= endIndex) {
      return {
        type: match[0].startsWith('@') ? 'agent' : 'resource',
        startIndex,
        endIndex,
        value: match[0],
      };
    }
  }

  return null;
}

// Function to get mention being typed at cursor position
export function getIncompleteAtCursor(
  text: string,
  cursorPos: number,
): {
  type: 'resource' | 'agent' | null;
  startIndex: number;
  filter: string;
} | null {
  if (!text || cursorPos <= 0) return null;

  const textBeforeCursor = text.substring(0, cursorPos);

  // Check for # symbol (resource mention being typed)
  const hashSymbolMatch = textBeforeCursor.match(/#([^@#\s:;,.!?()[\]{}]*)$/);
  if (hashSymbolMatch) {
    return {
      type: 'resource',
      startIndex: textBeforeCursor.lastIndexOf('#'),
      filter: hashSymbolMatch[1] || '',
    };
  }

  // Check for @ symbol (agent mention being typed)
  const atSymbolMatch = textBeforeCursor.match(/@([^@#\s:;,.!?()[\]{}]*)$/);
  if (atSymbolMatch) {
    return {
      type: 'agent',
      startIndex: textBeforeCursor.lastIndexOf('@'),
      filter: atSymbolMatch[1] || '',
    };
  }

  return null;
}
