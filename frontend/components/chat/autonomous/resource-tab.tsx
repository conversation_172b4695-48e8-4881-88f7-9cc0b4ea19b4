'use client';

import { useRef } from 'react';

import { ScrollArea } from '@/components/ui/scroll-area';
import { Skeleton } from '@/components/ui/skeleton';
import { recommendationQuery } from '@/features/recommendation/hooks/recommendation.query';
import { resourceQuery } from '@/features/resource/hooks/resource.query';
import { AlertCircle } from 'lucide-react';

import { CostRecommendationsCard } from './resource-tab/cost-recommendations-card';
import { ResourceInfoCard } from './resource-tab/resource-info-card';
import {
  ResourceRecommendationsSession,
  ResourceRecommendationsSessionRef,
} from './resource-tab/resource-recommendations-session';
import {
  SummarySavingsCard,
  SummaryStatsCard,
} from './resource-tab/summary-cards';

interface ResourceTabProps {
  resourceId?: string | null;
}

export function ResourceTab({ resourceId }: ResourceTabProps) {
  const recommendationsSessionRef =
    useRef<ResourceRecommendationsSessionRef>(null);

  const hasResourceId = !!resourceId;

  const {
    data: resource,
    isLoading,
    error,
  } = resourceQuery.query.useById(resourceId as string, {
    enabled: hasResourceId,
  });

  // Overall recommendations when no resourceId
  const { data: overal, isLoading: overalLoading } =
    recommendationQuery.query.useOveral();

  if (isLoading || (!hasResourceId && overalLoading)) {
    return (
      <div className="space-y-6 p-4 sm:p-6">
        <div className="grid grid-cols-1 gap-4 sm:gap-6 lg:grid-cols-2">
          {Array.from({ length: 2 }).map((_, i) => (
            <Skeleton key={i} className="h-[120px] w-full" />
          ))}
        </div>
      </div>
    );
  }

  if (hasResourceId && (error || !resource)) {
    return (
      <div className="flex h-full items-center justify-center p-4 sm:p-6">
        <div className="max-w-sm space-y-3 text-center">
          <AlertCircle className="text-muted-foreground mx-auto h-8 w-8 sm:h-10 sm:w-10" />
          <p className="text-muted-foreground px-4 text-sm sm:text-base">
            {error ? 'Failed to load resource' : 'Resource not found'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <ScrollArea className="size-full">
      <div className="w-full space-y-6 px-4 pt-4 sm:px-6 sm:pt-6">
        {/* Header cards */}
        {hasResourceId && resource ? (
          <div className="grid grid-cols-1 gap-4 sm:gap-6 lg:grid-cols-2">
            <ResourceInfoCard resource={resource} />
            <CostRecommendationsCard
              totalRecommendation={resource.total_recommendation}
              totalPotentialSaving={resource.total_potential_saving}
            />
          </div>
        ) : overal ? (
          <div className="grid grid-cols-1 gap-4 sm:gap-6 lg:grid-cols-2">
            <SummaryStatsCard
              totalResources={overal.total_resource_scanned}
              optimized={overal.total_well_optimized}
              opportunities={overal.total_optimization_opportunities}
            />
            <SummarySavingsCard amount={overal.total_estimated_saving_amount} />
          </div>
        ) : null}

        {/* Recommendations Session */}
        <div className="pt-2">
          <ResourceRecommendationsSession
            ref={recommendationsSessionRef}
            resourceId={resourceId || undefined}
          />
        </div>
      </div>
    </ScrollArea>
  );
}
