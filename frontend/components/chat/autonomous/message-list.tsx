import React, { useMemo } from 'react';

import { Message as MessageType } from '@/components/chat/types';
import { Button } from '@/components/ui/button';
import { If } from '@/components/ui/common/if';
import { ScrollArea } from '@/components/ui/scroll-area';
import { InterruptConfirmation } from '@/hooks/message-stream';
import { cn } from '@/lib/utils';
import { AnimatePresence, motion } from 'framer-motion';
import { ArrowDown } from 'lucide-react';

import { ThinkingAnimation } from '../components/common/thinking-animation';
import { useScrollHandling } from '../hooks/useScrollHandling';
import { highlightMentions } from '../utils/mention-highlighting';
import { AutonomousMessage } from './message';

// Constants
const ANIMATION_DURATION = {
  FADE: 0.2,
  SLIDE: 0.3,
};

interface MessageListProps {
  messages: MessageType[];
  isLoading?: boolean;
  confirmation?: InterruptConfirmation | null;
  className?: string;
  disableDisplayComponents?: boolean;
  disableToolCalls?: boolean;
  groupChatOnly?: boolean;
}

export function AutonomousMessageList({
  messages,
  isLoading,
  confirmation,
  disableDisplayComponents = false,
  disableToolCalls = false,
  groupChatOnly = false,
}: MessageListProps) {
  const messagesEndRef = React.useRef<HTMLDivElement>(null);

  // Filter out system messages for counting
  const visibleMessages = useMemo(() => {
    return messages.filter((message) => message.role !== 'system');
  }, [messages]);

  // Use the custom scroll handling hook
  const { scrollAreaRef, showScrollButton, unreadCount, scrollToBottom } =
    useScrollHandling({
      isLoading,
      messageCount: visibleMessages.length,
    });

  // Auto-scroll to bottom when new messages arrive
  React.useEffect(() => {
    if (unreadCount > 0 || (isLoading && visibleMessages.length > 0)) {
      scrollToBottom('smooth', true);
    }
  }, [unreadCount, isLoading, visibleMessages.length, scrollToBottom]);

  // Track if we should show the thinking state
  const showThinkingState = React.useMemo(() => {
    // Show thinking when loading and either:
    // 1. No messages yet, or
    // 2. Last message is from user (waiting for response), or
    // 3. Last assistant message is empty (stream hasn't started yet)
    if (!isLoading) return false;

    if (messages.length === 0) return true;

    const lastMessage = messages[messages.length - 1];

    if (lastMessage.role === 'user') return true;

    // If last message is from assistant but has no content yet, still show thinking
    if (
      lastMessage.role === 'assistant' &&
      (!lastMessage.content || lastMessage.content.trim() === '')
    )
      return true;

    return false;
  }, [messages, isLoading]);

  // Memoize message rendering to prevent unnecessary re-renders
  const renderMessages = React.useMemo(() => {
    const filteredMessages = messages.filter(
      (message) => message.role !== 'system',
    );

    return filteredMessages.map((message, index) => {
      // Create a proper contentDisplay if needed
      let contentDisplay = undefined;

      // Only process if it's a user message (AI messages are handled by MessageContent)
      if (message.role === 'user' && typeof message.content === 'string') {
        const { highlightedText, hasMentions } = highlightMentions(
          message.content,
        );
        if (hasMentions) {
          contentDisplay = (
            <div className="whitespace-pre-wrap">{highlightedText}</div>
          );
        }
      }

      return (
        <div key={`${message.id}-${index}`} className="w-full max-w-full">
          <div className="transition-all duration-300">
            <AutonomousMessage
              message={message}
              showStreaming={
                isLoading && message === messages[messages.length - 1]
              }
              confirmation={
                message === messages[messages.length - 1] ? confirmation : null
              }
              contentDisplay={contentDisplay}
              disableDisplayComponents={disableDisplayComponents}
              disableToolCalls={disableToolCalls}
              groupChatOnly={groupChatOnly}
            />
          </div>
        </div>
      );
    });
  }, [
    confirmation,
    disableDisplayComponents,
    disableToolCalls,
    groupChatOnly,
    isLoading,
    messages,
  ]);

  return (
    <div className="relative h-full flex-1 overflow-hidden">
      <ScrollArea ref={scrollAreaRef} className="h-full px-2" type="hover">
        <div className="flex flex-col gap-3 pb-4" ref={messagesEndRef}>
          {renderMessages}
          {/* Thinking indicator at the bottom */}
          <If condition={showThinkingState}>
            <ThinkingAnimation />
          </If>
        </div>
      </ScrollArea>

      {/* Single scroll button - positioned at the bottom right */}
      <AnimatePresence>
        {showScrollButton && messages.length > 0 && (
          <motion.div
            className={cn('absolute right-6 bottom-4', 'z-10')}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ duration: ANIMATION_DURATION.FADE }}
          >
            <Button
              size="sm"
              variant="secondary"
              onClick={() => scrollToBottom('smooth', true)}
              className="relative gap-2 rounded-full px-4 shadow-md will-change-transform"
            >
              {unreadCount > 0 && (
                <div className="bg-primary text-primary-foreground absolute -top-1 -right-1 flex h-5 min-w-5 items-center justify-center rounded-full text-xs font-medium">
                  {unreadCount}
                </div>
              )}
              <ArrowDown className="size-4" />
              <span className="text-xs">
                {unreadCount > 0
                  ? `${unreadCount} new message${unreadCount > 1 ? 's' : ''}`
                  : 'Latest'}
              </span>
            </Button>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
