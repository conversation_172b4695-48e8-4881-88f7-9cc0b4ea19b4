'use client';

import React, { useEffect, useRef } from 'react';

import { But<PERSON> } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { DashboardWidget } from '@/features/dashboard';
import { SchemaDashboard } from '@/openapi-ts/gens';
import { Minimize2 } from 'lucide-react';

import DashboardGrid from './dashboard-grid';
import DashboardHeader from './dashboard-header';

interface FullscreenDashboardModalProps {
  dashboard: SchemaDashboard;
  isOpen: boolean;
  onClose: () => void;
}

const FullscreenDashboardModal: React.FC<FullscreenDashboardModalProps> = ({
  dashboard,
  isOpen,
  onClose,
}) => {
  const modalRef = useRef<HTMLDivElement>(null);

  // Handle ESC key
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen, onClose]);

  // Handle click outside
  const handleBackdropClick = (event: React.MouseEvent) => {
    if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
      onClose();
    }
  };

  if (!isOpen) return null;

  // Parse dashboard data if it's a string
  let dashboardData: SchemaDashboard;
  try {
    if (typeof dashboard === 'string') {
      dashboardData = JSON.parse(dashboard);
    } else if (typeof dashboard === 'object' && dashboard !== null) {
      dashboardData = dashboard;
    } else {
      dashboardData = dashboard;
    }
  } catch {
    dashboardData = dashboard;
  }

  // Extract grid config with fallback
  const gridConfig = dashboardData.grid_config?.columns
    ? { columns: dashboardData.grid_config.columns }
    : { columns: 12 };

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm"
      onClick={handleBackdropClick}
    >
      <div
        ref={modalRef}
        className="bg-background relative flex h-[95vh] w-[95vw] max-w-none flex-col rounded-lg border shadow-2xl"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Close Button */}
        <div className="absolute top-4 right-4 z-10">
          <Button
            variant="outline"
            size="sm"
            onClick={onClose}
            className="bg-background/80 gap-2 backdrop-blur-sm"
            title="Exit Fullscreen (ESC)"
          >
            <Minimize2 className="h-4 w-4" />
            <span className="hidden sm:inline">Exit Fullscreen</span>
          </Button>
        </div>

        {/* Dashboard Content */}
        <ScrollArea className="flex-1">
          <div className="w-full p-6">
            {/* Dashboard Header */}
            {dashboardData?.title && (
              <DashboardHeader
                title={dashboardData.title}
                description={dashboardData.description}
                createdAt={dashboardData.created_at}
                // Don't show expand button in fullscreen mode
                onExpandFullscreen={undefined}
              />
            )}

            {/* Dashboard Grid */}
            {dashboardData?.widgets && Array.isArray(dashboardData.widgets) && (
              <DashboardGrid
                widgets={dashboardData.widgets as DashboardWidget[]}
                gridConfig={gridConfig}
              />
            )}

            {/* Fallback if no structured content exists but dashboard has data */}
            {dashboardData &&
              !dashboardData.title &&
              !dashboardData.widgets && (
                <div className="prose prose-sm max-w-none">
                  <pre className="bg-muted/30 text-foreground rounded-lg p-4 text-sm whitespace-pre-wrap">
                    {JSON.stringify(dashboardData, null, 2)}
                  </pre>
                </div>
              )}
          </div>
        </ScrollArea>
      </div>
    </div>
  );
};

export default FullscreenDashboardModal;
