'use client';

import React, { ReactNode, createContext, useContext, useState } from 'react';

import { SchemaDashboard } from '@/openapi-ts/gens';

interface FullscreenContextType {
  isFullscreen: boolean;
  dashboard: SchemaDashboard | null;
  openFullscreen: (dashboard: SchemaDashboard) => void;
  closeFullscreen: () => void;
}

const FullscreenContext = createContext<FullscreenContextType | undefined>(
  undefined,
);

export function FullscreenProvider({ children }: { children: ReactNode }) {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [dashboard, setDashboard] = useState<SchemaDashboard | null>(null);

  const openFullscreen = (dashboard: SchemaDashboard) => {
    setDashboard(dashboard);
    setIsFullscreen(true);
    // Prevent body scroll when fullscreen is open
    document.body.style.overflow = 'hidden';
  };

  const closeFullscreen = () => {
    setIsFullscreen(false);
    setDashboard(null);
    // Restore body scroll
    document.body.style.overflow = 'auto';
  };

  return (
    <FullscreenContext.Provider
      value={{
        isFullscreen,
        dashboard,
        openFullscreen,
        closeFullscreen,
      }}
    >
      {children}
    </FullscreenContext.Provider>
  );
}

export function useFullscreen() {
  const context = useContext(FullscreenContext);
  if (context === undefined) {
    throw new Error('useFullscreen must be used within a FullscreenProvider');
  }
  return context;
}
