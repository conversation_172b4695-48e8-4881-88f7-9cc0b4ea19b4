'use client';

import { Suspense, useEffect, useMemo } from 'react';

import dynamic from 'next/dynamic';

import { StructuredConsoleView } from '@/components/console/structured-console-view';
import { PageSkeleton } from '@/components/ui/common/page';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, Tabs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { AgentCardList } from '@/features/agent/components/agent-card-list';
import { AgentProvider } from '@/features/agent/provider/agent-provider';
import { useChatContext } from '@/features/chat/context/chat-context';
import { useDashboard } from '@/features/dashboard';
import { useReport } from '@/features/report';
import { cn } from '@/lib/utils';
import { SchemaMessageDisplayComponentPublic } from '@/openapi-ts/gens';
import { Message } from '@/types/chat';
import { updateLRUCache } from '@/utils/object';
import {
  BoxIcon,
  FileChartColumn,
  LayoutDashboardIcon,
  LucideIcon,
  TerminalIcon,
  UsersIcon,
} from 'lucide-react';
import { useLocalStorage } from 'usehooks-ts';

import { ToolCall } from '../types';

// Dynamically import tab components
const ReportTab = dynamic(() => import('./report-tab'));
const DashboardTab = dynamic(() =>
  import('./dashboard-tab').then((mod) => mod.DashboardTab),
);
// const ChartsTab = dynamic(() => import('./charts-tab'));
const ResourceTab = dynamic(() =>
  import('./resource-tab').then((mod) => mod.ResourceTab),
);
// const ConsoleCanvas = dynamic(() =>
//   import('./console-canvas').then((mod) => mod.ConsoleCanvas),
// );

interface ChartsCanvasProps {
  toolCalls: ToolCall[];
  className?: string;
  lastMessage?: Message;
  onPlanningToolsChange: (tools: ToolCall[]) => void;
  isEmptyConversation: boolean;
}

enum EConversationTabValue {
  Resource = 'resource',
  Report = 'report',
  Dashboard = 'dashboard',
  Console = 'console',
  Teams = 'teams',
  Workspace = 'workspace',
}

interface TabConfig {
  id: EConversationTabValue;
  icon: LucideIcon;
  label: React.ReactNode;
  enabled: boolean;
  content: React.ReactNode;
}

// Combined interface for all items that can be shown in the canvas

export interface CanvasItem {
  id: string;
  type: 'toolCall' | 'displayComponent';
  timestamp: Date;
  content: ToolCall | SchemaMessageDisplayComponentPublic;
  status?: 'running' | 'completed' | 'error';
  agentName?: string;
  agentRole?: string;
  messageIndex: number; // Changed from optional to required
}

// Local storage key for active tab
const ACTIVE_TAB_STORAGE_KEY = 'canvas-active-tab';

export function ChartsCanvas({
  toolCalls,
  className,
  onPlanningToolsChange,
  isEmptyConversation,
}: ChartsCanvasProps) {
  const {
    currentReport,
    currentDashboard,
    conversationId,
    hasReport,
    hasDashboard,
    conversationResourceId,
    selectedResourceId,
  } = useChatContext();

  const currentConversationIdOrNew = conversationId ?? 'new-conversation';
  const [activeTab, setActiveTab] = useLocalStorage<
    Record<string, EConversationTabValue>
  >(ACTIVE_TAB_STORAGE_KEY, {
    [currentConversationIdOrNew]: EConversationTabValue.Resource,
  });

  // Get initial report data using the hook - only enabled if hasReport is true
  const { report: initialReport } = useReport(conversationId || null, {
    enabled: hasReport,
  });

  // Get initial dashboard data using the hook - only enabled if hasDashboard is true
  const { dashboard: initialDashboard } = useDashboard(conversationId || null, {
    enabled: hasDashboard,
  });

  // Determine the current report to display (streaming takes precedence over initial)
  const displayReport = currentReport || initialReport;

  // Determine the current dashboard to display (streaming takes precedence over initial)
  const displayDashboard = currentDashboard || initialDashboard;

  // Process planning tool calls - keep this to populate planningTools but remove tab switching
  useEffect(() => {
    // Only process planning tools if the conversation is not empty
    if (!isEmptyConversation && toolCalls && toolCalls.length > 0) {
      // Extract planning tool calls by name
      const planningToolCalls = toolCalls.filter(
        (tool) =>
          tool.name === 'planning' ||
          tool.name.includes('planning') ||
          tool.name.includes('plan'),
      );

      // Update planning tools state if there are planning tools
      if (planningToolCalls.length > 0) {
        onPlanningToolsChange(planningToolCalls);
      }
    } else if (isEmptyConversation) {
      // Clear planning tools for empty conversations
      onPlanningToolsChange([]);
    }
  }, [toolCalls, onPlanningToolsChange, isEmptyConversation]);

  // Use selectedResourceId if available, otherwise fall back to conversationResourceId
  const activeResourceId = selectedResourceId || conversationResourceId;

  const tabs = useMemo(() => {
    const tabs: TabConfig[] = [
      {
        id: EConversationTabValue.Resource,
        icon: BoxIcon,
        label: 'Resource',
        enabled: true,
        content: <ResourceTab resourceId={activeResourceId} />,
      },
      {
        id: EConversationTabValue.Report,
        icon: FileChartColumn,
        label: 'Report',
        enabled: !!displayReport,
        content: displayReport && <ReportTab report={displayReport} />,
      },
      {
        id: EConversationTabValue.Dashboard,
        icon: LayoutDashboardIcon,
        label: 'Dashboard',
        enabled: !!displayDashboard,
        content: displayDashboard && (
          <DashboardTab dashboard={displayDashboard} />
        ),
      },
      // {
      //   id: 'charts',
      //   label: 'Chart & Table',
      //   requiresData: (data) => data.canvasItems.length > 0,
      //   renderContent: (data) =>
      //     data.canvasItems.length > 0 && (
      //       <ChartsTab canvasItems={data.canvasItems} />
      //     ),
      // },
      // {
      //   id: EConversationTabValue.Workspace,
      //   label: 'Workspace',
      //   enabled: true,
      //   content: (
      //     <ConsoleCanvas
      //       // TODO: Remove isVisible
      //       isVisible
      //       isUserActivated={true}
      //       conversationId={conversationId}
      //       toolCalls={toolCalls}
      //     />
      //   ),
      // },
      {
        id: EConversationTabValue.Console,
        icon: TerminalIcon,
        label: 'Console',
        enabled: true,
        content: <StructuredConsoleView toolCalls={toolCalls} />,
      },
      {
        id: EConversationTabValue.Teams,
        icon: UsersIcon,
        label: 'Teams',
        enabled: true,
        content: (
          <AgentProvider>
            <AgentCardList className="overflow-y-auto" />
          </AgentProvider>
        ),
      },
    ];

    return tabs.filter((tab) => tab.enabled);
  }, [activeResourceId, displayDashboard, displayReport, toolCalls]);

  // Determine the initial active tab
  const initialActiveTab = useMemo(() => {
    // If we have a stored active tab and it exists in current tabs, use it
    if (
      activeTab[currentConversationIdOrNew] &&
      tabs.some((tab) => tab.id === activeTab[currentConversationIdOrNew])
    ) {
      return activeTab[currentConversationIdOrNew];
    }

    // Otherwise, use the first available tab (fallback to priority order)
    return tabs.length > 0 ? tabs[0].id : '';
  }, [activeTab, currentConversationIdOrNew, tabs]);

  return (
    <div
      className={cn(
        '@container z-10 flex h-full flex-col shadow-lg max-md:pt-2',
        className,
      )}
    >
      <Tabs
        value={initialActiveTab}
        onValueChange={(newActiveTab) =>
          setActiveTab((prev) =>
            updateLRUCache(
              prev,
              currentConversationIdOrNew,
              newActiveTab as EConversationTabValue,
            ),
          )
        }
        className="flex h-full flex-col"
      >
        <TabsList>
          {tabs.map((tab) => (
            <TabsTrigger key={tab.id} value={tab.id} className="gap-2">
              <tab.icon className="size-4" />
              {tab.label}
            </TabsTrigger>
          ))}
        </TabsList>
        <ScrollArea className="grow">
          {tabs.map((tab) => (
            <TabsContent
              key={tab.id}
              value={tab.id}
              className="h-full grow overflow-hidden"
            >
              <Suspense fallback={<PageSkeleton />}>{tab.content}</Suspense>
            </TabsContent>
          ))}
        </ScrollArea>
      </Tabs>
    </div>
  );
}
