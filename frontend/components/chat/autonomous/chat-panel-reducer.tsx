import { SchemaMessageDisplayComponentPublic } from '@/openapi-ts/gens';

import { ToolCall } from '../types';

export const DEFAULT_CHAT_PANEL_SIZE = 65;
export const DEFAULT_CANVAS_PANEL_SIZE = 35;
export const PANEL_SIZES_STORAGE_KEY = 'autonomous-chat-panel-sizes';

export interface CanvasItemWithAgent {
  id: string;
  type: 'toolCall' | 'displayComponent';
  content: ToolCall | SchemaMessageDisplayComponentPublic;
  agentName?: string;
  agentRole?: string;
  messageIndex: number; // Track original message position
}

type ChatPanelAction =
  | { type: 'SET_ACTIVE_TAB'; payload: string }
  | { type: 'SET_UNREAD_COUNT'; payload: number }
  | { type: 'INCREMENT_UNREAD_COUNT' }
  | { type: 'RESET_UNREAD_COUNT' }
  | { type: 'TOGGLE_CANVAS'; payload: boolean }
  | { type: 'SET_MANUALLY_HIDDEN'; payload: boolean }
  | { type: 'SET_INPUT_VALUE'; payload: string }
  | { type: 'SET_HIDDEN_ROLES'; payload: string[] }
  | { type: 'TOGGLE_ROLE_VISIBILITY'; payload: string }
  | { type: 'UPDATE_TOOL_CALLS'; payload: ToolCall[] }
  | { type: 'UPDATE_PANEL_SIZES'; payload: number[] }
  | { type: 'RESET_STATE'; payload?: { sessionId?: string } }
  | { type: 'UPDATE_RESOURCE_ID'; payload: string }
  | { type: 'UPDATE_CANVAS_ITEMS'; payload: CanvasItemWithAgent[] }
  | { type: 'UPDATE_PLANNING_TOOLS'; payload: ToolCall[] };

interface ChatPanelState {
  activeTab: string;
  unreadCount: number;
  isCanvasVisible: boolean;
  manuallyHidden: boolean;
  inputValue: string;
  hiddenRoles: string[];
  activeToolCalls: ToolCall[];
  displayComponents: SchemaMessageDisplayComponentPublic[];
  canvasItemsWithAgent: CanvasItemWithAgent[];
  panelSizes: number[];
  resourceId?: string;
  planningTools: ToolCall[];
}

export const initialState: ChatPanelState = {
  activeTab: 'chat',
  unreadCount: 0,
  isCanvasVisible: true,
  manuallyHidden: false,
  inputValue: '',
  hiddenRoles: ['assistant', 'system'],
  activeToolCalls: [],
  displayComponents: [],
  canvasItemsWithAgent: [],
  panelSizes: [DEFAULT_CHAT_PANEL_SIZE, DEFAULT_CANVAS_PANEL_SIZE],
  planningTools: [],
};

export function chatPanelReducer(
  state: ChatPanelState,
  action: ChatPanelAction,
): ChatPanelState {
  switch (action.type) {
    case 'SET_ACTIVE_TAB':
      return {
        ...state,
        activeTab: action.payload,
        unreadCount: action.payload === 'chat' ? 0 : state.unreadCount,
      };

    case 'SET_UNREAD_COUNT':
      return { ...state, unreadCount: action.payload };

    case 'INCREMENT_UNREAD_COUNT':
      return { ...state, unreadCount: state.unreadCount + 1 };

    case 'RESET_UNREAD_COUNT':
      return { ...state, unreadCount: 0 };

    case 'TOGGLE_CANVAS':
      return {
        ...state,
        isCanvasVisible: action.payload,
        manuallyHidden: action.payload ? false : true,
      };

    case 'SET_MANUALLY_HIDDEN':
      return { ...state, manuallyHidden: action.payload };

    case 'SET_INPUT_VALUE':
      return { ...state, inputValue: action.payload };

    case 'SET_HIDDEN_ROLES':
      return { ...state, hiddenRoles: action.payload };

    case 'TOGGLE_ROLE_VISIBILITY':
      return {
        ...state,
        hiddenRoles: state.hiddenRoles.includes(action.payload)
          ? state.hiddenRoles.filter((r) => r !== action.payload)
          : [...state.hiddenRoles, action.payload],
      };

    case 'UPDATE_TOOL_CALLS':
      return {
        ...state,
        activeToolCalls: action.payload,
      };

    case 'UPDATE_PANEL_SIZES':
      try {
        localStorage.setItem(
          PANEL_SIZES_STORAGE_KEY,
          JSON.stringify(action.payload),
        );
      } catch (error) {
        console.error('Failed to save panel sizes to localStorage:', error);
      }
      return {
        ...state,
        panelSizes: action.payload,
      };

    case 'RESET_STATE':
      return {
        ...initialState,
        activeTab: 'chat',
        manuallyHidden: false,
        isCanvasVisible: true,
        hiddenRoles: state.hiddenRoles,
        panelSizes: state.panelSizes,
        planningTools: [],
      };

    case 'UPDATE_RESOURCE_ID':
      return { ...state, resourceId: action.payload };

    case 'UPDATE_CANVAS_ITEMS':
      return {
        ...state,
        canvasItemsWithAgent: action.payload,
        displayComponents: action.payload
          .filter((item) => item.type === 'displayComponent')
          .map((item) => item.content as SchemaMessageDisplayComponentPublic),
        isCanvasVisible: state.isCanvasVisible,
      };

    case 'UPDATE_PLANNING_TOOLS':
      return {
        ...state,
        planningTools: action.payload,
      };

    default:
      return state;
  }
}
