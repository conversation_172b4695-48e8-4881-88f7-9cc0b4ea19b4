'use client';

import { createElement } from 'react';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { RESOURCE_TYPE_CONFIG } from '@/features/resource/config/resource-type.config';
import { AWSResourceType } from '@/features/resource/config/resource-type.config';
import { AlertCircle, MapPin } from 'lucide-react';

interface ResourceInfoCardProps {
  resource: {
    name: string;
    type: string;
    region?: string;
    status: string;
  };
}

export function ResourceInfoCard({ resource }: ResourceInfoCardProps) {
  const resourceConfig =
    RESOURCE_TYPE_CONFIG.CONFIG[resource.type as AWSResourceType];

  const statusColor =
    {
      running: 'bg-green-500',
      active: 'bg-green-500',
      stopped: 'bg-red-500',
      inactive: 'bg-red-500',
    }[resource.status.toLowerCase()] || 'bg-yellow-500';

  return (
    <Card className="min-h-[120px] min-w-0 flex-1">
      <CardContent className="flex h-full flex-col p-4 sm:p-6">
        <div className="flex items-start gap-3 sm:gap-4">
          <div className="flex flex-col items-center">
            <div className="bg-muted flex shrink-0 items-center justify-center rounded-lg p-1">
              {createElement(resourceConfig?.icon || AlertCircle, {
                className: 'size-6 sm:size-8',
              })}
            </div>
            <Badge variant="secondary" className="font-large text-xs">
              {resource.type}
            </Badge>
          </div>
          <div className="min-w-0 flex-1 space-y-3">
            <h2 className="text-base leading-tight font-semibold break-words sm:text-lg">
              {resource.name}
            </h2>
            <div className="text-muted-foreground flex items-center gap-2 text-sm">
              <MapPin className="size-4 text-blue-500" />
              <span className="font-medium">{resource.region}</span>
              {resource.region && (
                <span className="text-muted-foreground/60">•</span>
              )}
              <div className={`h-2.5 w-2.5 rounded-full ${statusColor}`} />
              <span className="text-sm font-medium capitalize">
                {resource.status}
              </span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
