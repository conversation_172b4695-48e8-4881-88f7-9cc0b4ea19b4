'use client';

import { Card, CardContent } from '@/components/ui/card';
import { DollarSign, Target } from 'lucide-react';

interface CostRecommendationsCardProps {
  totalRecommendation: number;
  totalPotentialSaving: number;
}

export function CostRecommendationsCard({
  totalRecommendation,
  totalPotentialSaving,
}: CostRecommendationsCardProps) {
  return (
    <Card className="min-h-[120px] min-w-0 flex-1">
      <CardContent className="flex h-full flex-col p-3 sm:p-4">
        <div className="mb-2 flex shrink-0 items-center justify-between sm:mb-3">
          <p className="text-muted-foreground text-xs font-medium sm:text-sm">
            Cost & Recommendations
          </p>
          <Target className="text-muted-foreground/70 size-3 sm:size-4" />
        </div>
        <div className="flex items-start gap-3 sm:gap-4">
          <div className="min-w-0 flex-1 space-y-3">
            <div className="grid grid-cols-1 gap-3 sm:grid-cols-2">
              <div className="space-y-1">
                <p className="text-muted-foreground text-xs">Recommendations</p>
                <div className="flex items-center gap-1">
                  <Target className="size-4 text-blue-500" />
                  <span className="text-sm font-medium">
                    {totalRecommendation}
                  </span>
                </div>
              </div>
              <div className="space-y-1">
                <p className="text-muted-foreground text-xs">
                  Potential Savings
                </p>
                <div className="flex items-center gap-1">
                  <DollarSign className="size-4 text-green-500" />
                  <span className="text-sm font-medium text-green-600">
                    {totalPotentialSaving.toFixed(2)}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
