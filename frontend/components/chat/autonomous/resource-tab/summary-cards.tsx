'use client';

import { Card, CardContent } from '@/components/ui/card';
import { DollarSign, Layers, ListChecks } from 'lucide-react';

export function SummaryStatsCard(props: {
  totalResources: number;
  optimized: number;
  opportunities: number;
}) {
  const { totalResources, optimized, opportunities } = props;
  return (
    <Card className="min-h-[120px] min-w-0 flex-1">
      <CardContent className="flex h-full flex-col p-3 sm:p-4">
        <div className="mb-2 flex shrink-0 items-center justify-between sm:mb-3">
          <p className="text-muted-foreground text-xs font-medium sm:text-sm">
            Overall Optimization
          </p>
          <ListChecks className="text-muted-foreground/70 size-3 sm:size-4" />
        </div>
        <div className="grid grid-cols-3 gap-3">
          <div className="space-y-1">
            <p className="text-muted-foreground text-xs">Scanned</p>
            <div className="flex items-center gap-1">
              <Layers className="size-4 text-blue-500" />
              <span className="text-sm font-medium">{totalResources}</span>
            </div>
          </div>
          <div className="space-y-1">
            <p className="text-muted-foreground text-xs">Well Optimized</p>
            <div className="flex items-center gap-1">
              <ListChecks className="size-4 text-emerald-500" />
              <span className="text-sm font-medium">{optimized}</span>
            </div>
          </div>
          <div className="space-y-1">
            <p className="text-muted-foreground text-xs">Opportunities</p>
            <div className="flex items-center gap-1">
              <ListChecks className="size-4 text-amber-500" />
              <span className="text-sm font-medium">{opportunities}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export function SummarySavingsCard(props: { amount: number }) {
  const { amount } = props;
  return (
    <Card className="min-h-[120px] min-w-0 flex-1">
      <CardContent className="flex h-full flex-col p-3 sm:p-4">
        <div className="mb-2 flex shrink-0 items-center justify-between sm:mb-3">
          <p className="text-muted-foreground text-xs font-medium sm:text-sm">
            Estimated Savings
          </p>
          <DollarSign className="text-muted-foreground/70 size-3 sm:size-4" />
        </div>
        <div className="space-y-1">
          <p className="text-muted-foreground text-xs">Potential Savings</p>
          <div className="flex items-center gap-1">
            <DollarSign className="size-4 text-green-500" />
            <span className="text-sm font-medium text-green-600">
              {amount.toFixed(2)}
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
