'use client';

import { memo, useCallback, useEffect, useMemo, useReducer } from 'react';

import { If } from '@/components/ui/common/if';
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from '@/components/ui/resizable';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useChatContext } from '@/features/chat/context/chat-context';
import { useIsMobile } from '@/hooks/use-mobile';
import { cn } from '@/lib/utils';
import { SchemaMessageDisplayComponentPublic } from '@/openapi-ts/gens';
import { MessageSquareIcon, TerminalIcon } from 'lucide-react';

import { PlanningSession } from '../components/planning-session';
import { MessageInput } from '../message-input';
import { ToolCall } from '../types';
import { ChartsCanvas } from './canvas';
import {
  Chat<PERSON><PERSON>lHeaderActionsContent,
  ChatPanelHeaderContent,
} from './chat-panel-header';
import {
  CanvasItemWithAgent,
  DEFAULT_CANVAS_PANEL_SIZE,
  DEFAULT_CHAT_PANEL_SIZE,
  PANEL_SIZES_STORAGE_KEY,
  chatPanelReducer,
  initialState,
} from './chat-panel-reducer';
import { AutonomousMessageList } from './message-list';

const MemoizedAutonomousMessageList = memo(AutonomousMessageList);

export function ChatPanel() {
  const context = useChatContext();

  // Initialize state with saved panel sizes if available
  const initialStateWithSavedSizes = useMemo(() => {
    let sizes = [DEFAULT_CHAT_PANEL_SIZE, DEFAULT_CANVAS_PANEL_SIZE];
    if (typeof window !== 'undefined') {
      try {
        const savedSizes = localStorage.getItem(PANEL_SIZES_STORAGE_KEY);
        if (savedSizes) {
          const parsedSizes = JSON.parse(savedSizes);
          if (Array.isArray(parsedSizes) && parsedSizes.length === 2) {
            sizes = parsedSizes;
          }
        }
      } catch (error) {
        console.error('Failed to load panel sizes from localStorage:', error);
      }
    }

    // Always set canvas to visible by default
    return {
      ...initialState,
      isCanvasVisible: true,
      manuallyHidden: false,
      panelSizes: sizes,
    };
  }, []);

  // Use the memoized initial state
  const [state, dispatch] = useReducer(
    chatPanelReducer,
    initialStateWithSavedSizes,
  );

  const isMobile = useIsMobile();

  // Reset state when session changes
  useEffect(() => {
    if (context.currentSession?.id) {
      // Only reset non-essential parts of state to preserve context
      dispatch({
        type: 'RESET_STATE',
        payload: {
          sessionId: context.currentSession.id,
        },
      });
    }
  }, [context.currentSession?.id]);

  // Update context when resourceId changes
  useEffect(() => {
    if (context.resourceId) {
      dispatch({
        type: 'UPDATE_RESOURCE_ID',
        payload: context.resourceId,
      });
    }
  }, [context.resourceId]);

  // Combined effect to extract both display components and tool calls from messages
  useEffect(() => {
    const extractedDisplayComponents: CanvasItemWithAgent[] = [];
    const allToolCalls: ToolCall[] = [];
    const toolsWithAgentInfo: CanvasItemWithAgent[] = [];

    // Process messages to extract both display components and tool calls
    context.messages.forEach((message, messageIndex) => {
      // Extract agent information from the message
      const agentName = message.role !== 'user' ? message.role : undefined;
      const agentRole = message.role;

      // Extract display components with agent information
      if (message.displayComponents && message.displayComponents.length > 0) {
        message.displayComponents.forEach((component) => {
          extractedDisplayComponents.push({
            id: component.id,
            type: 'displayComponent',
            content: component as SchemaMessageDisplayComponentPublic,
            agentName,
            agentRole,
            messageIndex,
          });
        });
      }

      // Extract tool calls with agent information
      if (message.toolCalls && message.toolCalls.length > 0) {
        message.toolCalls.forEach((toolCall) => {
          // Create a new tool call with agent info
          const toolCallWithAgent = {
            ...toolCall,
            agentName: agentName || 'assistant',
            agentRole: agentRole || 'assistant',
          };

          // Add to the plain tool calls array with agent info attached
          allToolCalls.push(toolCallWithAgent);

          // Add to the tool calls with agent info
          toolsWithAgentInfo.push({
            id: toolCall.id,
            type: 'toolCall',
            content: toolCallWithAgent, // Use the enhanced tool call with agent info
            agentName,
            agentRole,
            messageIndex,
          });
        });
      }
    });

    // Combine both display components and tool calls
    const allCanvasItems = [
      ...extractedDisplayComponents,
      ...toolsWithAgentInfo,
    ];

    // Update state with extracted components - use a single combined action
    dispatch({ type: 'UPDATE_CANVAS_ITEMS', payload: allCanvasItems });

    // Update state with all extracted tool calls
    if (allToolCalls.length > 0) {
      dispatch({ type: 'UPDATE_TOOL_CALLS', payload: allToolCalls });
    }
  }, [context.messages, context.currentSession?.id, context.isStreaming]);

  const handleNewMessage = useCallback(
    (content: string, attachmentIds?: string[], resourceId?: string) => {
      context.onSendMessage(content, attachmentIds, resourceId);

      // Clear the input value when sending a message
      dispatch({ type: 'SET_INPUT_VALUE', payload: '' });

      // Increment badge counter if not on group-chat tab
      if (state.activeTab !== 'group-chat') {
        dispatch({ type: 'INCREMENT_UNREAD_COUNT' });
      }
    },
    [state.activeTab, context.onSendMessage],
  );

  const handleInputChange = useCallback((value: string) => {
    dispatch({ type: 'SET_INPUT_VALUE', payload: value });
  }, []);

  // Handle panel resize
  const handlePanelResize = useCallback((sizes: number[]) => {
    dispatch({ type: 'UPDATE_PANEL_SIZES', payload: sizes });
  }, []);

  // Handle planning tools change
  const handlePlanningToolsChange = useCallback((tools: ToolCall[]) => {
    dispatch({ type: 'UPDATE_PLANNING_TOOLS', payload: tools });
  }, []);

  // Simplified check for input disabled state
  const isInputDisabled = context.isStreaming;

  const renderChatArea = () => {
    return (
      <div className="@container flex h-full flex-col">
        <div className="flex justify-between">
          <ChatPanelHeaderContent
            resourceId={state.resourceId || context.resourceId}
          />

          <ChatPanelHeaderActionsContent />
        </div>

        <div className="overflow-thin-auto flex grow flex-col">
          {/* Chat tab content */}
          <div
            className={cn('flex h-full w-full flex-col', {
              hidden: state.activeTab !== 'chat',
            })}
          >
            <div className="custom-scrollbar grow overflow-y-auto">
              <MemoizedAutonomousMessageList
                messages={context.messages}
                isLoading={context.isStreaming}
                confirmation={
                  state.activeTab === 'chat' ? context.confirmation : null
                }
              />
            </div>
            <div
              className={cn('flex w-full flex-col justify-end', {
                'grow overflow-y-auto': context.messages.length === 0,
              })}
            >
              <PlanningSession />
              <MessageInput
                value={state.inputValue}
                onChange={handleInputChange}
                onSendMessage={handleNewMessage}
                disabled={isInputDisabled}
              />
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderChartCanvas = () => {
    return (
      <ChartsCanvas
        toolCalls={state.activeToolCalls}
        onPlanningToolsChange={handlePlanningToolsChange}
        isEmptyConversation={
          context.messages.length === 0 && !context.isStreaming
        }
      />
    );
  };

  return (
    <div
      className="flex h-full w-full flex-col"
      style={{
        contain: 'layout size style',
        boxSizing: 'border-box',
        position: 'relative',
        maxHeight: '100dvh',
        transform: 'translateZ(0)',
        backfaceVisibility: 'hidden',
        overscrollBehavior: 'none',
      }}
    >
      {/* Main container with proper layout for tool canvas */}
      <div
        className="relative flex h-full w-full flex-col"
        style={{
          contain: 'layout size style',
          boxSizing: 'border-box',
          position: 'relative',
        }}
      >
        {/* Main content */}
        <div
          className="flex h-full w-full flex-1 flex-col overflow-hidden"
          style={{
            position: 'relative',
            contain: 'layout size style',
          }}
        >
          <If
            condition={!isMobile}
            fallback={
              <>
                <Tabs
                  defaultValue="chat"
                  className="flex size-full flex-col gap-4 overflow-hidden"
                >
                  <ScrollArea className="flex grow flex-col">
                    <TabsContent
                      value="chat"
                      className="mt-0 h-full grow overflow-hidden"
                    >
                      {renderChatArea()}
                    </TabsContent>
                    <TabsContent
                      value="console"
                      className="mt-0 h-full grow overflow-hidden"
                    >
                      {renderChartCanvas()}
                    </TabsContent>
                  </ScrollArea>
                  <TabsList className="w-full [&>*]:flex-1 [&>*]:gap-2">
                    <TabsTrigger value="chat">
                      <MessageSquareIcon className="size-4" />
                      Chat
                    </TabsTrigger>
                    <TabsTrigger value="console">
                      <TerminalIcon className="size-4" />
                      Console
                    </TabsTrigger>
                  </TabsList>
                </Tabs>
              </>
            }
          >
            <ResizablePanelGroup
              direction="horizontal"
              onLayout={handlePanelResize}
              className="relative h-full py-2"
            >
              {/* Main chat area panel */}
              <ResizablePanel
                defaultSize={state.panelSizes[0]}
                minSize={30}
                className="border-none shadow"
              >
                {renderChatArea()}
              </ResizablePanel>

              {/* Resize handle */}
              <ResizableHandle className="w-2 cursor-col-resize bg-transparent pl-2" />

              {/* Charts & Data Canvas panel */}
              <ResizablePanel
                defaultSize={state.panelSizes[1]}
                minSize={20}
                className="shadow-primary/20 rounded-lg border p-2 shadow-lg"
              >
                {renderChartCanvas()}
              </ResizablePanel>
            </ResizablePanelGroup>
          </If>
        </div>
      </div>
    </div>
  );
}
