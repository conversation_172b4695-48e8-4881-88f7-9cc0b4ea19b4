import React, { useRef } from 'react';

import { ScrollArea } from '@/components/ui/scroll-area';
import { SchemaReport } from '@/openapi-ts/gens';
import { FileText } from 'lucide-react';

import ExecutiveSummary from './report-content/executive-summary';
import ReportHeader from './report-content/report-header';
import ReportSection from './report-content/report-section';

export interface ReportTabProps {
  report?: SchemaReport | null;
}

const ReportTab: React.FC<ReportTabProps> = ({ report }) => {
  const reportContentRef = useRef<HTMLDivElement>(null);

  const handleExportPDF = async () => {
    if (!reportContentRef.current || !report) return;

    // TODO: Implement PDF export functionality
    console.log('PDF export will be implemented later');

    // The reportContentRef.current contains the HTML element to export
    // You can add your custom implementation here
  };

  // Empty state when no report exists
  if (!report) {
    return (
      <div className="flex flex-1 items-center justify-center">
        <div className="mx-auto flex max-w-md flex-col items-center gap-4 p-6 text-center">
          <div className="bg-muted/50 flex h-16 w-16 items-center justify-center rounded-full">
            <FileText className="text-muted-foreground h-8 w-8" />
          </div>
          <div>
            <h3 className="text-foreground mb-2 text-lg font-semibold">
              No Report Generated
            </h3>
            <p className="text-muted-foreground text-sm leading-relaxed">
              Reports will appear here when generated by the agent. Ask the
              agent to create a report to see the analysis and findings by using
              #report.
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Parse report data if it's a string
  let reportData: SchemaReport;
  try {
    if (typeof report === 'string') {
      reportData = JSON.parse(report);
    } else if (typeof report === 'object' && report !== null) {
      reportData = report;
    } else {
      // Invalid report data, show empty state
      return (
        <div className="flex flex-1 items-center justify-center">
          <div className="mx-auto flex max-w-md flex-col items-center gap-4 p-6 text-center">
            <div className="bg-muted/50 flex h-16 w-16 items-center justify-center rounded-full">
              <FileText className="text-muted-foreground h-8 w-8" />
            </div>
            <div>
              <h3 className="text-foreground mb-2 text-lg font-semibold">
                Invalid Report Data
              </h3>
              <p className="text-muted-foreground text-sm leading-relaxed">
                The report data appears to be corrupted. Please try refreshing
                or ask the agent to regenerate the report.
              </p>
            </div>
          </div>
        </div>
      );
    }
  } catch {
    // If parsing fails, show error state
    return (
      <div className="flex flex-1 items-center justify-center">
        <div className="mx-auto flex max-w-md flex-col items-center gap-4 p-6 text-center">
          <div className="bg-muted/50 flex h-16 w-16 items-center justify-center rounded-full">
            <FileText className="text-muted-foreground h-8 w-8" />
          </div>
          <div>
            <h3 className="text-foreground mb-2 text-lg font-semibold">
              Report Parsing Error
            </h3>
            <p className="text-muted-foreground text-sm leading-relaxed">
              Unable to parse the report data. Please try refreshing or ask the
              agent to regenerate the report.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-full flex-1 flex-col">
      <ScrollArea className="flex-1">
        <div ref={reportContentRef} className="mx-auto max-w-full p-6">
          {/* Report Header */}
          {reportData?.title && (
            <ReportHeader
              title={reportData.title}
              description={reportData.description ?? ''}
              createdAt={reportData.created_at}
              _onExportPDF={handleExportPDF}
              reportId={reportData.id}
              shareId={reportData.share_id}
              isShared={reportData.is_shared}
            />
          )}

          {/* Executive Summary */}
          {reportData?.executive_summary && (
            <ExecutiveSummary data={reportData.executive_summary} />
          )}

          {/* Report Sections */}
          {reportData?.sections && Array.isArray(reportData.sections) && (
            <div className="space-y-8">
              {reportData.sections
                .sort((a, b) => (a.index || 0) - (b.index || 0))
                .map((section, index) => (
                  <ReportSection
                    key={`section-${section.index || index}`}
                    section={section}
                  />
                ))}
            </div>
          )}

          {/* Fallback if no structured content exists but report has data */}
          {reportData &&
            !reportData.title &&
            !reportData.sections &&
            !reportData.executive_summary && (
              <div className="prose prose-sm max-w-none">
                <pre className="bg-muted/30 text-foreground rounded-lg p-4 text-sm whitespace-pre-wrap">
                  {JSON.stringify(reportData, null, 2)}
                </pre>
              </div>
            )}
        </div>
      </ScrollArea>
    </div>
  );
};

export default ReportTab;
