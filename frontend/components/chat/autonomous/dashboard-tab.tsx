import React, { useRef } from 'react';

import { ScrollArea } from '@/components/ui/scroll-area';
import {
  DashboardWidget,
  GridConfig,
} from '@/features/dashboard/models/dashboard.type';
import { SchemaDashboard } from '@/openapi-ts/gens';
import { LayoutDashboard } from 'lucide-react';

import DashboardGrid from './dashboard-content/dashboard-grid';
import DashboardHeader from './dashboard-content/dashboard-header';
import {
  FullscreenProvider,
  useFullscreen,
} from './dashboard-content/fullscreen-context';
import FullscreenDashboardModal from './dashboard-content/fullscreen-dashboard-modal';

interface DashboardTabProps {
  dashboard: SchemaDashboard;
}

// Internal component that has access to fullscreen context
const DashboardTabContent: React.FC<{ dashboard: SchemaDashboard }> = ({
  dashboard,
}) => {
  const dashboardContentRef = useRef<HTMLDivElement>(null);
  const { openFullscreen, isFullscreen, closeFullscreen } = useFullscreen();

  const handleExportPDF = async () => {
    if (!dashboardContentRef.current || !dashboard) return;
    console.log('Dashboard PDF export will be implemented later');
  };

  const handleExpandFullscreen = () => {
    openFullscreen(dashboard);
  };

  // Parse dashboard data if it's a string
  let dashboardData: SchemaDashboard;
  try {
    if (typeof dashboard === 'string') {
      dashboardData = JSON.parse(dashboard);
    } else if (typeof dashboard === 'object' && dashboard !== null) {
      dashboardData = dashboard;
    } else {
      // Invalid dashboard data, show empty state
      return (
        <div className="flex flex-1 items-center justify-center">
          <div className="mx-auto flex max-w-md flex-col items-center gap-4 p-6 text-center">
            <div className="bg-muted/50 flex h-16 w-16 items-center justify-center rounded-full">
              <LayoutDashboard className="text-muted-foreground h-8 w-8" />
            </div>
            <div>
              <h3 className="text-foreground mb-2 text-lg font-semibold">
                Invalid Dashboard Data
              </h3>
              <p className="text-muted-foreground text-sm leading-relaxed">
                The dashboard data appears to be corrupted. Please try
                refreshing or ask the agent to regenerate the dashboard.
              </p>
            </div>
          </div>
        </div>
      );
    }
  } catch {
    // If parsing fails, show error state
    return (
      <div className="flex flex-1 items-center justify-center">
        <div className="mx-auto flex max-w-md flex-col items-center gap-4 p-6 text-center">
          <div className="bg-muted/50 flex h-16 w-16 items-center justify-center rounded-full">
            <LayoutDashboard className="text-muted-foreground h-8 w-8" />
          </div>
          <div>
            <h3 className="text-foreground mb-2 text-lg font-semibold">
              Dashboard Parsing Error
            </h3>
            <p className="text-muted-foreground text-sm leading-relaxed">
              Unable to parse the dashboard data. Please try refreshing or ask
              the agent to regenerate the dashboard.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-full flex-1 flex-col">
      <ScrollArea className="flex-1">
        <div ref={dashboardContentRef} className="w-full py-2 md:px-4">
          {/* Dashboard Header */}
          {dashboardData?.title && (
            <DashboardHeader
              title={dashboardData.title}
              description={dashboardData.description}
              createdAt={dashboardData.created_at}
              onExportPDF={handleExportPDF}
              onExpandFullscreen={handleExpandFullscreen}
              dashboardId={dashboardData.id}
              shareId={dashboardData.share_id}
              isShared={dashboardData.is_shared}
            />
          )}

          {/* Dashboard Grid */}
          {dashboardData?.widgets && Array.isArray(dashboardData.widgets) && (
            <DashboardGrid
              widgets={dashboardData.widgets as DashboardWidget[]}
              gridConfig={
                (dashboardData.grid_config as GridConfig) || { columns: 12 }
              }
            />
          )}

          {/* Fallback if no structured content exists but dashboard has data */}
          {dashboardData && !dashboardData.title && !dashboardData.widgets && (
            <div className="prose prose-sm max-w-none">
              <pre className="bg-muted/30 text-foreground rounded-lg p-4 text-sm whitespace-pre-wrap">
                {JSON.stringify(dashboardData, null, 2)}
              </pre>
            </div>
          )}
        </div>
      </ScrollArea>

      {/* Fullscreen Modal */}
      <FullscreenDashboardModal
        dashboard={dashboard}
        isOpen={isFullscreen}
        onClose={closeFullscreen}
      />
    </div>
  );
};

// Main component with provider
export const DashboardTab: React.FC<DashboardTabProps> = ({ dashboard }) => {
  return (
    <FullscreenProvider>
      <DashboardTabContent dashboard={dashboard} />
    </FullscreenProvider>
  );
};
