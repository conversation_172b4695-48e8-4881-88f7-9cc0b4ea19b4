'use client';

import React, {
  ComponentProps,
  RefObject,
  useCallback,
  useMemo,
  useRef,
  useState,
} from 'react';

import { Button } from '@/components/ui/button';
import { If } from '@/components/ui/common/if';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { InterruptConfirmation } from '@/hooks/message-stream';
import { useMessageFeedback } from '@/hooks/use-message-feedback';
import { cn } from '@/lib/utils';
import { ThumbsDown, ThumbsUp } from 'lucide-react';
import { useHover } from 'usehooks-ts';

import { BotAvatar } from '../components/common/bot-icon';
import { MessageAttachments } from '../components/message/message-attachments';
import { MessageContent } from '../components/message/message-content';
import { NewMessageAttachments } from '../components/message/new-message-attachments';
import { Message as ChatMessage } from '../types';
import { highlightMentions } from '../utils/mention-highlighting';
import { FeedbackDialog } from './feedback-dialog';

interface MessageProps {
  message: ChatMessage;
  showStreaming?: boolean;
  confirmation?: InterruptConfirmation | null;
  contentDisplay?: React.ReactNode;
  disableDisplayComponents?: boolean;
  disableToolCalls?: boolean;
  groupChatOnly?: boolean;
}

export const AutonomousMessage = React.memo(function AutonomousMessage({
  message,
  showStreaming = true,
  confirmation,
  contentDisplay,
  disableDisplayComponents = false,
  disableToolCalls = false,
  groupChatOnly = false,
}: MessageProps) {
  const isAI = message.role != 'user';

  // For AI messages, use structured fields directly
  const messageRef = useRef<HTMLDivElement>(null);

  // For user messages, just show the content
  // For AI messages, pass toolCalls, displayComponents, and empty contentParts
  const messageRenderProps = useMemo((): ComponentProps<
    typeof MessageContent
  > => {
    return {
      toolCalls: isAI && !disableToolCalls ? message.toolCalls : undefined,
      displayComponents:
        isAI && !disableDisplayComponents
          ? message.displayComponents
          : undefined,
      isStreaming: showStreaming,
      confirmation,
      groupChatOnly,
    };
  }, [
    confirmation,
    disableDisplayComponents,
    disableToolCalls,
    groupChatOnly,
    isAI,
    message.displayComponents,
    message.toolCalls,
    showStreaming,
  ]);

  // Feedback functionality
  const { feedback, submitFeedback, isSubmitting } = useMessageFeedback(
    message.id,
  );
  const [feedbackDialogOpen, setFeedbackDialogOpen] = useState(false);

  // Handle like immediately without dialog
  const handleLike = useCallback(() => {
    submitFeedback('good');
  }, [submitFeedback]);

  // Handle dislike - show simplified dialog
  const handleDislike = useCallback(() => {
    setFeedbackDialogOpen(true);
  }, []);

  const handleFeedbackSubmit = useCallback(
    (reason: string) => {
      submitFeedback('bad', reason);
      setFeedbackDialogOpen(false);
    },
    [submitFeedback],
  );

  // Reference for the message content
  const messageContentRef = useRef<HTMLDivElement>(null);
  const isHovering = useHover(messageContentRef as RefObject<HTMLDivElement>);

  return (
    <div
      className={cn(
        'flex w-full flex-col py-2',
        !isAI ? 'items-end' : 'items-start',
      )}
      ref={messageRef}
    >
      <div className="flex w-full items-center">
        <div
          className={cn(
            'relative flex flex-col gap-1',
            !isAI
              ? 'ml-auto max-w-[60%] items-end'
              : 'w-full flex-1 items-start',
          )}
        >
          <If
            condition={isAI}
            fallback={
              <>
                {/* Historical attachments (from database) */}
                {message.attachments && message.attachments.length > 0 && (
                  <div className="mb-2">
                    <MessageAttachments attachments={message.attachments} />
                  </div>
                )}

                {/* Newly sent attachments (using attachment IDs) */}
                {message.attachmentIds &&
                  message.attachmentIds.length > 0 &&
                  !message.attachments && (
                    <NewMessageAttachments
                      attachmentIds={message.attachmentIds}
                    />
                  )}
              </>
            }
          >
            <BotAvatar role={message.role} variant="default" />
          </If>

          <div
            className={cn(
              'group w-full',
              isAI
                ? 'relative min-h-10'
                : 'bg-primary/10 text-foreground relative rounded-lg border px-3 py-1.5',
            )}
            ref={messageContentRef}
          >
            <If
              condition={isAI}
              fallback={
                <div className="flex min-h-8 items-center text-sm">
                  {contentDisplay || (
                    <ProcessUserContent content={message.content} />
                  )}
                </div>
              }
            >
              <MessageContent {...messageRenderProps} />
            </If>
          </div>
          {/* Action buttons container */}
          <div
            className={cn(
              'mt-1 flex items-center gap-1 transition-opacity duration-200',
              isHovering ? 'opacity-100' : 'opacity-0',
              !isAI ? 'justify-end' : 'justify-start', // Align buttons based on sender
            )}
          >
            {/* Like, Dislike, Copy buttons - show only for AI messages and when not streaming */}
            {isAI && !showStreaming && (
              <>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        size="icon"
                        variant="ghost"
                        className={cn(
                          'text-muted-foreground hover:bg-accent hover:text-accent-foreground h-6 w-6 rounded-full',
                          feedback?.feedback_type === 'good' &&
                            'bg-green-50 text-green-600 hover:bg-green-100 dark:bg-green-900/30 dark:text-green-400 dark:hover:bg-green-900/50',
                        )}
                        onClick={handleLike}
                        disabled={isSubmitting}
                      >
                        <ThumbsUp className="h-3.5 w-3.5" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent side="bottom">
                      <p>
                        {feedback?.feedback_type === 'good' ? 'Liked' : 'Like'}
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        size="icon"
                        variant="ghost"
                        className={cn(
                          'text-muted-foreground hover:bg-accent hover:text-accent-foreground h-6 w-6 rounded-full',
                          feedback?.feedback_type === 'bad' &&
                            'bg-red-50 text-red-600 hover:bg-red-100 dark:bg-red-900/30 dark:text-red-400 dark:hover:bg-red-900/50',
                        )}
                        onClick={handleDislike}
                        disabled={isSubmitting}
                      >
                        <ThumbsDown className="h-3.5 w-3.5" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent side="bottom">
                      <p>
                        {feedback?.feedback_type === 'bad'
                          ? 'Disliked'
                          : 'Dislike'}
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Simplified Feedback Dialog - only for dislike */}
      <FeedbackDialog
        open={feedbackDialogOpen}
        onOpenChange={setFeedbackDialogOpen}
        feedbackType="bad"
        onSubmit={handleFeedbackSubmit}
        isSubmitting={isSubmitting}
      />
    </div>
  );
});

const ProcessUserContent = ({ content }: { content: string }) => {
  const { highlightedText, hasMentions } = highlightMentions(content);

  return (
    <div className="whitespace-pre-wrap">
      <If condition={hasMentions} fallback={content}>
        {highlightedText}
      </If>
    </div>
  );
};
