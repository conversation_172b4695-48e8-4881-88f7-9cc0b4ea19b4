'use client';

import { useMemo } from 'react';

import { ConsoleTerminal, StructuredConsoleView } from '@/components/console';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useUserActivation } from '@/hooks/use-user-activation';
import { LayoutIcon, TerminalIcon } from 'lucide-react';

import { ToolCall } from '../types';

interface ConsoleCanvasProps {
  isVisible: boolean;
  className?: string;
  isUserActivated?: boolean;
  conversationId?: string;
  toolCalls?: ToolCall[];
}

/**
 * ConsoleCanvas - Main container component for console functionality
 * Orchestrates view switching between structured tool view, interactive console, and file explorer
 * Now provides enhanced workflow management with structured tool execution display
 */
export const ConsoleCanvas = ({
  isVisible,
  isUserActivated = false,
  conversationId,
  toolCalls,
}: ConsoleCanvasProps) => {
  // Custom hooks for managing complex state
  const { hasBeenUserActivated, shouldActivateTerminal } =
    useUserActivation(isUserActivated);

  const workspaceTabs = useMemo(
    () =>
      [
        {
          label: 'Tool View',
          icon: LayoutIcon,
          value: 'structured',
          content: <StructuredConsoleView toolCalls={toolCalls} />,
        },
        {
          label: 'Console',
          icon: TerminalIcon,
          value: 'interactive',
          content: (
            <ConsoleTerminal
              shouldActivateTerminal={shouldActivateTerminal}
              hasBeenUserActivated={hasBeenUserActivated}
              isVisible={isVisible}
              conversationId={conversationId}
            />
          ),
        },
        // {
        //   label: 'Explorer',
        //   icon: FolderIcon,
        //   value: 'explorer',
        //   content: (
        //     <FileExplorer
        //       isUserActivated={isUserActivated}
        //       conversationId={conversationId}
        //     />
        //   ),
        // },
      ] as const,
    [
      conversationId,
      hasBeenUserActivated,
      isVisible,
      shouldActivateTerminal,
      toolCalls,
    ],
  );

  if (!isVisible) return null;

  return (
    <div className="flex h-full w-full flex-col">
      {/* View Toggle Header */}
      <Tabs
        defaultValue={workspaceTabs[0].value}
        className="flex size-full flex-col"
      >
        <TabsList>
          {workspaceTabs.map((tab) => (
            <TabsTrigger key={tab.value} value={tab.value} className="gap-2">
              <tab.icon className="size-4" />
              {tab.label}
            </TabsTrigger>
          ))}
        </TabsList>
        <ScrollArea className="flex grow flex-col pr-2">
          {workspaceTabs.map((tab) => (
            <TabsContent
              key={tab.value}
              value={tab.value}
              className="h-full grow overflow-hidden"
            >
              {tab.content}
            </TabsContent>
          ))}
        </ScrollArea>
      </Tabs>
    </div>
  );
};
