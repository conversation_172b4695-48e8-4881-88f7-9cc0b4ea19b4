import { RecommendationData } from '@/components/chat/components/message/tables/common/types';

import {
  StreamingRecommendation,
  TransformedRecommendationData,
} from './types';

export const transformStreamingRowToRecommendation = (
  headers: string[],
  row: any[],
): TransformedRecommendationData | null => {
  if (!row || !Array.isArray(row) || !headers || headers.length === 0) {
    return null;
  }

  const findColumnValue = (columnNames: string[]): string => {
    for (const columnName of columnNames) {
      const index = headers.findIndex(
        (h) => h && h.toLowerCase().includes(columnName.toLowerCase()),
      );
      if (index !== -1 && row[index] != null) {
        return String(row[index]);
      }
    }
    return '';
  };

  const findNumericValue = (columnNames: string[]): number => {
    for (const columnName of columnNames) {
      const index = headers.findIndex(
        (h) => h && h.toLowerCase().includes(columnName.toLowerCase()),
      );
      if (index !== -1 && row[index] != null) {
        const value = row[index];
        if (typeof value === 'number') return value;
        if (typeof value === 'string') {
          // Handle "None" case
          if (value.toLowerCase() === 'none' || value.toLowerCase() === 'n/a')
            return 0;
          // Remove currency symbols and parse
          const cleaned = value.replace(/[$,]/g, '');
          const parsed = parseFloat(cleaned);
          if (!isNaN(parsed)) return parsed;
        }
      }
    }
    return 0;
  };

  return {
    title: findColumnValue(['title']) || 'Untitled Recommendation',
    description: findColumnValue(['description']) || 'No description available',
    type: findColumnValue(['type']) || 'optimization',
    potential_savings: findNumericValue([
      'monthly savings ($)',
      'monthly savings',
      'savings',
      'potential_savings',
    ]),
    effort: findColumnValue(['effort']) || 'medium',
    risk: findColumnValue(['risk']) || 'low',
  };
};

export const transformStreamingToRecommendation = (
  data: RecommendationData,
): TransformedRecommendationData | null => {
  if (!data || !data.headers || !data.rows || data.rows.length === 0) {
    return null;
  }

  const headers = data.headers.map((h) => h?.header || '');
  const firstRow = data.rows[0];

  return transformStreamingRowToRecommendation(headers, firstRow);
};

export const generateTempId = (): string => {
  return `temp_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
};

export const createStreamingRecommendations = (
  data: RecommendationData,
): StreamingRecommendation[] => {
  if (!data || !data.headers || !data.rows || data.rows.length === 0) {
    return [];
  }

  const headers = data.headers.map((h) => h?.header || h || '');
  const recommendations: StreamingRecommendation[] = [];

  // Create a recommendation for each row
  for (let i = 0; i < data.rows.length; i++) {
    const row = data.rows[i];
    const transformedData = transformStreamingRowToRecommendation(headers, row);

    if (transformedData) {
      // Create a unique data object for this row
      const rowData: RecommendationData = {
        headers: data.headers,
        rows: [row], // Only this specific row
      };

      recommendations.push({
        tempId: generateTempId(),
        type: 'streaming',
        data: rowData,
        status: 'pending',
        transformedData,
      });
    }
  }

  return recommendations;
};

export const createStreamingRecommendation = (
  data: RecommendationData,
): StreamingRecommendation | null => {
  const recommendations = createStreamingRecommendations(data);
  return recommendations.length > 0 ? recommendations[0] : null;
};

