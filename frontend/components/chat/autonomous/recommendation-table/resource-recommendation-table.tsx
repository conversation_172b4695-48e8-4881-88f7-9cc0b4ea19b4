import React, { useMemo, useState } from 'react';

import { If } from '@/components/ui/common/if';
import { Heading } from '@/components/ui/heading';
import { InputIconPrefix } from '@/components/ui/input-icon-prefix';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { TableUI } from '@/components/ui/table/table-ui';
import {
  ColumnFiltersState,
  SortingState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { SearchIcon } from 'lucide-react';

import { createResourceRecommendationColumns } from './resource-recommendation-columns';
import {
  ResourceRecommendationTableProps,
  UnifiedRecommendation,
} from './types';

export const ResourceRecommendationTable: React.FC<
  ResourceRecommendationTableProps
> = ({
  savedRecommendations,
  streamingRecommendations,
  onSaveRecommendation,
  onRemoveRecommendation,
  isLoading = false,
  meta,
  onPageChange,
  onSearchChange,
}) => {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);

  // Combine saved and streaming recommendations
  const unifiedData: UnifiedRecommendation[] = useMemo(() => {
    const saved: UnifiedRecommendation[] = savedRecommendations.map((rec) => ({
      type: 'saved' as const,
      data: rec,
    }));

    const streaming: UnifiedRecommendation[] = streamingRecommendations.map(
      (rec) => ({
        ...rec,
        type: 'streaming' as const,
      }),
    );

    // Show saved recommendations first, then streaming ones
    return [...saved, ...streaming];
  }, [savedRecommendations, streamingRecommendations]);

  const columns = useMemo(
    () =>
      createResourceRecommendationColumns(
        onSaveRecommendation,
        onRemoveRecommendation,
      ),
    [onSaveRecommendation, onRemoveRecommendation],
  );

  const table = useReactTable({
    data: unifiedData,
    columns,
    state: {
      sorting,
      columnFilters,
    },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
  });

  if (isLoading && unifiedData.length === 0) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Heading level={4}>Recommendations</Heading>
        </div>
        <div className="space-y-4">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-full" />
        </div>
      </div>
    );
  }

  // Don't return early - always show the table structure to maintain search functionality

  const hasStreamingRecommendations = streamingRecommendations.length > 0;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between gap-4">
        <Heading level={4}>Recommendations</Heading>

        <div className="flex items-center gap-4">
          <If condition={!!onSearchChange}>
            <InputIconPrefix
              Icon={SearchIcon}
              placeholder="Search recommendations..."
              onChange={(e) => onSearchChange?.(e.target.value)}
              className="w-[240px]"
            />
          </If>
          <div className="text-muted-foreground flex shrink-0 items-center gap-4 text-sm">
            <span>Saved: {savedRecommendations.length}</span>
            {hasStreamingRecommendations && (
              <span className="text-primary/70 dark:text-primary/80">
                New: {streamingRecommendations.length}
              </span>
            )}
          </div>
        </div>
      </div>

      {hasStreamingRecommendations && (
        <div className="bg-primary/5 border-primary/20 rounded-lg border p-4">
          <p className="text-primary/80 dark:text-primary/70 text-sm">
            New recommendations from analysis are highlighted. Save them to
            persist permanently.
          </p>
        </div>
      )}

      {meta ? (
        <div className="rounded-lg border">
          <TableUI
            data={unifiedData}
            columns={columns}
            meta={meta}
            isRefetching={isLoading}
            onPaginationChange={(p) => onPageChange?.(p.pageIndex + 1)}
          />
        </div>
      ) : (
        <div className="bg-card overflow-x-auto rounded-lg border">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id} className="bg-muted/30">
                  {headerGroup.headers.map((header) => (
                    <TableHead
                      key={header.id}
                      className={
                        header.id === 'actions'
                          ? 'bg-card border-border sticky right-0 z-20 border-l px-6 py-4 font-medium whitespace-nowrap shadow-[-4px_0_8px_rgba(0,0,0,0.08)]'
                          : 'px-6 py-4 font-medium whitespace-nowrap'
                      }
                      style={{
                        width: header.getSize(),
                        minWidth: header.column.columnDef.minSize,
                      }}
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows.length > 0 ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    key={row.id}
                    className="hover:bg-muted/50 transition-colors duration-150"
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell
                        key={cell.id}
                        className={
                          cell.column.id === 'actions'
                            ? 'bg-card border-border sticky right-0 z-20 border-l px-6 py-4 shadow-[-4px_0_8px_rgba(0,0,0,0.08)]'
                            : 'px-6 py-4'
                        }
                        style={{
                          width: cell.column.getSize(),
                          minWidth: cell.column.columnDef.minSize,
                        }}
                      >
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext(),
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={columns.length}
                    className="text-muted-foreground h-32 px-6 py-8 text-center"
                  >
                    {savedRecommendations.length === 0 &&
                    streamingRecommendations.length === 0
                      ? 'No recommendations available for this resource yet.'
                      : 'No recommendations match your search criteria.'}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      )}
    </div>
  );
};
