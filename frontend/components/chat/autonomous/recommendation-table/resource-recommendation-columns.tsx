import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { SeverityBadge } from '@/components/ui/severity-badge';
import { StatusBadge } from '@/components/ui/status-badge';
import { ConfirmDeleteRecommendationDialog } from '@/features/recommendation/components/confirm-delete-recommendation-dialog';
import { EditRecommendationDialog } from '@/features/recommendation/components/edit-recommendation-dialog';
import { ColumnDef } from '@tanstack/react-table';
import {
  Check,
  Loader2,
  MoreHorizontal as MoreHorizontalIcon,
  X,
} from 'lucide-react';

import { StreamingRecommendation, UnifiedRecommendation } from './types';

interface ActionColumnProps {
  recommendation: UnifiedRecommendation;
  onSave: (recommendation: StreamingRecommendation) => void;
  onRemove: (tempId: string) => void;
}

const ActionColumn = ({
  recommendation,
  onSave,
  onRemove,
}: ActionColumnProps) => {
  if (recommendation.type === 'saved') {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="icon">
            <MoreHorizontalIcon className="size-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent onClick={(e) => e.stopPropagation()}>
          <EditRecommendationDialog recommendation={recommendation.data}>
            <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
              Edit
            </DropdownMenuItem>
          </EditRecommendationDialog>
          <ConfirmDeleteRecommendationDialog
            recommendation={recommendation.data}
          >
            <DropdownMenuItem
              onSelect={(e) => e.preventDefault()}
              className="text-destructive/80 hover:!text-destructive"
            >
              Delete
            </DropdownMenuItem>
          </ConfirmDeleteRecommendationDialog>
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  const isLoading = recommendation.status === 'saving';
  const hasError = recommendation.status === 'error';

  return (
    <div className="flex items-center gap-2">
      <Button
        size="sm"
        variant={hasError ? 'destructive' : 'default'}
        onClick={() => onSave(recommendation)}
        disabled={isLoading}
        className="h-8 w-8 p-0"
        title={hasError ? 'Retry save' : 'Save recommendation'}
      >
        {isLoading ? (
          <Loader2 className="h-4 w-4 animate-spin" />
        ) : (
          <Check className="h-4 w-4" />
        )}
      </Button>
      <Button
        size="sm"
        variant="outline"
        onClick={() => onRemove(recommendation.tempId)}
        disabled={isLoading}
        className="h-8 w-8 p-0"
        title="Remove recommendation"
      >
        <X className="h-4 w-4" />
      </Button>
    </div>
  );
};

export const createResourceRecommendationColumns = (
  onSave: (recommendation: StreamingRecommendation) => void,
  onRemove: (tempId: string) => void,
): ColumnDef<UnifiedRecommendation>[] => [
  {
    accessorKey: 'title',
    header: 'Title',
    size: 200,
    minSize: 150,
    meta: {
      className: 'min-w-[200px]',
    },
    cell: ({ row }) => {
      const recommendation = row.original;
      const title =
        recommendation.type === 'saved'
          ? recommendation.data.title
          : recommendation.transformedData?.title || 'Untitled';

      return (
        <div
          className={`font-medium ${
            recommendation.type === 'streaming'
              ? 'text-primary/70 dark:text-primary/80'
              : ''
          }`}
        >
          {title}
        </div>
      );
    },
  },
  {
    accessorKey: 'description',
    header: 'Description',
    size: 400,
    cell: ({ row }) => {
      const recommendation = row.original;
      const description =
        recommendation.type === 'saved'
          ? recommendation.data.description
          : recommendation.transformedData?.description || 'No description';

      return (
        <div
          className={`max-w-none min-w-[200px] break-words whitespace-normal ${
            recommendation.type === 'streaming'
              ? 'text-primary/60 dark:text-primary/70'
              : ''
          }`}
        >
          {description}
        </div>
      );
    },
  },
  {
    accessorKey: 'type',
    header: 'Type',
    size: 120,
    minSize: 100,
    cell: ({ row }) => {
      const recommendation = row.original;
      const type =
        recommendation.type === 'saved'
          ? recommendation.data.type
          : recommendation.transformedData?.type || 'optimization';

      return (
        <div
          className={
            recommendation.type === 'streaming'
              ? 'text-primary/60 dark:text-primary/70'
              : ''
          }
        >
          {type?.replace(/_/g, ' ')?.toUpperCase()}
        </div>
      );
    },
  },
  {
    accessorKey: 'potential_savings',
    header: () => <div className="text-right">Monthly Savings ($)</div>,
    size: 120,
    minSize: 100,
    cell: ({ row }) => {
      const recommendation = row.original;
      const savings =
        recommendation.type === 'saved'
          ? recommendation.data.potential_savings
          : recommendation.transformedData?.potential_savings || 0;

      return (
        <div
          className={`text-right font-medium ${
            recommendation.type === 'streaming'
              ? 'text-primary/60 dark:text-primary/70'
              : ''
          }`}
        >
          {savings !== null && savings !== undefined ? `$${savings}` : '-'}
        </div>
      );
    },
  },
  {
    accessorKey: 'effort',
    header: () => <div className="text-left">Effort</div>,
    size: 120,
    minSize: 100,
    cell: ({ row }) => {
      const recommendation = row.original;
      const effort =
        recommendation.type === 'saved'
          ? recommendation.data.effort
          : recommendation.transformedData?.effort || 'medium';

      return (
        <div className="justify-left flex">
          {effort ? (
            <SeverityBadge
              severity={effort}
              className={`p-2 opacity-80 ${
                recommendation.type === 'streaming'
                  ? 'ring-primary/30 dark:ring-primary/40 ring-2'
                  : ''
              }`}
            />
          ) : null}
        </div>
      );
    },
  },
  {
    accessorKey: 'risk',
    header: () => <div className="text-left">Risk</div>,
    size: 120,
    minSize: 100,
    cell: ({ row }) => {
      const recommendation = row.original;
      const risk =
        recommendation.type === 'saved'
          ? recommendation.data.risk
          : recommendation.transformedData?.risk || 'low';

      return (
        <div className="justify-left flex">
          {risk ? (
            <SeverityBadge
              severity={risk}
              className={`p-2 opacity-80 ${
                recommendation.type === 'streaming'
                  ? 'ring-primary/30 dark:ring-primary/40 ring-2'
                  : ''
              }`}
            />
          ) : null}
        </div>
      );
    },
  },
  {
    accessorKey: 'status',
    header: () => <div className="text-left">Status</div>,
    size: 120,
    minSize: 100,
    cell: ({ row }) => {
      const recommendation = row.original;

      if (recommendation.type === 'saved') {
        return (
          <div className="justify-left flex">
            <StatusBadge
              status={recommendation.data.status}
              className="p-2 opacity-80"
            />
          </div>
        );
      }

      // For streaming recommendations, show pending status with blue styling
      return (
        <div className="justify-left flex">
          <StatusBadge
            status="pending"
            className="ring-primary/30 dark:ring-primary/40 p-2 opacity-80 ring-2"
          />
        </div>
      );
    },
  },
  {
    id: 'actions',
    header: () => <div className="text-left">Actions</div>,
    size: 120,
    minSize: 100,
    cell: ({ row }) => (
      <ActionColumn
        recommendation={row.original}
        onSave={onSave}
        onRemove={onRemove}
      />
    ),
  },
];
