import { RecommendationData } from '@/components/chat/components/message/tables/common/types';
import { components } from '@/openapi-ts/gens';

export type SchemaRecommendationPublic =
  components['schemas']['RecommendationPublic'];
export type RecommendationCreate =
  components['schemas']['RecommendationCreate'];
export type RecommendationStatus =
  components['schemas']['RecommendationStatus'];

export interface StreamingRecommendation {
  tempId: string;
  type: 'streaming';
  data: RecommendationData;
  status: 'pending' | 'saving' | 'error';
  transformedData?: TransformedRecommendationData;
}

export interface SavedRecommendation {
  type: 'saved';
  data: SchemaRecommendationPublic;
}

export type UnifiedRecommendation =
  | StreamingRecommendation
  | SavedRecommendation;

export interface TransformedRecommendationData {
  title: string;
  description: string;
  type: string;
  potential_savings: number;
  effort: string;
  risk: string;
}

export interface ResourceRecommendationTableProps {
  resourceId?: string;
  savedRecommendations: SchemaRecommendationPublic[];
  streamingRecommendations: StreamingRecommendation[];
  onSaveRecommendation: (recommendation: StreamingRecommendation) => void;
  onRemoveRecommendation: (tempId: string) => void;
  isLoading?: boolean;
  meta?: import('@/openapi-ts/gens').SchemaPaginationMeta;
  onPageChange?: (page: number) => void;
  onSearchChange?: (value: string) => void;
}

export interface ActionColumnProps {
  recommendation: UnifiedRecommendation;
  onSave: (recommendation: StreamingRecommendation) => void;
  onRemove: (tempId: string) => void;
}
