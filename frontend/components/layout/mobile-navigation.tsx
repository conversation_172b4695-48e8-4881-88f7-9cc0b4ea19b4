'use client';

import { Suspense, createElement, useState } from 'react';

import dynamic from 'next/dynamic';
import Link from 'next/link';

import { HistoriesDialog } from '@/features/conversation/components/histories-dialog';
import { useGetNavigationConfig } from '@/hooks/use-get-navigation-config';
import useAuth from '@/hooks/useAuth';
import { HistoryIcon, LogOutIcon, MenuIcon } from 'lucide-react';

import { AppLogo } from '../app-logo';
import { NotificationButton } from '../notifications/notification-button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu';
import { Skeleton } from '../ui/skeleton';

const WorkspaceSelector = dynamic(
  () => import('./workspace-selector').then((mod) => mod.WorkspaceSelector),
  {
    ssr: false,
  },
);

export default function MobileNavigation() {
  return (
    <div className="flex w-full items-center justify-between border-b p-2 md:hidden lg:px-0">
      <AppLogo />
      <div className="flex items-center gap-2">
        <Suspense fallback={<Skeleton className="h-10 w-40" />}>
          <WorkspaceSelector />
        </Suspense>
        <NotificationButton side="bottom" />
        <HomeMobileNavigation />
      </div>
    </div>
  );
}

export function HomeMobileNavigation() {
  const { logout } = useAuth();
  // const signOut = useSignOut();
  const config = useGetNavigationConfig();
  const [open, setOpen] = useState(false);

  const Links = config.routes.map((item, index) => {
    if ('children' in item) {
      return item.children.map((child) => {
        return (
          <DropdownLink
            key={child.path}
            Icon={
              child.Icon &&
              createElement(child.Icon, {
                className: 'size-4',
              })
            }
            path={child.path}
            label={child.label}
          />
        );
      });
    }

    if ('divider' in item) {
      return <DropdownMenuSeparator key={index} />;
    }
  });

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger>
        <MenuIcon className={'h-9'} />
      </DropdownMenuTrigger>

      <DropdownMenuContent
        sideOffset={10}
        className={
          'h-[calc(100dvh-53px)] w-screen overflow-y-auto rounded-none'
        }
      >
        <HistoriesDialog onCloseDropdown={() => setOpen(false)}>
          <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
            <div className="flex items-center gap-2">
              <HistoryIcon className="size-4" />
              <span>View All History</span>
            </div>
          </DropdownMenuItem>
        </HistoriesDialog>
        <DropdownMenuSeparator />

        <DropdownMenuGroup>{Links}</DropdownMenuGroup>

        <DropdownMenuSeparator />

        <SignOutDropdownItem onSignOut={logout} />
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

function DropdownLink(
  props: React.PropsWithChildren<{
    path: string;
    label: string;
    Icon: React.ReactNode;
  }>,
) {
  return (
    <DropdownMenuItem asChild key={props.path}>
      <Link
        href={props.path}
        className={'flex h-12 w-full items-center space-x-2'}
      >
        {props.Icon}

        <span>{props.label}</span>
      </Link>
    </DropdownMenuItem>
  );
}

function SignOutDropdownItem(
  props: React.PropsWithChildren<{
    onSignOut: () => unknown;
  }>,
) {
  return (
    <DropdownMenuItem
      className={'flex h-12 w-full items-center space-x-2'}
      onClick={props.onSignOut}
    >
      <LogOutIcon className="size-4" />
      <span>Sign out</span>
    </DropdownMenuItem>
  );
}
