'use client';

import { createElement } from 'react';

import {
  AWSResourceType,
  RESOURCE_TYPE_CONFIG,
} from '@/features/resource/config/resource-type.config';
import { SchemaResourceDetail, SchemaResourcePublic } from '@/openapi-ts/gens';
import { X } from 'lucide-react';

export const ResourceAttachmentIndicator = ({
  selectedResource,
  conversationResource,
  onRemove,
}: {
  selectedResource: SchemaResourcePublic | null | undefined;
  conversationResource: SchemaResourceDetail | null | undefined;
  onRemove: () => void;
}) => {
  const displayResource = selectedResource || conversationResource;
  const isSelectedResource = !!selectedResource;

  if (!displayResource) return null;

  return (
    <div className="group/resource relative">
      <div className="bg-muted/50 hover:bg-muted/70 border-muted-foreground/20 flex items-center gap-2 rounded-md border px-2 py-1 text-xs transition-colors">
        <div className="bg-background/60 flex h-4 w-4 shrink-0 items-center justify-center rounded">
          {createElement(
            RESOURCE_TYPE_CONFIG.CONFIG[displayResource.type as AWSResourceType]
              .icon,
            {
              className: 'h-3 w-3 text-foreground',
            },
          )}
        </div>
        <span className="text-foreground font-medium">
          {isSelectedResource ? '1 resource attached' : 'Resource context'}
        </span>
        {isSelectedResource && (
          <button
            type="button"
            onClick={onRemove}
            className="text-muted-foreground hover:text-foreground ml-1 transition-colors"
          >
            <X className="h-3 w-3" />
          </button>
        )}
      </div>

      {/* Hover card */}
      <div
        className="pointer-events-none invisible absolute bottom-full left-0 mb-2 opacity-0 transition-all duration-200 group-hover/resource:visible group-hover/resource:opacity-100"
        style={{ zIndex: 9999 }}
      >
        <div className="bg-popover border-border min-w-[260px] rounded-lg border p-3 shadow-xl">
          <div className="flex items-start gap-3">
            <div className="bg-muted flex h-8 w-8 shrink-0 items-center justify-center rounded-lg">
              {createElement(
                RESOURCE_TYPE_CONFIG.CONFIG[
                  displayResource.type as AWSResourceType
                ].icon,
                {
                  className: 'h-4 w-4 text-foreground',
                },
              )}
            </div>
            <div className="min-w-0 flex-1">
              <h4 className="text-foreground mb-1 truncate text-sm leading-tight font-semibold">
                {displayResource.name}
              </h4>
              <div className="text-muted-foreground flex items-center gap-2 text-xs">
                <span>{displayResource.type}</span>
                {displayResource.region && (
                  <>
                    <span>•</span>
                    <span>{displayResource.region}</span>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
