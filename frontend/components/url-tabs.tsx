'use client';

import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { LucideIcon } from 'lucide-react';
import { useQueryState } from 'nuqs';

export interface TabItem {
  value: string;
  label: string;
  icon: LucideIcon;
  content: React.ReactNode;
}

interface UrlTabsProps {
  tabs: TabItem[];
  defaultTab?: string;
  queryParam?: string;
  className?: string;
}

export function UrlTabs({
  tabs,
  defaultTab = tabs[0]?.value,
  queryParam = 'tab',
  className,
}: UrlTabsProps) {
  const [activeTab, setActiveTab] = useQueryState(queryParam, {
    defaultValue: defaultTab,
    parse: (value: string) => value,
    serialize: (value: string) => value,
  });

  return (
    <Tabs
      value={activeTab}
      onValueChange={setActiveTab}
      defaultValue={defaultTab}
      className={className}
    >
      <TabsList className="w-full md:w-auto">
        {tabs.map((tab) => (
          <TabsTrigger key={tab.value} value={tab.value} className="gap-2">
            <tab.icon className="size-4" />
            {tab.label}
          </TabsTrigger>
        ))}
      </TabsList>
      {tabs.map((tab) => (
        <TabsContent key={tab.value} value={tab.value}>
          {tab.content}
        </TabsContent>
      ))}
    </Tabs>
  );
}
