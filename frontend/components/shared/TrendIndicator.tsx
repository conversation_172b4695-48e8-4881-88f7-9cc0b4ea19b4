import React from 'react';

import {
  type TrendData,
  renderTrendWithIcon,
  renderTrendWithSymbol,
} from '@/utils/trend-helpers';

export interface TrendIndicatorProps {
  trend?: TrendData;
  variant?: 'icon' | 'symbol';
  className?: string;
  fallback?: React.ReactNode;
}

/**
 * Functional TrendIndicator component that renders trend data
 * Uses utilities for consistent styling and behavior
 */
const TrendIndicator: React.FC<TrendIndicatorProps> = ({
  trend,
  variant = 'icon',
  className,
  fallback = null,
}) => {
  // Early return if trend is invalid
  if (!trend) {
    return <>{fallback}</>;
  }

  // Select renderer based on variant
  const renderer =
    variant === 'symbol' ? renderTrendWithSymbol : renderTrendWithIcon;

  // Render trend with optional wrapper className
  const trendElement = renderer(trend);

  return className ? (
    <div className={className}>{trendElement}</div>
  ) : (
    trendElement
  );
};

export default TrendIndicator;
