import React from 'react';

import TrendIndicator from '@/components/shared/TrendIndicator';
import type { TrendData } from '@/features/dashboard';
import { cn } from '@/lib/utils';
import { renderAlertOverlay } from '@/utils/alert-styles';
import { renderLucideIcon } from '@/utils/icon-helpers';
import { AlertTriangle } from 'lucide-react';

interface BaseCardProps {
  title?: string;
  description?: string;
  value?: string | number;
  icon?: string;
  alert?: boolean;
  trend?: TrendData;
  children?: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  minHeight?: string;
  variant?: 'default' | 'compact' | 'extended';
}

/**
 * Enhanced BaseCard component using functional utilities
 * Consolidates common card patterns from widget and content components
 */
const BaseCard: React.FC<BaseCardProps> = ({
  title,
  description,
  value,
  icon,
  alert = false,
  trend,
  children,
  className,
  style,
  minHeight = 'min-h-[200px]',
  variant = 'default',
}) => {
  // Variant-based styling
  const variantStyles = {
    default: 'p-4',
    compact: 'p-3',
    extended: 'p-6',
  };

  return (
    <div
      className={cn(
        'bg-card/40 relative overflow-hidden rounded-lg border text-left backdrop-blur-sm',
        'focus-visible:ring-primary/30 focus-visible:ring-2 focus-visible:outline-none',
        minHeight,
        variantStyles[variant],
        className,
      )}
      style={style}
    >
      {/* Alert overlay for error states */}
      {renderAlertOverlay(alert)}

      <div className="relative z-10 flex h-full flex-col">
        {/* Header Section */}
        {(title || icon) && (
          <div className="mb-3 flex items-start justify-between">
            <div className="flex items-center gap-3">
              {icon && (
                <div className="from-primary/8 to-secondary/8 text-primary/80 flex h-10 w-10 items-center justify-center rounded-lg bg-gradient-to-br shadow-sm">
                  {renderLucideIcon(icon, 'size-5')}
                </div>
              )}
              {title && (
                <div className="flex flex-col">
                  <h3 className="text-foreground/90 text-sm leading-tight font-semibold">
                    {title}
                  </h3>
                </div>
              )}
            </div>
            {alert && (
              <div className="flex h-6 w-6 items-center justify-center rounded-md bg-red-100/80 text-red-600 dark:bg-red-900/20 dark:text-red-400">
                <AlertTriangle className="size-3" />
              </div>
            )}
          </div>
        )}

        {/* Value Section */}
        {value && (
          <div className="mb-3 flex-1">
            <div className="text-foreground/90 text-2xl font-bold">{value}</div>
          </div>
        )}

        {/* Description Section */}
        {description && variant !== 'compact' && (
          <div className="mb-3">
            <p className="text-muted-foreground/80 text-sm leading-relaxed">
              {description}
            </p>
          </div>
        )}

        {/* Content Section */}
        {children && <div className="flex-1">{children}</div>}

        {/* Footer Section */}
        {(description && variant === 'compact') || trend ? (
          <div className="mt-auto space-y-2">
            {description && variant === 'compact' && (
              <p className="text-muted-foreground/80 text-sm leading-relaxed">
                {description}
              </p>
            )}
            {trend && <TrendIndicator trend={trend} />}
          </div>
        ) : null}
      </div>
    </div>
  );
};

export default BaseCard;
