@utility prose {
  max-width: 100%;
  color: var(--foreground);
  @apply text-sm;

  [class~='lead'] {
    color: var(--foreground);
  }

  a {
    color: var(--primary);
  }

  strong {
    /* color: var(--foreground); */
  }

  ol > li::marker {
    /* color: var(--foreground); */
  }

  ul > li::marker {
    /* color: var(--foreground); */
  }

  hr {
    border-color: var(--border);
  }

  blockquote {
    border-left-color: var(--border);
    /* color: var(--foreground); */
  }

  blockquote p:first-of-type::before {
    content: none;
  }

  h1 {
    /* color: var(--foreground); */
    margin-top: 0;
  }

  h2 {
    /* color: var(--foreground); */
    margin-top: 0;
  }

  h3 {
    /* color: var(--foreground); */
  }

  h4 {
    /* color: var(--foreground); */
  }

  figure figcaption {
    color: var(--muted-foreground);
  }

  code {
    @apply whitespace-pre-wrap;
  }

  a code {
    color: var(--primary);
  }

  pre {
    @apply !bg-muted/30 overflow-x-auto text-xs break-words;
  }

  pre code::before,
  pre code::after {
    content: none;
  }

  thead {
    border-bottom-color: var(--border);
  }

  tbody tr {
    border-bottom-color: var(--border);
  }
}
