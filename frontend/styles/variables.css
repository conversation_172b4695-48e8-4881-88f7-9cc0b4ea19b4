/* ==========================================================================
      CSS Custom Properties (Variables) - Dark Mode Default
      ========================================================================== */
@layer base {
  :root {
    /* Brand Colors */
    --brand-teal: #0fb3b3;
    --brand-blue: #1093c6;
    --brand-green: #06ef6b;

    /* Base Colors - Dark Mode Default */
    --background: #101218;
    --foreground: #e2e4e9;

    --muted: #161a22;
    --muted-foreground: #98a1b3;
    --popover: #12151c;
    --popover-foreground: #e2e4e9;
    --card: #161a22;
    --card-foreground: #e2e4e9;
    --border: var(--color-neutral-800);
    --input: #21242c;

    /* Interactive Colors - Dark Mode Default */
    --primary: #0fbbc7;
    --primary-foreground: var(--color-neutral-900);
    --secondary: #181c25;
    --secondary-foreground: #d3d7de;
    --accent: #21242c;
    --accent-foreground: #e8eaee;

    /* Status Colors - Dark Mode Default */
    --success: var(--color-green-600);
    --success-foreground: var(--color-white);
    --info: var(--color-blue-600);
    --info-foreground: var(--color-white);
    --warning: var(--color-yellow-600);
    --warning-foreground: var(--color-white);
    --destructive: var(--color-red-600);
    --destructive-foreground: var(--color-white);

    /* UI Elements */
    --ring: #21242c;
    --radius: 0.5rem;

    /* Sidebar Theme - Dark Mode Default */
    --sidebar-background: #0e1016;
    --sidebar-foreground: #e2e4e9;
    --sidebar-primary: var(--brand-teal);
    --sidebar-primary-foreground: var(--color-white);
    --sidebar-accent: #161a22;
    --sidebar-accent-foreground: #e2e4e9;
    --sidebar-border: #21242c;
    --sidebar-ring: var(--brand-teal);

    /* Animation */
    --transition-duration: 0.2s;
    --transition-ease: cubic-bezier(0.2, 0, 0.2, 1);
  }
}
