import { useCallback, useEffect, useRef, useState } from 'react';

import { OpenAPI, RecommendationPublic } from '@/client';
import { StreamingRecommendation } from '@/components/chat/autonomous/recommendation-table/types';
import { Message as ChatMessage } from '@/components/chat/types';
import { conversationApi } from '@/features/conversation';
import { useUserContext } from '@/features/user/provider/user-provider';
import { toast, useToast } from '@/hooks/use-toast';
import { convertToUIMessage } from '@/lib/message-converters';
import { generateId } from '@/lib/utils';
import {
  MessageDisplayComponentType,
  type SchemaDashboard,
  type SchemaMessageDisplayComponentPublic,
  SchemaMessagePlanList,
  SchemaMessagePublic,
  SchemaMessageToolComponentPublic,
  type SchemaReport,
} from '@/openapi-ts/gens';
import { useQueryClient } from '@tanstack/react-query';
import { v4 as uuidv4 } from 'uuid';

import {
  InterruptConfirmation,
  InterruptMessage,
} from './message-stream/types/interrupt';
import { playNotificationSound } from './message-stream/utils/play-sound';

const createInterruptHandlers = (
  formattedMessage: InterruptMessage,
  capturedConversationId: string,
  handleSendMessage: (
    content: string,
    attachmentIds?: string[],
    resourceId?: string,
    resumeOptions?: { resume: boolean; approve: boolean },
    overrideConversationId?: string,
  ) => Promise<void>,
  setInterruptConfirmation: (
    confirmation: InterruptConfirmation | null,
  ) => void,
) => {
  return {
    message: formattedMessage,
    onConfirm: async () => {
      setInterruptConfirmation(null);
      await handleSendMessage(
        '',
        undefined,
        undefined,
        { resume: true, approve: true },
        capturedConversationId,
      );
    },
    onCancel: async (cancelMessage: string) => {
      setInterruptConfirmation(null);
      await handleSendMessage(
        cancelMessage,
        undefined,
        undefined,
        { resume: true, approve: false },
        capturedConversationId,
      );
    },
  };
};

interface ChatStreamParams {
  conversationId: string | null;
  message: {
    content: string;
    resume: boolean;
    approve: boolean;
    message_id?: string;
    attachment_ids?: string[];
    resource_id?: string;
  };
}

// custom function to stream chat messages with an agent
/**
 * Chat Stream
 * Stream chat messages with an agent
 * @param data The data for the request.
 * @param data.conversationId
 * @param data.message
 * @param abortController Optional AbortController to enable stream termination
 * @returns Response Successful Response
 * @throws ApiError
 */
export async function chatStream(
  { conversationId, message }: ChatStreamParams,
  abortController?: AbortController,
): Promise<Response> {
  if (!OpenAPI.BASE) {
    throw new Error('API base URL is not configured');
  }

  let tokenValue = await OpenAPI.TOKEN;
  if (typeof tokenValue === 'function') {
    tokenValue = await tokenValue({
      method: 'POST',
      url: '/api/v1/autonomous-agents/chat/stream',
    });
  }

  if (!tokenValue) {
    throw new Error('Authentication token is required');
  }

  // Sanitize the token
  const sanitizedToken = String(tokenValue)
    .trim()
    .replace(/[\r\n]+/g, '');

  try {
    // Validate the URL construction
    const baseUrl = String(OpenAPI.BASE).trim().replace(/\/+$/, '');
    const url = new URL(`${baseUrl}/api/v1/autonomous-agents/chat/stream`);

    // Add conversation_id as query param if present
    if (conversationId) {
      url.searchParams.set('conversation_id', conversationId);
    }

    // Check if abort was already requested
    if (abortController?.signal.aborted) {
      throw new Error('Aborted');
    }

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${sanitizedToken}`,
      },
      body: JSON.stringify(message),
      signal: abortController?.signal, // Add abort signal to fetch request
    });

    if (!response.ok && response.status == 403) {
      toast({
        variant: 'destructive',
        title: 'MISSING CREDENTIALS!',
        description: 'Please configure workspace credentials!',
      });

      return response;
    }

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response;
  } catch (error) {
    if (error instanceof Error) {
      // Add more context to the error message
      if (error.name === 'AbortError') {
        throw error; // Re-throw abort errors to be handled by caller
      }
      if (error.message.includes('Invalid value')) {
        console.error('Detailed request info:', {
          baseUrl: OpenAPI.BASE,
          conversationId: conversationId,
          tokenLength: tokenValue?.length,
        });
      }
      throw error;
    }
    throw new Error('Failed to make chat stream request');
  }
}

export function useMessageStream(
  conversationId: string | null,
  options?: {
    onRecommendations?: (recommendations: RecommendationPublic[]) => void;
    resourceId?: string;
    onConversationCreated?: (conversationId: string) => void;
  },
) {
  const [isStreaming, setIsStreaming] = useState(false);
  const [streamingMessages, setStreamingMessages] = useState<ChatMessage[]>([]);
  const [interruptConfirmation, setInterruptConfirmation] =
    useState<InterruptConfirmation | null>(null);

  // Thinking and planning content
  const [thinkingContent, setThinkingContent] = useState<string | null>(null);
  const [planningContent, setPlanningContent] =
    useState<SchemaMessagePlanList | null>(null);

  const currentGlobalMessageRef = useRef<SchemaMessagePublic | null>(null);
  const activeStreamConversationIdRef = useRef<string | null>(null);
  const [isLoadingMessages, setIsLoadingMessages] = useState(false);
  const abortControllerRef = useRef<AbortController | null>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [currentReport, setCurrentReport] = useState<SchemaReport | null>(null);
  const [currentDashboard, setCurrentDashboard] =
    useState<SchemaDashboard | null>(null);
  const [currentRecommendations, setCurrentRecommendations] = useState<
    StreamingRecommendation[]
  >([]);

  // Track if conversation ID came from streaming to prevent loading messages
  const streamingConversationIds = useRef(new Set<string>());

  // Track processed message IDs to prevent duplicates during reconnection
  const processedMessageIds = useRef(new Set<string>());

  // MessagePublicList fields
  const [conversationResourceId, setConversationResourceId] = useState<
    string | null
  >(null);
  const [hasReport, setHasReport] = useState<boolean>(false);
  const [hasDashboard, setHasDashboard] = useState<boolean>(false);

  const { user } = useUserContext();

  // Helper to update both state and ref
  const updateCurrentGlobalMessage = (msg: SchemaMessagePublic | null) => {
    currentGlobalMessageRef.current = msg;
  };

  // Reconnect to an active stream
  const reconnectToStream = useCallback(
    async (conversationId: string): Promise<boolean> => {
      try {
        console.log('Attempting to reconnect to stream:', conversationId);

        // Create new AbortController for the reconnection
        if (abortControllerRef.current) {
          abortControllerRef.current.abort();
        }
        abortControllerRef.current = new AbortController();

        const baseUrl = String(OpenAPI.BASE).trim().replace(/\/+$/, '');
        const reconnectUrl = new URL(
          `${baseUrl}/api/v1/autonomous-agents/chat/${conversationId}/reconnect-stream`,
        );

        let tokenValue = await OpenAPI.TOKEN;
        if (typeof tokenValue === 'function') {
          tokenValue = await tokenValue({
            method: 'GET',
            url: reconnectUrl.toString(),
          });
        }

        if (!tokenValue) {
          throw new Error('Authentication token is required');
        }

        const sanitizedToken = String(tokenValue)
          .trim()
          .replace(/[\r\n]+/g, '');

        const response = await fetch(reconnectUrl.toString(), {
          method: 'GET',
          headers: {
            Accept: 'text/event-stream',
            'Cache-Control': 'no-cache',
            Authorization: `Bearer ${sanitizedToken}`,
          },
          signal: abortControllerRef.current.signal,
        });

        if (!response.ok) {
          if (response.status === 404) {
            console.log('No active stream to reconnect to');
            return false;
          }
          throw new Error(`Reconnection failed: ${response.statusText}`);
        }

        if (!response.body) {
          throw new Error('No response body received from reconnection');
        }

        console.log(
          'Successfully reconnected to stream, processing messages...',
        );

        // Update active conversation ref to track the reconnected stream
        activeStreamConversationIdRef.current = conversationId;

        // Process the reconnected stream with special reconnection flag
        await processMessageStream(
          response.body.getReader(),
          {
            onRecommendations: options?.onRecommendations,
            resourceId: options?.resourceId,
            onConversationCreated: options?.onConversationCreated,
          },
          true,
        ); // Pass reconnection flag

        return true;
      } catch (error) {
        console.error('Error reconnecting to stream:', error);
        if (error instanceof Error && error.name !== 'AbortError') {
          toast({
            variant: 'destructive',
            title: 'Reconnection Failed',
            description:
              'Unable to reconnect to the active stream. Please refresh the page.',
          });
        }
        return false;
      }
    },
    [options, toast],
  );

  // Load messages from API when conversation ID changes
  useEffect(() => {
    if (conversationId) {
      // Initialize the active conversation ID from props if available
      activeStreamConversationIdRef.current = conversationId;
      // Check if we're already streaming for this conversation or have messages for it
      // If so, don't reload messages (this prevents clearing messages when URL is updated)
      if (isStreaming) {
        return;
      }

      // Check if we already have messages for this conversation from streaming
      // This prevents reloading when the URL is updated from a streaming conversation_id event
      if (streamingMessages.length > 0) {
        return;
      }

      // Skip loading if this conversation ID came from streaming
      if (streamingConversationIds.current.has(conversationId)) {
        return;
      }

      setInterruptConfirmation(null);
      setCurrentReport(null); // Clear report when conversation changes
      setCurrentDashboard(null); // Clear dashboard when conversation changes
      setCurrentRecommendations([]); // Clear recommendations when conversation changes

      // Clear MessagePublicList fields when conversation changes
      setConversationResourceId(null);
      setHasReport(false);
      setHasDashboard(false);
      setPlanningContent(null); // Clear planning content when conversation changes

      // Clear processed message IDs when conversation changes
      processedMessageIds.current.clear();

      // Clean up streaming conversation IDs for old conversations (but keep current one)
      const currentStreamingIds = new Set<string>();
      if (streamingConversationIds.current.has(conversationId)) {
        currentStreamingIds.add(conversationId);
      }
      streamingConversationIds.current = currentStreamingIds;

      // Skip loading messages if this is a newly created conversation
      // We can detect this by checking if the conversation ID is in sessionStorage
      const isNewlyCreated = sessionStorage.getItem(
        `new-conversation-${conversationId}`,
      );
      if (isNewlyCreated) {
        // Clear the flag after using it
        sessionStorage.removeItem(`new-conversation-${conversationId}`);
        setIsLoadingMessages(false);
        setStreamingMessages([]);
        return;
      }

      const loadMessages = async () => {
        setIsLoadingMessages(true);
        try {
          // First check if there's an active stream for this conversation
          const streamStatusResponse = await conversationApi
            .detail(conversationId)
            .getStreamStatus();

          // Check the nested redis_stream_status for active stream
          const isStreamActive =
            streamStatusResponse?.redis_stream_status?.is_streaming_active;
          if (streamStatusResponse && isStreamActive) {
            console.log('Active stream detected, attempting reconnection...');
            setIsLoadingMessages(false);
            // Attempt to reconnect to the active stream
            const reconnected = await reconnectToStream(conversationId);
            if (reconnected) {
              console.log('Successfully reconnected to active stream');
              return; // Skip loading static messages
            } else {
              console.log(
                'Reconnection failed, loading static messages instead',
              );
            }
          }

          const response = await conversationApi
            .detail(conversationId)
            .getMessagesHistory();

          const plans = await conversationApi
            .detail(conversationId)
            .getMessagePlans();

          // Extract MessagePublicList fields
          setConversationResourceId(response.resource_id || null);
          setHasReport(response.has_report || false);
          setHasDashboard(response.has_dashboard || false);
          setPlanningContent(plans);

          const messages =
            response.messages?.map((message) => {
              if (message.is_interrupt) {
                const interruptContent = message.interrupt_message;
                const formattedMessage = {
                  value: interruptContent || '',
                };
                const activeConversationId =
                  activeStreamConversationIdRef.current || conversationId;
                if (activeConversationId) {
                  setInterruptConfirmation(
                    createInterruptHandlers(
                      formattedMessage,
                      activeConversationId,
                      handleSendMessage,
                      setInterruptConfirmation,
                    ),
                  );
                }
              }
              return convertToUIMessage(message);
            }) || [];
          const lastMessage =
            response.messages?.[response.messages?.length - 1];
          if (lastMessage) {
            updateCurrentGlobalMessage(lastMessage);
          }

          setStreamingMessages(messages);
        } catch (error) {
          console.error('Error loading messages:', error);
          toast({
            variant: 'destructive',
            title: 'Error',
            description: 'Failed to load message history',
          });
        } finally {
          setIsLoadingMessages(false);
        }
      };
      loadMessages();
    } else {
      // Clean up when no conversation is selected
      activeStreamConversationIdRef.current = null;
      streamingConversationIds.current.clear();
      setStreamingMessages([]);
      setInterruptConfirmation(null);
      setCurrentReport(null); // Clear report when no conversation is selected
      setCurrentDashboard(null); // Clear dashboard when no conversation is selected
      setCurrentRecommendations([]); // Clear recommendations when no conversation is selected
    }
  }, [conversationId, toast]);

  // Cleanup effect for component unmount
  useEffect(() => {
    return () => {
      // Cleanup on unmount
      if (abortControllerRef.current) {
        try {
          abortControllerRef.current.abort();
        } catch (error) {
          console.warn('Error aborting controller during cleanup:', error);
        }
        abortControllerRef.current = null;
      }

      // Clear streaming conversation IDs
      streamingConversationIds.current.clear();

      // Clear processed message IDs
      processedMessageIds.current.clear();

      // Reset refs to prevent memory leaks
      currentGlobalMessageRef.current = null;
    };
  }, []);

  const processMessageStream = async (
    reader: ReadableStreamDefaultReader<Uint8Array>,
    streamOptions?: {
      onRecommendations?: (recommendations: RecommendationPublic[]) => void;
      resourceId?: string;
      onConversationCreated?: (conversationId: string) => void;
    },
    isReconnection: boolean = false,
  ) => {
    const decoder = new TextDecoder();
    let currentMessage: SchemaMessagePublic | null =
      currentGlobalMessageRef.current || null;
    let readerCancelled = false;
    let hasSeenLiveEvent = false; // Track if we've seen any live streaming events

    // Ensure arrays are initialized if currentMessage exists
    if (currentMessage) {
      if (!currentMessage.tool_calls) {
        currentMessage.tool_calls = [];
      }
      if (!currentMessage.display_components) {
        currentMessage.display_components = [];
      }
    }

    // --- Current tool call tracking ---
    let currentToolCall: SchemaMessageToolComponentPublic | null =
      currentMessage?.tool_calls?.[currentMessage?.tool_calls?.length - 1] ||
      null;

    // Track active function streams
    // name -> tool_call_id
    const activeFunctionStreamByName: Record<string, string> = {};
    // position -> tool_call_id
    const activeFunctionStreamByPosition: Record<number, string> = {};
    // id -> tool_call_id (prefer id when present)
    const activeFunctionStreamById: Record<string, string> = {};
    // fallback when neither name nor position is present
    let lastActiveFunctionStreamId: string | null = null;
    // Accumulate partial JSON payloads per tool call id for function_stream chunks
    const partialFunctionJsonByToolCallId: Record<string, string> = {};

    // --- Helper: attempt strict parse, else try partial repair like Python's partial_json_parser.loads ---
    const tryParseJsonStrict = (text: string): unknown | null => {
      try {
        return JSON.parse(text);
      } catch {
        return null;
      }
    };

    const parsePartialJsonWithClosures = (
      input: string,
    ): Record<string, unknown> | null => {
      if (!input || typeof input !== 'string') return null;
      let s = input.trim();
      const firstBrace = s.indexOf('{');
      const firstBracket = s.indexOf('[');
      if (firstBrace === -1 && firstBracket === -1) return null;
      let startIndex = -1;
      if (firstBrace === -1) startIndex = firstBracket;
      else if (firstBracket === -1) startIndex = firstBrace;
      else startIndex = Math.min(firstBrace, firstBracket);
      s = s.slice(startIndex);

      const stack: string[] = [];
      let inString = false;
      let isEscaped = false;
      for (let i = 0; i < s.length; i++) {
        const ch = s[i];
        if (inString) {
          if (isEscaped) {
            isEscaped = false;
            continue;
          }
          if (ch === '\\') {
            isEscaped = true;
            continue;
          }
          if (ch === '"') {
            inString = false;
          }
          continue;
        } else {
          if (ch === '"') {
            inString = true;
            continue;
          }
          if (ch === '{' || ch === '[') {
            stack.push(ch);
          } else if (ch === '}') {
            if (stack.length && stack[stack.length - 1] === '{') {
              stack.pop();
            }
          } else if (ch === ']') {
            if (stack.length && stack[stack.length - 1] === '[') {
              stack.pop();
            }
          }
        }
      }

      let fixed = s;
      if (inString) fixed += '"';
      // Remove trailing commas/whitespace to avoid JSON errors on closing braces
      fixed = fixed.replace(/[\s,]+$/u, '');
      for (let i = stack.length - 1; i >= 0; i--) {
        fixed += stack[i] === '{' ? '}' : ']';
      }

      try {
        const parsed = JSON.parse(fixed) as unknown;
        if (parsed && typeof parsed === 'object' && !Array.isArray(parsed)) {
          return parsed as Record<string, unknown>;
        }
        // Optionally convert arrays into an object for merging semantics
        if (Array.isArray(parsed)) {
          return { _array: parsed } as unknown as Record<string, unknown>;
        }
      } catch {
        // Still not parseable
      }
      return null;
    };

    // Compute the next tool position as (latest position + 1)
    const getNextToolPosition = (): number => {
      const toolCalls = currentMessage?.tool_calls || [];
      let maxPos = -1;
      for (const tc of toolCalls) {
        if (typeof tc.position === 'number' && tc.position > maxPos) {
          maxPos = tc.position;
        }
      }
      return maxPos + 1;
    };

    // Function to cancel the reader properly
    const cancelReader = async () => {
      if (!readerCancelled) {
        try {
          await reader.cancel();
          readerCancelled = true;
        } catch (e) {
          console.error('Error cancelling reader:', e);
        }
      }
    };

    // Error recovery function
    const handleStreamError = (error: unknown, context: string) => {
      console.error(`Error in ${context}:`, error);

      // Show user-friendly error message
      toast({
        variant: 'destructive',
        title: 'Stream Error',
        description: `An error occurred while processing the stream. Please try again.`,
      });

      // Ensure streaming state is reset
      setIsStreaming(false);
    };

    let buffer = '';
    const MAX_BUFFER_SIZE = 1024 * 1024; // 1MB limit for string buffer

    try {
      while (true) {
        try {
          // Check before read if stream has been aborted
          if (
            !abortControllerRef.current ||
            abortControllerRef.current.signal.aborted
          ) {
            await cancelReader();
            break;
          }

          const { value, done } = await reader.read();
          if (done) {
            break;
          }

          // Check if stream is still active - if not, break out early
          if (
            !abortControllerRef.current ||
            abortControllerRef.current.signal.aborted
          ) {
            await cancelReader();
            break;
          }

          const decoded = decoder.decode(value, { stream: true });

          // Check buffer size before adding new data
          if (buffer.length + decoded.length > MAX_BUFFER_SIZE) {
            console.warn(
              `Buffer size limit (${MAX_BUFFER_SIZE} bytes) reached, clearing buffer`,
            );
            buffer = decoded; // Start fresh with new data
          } else {
            buffer += decoded;
          }

          // Split on double newlines which separate SSE events (part of SSE specification)
          const events = buffer.split('\n\n');

          // Save the last incomplete event back to buffer
          buffer = events.pop() || '';

          for (const event of events) {
            const lines = event.split('\n');

            for (const line of lines) {
              try {
                let content = null;

                // Parse proper SSE format: "data: {json}" (part of SSE specification)
                if (line.startsWith('data: ')) {
                  const jsonData = line.slice(6);
                  try {
                    content = JSON.parse(jsonData);
                  } catch (e) {
                    console.error('Error parsing SSE JSON:', e);
                    continue;
                  }
                } else if (line.trim() === '') {
                  continue;
                } else {
                  // For backward compatibility, try parsing as raw JSON
                  try {
                    content = JSON.parse(line);
                  } catch (e) {
                    console.error('Error parsing line as JSON:', line, e);
                    continue;
                  }
                }

                if (!content) continue;

                // Handle both old and new format
                // Old format: {"event": {"type": "...", ...}, "timestamp": ..., "position": ...}
                // New format: {"type": "...", ...}
                // Extract the actual event data and reassign to content
                content = content.event || content;

                // Debug logging for reconnection
                if (isReconnection) {
                  console.log(
                    '🔍 [RECONNECTION DEBUG] Event type:',
                    content.type,
                    'Content:',
                    content,
                  );
                }

                const role =
                  content.type != 'complete' ? content.namespace : '';

                // --- Handle conversation ID for new conversations ---
                if (content.type === 'conversation_id') {
                  // This is a new conversation, update the URL
                  const newConversationId = content.conversation_id;
                  if (newConversationId) {
                    // Store the active conversation ID from streaming
                    activeStreamConversationIdRef.current = newConversationId;

                    if (streamOptions?.onConversationCreated) {
                      // Mark this conversation ID as coming from streaming
                      streamingConversationIds.current.add(newConversationId);
                      streamOptions.onConversationCreated(newConversationId);
                    }
                  }
                  continue;
                }

                // --- Handle message ID ---
                if (
                  content.type === 'message_id' &&
                  (!currentMessage || currentMessage.id !== content.message_id)
                ) {
                  try {
                    // Check if we've already processed this message to prevent duplicates
                    if (processedMessageIds.current.has(content.message_id)) {
                      console.log(
                        'Skipping duplicate message:',
                        content.message_id,
                      );
                      continue;
                    }

                    // Add to processed messages
                    processedMessageIds.current.add(content.message_id);

                    currentMessage = {
                      id: content.message_id,
                      content: '',
                      role: content.namespace,
                      created_at: new Date(
                        Math.floor(Date.now() / 1000),
                      ).toISOString(),
                      is_interrupt: false,
                      tool_calls: [],
                      display_components: [],
                    };
                    currentToolCall = null;
                    setThinkingContent(null);

                    // IMMEDIATE UI UPDATE - Show agent message with role
                    const convertedMessage = convertToUIMessage(currentMessage);
                    setStreamingMessages((prev) => {
                      // Check if message already exists to prevent duplicates
                      const existingMessageIndex = prev.findIndex(
                        (msg) => msg.id === convertedMessage.id,
                      );
                      if (existingMessageIndex !== -1) {
                        // Update existing message
                        const updated = [...prev];
                        updated[existingMessageIndex] = convertedMessage;
                        return updated;
                      }

                      const lastMessage = prev[prev.length - 1];
                      if (lastMessage?.id === convertedMessage.id) {
                        return [...prev.slice(0, -1), convertedMessage];
                      }
                      return [...prev, convertedMessage];
                    });
                    updateCurrentGlobalMessage(currentMessage);

                    if (user?.id) {
                      queryClient.invalidateQueries({
                        queryKey: ['quota-info', user.id],
                      });
                    }
                  } catch (error) {
                    handleStreamError(error, 'message_id processing');
                    continue;
                  }
                }
                // --- AGENT THOUGHT MANAGEMENT LOGIC ---
                else if (
                  content.type === 'stream' ||
                  content.type === 'function_call' ||
                  content.type === 'function_stream'
                ) {
                  try {
                    // For reconnections, set streaming state when we see live streaming events
                    if (isReconnection && !hasSeenLiveEvent) {
                      hasSeenLiveEvent = true;
                      setIsStreaming(true);
                      console.log(
                        'Detected live streaming event during reconnection, enabling streaming indicator',
                      );
                    }

                    if (!currentMessage) {
                      currentMessage = {
                        id: content.message_id || generateId(),
                        content: '',
                        role: role,
                        created_at: new Date(
                          Math.floor(Date.now() / 1000),
                        ).toISOString(),
                        is_interrupt: false,
                        tool_calls: [],
                        display_components: [],
                      };
                    }

                    // Ensure tool_calls array is initialized
                    if (!currentMessage.tool_calls) {
                      currentMessage.tool_calls = [];
                    }
                    if (content.type === 'stream') {
                      // Stream content goes to message content, not tool calls
                      currentMessage.content += content.content || '';
                    } else if (content.type === 'function_call') {
                      // Follow the function_call: update the current/matching tool call with name, input, position and reasoning
                      const idKey = typeof content.id === 'string' ? content.id : undefined;
                      const nameKey = content.name || '';
                      const posKey =
                        typeof content.position === 'number' ? content.position : undefined;

                      let targetToolCall: SchemaMessageToolComponentPublic | null = null;

                      // Prefer matching by explicit id if provided
                      if (idKey && currentMessage?.tool_calls) {
                        targetToolCall =
                          currentMessage.tool_calls.find((tc) => tc.id === idKey) || null;
                      }

                      // If not found by id, fall back to active by name/position, or last-active
                      if (!targetToolCall) {
                        const idByExplicitId = idKey ? activeFunctionStreamById[idKey] : undefined;
                        const idByName = nameKey
                          ? activeFunctionStreamByName[nameKey]
                          : undefined;
                        const idByPos =
                          posKey !== undefined
                            ? activeFunctionStreamByPosition[posKey]
                            : undefined;
                        const activeId: string | null | undefined =
                          idByExplicitId ?? idByName ?? idByPos ?? lastActiveFunctionStreamId ?? null;
                        if (activeId && currentMessage?.tool_calls) {
                          targetToolCall =
                            currentMessage.tool_calls.find((tc) => tc.id === activeId) || null;
                        }
                      }

                      // If still none exist, create a new tool call entry (use incoming id when available)
                      if (!targetToolCall) {
                        const toolCallId = idKey ?? generateId();
                        targetToolCall = {
                          id: toolCallId,
                          position: getNextToolPosition(),
                          tool_name: nameKey,
                          tool_input:
                            typeof content.content === 'object' && content.content
                              ? ((content.content as unknown) as Record<string, never>)
                              : ({} as Record<string, never>),
                          tool_output:
                            typeof content.content === 'string'
                              ? (content.content as string)
                              : '',
                          tool_reasoning: content.reasoning || '',
                          tool_runtime: 0,
                          created_at: new Date().toISOString(),
                        };
                        currentMessage.tool_calls!.push(targetToolCall);
                        // mark active
                        if (idKey) activeFunctionStreamById[idKey] = toolCallId;
                        if (nameKey) activeFunctionStreamByName[nameKey] = toolCallId;
                        if (posKey !== undefined)
                          activeFunctionStreamByPosition[posKey] = toolCallId;
                        lastActiveFunctionStreamId = toolCallId;
                      } else {
                        // If we matched an existing tool call, refresh active mappings
                        if (idKey) activeFunctionStreamById[idKey] = targetToolCall.id;
                        if (nameKey) activeFunctionStreamByName[nameKey] = targetToolCall.id;
                        if (posKey !== undefined)
                          activeFunctionStreamByPosition[posKey] = targetToolCall.id;
                        lastActiveFunctionStreamId = targetToolCall.id;
                      }

                      // Update fields from function_call
                      if (targetToolCall) {
                        currentToolCall = targetToolCall;
                        if (nameKey) currentToolCall.tool_name = nameKey;
                        if (typeof content.position === 'number') {
                          currentToolCall.position = Math.max(
                            currentToolCall.position ?? 0,
                            content.position,
                          );
                        }
                        if (typeof content.reasoning === 'string') {
                          currentToolCall.tool_reasoning = content.reasoning;
                        }
                        if (content.content && typeof content.content === 'object') {
                          currentToolCall.tool_input = (content.content as unknown) as Record<string, never>;
                        } else if (typeof content.content === 'string') {
                          // In rare cases string payloads, append to output as diagnostic
                          const prevOut = currentToolCall.tool_output || '';
                          currentToolCall.tool_output = `${prevOut}${content.content}`;
                        }
                      }
                    } else if (content.type === 'function_stream') {
                      // Create once per function key (prefer explicit id), then update subsequent chunks
                      const idKey = typeof content.id === 'string' ? content.id : undefined;
                      const nameKey = content.name || '';
                      const posKey =
                        typeof content.position === 'number' ? content.position : undefined;
                      let targetToolCall: SchemaMessageToolComponentPublic | null = null;

                      // Prefer matching by explicit id if provided
                      if (idKey && currentMessage?.tool_calls) {
                        targetToolCall =
                          currentMessage.tool_calls.find((tc) => tc.id === idKey) || null;
                      }

                      // If no active match by id, prefer active by name or position
                      if (!targetToolCall) {
                        const idByExplicitId = idKey ? activeFunctionStreamById[idKey] : undefined;
                        const idByName = nameKey
                          ? activeFunctionStreamByName[nameKey]
                          : undefined;
                        const idByPos =
                          posKey !== undefined
                            ? activeFunctionStreamByPosition[posKey]
                            : undefined;
                        const activeId: string | null | undefined = idByExplicitId ?? idByName ?? idByPos ?? lastActiveFunctionStreamId ?? null;
                        if (activeId && currentMessage?.tool_calls) {
                          targetToolCall =
                            currentMessage.tool_calls.find((tc) => tc.id === activeId) || null;
                        }
                      }

                      // If still none found, create a new tool call for this streamed function (use incoming id when available)
                      if (!targetToolCall) {
                        const toolCallId = idKey ?? generateId();
                        targetToolCall = {
                          id: toolCallId,
                          position: getNextToolPosition(),
                          tool_name: nameKey,
                          tool_input:
                            typeof content.content === 'object' && content.content
                              ? ((content.content as unknown) as Record<string, never>)
                              : ({} as Record<string, never>),
                          tool_output:
                            typeof content.content === 'string'
                              ? (content.content as string)
                              : '',
                          tool_reasoning: content.reasoning || '',
                          tool_runtime: 0,
                          created_at: new Date().toISOString(),
                        };
                        currentMessage.tool_calls!.push(targetToolCall);
                        // mark as active for subsequent chunks
                        if (idKey) activeFunctionStreamById[idKey] = toolCallId;
                        if (nameKey) activeFunctionStreamByName[nameKey] = toolCallId;
                        if (posKey !== undefined)
                          activeFunctionStreamByPosition[posKey] = toolCallId;
                        lastActiveFunctionStreamId = toolCallId;
                      } else {
                        // If we matched an existing tool call, refresh active mappings
                        if (idKey) activeFunctionStreamById[idKey] = targetToolCall.id;
                        if (nameKey) activeFunctionStreamByName[nameKey] = targetToolCall.id;
                        if (posKey !== undefined)
                          activeFunctionStreamByPosition[posKey] = targetToolCall.id;
                        lastActiveFunctionStreamId = targetToolCall.id;
                      }

                      if (targetToolCall) {
                        currentToolCall = targetToolCall;
                        const incoming = content.content;
                        if (incoming && typeof incoming === 'object') {
                          const previousInput =
                            ((currentToolCall.tool_input as unknown) as Record<string, unknown>) ?? {};
                          const merged = {
                            ...previousInput,
                            ...(incoming as Record<string, unknown>),
                          };
                          currentToolCall.tool_input = (merged as unknown) as Record<string, never>;
                          // Reset accumulator to the latest full object snapshot
                          try {
                            partialFunctionJsonByToolCallId[currentToolCall.id] = JSON.stringify(
                              incoming,
                            );
                          } catch {
                            // ignore stringify errors
                          }
                        } else if (typeof incoming === 'string') {
                          // Append streamed string chunks to tool_output due to schema typing
                          const previousOutput = currentToolCall.tool_output || '';
                          currentToolCall.tool_output = `${previousOutput}${incoming}`;

                          // Also accumulate for JSON reconstruction and attempt to parse when complete
                          const accumulatorKey = currentToolCall.id;
                          const prevAccumulated =
                            partialFunctionJsonByToolCallId[accumulatorKey] || '';
                          const nextAccumulated = `${prevAccumulated}${incoming}`;
                          partialFunctionJsonByToolCallId[accumulatorKey] = nextAccumulated;

                          // Try strict parse first
                          let parsed: unknown | null = tryParseJsonStrict(nextAccumulated.trim());
                          // If strict fails, attempt partial repair (balanced closures and string termination)
                          if (parsed == null) {
                            const repaired = parsePartialJsonWithClosures(nextAccumulated);
                            if (repaired != null) parsed = repaired as unknown;
                          }
                          // If we obtained a JSON object (or object-like), merge into tool_input
                          if (parsed && typeof parsed === 'object') {
                            const previousInput =
                              ((currentToolCall.tool_input as unknown) as Record<string, unknown>) ?? {};
                            const merged = {
                              ...previousInput,
                              ...(parsed as Record<string, unknown>),
                            };
                            currentToolCall.tool_input = (merged as unknown) as Record<string, never>;
                          }
                        }

                        if (typeof content.position === 'number') {
                          currentToolCall.position = Math.max(
                            currentToolCall.position ?? 0,
                            content.position,
                          );
                        }
                        if (typeof content.reasoning === 'string') {
                          currentToolCall.tool_reasoning = content.reasoning;
                        }
                      }
                    }

                    // Update UI
                    const convertedMessage = convertToUIMessage(currentMessage);
                    setStreamingMessages((prev) => {
                      const lastMessage = prev[prev.length - 1];
                      if (lastMessage?.id === convertedMessage.id) {
                        return [...prev.slice(0, -1), convertedMessage];
                      }
                      return [...prev, convertedMessage];
                    });
                    updateCurrentGlobalMessage(currentMessage);
                  } catch (error) {
                    handleStreamError(
                      error,
                      'stream/function_call/function_stream processing',
                    );
                    continue;
                  }
                } else if (content.type === 'function_result') {
                  try {
                    // Update the current agent thought's observation
                    const idKey = typeof content.id === 'string' ? content.id : undefined;

                    // Prefer selecting the correct tool call by id, falling back to currentToolCall
                    let targetToolCall: SchemaMessageToolComponentPublic | null = currentToolCall;
                    if (idKey && currentMessage?.tool_calls) {
                      const found = currentMessage.tool_calls.find((tc) => tc.id === idKey) || null;
                      if (found) targetToolCall = found;
                    }

                    if (targetToolCall) {
                      // Handle both string and object content
                      let observation = '';
                      if (typeof content.content === 'string') {
                        observation = content.content;
                      } else if (
                        content.content &&
                        typeof content.content === 'object'
                      ) {
                        // For function results that return structured data, stringify it
                        observation = JSON.stringify(content.content, null, 2);
                      }

                      targetToolCall.tool_output = observation;
                    }

                    // Update UI
                    const convertedMessage = convertToUIMessage(
                      currentMessage!,
                    );
                    setStreamingMessages((prev) => {
                      const lastMessage = prev[prev.length - 1];
                      if (lastMessage?.id === convertedMessage.id) {
                        return [...prev.slice(0, -1), convertedMessage];
                      }
                      return [...prev, convertedMessage];
                    });
                    // Function result processed - reset mappings for this id and current tool call
                    currentToolCall = null;
                    const nameKey = content.name || '';
                    const posKey = typeof content.position === 'number' ? content.position : undefined;
                    // Clear any partial JSON accumulator for this tool call
                    if (idKey) {
                      const resolvedId = activeFunctionStreamById[idKey] ?? idKey;
                      if (partialFunctionJsonByToolCallId[resolvedId]) {
                        delete partialFunctionJsonByToolCallId[resolvedId];
                      }
                    }
                    if (idKey && activeFunctionStreamById[idKey]) {
                      delete activeFunctionStreamById[idKey];
                    }
                    if (nameKey && activeFunctionStreamByName[nameKey]) {
                      delete activeFunctionStreamByName[nameKey];
                    }
                    if (posKey !== undefined && activeFunctionStreamByPosition[posKey]) {
                      delete activeFunctionStreamByPosition[posKey];
                    }
                    lastActiveFunctionStreamId = null;
                    updateCurrentGlobalMessage(currentMessage);
                  } catch (error) {
                    handleStreamError(error, 'function_result processing');
                    continue;
                  }
                } else if (content.type === 'display_component') {
                  try {
                    // Handle display component event like tables, charts, etc.
                    if (!currentMessage) {
                      currentMessage = {
                        id: content.message_id || generateId(),
                        content: '',
                        role: role,
                        created_at: new Date(
                          Math.floor(Date.now() / 1000),
                        ).toISOString(),
                        is_interrupt: false,
                        tool_calls: [],
                        display_components: [],
                      };
                    }

                    // Ensure display_components array is initialized
                    if (!currentMessage.display_components) {
                      currentMessage.display_components = [];
                    }

                    // Check if this component already exists
                    const componentExists =
                      currentMessage.display_components.some(
                        (comp) => comp.id === content.content.id,
                      );

                    if (!componentExists) {
                      // Create display component using API provided position
                      const displayComponent: SchemaMessageDisplayComponentPublic =
                        {
                          id: content.content.id,
                          type: content.content.type,
                          chart_type: content.content.chart_type || null,
                          title: content.content.title || null,
                          description: content.content.description || null,
                          data: content.content.data,
                          position: content.content.position,
                          created_at:
                            content.content.created_at ||
                            new Date().toISOString(),
                        };

                      // Add to message
                      currentMessage.display_components.push(displayComponent);

                      // Update UI
                      const convertedMessage =
                        convertToUIMessage(currentMessage);
                      setStreamingMessages((prev) => {
                        const lastMessage = prev[prev.length - 1];
                        if (lastMessage?.id === convertedMessage.id) {
                          return [...prev.slice(0, -1), convertedMessage];
                        }
                        return [...prev, convertedMessage];
                      });
                      currentToolCall = null;
                      updateCurrentGlobalMessage(currentMessage);

                      // Handle recommendations table directly
                      if (content.content.type === 'table') {
                        // Check if this is a recommendations table by looking at the data structure
                        const tableData = content.content.data;
                        if (tableData?.headers && tableData?.rows) {
                          // Check if headers contain recommendation-related columns
                          const headers = tableData.headers.map((h: unknown): string => {
                            const maybeObj = h as { header?: unknown };
                            const value =
                              maybeObj && typeof maybeObj === 'object' && 'header' in maybeObj
                                ? maybeObj.header
                                : h;
                            return typeof value === 'string' ? value : String(value ?? '');
                          });

                          const hasRecommendationHeaders = headers.some(
                            (header: string) =>
                              header.toLowerCase().includes('title') ||
                              header.toLowerCase().includes('description') ||
                              header.toLowerCase().includes('savings') ||
                              header.toLowerCase().includes('effort') ||
                              header.toLowerCase().includes('risk'),
                          );

                          if (
                            hasRecommendationHeaders &&
                            tableData.rows.length > 0
                          ) {
                            // Convert table data to streaming recommendations
                            const recommendations = tableData.rows.map(
                              (row: unknown, index: number) => {
                                const findColumnValue = (
                                  columnNames: string[],
                                ): string => {
                                  for (const columnName of columnNames) {
                                    const headerIndex = headers.findIndex(
                                      (h: string) =>
                                        h &&
                                        h
                                          .toLowerCase()
                                          .includes(columnName.toLowerCase()),
                                    );
                                    if (
                                      headerIndex !== -1 &&
                                      (Array.isArray(row)
                                        ? (row as unknown[])[headerIndex]
                                        : undefined) != null
                                    ) {
                                      return String((row as unknown[])[headerIndex]);
                                    }
                                  }
                                  return '';
                                };

                                const findNumericValue = (
                                  columnNames: string[],
                                ): number => {
                                  for (const columnName of columnNames) {
                                    const headerIndex = headers.findIndex(
                                      (h: string) =>
                                        h &&
                                        h
                                          .toLowerCase()
                                          .includes(columnName.toLowerCase()),
                                    );
                                    if (
                                      headerIndex !== -1 &&
                                      (Array.isArray(row)
                                        ? (row as unknown[])[headerIndex]
                                        : undefined) != null
                                    ) {
                                      const value = (row as unknown[])[headerIndex];
                                      if (typeof value === 'number')
                                        return value;
                                      if (typeof value === 'string') {
                                        if (
                                          value.toLowerCase() === 'none' ||
                                          value.toLowerCase() === 'n/a'
                                        )
                                          return 0;
                                        const cleaned = value.replace(
                                          /[$,]/g,
                                          '',
                                        );
                                        const parsed = parseFloat(cleaned);
                                        if (!isNaN(parsed)) return parsed;
                                      }
                                    }
                                  }
                                  return 0;
                                };

                                return {
                                  tempId: `temp_${Date.now()}_${index}_${Math.random().toString(36).substring(2, 9)}`,
                                  type: 'streaming' as const,
                                  data: {
                                    headers: tableData.headers,
                                    rows: [row as unknown[]],
                                  },
                                  status: 'pending' as const,
                                  transformedData: {
                                    title:
                                      findColumnValue(['title']) ||
                                      'Untitled Recommendation',
                                    description:
                                      findColumnValue(['description']) ||
                                      'No description available',
                                    type:
                                      findColumnValue(['type']) ||
                                      'optimization',
                                    potential_savings: findNumericValue([
                                      'monthly savings ($)',
                                      'monthly savings',
                                      'savings',
                                      'potential_savings',
                                    ]),
                                    effort:
                                      findColumnValue(['effort']) || 'medium',
                                    risk: findColumnValue(['risk']) || 'low',
                                  },
                                };
                              },
                            );

                            setCurrentRecommendations(recommendations);
                          }
                        }
                      }
                    }
                  } catch (error) {
                    handleStreamError(error, 'display_component processing');
                    continue;
                  }
                } else if (content.type === 'chart_data') {
                  try {
                    // Handle chart data event
                    if (!currentMessage) {
                      currentMessage = {
                        id: content.message_id || generateId(),
                        content: '',
                        role: role,
                        created_at: new Date(
                          Math.floor(Date.now() / 1000),
                        ).toISOString(),
                        is_interrupt: false,
                        tool_calls: [],
                        display_components: [],
                      };
                    }

                    // Ensure display_components array is initialized
                    if (!currentMessage.display_components) {
                      currentMessage.display_components = [];
                    }

                    // Check if this chart component already exists
                    const componentExists =
                      currentMessage.display_components.some(
                        (comp) => comp.id === content.content.id,
                      );

                    if (!componentExists) {
                      // Create a new display component for the chart using API provided position
                      const chartComponent: SchemaMessageDisplayComponentPublic =
                        {
                          id: content.content.id,
                          type: MessageDisplayComponentType.chart,
                          chart_type: content.content.chart_type,
                          title: null,
                          description: null,
                          data: content.content.data,
                          position: content.content.position || 0,
                          created_at: new Date().toISOString(),
                        };

                      currentMessage.display_components.push(chartComponent);
                      const convertedMessage =
                        convertToUIMessage(currentMessage);
                      setStreamingMessages((prev) => {
                        const lastMessage = prev[prev.length - 1];
                        if (lastMessage?.id === convertedMessage.id) {
                          return [...prev.slice(0, -1), convertedMessage];
                        }
                        return [...prev, convertedMessage];
                      });
                      currentToolCall = null;
                      updateCurrentGlobalMessage(currentMessage);
                    }
                  } catch (error) {
                    handleStreamError(error, 'chart_data processing');
                    continue;
                  }
                } else if (
                  content.type === 'on_recommendation_generation_response'
                ) {
                  // Skip updating UI here since we already have the components
                  continue;
                } else if (content.type === 'on_report_generation_response') {
                  try {
                    // Handle report generation response
                    const reportData = content.content;
                    setCurrentReport(reportData);
                  } catch (error) {
                    console.error('❌ Error processing report:', error);
                    handleStreamError(
                      error,
                      'report generation response processing',
                    );
                    continue;
                  }
                } else if (
                  content.type === 'on_dashboard_generation_response'
                ) {
                  try {
                    // Handle dashboard generation response
                    const dashboardData = content.content;
                    setCurrentDashboard(dashboardData);
                  } catch (error) {
                    console.error('❌ Error processing dashboard:', error);
                    handleStreamError(
                      error,
                      'dashboard generation response processing',
                    );
                    continue;
                  }
                } else if (content.type === 'error') {
                  try {
                    toast({
                      variant: 'destructive',
                      title: 'Error',
                      description: content.content,
                    });
                    break;
                  } catch (error) {
                    handleStreamError(error, 'error message processing');
                    break;
                  }
                } else if (content.type === 'thinking') {
                  try {
                    // For reconnections, set streaming state when we see live streaming events
                    if (isReconnection && !hasSeenLiveEvent) {
                      hasSeenLiveEvent = true;
                      setIsStreaming(true);
                      console.log(
                        '🎯 [STREAMING INDICATOR] Detected live thinking event during reconnection, enabling streaming indicator',
                      );
                      console.log(
                        '🎯 [STREAMING INDICATOR] isStreaming state should now be TRUE',
                      );
                    }

                    // Handle thinking stream type
                    setThinkingContent(content.content || 'Working...');

                    // Don't create a new message, just update thinking state
                    // This allows UI to show "Working..." or actual thinking content
                  } catch (error) {
                    handleStreamError(error, 'thinking processing');
                    continue;
                  }
                } else if (content.type === 'planning') {
                  try {
                    // For reconnections, set streaming state when we see live streaming events
                    if (isReconnection && !hasSeenLiveEvent) {
                      hasSeenLiveEvent = true;
                      setIsStreaming(true);
                      console.log(
                        'Detected live planning event during reconnection, enabling streaming indicator',
                      );
                    }

                    // Handle planning event
                    setPlanningContent(content.content);
                  } catch (error) {
                    handleStreamError(error, 'planning processing');
                    continue;
                  }
                } else if (content.type === 'user_message') {
                  try {
                    // Handle user message event from Redis during reconnection
                    // This provides complete conversation context
                    if (
                      processedMessageIds.current.has(content.message_id || '')
                    ) {
                      console.log(
                        'Skipping duplicate user message:',
                        content.message_id,
                      );
                      continue;
                    }

                    // Add to processed messages to prevent duplicates
                    if (content.message_id) {
                      processedMessageIds.current.add(content.message_id);
                    }

                    // Create user message for UI
                    const userMessage: ChatMessage = {
                      id: content.message_id || generateId(),
                      role: 'user',
                      content: content.content || '',
                      timestamp: content.timestamp
                        ? new Date(content.timestamp)
                        : new Date(),
                    };

                    // Add user message to streaming messages
                    setStreamingMessages((prev) => {
                      // Check if message already exists to prevent duplicates
                      const existingMessageIndex = prev.findIndex(
                        (msg) => msg.id === userMessage.id,
                      );
                      if (existingMessageIndex !== -1) {
                        return prev; // Message already exists, don't add
                      }

                      return [...prev, userMessage];
                    });

                    console.log('Added user message from Redis:', userMessage);
                  } catch (error) {
                    handleStreamError(error, 'user_message processing');
                    continue;
                  }
                } else if (content.type === 'interrupt') {
                  try {
                    // Handle interrupt event
                    const interruptContent =
                      typeof content.content === 'string'
                        ? content.content
                        : content.content?.value ||
                          'Would you like to proceed?';

                    const formattedMessage = {
                      value: interruptContent,
                      resumable: content.content?.resumable ?? true,
                      ns: content.content?.ns,
                      when: content.content?.when || new Date().toISOString(),
                    };

                    // Remove the latest tool call to avoid duplicate tool renders
                    if (
                      currentMessage?.tool_calls &&
                      currentMessage.tool_calls.length > 0
                    ) {
                      const removedToolCall =
                        currentMessage.tool_calls[
                          currentMessage.tool_calls.length - 1
                        ];

                      // Remove from the message
                      currentMessage.tool_calls = currentMessage.tool_calls.slice(
                        0,
                        -1,
                      );

                      // Clean up mappings/accumulators for the removed tool call
                      if (removedToolCall?.id) {
                        if (activeFunctionStreamById[removedToolCall.id]) {
                          delete activeFunctionStreamById[removedToolCall.id];
                        }
                        if (
                          partialFunctionJsonByToolCallId[removedToolCall.id]
                        ) {
                          delete partialFunctionJsonByToolCallId[
                            removedToolCall.id
                          ];
                        }
                      }
                      if (removedToolCall?.tool_name) {
                        const nameKey = removedToolCall.tool_name;
                        if (activeFunctionStreamByName[nameKey] === removedToolCall.id) {
                          delete activeFunctionStreamByName[nameKey];
                        }
                      }
                      if (typeof removedToolCall?.position === 'number') {
                        const posKey = removedToolCall.position;
                        if (activeFunctionStreamByPosition[posKey] === removedToolCall.id) {
                          delete activeFunctionStreamByPosition[posKey];
                        }
                      }

                      // Point current tool call to the previous one if any
                      currentToolCall =
                        currentMessage.tool_calls[
                          currentMessage.tool_calls.length - 1
                        ] || null;

                      // Update UI immediately so the duplicate tool disappears
                      const convertedMessage = convertToUIMessage(currentMessage);
                      setStreamingMessages((prev) => {
                        const lastMessage = prev[prev.length - 1];
                        if (lastMessage?.id === convertedMessage.id) {
                          return [...prev.slice(0, -1), convertedMessage];
                        }
                        return [...prev, convertedMessage];
                      });
                      updateCurrentGlobalMessage(currentMessage);
                    }

                    // Important: Stop the stream completely
                    if (abortControllerRef.current) {
                      abortControllerRef.current.abort();
                    }

                    // Set streaming to false immediately
                    setIsStreaming(false);

                    // Set the confirmation dialog
                    const activeConversationId =
                      activeStreamConversationIdRef.current || conversationId;

                    if (activeConversationId) {
                      setInterruptConfirmation(
                        createInterruptHandlers(
                          formattedMessage,
                          activeConversationId,
                          handleSendMessage,
                          setInterruptConfirmation,
                        ),
                      );
                    }
                    // Break out of the while loop to stop streaming
                    break;
                  } catch (error) {
                    handleStreamError(error, 'interrupt processing');
                    break;
                  }
                }
                if (content.type === 'complete') {
                  try {
                    console.log(
                      '🎯 [STREAMING INDICATOR] Complete event received, isReconnection:',
                      isReconnection,
                      'hasSeenLiveEvent:',
                      hasSeenLiveEvent,
                      'currentMessage:',
                      currentMessage?.id,
                    );

                    // Always turn off streaming for complete events
                    // The logic should be: if this complete event is for the current active message, turn off streaming
                    // For reconnections, we should turn off streaming when we get the complete event for the current message
                    console.log(
                      '🎯 [STREAMING INDICATOR] Setting isStreaming to FALSE - stream completed',
                    );
                    setIsStreaming(false);

                    setThinkingContent(null); // Clear thinking content when stream completes

                    // Play notification sound when stream completes
                    playNotificationSound();

                    // Invalidate quota query when stream completes
                    if (user?.id) {
                      queryClient.invalidateQueries({
                        queryKey: ['quota-info', user.id],
                      });
                    }
                    // Dispatch stream-end event
                  } catch (error) {
                    handleStreamError(error, 'complete processing');
                  }
                }
              } catch (error) {
                // Check if the error is related to abort
                if (
                  error instanceof Error &&
                  (error.name === 'AbortError' ||
                    error.message.includes('abort') ||
                    error.message.includes('cancel'))
                ) {
                  await cancelReader();
                  break;
                }

                // Use centralized error handling
                handleStreamError(error, 'message processing');

                // Continue to next line instead of breaking the entire stream
                continue;
              }
            }
          }
        } catch (error) {
          // Handle read loop errors
          if (
            error instanceof Error &&
            (error.name === 'AbortError' ||
              error.message.includes('abort') ||
              error.message.includes('cancel'))
          ) {
            await cancelReader();
            break;
          }

          // Use centralized error handling for other errors
          handleStreamError(error, 'stream read loop');

          // Try to recover by breaking out of the loop
          break;
        }
      }
    } catch (error) {
      // Handle fatal errors that escape the inner try-catch blocks
      handleStreamError(error, 'fatal stream error');
    } finally {
      // Ensure reader is canceled and reset streaming state
      try {
        await cancelReader();
      } catch (cleanupError) {
        console.error('Error during reader cleanup:', cleanupError);
      }

      // For reconnections, only reset streaming if we never saw live events
      // For regular streams, reset streaming state when stream ends
      // Special case: if we saw live events during reconnection but stream truly ended, reset it
      const shouldResetStreaming =
        !isReconnection ||
        !hasSeenLiveEvent ||
        !abortControllerRef.current ||
        abortControllerRef.current.signal.aborted;

      if (shouldResetStreaming) {
        console.log(
          '🎯 [STREAMING INDICATOR] Finally block resetting isStreaming to FALSE, shouldResetStreaming:',
          shouldResetStreaming,
          'isReconnection:',
          isReconnection,
          'hasSeenLiveEvent:',
          hasSeenLiveEvent,
        );
        setIsStreaming(false);
        setThinkingContent(null); // Clear thinking content when stream stops
        // Note: Don't clear planning content - it should persist after stream completes

        // Clear any pending abort controller
        if (
          abortControllerRef.current &&
          typeof abortControllerRef.current.abort === 'function'
        ) {
          try {
            abortControllerRef.current.abort();
          } catch (abortError) {
            console.warn(
              'Error aborting controller in fatal error handler:',
              abortError,
            );
          }
          abortControllerRef.current = null;
        }
      } else {
        console.log(
          'Keeping streaming state active after reconnection processing',
        );
      }
    }
  };

  // When sending a message, only use the stream for the new message data
  // Don't re-fetch the conversation history
  const handleSendMessage = useCallback(
    async (
      content: string,
      attachmentIds?: string[],
      resourceId?: string,
      resumeOptions?: { resume: boolean; approve: boolean },
      overrideConversationId?: string,
    ) => {
      if (abortControllerRef.current) {
        // Cancel any ongoing stream
        try {
          abortControllerRef.current.abort();
        } catch (error) {
          console.warn('Error aborting previous controller:', error);
        }
        abortControllerRef.current = null;
      }

      try {
        // Main message sending logic
        try {
          // Create a new abort controller for this stream
          const abortController = new AbortController();
          abortControllerRef.current = abortController;

          // Set streaming state to true
          setIsStreaming(true);

          // If a resourceId is provided, update the conversation resource ID immediately
          if (resourceId) {
            setConversationResourceId(resourceId);
          }

          // Add user message to UI (skip for empty resume messages)
          if (content.trim() || !resumeOptions?.resume) {
            const tempId = uuidv4();
            const userMessage: ChatMessage = {
              id: tempId,
              role: 'user',
              content: content,
              timestamp: new Date(), // Use Date object instead of string
              attachmentIds: attachmentIds, // Include attachment IDs for immediate display
            };

            // Update streaming messages to include the user message
            setStreamingMessages((prev) => [...prev, userMessage]);
          }

          // Stream the response
          let response: Response;

          try {
            // Use the most reliable conversationId: override > active stream ref > current message > prop
            const activeConversationId =
              overrideConversationId ||
              activeStreamConversationIdRef.current ||
              conversationId;

            response = await chatStream(
              {
                conversationId: activeConversationId,
                message: {
                  content: content,
                  resume: resumeOptions?.resume || false,
                  approve: resumeOptions?.approve ?? true,
                  attachment_ids: attachmentIds,
                  resource_id: resourceId,
                },
              },
              abortController,
            );

            if (!response.body) {
              throw new Error('No response body received');
            }

            // Pass the options to processMessageStream
            await processMessageStream(
              response.body.getReader(),
              {
                onRecommendations: options?.onRecommendations,
                resourceId: options?.resourceId,
                onConversationCreated: options?.onConversationCreated,
              },
              false,
            ); // Not a reconnection
          } catch (error) {
            // Add type annotation
            console.error('Chat stream error:', error);

            if (error instanceof Error && error.name === 'AbortError') {
              // AbortError is expected when stopping the stream
              console.log('Stream abort completed');
            } else {
              try {
                // Create error message with proper type
                const errorMessage: ChatMessage = {
                  id: uuidv4(),
                  role: 'assistant',
                  content:
                    'Sorry, I encountered an error while processing your request. Please try again.',
                  timestamp: new Date(),
                };
                setStreamingMessages((prev) => [...prev, errorMessage]);

                // Show user-friendly error toast
                toast({
                  variant: 'destructive',
                  title: 'Connection Error',
                  description:
                    'Failed to send message. Please check your connection and try again.',
                });
              } catch (recoveryError) {
                console.error('Error during error recovery:', recoveryError);
                // Last resort: just show a simple toast
                try {
                  toast({
                    variant: 'destructive',
                    title: 'Error',
                    description:
                      'An unexpected error occurred. Please refresh the page.',
                  });
                } catch (toastError) {
                  console.error('Failed to show error toast:', toastError);
                }
              }
            }
          }
        } finally {
          // Ensure we clean up properly
          setIsStreaming(false);
          setThinkingContent(null); // Clear thinking content when stream stops
          // Note: Don't clear planning content - it should persist after stream completes

          if (abortControllerRef.current) {
            abortControllerRef.current = null;
          }
        }
      } catch (fatalError) {
        // Top-level error handler to prevent page crashes
        console.error('Fatal error in handleSendMessage:', fatalError);

        // Ensure streaming state is reset
        setIsStreaming(false);

        // Clear abort controller
        if (abortControllerRef.current) {
          try {
            (abortControllerRef.current as AbortController).abort();
          } catch (abortError) {
            console.warn(
              'Error aborting controller in fatal error handler:',
              abortError,
            );
          }
          abortControllerRef.current = null;
        }

        // Show user-friendly error message
        try {
          toast({
            variant: 'destructive',
            title: 'Critical Error',
            description:
              'A critical error occurred. Please refresh the page and try again.',
          });
        } catch (toastError) {
          console.error('Failed to show critical error toast:', toastError);
        }
      }
    },
    [conversationId, setStreamingMessages, options],
  );

  const clearStreamingMessages = useCallback(() => {
    setStreamingMessages([]);
    setConversationResourceId(null);
    setHasReport(false);
    setHasDashboard(false);
    setCurrentReport(null);
    setCurrentDashboard(null);
    setThinkingContent(null);
    setPlanningContent(null);
    // Clear processed message IDs when clearing messages
    processedMessageIds.current.clear();
  }, []);

  const stopStream = useCallback(() => {
    // First set streaming to false to update UI immediately
    setIsStreaming(false);
    setThinkingContent(null); // Clear thinking content when manually stopping

    // Function to cancel server-side stream
    const cancelServerStream = async () => {
      const activeConversationId =
        activeStreamConversationIdRef.current || conversationId;

      if (activeConversationId) {
        try {
          // Call server-side cancel endpoint
          const baseUrl = String(OpenAPI.BASE).trim().replace(/\/+$/, '');
          const cancelUrl = new URL(
            `${baseUrl}/api/v1/autonomous-agents/chat/${activeConversationId}/cancel-stream`,
          );

          let tokenValue = await OpenAPI.TOKEN;
          if (typeof tokenValue === 'function') {
            tokenValue = await tokenValue({
              method: 'POST',
              url: cancelUrl.toString(),
            });
          }

          if (!tokenValue) {
            throw new Error('Authentication token is required');
          }

          // Sanitize the token
          const sanitizedToken = String(tokenValue)
            .trim()
            .replace(/[\r\n]+/g, '');

          await fetch(cancelUrl.toString(), {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${sanitizedToken}`,
            },
          });

          console.log('Server-side stream cancelled successfully');
        } catch (error) {
          console.error('Error cancelling stream on server:', error);
        }
      }
    };

    // Attempt to abort the stream controller if available
    if (abortControllerRef.current) {
      try {
        // Abort client-side stream
        abortControllerRef.current.abort();
        // Also cancel on server - intentionally not awaiting to avoid blocking UI
        void cancelServerStream();
      } catch (err) {
        console.error('Error when aborting stream:', err);
      } finally {
        // Always clear the abort controller reference
        abortControllerRef.current = null;
        // Dispatch stream-end event when manually stopped
        window.dispatchEvent(new Event('stream-end'));
        // Play notification sound when stream is manually stopped
        playNotificationSound();
      }
    } else {
      console.warn('No abort controller available to stop stream');
      // Still try to cancel server-side stream - intentionally not awaiting to avoid blocking UI
      void cancelServerStream();
      // Still dispatch stream-end event even if no controller
      window.dispatchEvent(new Event('stream-end'));
      // Play notification sound even if no controller
      playNotificationSound();
    }

    // Force any async processes to recognize the stream has stopped
    if (typeof window !== 'undefined') {
      // Use a timeout to ensure any pending UI updates are processed
      setTimeout(() => {
        if (isStreaming) {
          setIsStreaming(false);
        }
      }, 100);
    }
  }, [isStreaming, conversationId]);

  return {
    isStreaming,
    streamingMessages,
    handleSendMessage,
    clearStreamingMessages,
    interruptConfirmation,
    setInterruptConfirmation,
    stopStream,
    isLoadingMessages,
    currentReport,
    currentDashboard,
    currentRecommendations,
    thinkingContent,
    planningContent,
    // MessagePublicList fields
    conversationResourceId,
    hasReport,
    hasDashboard,
  };
}
