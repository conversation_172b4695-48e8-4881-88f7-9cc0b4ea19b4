'use client';

import { MessageFeedbackService } from '@/client/sdk.gen';
import {
  FeedbackType,
  MessageFeedbackCreate,
  MessageFeedbackUpdate,
} from '@/client/types.gen';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export function useMessageFeedback(messageId: string) {
  const queryClient = useQueryClient();

  // Query to get existing feedback for a message
  const { data: feedback, isLoading } = useQuery({
    queryKey: ['message-feedback', messageId],
    queryFn: () =>
      MessageFeedbackService.getMessageFeedback({ messageId: messageId }),
    enabled: !!messageId,
  });

  // Mutation to create feedback
  const createFeedbackMutation = useMutation({
    mutationFn: (data: MessageFeedbackCreate) =>
      MessageFeedbackService.createMessageFeedback({ requestBody: data }),
    onSuccess: (data) => {
      queryClient.setQueryData(['message-feedback', messageId], data);
      toast.success('Feedback submitted successfully!');
    },
    onError: (error: any) => {
      toast.error(error?.body?.detail || 'Failed to submit feedback');
    },
  });

  // Mutation to update feedback
  const updateFeedbackMutation = useMutation({
    mutationFn: (data: MessageFeedbackUpdate) =>
      MessageFeedbackService.updateMessageFeedback({
        messageId: messageId,
        requestBody: data,
      }),
    onSuccess: (data) => {
      queryClient.setQueryData(['message-feedback', messageId], data);
      toast.success('Feedback updated successfully!');
    },
    onError: (error: any) => {
      toast.error(error?.body?.detail || 'Failed to update feedback');
    },
  });

  // Mutation to delete feedback
  const deleteFeedbackMutation = useMutation({
    mutationFn: () =>
      MessageFeedbackService.deleteMessageFeedback({ messageId: messageId }),
    onSuccess: () => {
      queryClient.setQueryData(['message-feedback', messageId], null);
      toast.success('Feedback removed successfully!');
    },
    onError: (error: any) => {
      toast.error(error?.body?.detail || 'Failed to remove feedback');
    },
  });

  // Helper function to submit feedback
  const submitFeedback = (
    feedbackType: FeedbackType,
    reason?: string,
    additionalComments?: string,
  ) => {
    const feedbackData: MessageFeedbackCreate = {
      message_id: messageId,
      feedback_type: feedbackType,
      reason: reason || null,
      additional_comments: additionalComments || null,
    };

    if (feedback) {
      // Update existing feedback
      const updateData: MessageFeedbackUpdate = {
        feedback_type: feedbackType,
        reason: reason || null,
        additional_comments: additionalComments || null,
      };
      updateFeedbackMutation.mutate(updateData);
    } else {
      // Create new feedback
      createFeedbackMutation.mutate(feedbackData);
    }
  };

  return {
    feedback,
    isLoading,
    submitFeedback,
    deleteFeedback: () => deleteFeedbackMutation.mutate(),
    isSubmitting:
      createFeedbackMutation.isPending || updateFeedbackMutation.isPending,
    isDeleting: deleteFeedbackMutation.isPending,
  };
}
