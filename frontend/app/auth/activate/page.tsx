'use client';

import { Suspense, useEffect, useState } from 'react';

import { useRouter, useSearchParams } from 'next/navigation';

import { AuthService } from '@/client';
import { BrandingSection } from '@/components/BrandingSection';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { CloudThinkerLogoFull } from '@/components/ui/logo';
import { Progress } from '@/components/ui/progress';
import pathsConfig from '@/config/paths.config';
import { ArrowRight, CheckCircle2, Loader2, XCircle } from 'lucide-react';

function ActivatePageContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const token = searchParams.get('token');
  const [isLoading, setIsLoading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [status, setStatus] = useState<
    'idle' | 'loading' | 'success' | 'error'
  >('idle');
  const [countdown, setCountdown] = useState(5);

  useEffect(() => {
    if (token) {
      handleActivation(token);
    }
  }, [token]);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isLoading && status === 'loading') {
      interval = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 90) return prev;
          return prev + 10;
        });
      }, 500);
    }
    return () => clearInterval(interval);
  }, [isLoading, status]);

  useEffect(() => {
    let countdownInterval: NodeJS.Timeout;
    if (status === 'success' && countdown > 0) {
      countdownInterval = setInterval(() => {
        setCountdown((prev) => prev - 1);
      }, 1000);
    } else if (status === 'success' && countdown === 0) {
      router.replace(pathsConfig.app.home);
    }
    return () => clearInterval(countdownInterval);
  }, [status, countdown, router]);

  const handleActivation = async (activationToken: string) => {
    try {
      setIsLoading(true);
      setStatus('loading');
      setProgress(0);

      const response = await AuthService.activateAccount({
        token: activationToken,
      });

      if (response.success) {
        setStatus('success');
        setProgress(100);
        setCountdown(5);
      }
    } catch {
      setStatus('error');
      setProgress(0);
    } finally {
      setIsLoading(false);
    }
  };

  const handleImmediateRedirect = () => {
    router.push(pathsConfig.auth.signIn);
  };

  const renderStatusIcon = () => {
    switch (status) {
      case 'success':
        return (
          <CheckCircle2 className="animate-in fade-in-50 h-12 w-12 text-green-500" />
        );
      case 'error':
        return (
          <XCircle className="animate-in fade-in-50 h-12 w-12 text-red-500" />
        );
      case 'loading':
        return <Loader2 className="text-primary h-12 w-12 animate-spin" />;
      default:
        return null;
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Account Activation</CardTitle>
        <CardDescription>
          {token
            ? 'Activating your account...'
            : 'Enter your email to receive a new activation link'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {status === 'loading' && (
          <div className="space-y-4">
            <div className="flex justify-center">{renderStatusIcon()}</div>
            <Progress value={progress} className="w-full" />
            <p className="text-muted-foreground text-center text-sm">
              {token
                ? 'Activating your account...'
                : 'Sending activation link...'}
            </p>
          </div>
        )}

        {status === 'success' && (
          <div className="space-y-4">
            <div className="flex justify-center">{renderStatusIcon()}</div>
            <p className="text-center text-sm text-green-600 dark:text-green-400">
              {token
                ? 'Account activated successfully!'
                : 'Activation link sent successfully!'}
            </p>
            {token && (
              <div className="space-y-4">
                <p className="text-muted-foreground text-center text-sm">
                  Redirecting to onboarding in {countdown} seconds...
                </p>
                <Button className="w-full" onClick={handleImmediateRedirect}>
                  Go to Onboarding Now
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            )}
          </div>
        )}

        {status === 'error' && (
          <div className="space-y-4">
            <div className="flex justify-center">{renderStatusIcon()}</div>
            <p className="text-center text-sm text-red-600 dark:text-red-400">
              {token
                ? 'Invalid or expired activation link.'
                : 'Failed to send activation link.'}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export default function ActivatePage() {
  return (
    <div className="relative h-dvh flex-col items-center justify-center md:grid lg:max-w-none lg:grid-cols-2 lg:px-0">
      <BrandingSection
        title="Welcome to CloudThinker"
        description="Your account is almost ready. Just one more step to activate your account and start optimizing your cloud costs."
        image={{
          src: '/hero-banner.png',
          alt: 'Cloud Thinker Dashboard Preview',
        }}
        logo={
          <CloudThinkerLogoFull
          // Todo: Not sure why pass className when not using it in CloudThinkerLogoFull
          // className="mr-2 w-8 h-8"
          />
        }
        footer={
          <div className="space-y-2 text-sm text-white/70">
            <p>Need help? Contact our support team</p>
            <a
              href="mailto:<EMAIL>"
              className="transition-colors hover:text-white"
            >
              <EMAIL>
            </a>
          </div>
        }
      />

      <div className="relative flex h-full min-h-dvh items-center justify-center lg:min-h-0 lg:p-8">
        {/* Background effects */}
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,var(--tw-gradient-stops))] from-zinc-50 via-zinc-100 to-zinc-200 transition-all duration-300 dark:from-zinc-900 dark:via-zinc-900 dark:to-black"></div>
        <div className="animate-gradient-slow absolute inset-0 bg-linear-to-br from-blue-50/50 via-white/50 to-pink-50/50 dark:from-blue-900/10 dark:via-zinc-900/10 dark:to-purple-900/10"></div>

        <div className="relative w-full max-w-md">
          <Suspense
            fallback={
              <Card className="w-full">
                <CardHeader>
                  <CardTitle>Account Activation</CardTitle>
                  <CardDescription>Loading...</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-center">
                      <Loader2 className="text-primary h-12 w-12 animate-spin" />
                    </div>
                    <Progress value={0} className="w-full" />
                    <p className="text-muted-foreground text-center text-sm">
                      Loading activation details...
                    </p>
                  </div>
                </CardContent>
              </Card>
            }
          >
            <ActivatePageContent />
          </Suspense>
        </div>
      </div>
    </div>
  );
}
