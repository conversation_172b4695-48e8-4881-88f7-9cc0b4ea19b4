'use client';

import { useTransition } from 'react';

import Link from 'next/link';
import { useRouter } from 'next/navigation';

import { LoginService } from '@/client';
import { Icons } from '@/components/icons';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { PasswordInput } from '@/components/ui/password-input';
import pathsConfig from '@/config/paths.config';
import { cn } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import z from 'zod';

const formSchema = z
  .object({
    new_password: z
      .string()
      .min(8, { message: 'Password must be at least 8 characters' })
      .regex(/^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[^A-Za-z0-9]).+$/, {
        message:
          'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
      }),
    confirm_password: z.string(),
  })
  .refine((data) => data.new_password === data.confirm_password, {
    message: "Passwords don't match",
    path: ['confirm_password'],
  });

type Props = {
  token: string;
};

export function ResetPasswordForm({ token }: Props) {
  const [loading, startTransition] = useTransition();
  const router = useRouter();

  const defaultValues = {
    new_password: '',
    confirm_password: '',
  };

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues,
  });

  const onSubmit = async (data: z.infer<typeof formSchema>) => {
    if (!token) {
      toast.error('Invalid reset link', {
        description: 'Please request a new password reset link.',
      });
      return;
    }

    startTransition(async () => {
      try {
        await LoginService.resetPassword({
          requestBody: {
            token,
            new_password: data.new_password,
          },
        });

        toast.success('Password reset successful', {
          description:
            'Your password has been updated. Please log in with your new password.',
        });

        router.replace(pathsConfig.auth.signIn);
      } catch (error: any) {
        toast.error('Failed to reset password', {
          description:
            error.message || 'Please try again or request a new reset link.',
        });
      }
    });
  };

  return (
    <div className="grid gap-6">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="new_password"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-zinc-700 dark:text-zinc-300">
                  New Password
                </FormLabel>
                <FormControl>
                  <PasswordInput
                    placeholder="••••••••"
                    disabled={loading}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="confirm_password"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-zinc-700 dark:text-zinc-300">
                  Confirm Password
                </FormLabel>
                <FormControl>
                  <PasswordInput
                    placeholder="••••••••"
                    disabled={loading}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <Button disabled={loading} type="submit" className="w-full">
            {loading && <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />}
            Reset Password
          </Button>
        </form>
      </Form>

      <div className="text-center text-sm">
        <p className="text-zinc-600 dark:text-zinc-400">
          Remember your password?{' '}
          <Link
            className={cn(
              'text-blue-600 hover:text-blue-500',
              'dark:text-blue-400 dark:hover:text-blue-300',
              'transition-colors duration-200',
              'font-medium',
            )}
            href={pathsConfig.auth.signIn}
          >
            Sign in
          </Link>
        </p>
      </div>
    </div>
  );
}
