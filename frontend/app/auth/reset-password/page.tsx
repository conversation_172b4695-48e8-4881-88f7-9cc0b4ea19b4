import { Suspense } from 'react';

import { Metadata } from 'next';

import { BrandingSection } from '@/components/BrandingSection';
import { cn } from '@/lib/utils';

import { ResetPasswordForm } from './_components/reset-password-form';

export const metadata: Metadata = {
  title: 'Reset Password',
  description: 'Set a new password for your CloudThinker account.',
};

// This page is accessed when users click the reset password link from their email
// It does not use internal application state and handles password reset via URL parameters
type Props = {
  searchParams: Promise<{
    token: string;
  }>;
};

export default async function Page({ searchParams }: Props) {
  const { token } = await searchParams;

  return (
    <div className="relative h-dvh flex-col items-center justify-center md:grid lg:max-w-none lg:grid-cols-2 lg:px-0">
      <BrandingSection
        title="Set New Password"
        description="Enter your new password below to complete the password reset process."
        image={{
          src: '/hero-banner.png',
          alt: 'Cloud Thinker Dashboard Preview',
        }}
      />

      {/* Enhanced right side with better styling */}
      <div className="relative flex h-full min-h-dvh items-center justify-center lg:min-h-0 lg:p-8">
        {/* Background effects */}
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,var(--tw-gradient-stops))] from-zinc-50 via-zinc-100 to-zinc-200 transition-all duration-300 dark:from-zinc-900 dark:via-zinc-900 dark:to-black"></div>

        {/* Subtle animated gradient */}
        <div className="animate-gradient-slow absolute inset-0 bg-linear-to-br from-blue-50/50 via-white/50 to-pink-50/50 dark:from-blue-900/10 dark:via-zinc-900/10 dark:to-purple-900/10"></div>

        {/* Main container */}
        <div className="relative mx-auto w-full max-w-lg px-4 sm:px-6 lg:px-8">
          {/* Glass card container */}
          <div
            className={cn(
              // Base styles
              'relative overflow-hidden',
              'rounded-xl p-4 sm:p-6 lg:p-8',
              // Background and border
              'bg-white/80 dark:bg-zinc-900/80',
              'backdrop-blur-xl',
              'border border-zinc-200/50 dark:border-zinc-800/50',
              // Shadow effects
              'shadow-[0_2px_4px_rgba(0,0,0,0.02),0_8px_24px_rgba(0,0,0,0.06)]',
              'dark:shadow-[0_2px_4px_rgba(0,0,0,0.2),0_8px_24px_rgba(0,0,0,0.3)]',
              // Transitions
              'transition-all duration-300',
            )}
          >
            {/* Welcome text */}
            <div className="mb-8 space-y-2 text-center">
              <h1
                className={cn(
                  'text-2xl font-semibold tracking-tight lg:text-3xl',
                  'bg-linear-to-br from-zinc-900 via-zinc-700 to-zinc-900',
                  'dark:from-white dark:via-zinc-200 dark:to-white',
                  'bg-clip-text text-transparent',
                  'transition-all duration-300',
                )}
              >
                Set New Password
              </h1>
              <p className="text-sm text-zinc-600 dark:text-zinc-400">
                Please enter your new password below
              </p>
            </div>

            {/* Reset Password form */}
            <div className="space-y-6">
              <Suspense fallback={<div>Loading...</div>}>
                <ResetPasswordForm token={token} />
              </Suspense>
            </div>
          </div>

          {/* Terms and privacy links */}
          <div className="mt-6 space-y-2 text-center">
            <p className="text-xs text-zinc-500 dark:text-zinc-400">
              By continuing, you agree to our{' '}
              <a
                href="https://www.cloudthinker.io/terms-of-service"
                className="underline transition-colors duration-200 hover:text-zinc-900 dark:hover:text-zinc-100"
              >
                Terms of Service
              </a>{' '}
              and{' '}
              <a
                href="https://www.cloudthinker.io/privacy-policy"
                className="underline transition-colors duration-200 hover:text-zinc-900 dark:hover:text-zinc-100"
              >
                Privacy Policy
              </a>
            </p>
          </div>
        </div>

        {/* Bottom decoration */}
        <div className="absolute right-0 bottom-0 left-0 h-px bg-linear-to-r from-transparent via-zinc-200 to-transparent dark:via-zinc-700"></div>
      </div>
    </div>
  );
}
