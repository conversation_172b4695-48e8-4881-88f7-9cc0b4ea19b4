'use client';

import { AppLogo } from '@/components/app-logo';
import DashboardGrid from '@/components/chat/autonomous/dashboard-content/dashboard-grid';
import { Button } from '@/components/ui/button';
import { If } from '@/components/ui/common/if';
import { Heading } from '@/components/ui/heading';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  DashboardWidget,
  GridConfig,
} from '@/features/dashboard/models/dashboard.type';
import { DateFormat, formatDateWithTimezone } from '@/lib/date-utils';
import { SchemaDashboard } from '@/openapi-ts/gens';
import { ExternalLink, LayoutDashboard } from 'lucide-react';

interface SharedDashboardViewProps {
  dashboard: SchemaDashboard;
}

export default function SharedDashboardView({
  dashboard,
}: SharedDashboardViewProps) {
  // Parse dashboard data if it's a string
  let dashboardData: SchemaDashboard;
  try {
    if (typeof dashboard === 'string') {
      dashboardData = JSON.parse(dashboard);
    } else if (typeof dashboard === 'object' && dashboard !== null) {
      dashboardData = dashboard;
    } else {
      // Invalid dashboard data, show empty state
      return (
        <div className="flex h-screen items-center justify-center">
          <div className="mx-auto flex max-w-md flex-col items-center gap-4 p-6 text-center">
            <div className="bg-muted/50 flex h-16 w-16 items-center justify-center rounded-full">
              <LayoutDashboard className="text-muted-foreground h-8 w-8" />
            </div>
            <div>
              <h3 className="text-foreground mb-2 text-lg font-semibold">
                Invalid Dashboard Data
              </h3>
              <p className="text-muted-foreground text-sm leading-relaxed">
                The dashboard data appears to be corrupted.
              </p>
            </div>
          </div>
        </div>
      );
    }
  } catch {
    // If parsing fails, show error state
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="mx-auto flex max-w-md flex-col items-center gap-4 p-6 text-center">
          <div className="bg-muted/50 flex h-16 w-16 items-center justify-center rounded-full">
            <LayoutDashboard className="text-muted-foreground h-8 w-8" />
          </div>
          <div>
            <h3 className="text-foreground mb-2 text-lg font-semibold">
              Dashboard Parsing Error
            </h3>
            <p className="text-muted-foreground text-sm leading-relaxed">
              Unable to parse the dashboard data.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen flex-col">
      {/* Header Bar */}
      <div className="bg-background/95 supports-[backdrop-filter]:bg-background/60 border-b backdrop-blur">
        <div className="container mx-auto flex h-16 items-center justify-between px-4">
          <AppLogo href="/" />
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.open('/', '_blank')}
            className="flex items-center gap-2"
          >
            <ExternalLink className="h-4 w-4" />
            Open App
          </Button>
        </div>
      </div>

      {/* Dashboard Content */}
      <ScrollArea className="flex-1">
        <div className="w-full py-2 md:px-4">
          {/* Dashboard Header */}
          {dashboardData?.title && (
            <div className="mb-8 border-b py-2">
              <div className="flex items-start justify-between">
                <div className="flex-1 space-y-1">
                  <Heading level={1}>{dashboardData.title}</Heading>
                  <If condition={dashboardData.description}>
                    {(description) => (
                      <div className="text-muted-foreground text-base">
                        <span>{description}</span>
                      </div>
                    )}
                  </If>
                  <If condition={dashboardData.created_at}>
                    {(createdAt) => (
                      <div className="text-muted-foreground text-sm">
                        <span>
                          Generated on{' '}
                          {formatDateWithTimezone(
                            createdAt,
                            DateFormat.LOCAL_DATE_TIME,
                            true,
                          )}
                        </span>
                      </div>
                    )}
                  </If>
                </div>
              </div>
            </div>
          )}

          {/* Dashboard Grid */}
          {dashboardData?.widgets && Array.isArray(dashboardData.widgets) && (
            <DashboardGrid
              widgets={dashboardData.widgets as DashboardWidget[]}
              gridConfig={
                (dashboardData.grid_config as GridConfig) || {
                  columns: 12,
                }
              }
            />
          )}

          {/* Fallback if no structured content exists but dashboard has data */}
          {dashboardData && !dashboardData.title && !dashboardData.widgets && (
            <div className="prose prose-sm max-w-none">
              <pre className="bg-muted/30 text-foreground rounded-lg p-4 text-sm whitespace-pre-wrap">
                {JSON.stringify(dashboardData, null, 2)}
              </pre>
            </div>
          )}

          {/* Footer */}
          <div className="mt-12 border-t pt-6 text-center">
            <p className="text-muted-foreground text-sm">
              This dashboard was shared from{' '}
              <a
                href="/"
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-500 underline hover:text-blue-600"
              >
                Cloud Cost Optimization Platform
              </a>
            </p>
          </div>
        </div>
      </ScrollArea>
    </div>
  );
}
