'use client';

import { AppLogo } from '@/components/app-logo';
import ExecutiveSummary from '@/components/chat/autonomous/report-content/executive-summary';
import ReportSection from '@/components/chat/autonomous/report-content/report-section';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { SchemaReport } from '@/openapi-ts/gens';
import { formatters } from '@/utils/date-formatters';
import { Calendar, ExternalLink, FileText } from 'lucide-react';

interface SharedReportViewProps {
  report: SchemaReport;
}

export default function SharedReportView({ report }: SharedReportViewProps) {
  // Parse report data if it's a string
  let reportData: SchemaReport;
  try {
    if (typeof report === 'string') {
      reportData = JSON.parse(report);
    } else if (typeof report === 'object' && report !== null) {
      reportData = report;
    } else {
      // Invalid report data, show empty state
      return (
        <div className="flex h-screen items-center justify-center">
          <div className="mx-auto flex max-w-md flex-col items-center gap-4 p-6 text-center">
            <div className="bg-muted/50 flex h-16 w-16 items-center justify-center rounded-full">
              <FileText className="text-muted-foreground h-8 w-8" />
            </div>
            <div>
              <h3 className="text-foreground mb-2 text-lg font-semibold">
                Invalid Report Data
              </h3>
              <p className="text-muted-foreground text-sm leading-relaxed">
                The report data appears to be corrupted.
              </p>
            </div>
          </div>
        </div>
      );
    }
  } catch {
    // If parsing fails, show error state
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="mx-auto flex max-w-md flex-col items-center gap-4 p-6 text-center">
          <div className="bg-muted/50 flex h-16 w-16 items-center justify-center rounded-full">
            <FileText className="text-muted-foreground h-8 w-8" />
          </div>
          <div>
            <h3 className="text-foreground mb-2 text-lg font-semibold">
              Report Parsing Error
            </h3>
            <p className="text-muted-foreground text-sm leading-relaxed">
              Unable to parse the report data.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen flex-col">
      {/* Header Bar */}
      <div className="bg-background/95 supports-[backdrop-filter]:bg-background/60 border-b backdrop-blur">
        <div className="container mx-auto flex h-14 items-center justify-between px-4">
          <AppLogo href="/" />
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.open('/', '_blank')}
            className="flex items-center gap-2"
          >
            <ExternalLink className="h-4 w-4" />
            Open App
          </Button>
        </div>
      </div>

      {/* Report Content */}
      <ScrollArea className="flex-1">
        <div className="container mx-auto max-w-5xl p-6">
          {/* Report Header */}
          {reportData?.title && (
            <div className="mb-8 border-b pb-6">
              <div className="mb-4 flex items-start justify-between">
                <div className="flex-1">
                  <h1 className="text-foreground text-3xl font-bold">
                    {reportData.title}
                  </h1>
                </div>

                <div className="ml-6 flex items-center gap-2">
                  {reportData.created_at && (
                    <div className="text-muted-foreground flex items-center gap-2 text-sm">
                      <Calendar className="h-4 w-4" />
                      <span>{formatters.long(reportData.created_at)}</span>
                    </div>
                  )}
                </div>
              </div>

              {reportData.description && (
                <div className="mt-3">
                  <p className="text-muted-foreground leading-relaxed">
                    {reportData.description}
                  </p>
                </div>
              )}
            </div>
          )}

          {/* Executive Summary */}
          {reportData?.executive_summary && (
            <ExecutiveSummary data={reportData.executive_summary as any} />
          )}

          {/* Report Sections */}
          {reportData?.sections && Array.isArray(reportData.sections) && (
            <div className="space-y-8">
              {reportData.sections
                .sort((a: any, b: any) => (a.index || 0) - (b.index || 0))
                .map((section: any, index: number) => (
                  <ReportSection
                    key={`section-${section.index || index}`}
                    section={section}
                  />
                ))}
            </div>
          )}

          {/* Fallback if no structured content exists but report has data */}
          {reportData &&
            !reportData.title &&
            !reportData.sections &&
            !reportData.executive_summary && (
              <div className="prose prose-sm max-w-none">
                <pre className="bg-muted/30 text-foreground rounded-lg p-4 text-sm whitespace-pre-wrap">
                  {JSON.stringify(reportData, null, 2)}
                </pre>
              </div>
            )}

          {/* Footer */}
          <div className="mt-12 border-t pt-6 text-center">
            <p className="text-muted-foreground text-sm">
              This report was shared from{' '}
              <a
                href="/"
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-500 underline hover:text-blue-600"
              >
                Cloud Cost Optimization Platform
              </a>
            </p>
          </div>
        </div>
      </ScrollArea>
    </div>
  );
}
