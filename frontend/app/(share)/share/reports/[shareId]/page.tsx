'use client';

import { useParams } from 'next/navigation';

import SharedReportView from '@/app/(share)/_components/shared-report-view';
import { shareQuery } from '@/features/share';
import { AlertCircle, Loader2 } from 'lucide-react';

export default function SharedReportPage() {
  const params = useParams();
  const shareId = params?.shareId as string;

  const {
    data: report,
    isLoading,
    error,
  } = shareQuery.query.useSharedReport(shareId);

  if (isLoading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
          <p className="text-muted-foreground">Loading shared report...</p>
        </div>
      </div>
    );
  }

  if (error || !report) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="mx-auto flex max-w-md flex-col items-center gap-4 p-6 text-center">
          <div className="bg-muted/50 flex h-16 w-16 items-center justify-center rounded-full">
            <AlertCircle className="text-muted-foreground h-8 w-8" />
          </div>
          <div>
            <h3 className="text-foreground mb-2 text-lg font-semibold">
              Report Not Found
            </h3>
            <p className="text-muted-foreground text-sm leading-relaxed">
              This report may have been removed or is no longer being shared.
              Please check the link and try again.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return <SharedReportView report={report} />;
}
