/**
 * Utility for managing KB task IDs in localStorage
 */

const STORAGE_KEY_PREFIX = 'kb_tasks_';

/**
 * Check if localStorage is available (handles SSR and browser compatibility)
 */
function isLocalStorageAvailable(): boolean {
  try {
    return typeof window !== 'undefined' && window.localStorage != null;
  } catch {
    return false;
  }
}

export interface StoredTask {
  taskId: string;
  kbId: string;
  createdAt: number;
  type: 'file_upload' | 'website_ingestion';
}

/**
 * Get the localStorage key for a specific KB
 */
function getStorageKey(kbId: string): string {
  return `${STORAGE_KEY_PREFIX}${kbId}`;
}

/**
 * Get all stored task IDs for a specific KB
 */
export function getStoredTaskIds(kbId: string): string[] {
  if (!isLocalStorageAvailable()) return [];

  try {
    const key = getStorageKey(kbId);
    const stored = localStorage.getItem(key);
    if (!stored) return [];

    const tasks: StoredTask[] = JSON.parse(stored);
    return tasks.map((task) => task.taskId);
  } catch (error) {
    console.error('Error reading stored task IDs:', error);
    return [];
  }
}

/**
 * Get all stored tasks for a specific KB
 */
export function getStoredTasks(kbId: string): StoredTask[] {
  if (!isLocalStorageAvailable()) return [];

  try {
    const key = getStorageKey(kbId);
    const stored = localStorage.getItem(key);
    if (!stored) return [];

    return JSON.parse(stored);
  } catch (error) {
    console.error('Error reading stored tasks:', error);
    return [];
  }
}

/**
 * Add a new task ID to localStorage for a specific KB
 */
export function addTaskId(
  kbId: string,
  taskId: string,
  type: 'file_upload' | 'website_ingestion',
): void {
  if (!isLocalStorageAvailable()) return;

  try {
    const key = getStorageKey(kbId);
    const currentTasks = getStoredTasks(kbId);

    // Check if task already exists
    if (currentTasks.some((task) => task.taskId === taskId)) {
      return;
    }

    const newTask: StoredTask = {
      taskId,
      kbId,
      createdAt: Date.now(),
      type,
    };

    const updatedTasks = [...currentTasks, newTask];
    localStorage.setItem(key, JSON.stringify(updatedTasks));
  } catch (error) {
    console.error('Error storing task ID:', error);
  }
}

/**
 * Remove a task ID from localStorage for a specific KB
 */
export function removeTaskId(kbId: string, taskId: string): void {
  if (!isLocalStorageAvailable()) return;

  try {
    const key = getStorageKey(kbId);
    const currentTasks = getStoredTasks(kbId);
    const updatedTasks = currentTasks.filter((task) => task.taskId !== taskId);

    if (updatedTasks.length === 0) {
      localStorage.removeItem(key);
    } else {
      localStorage.setItem(key, JSON.stringify(updatedTasks));
    }
  } catch (error) {
    console.error('Error removing task ID:', error);
  }
}

/**
 * Clear all task IDs for a specific KB
 */
export function clearTaskIds(kbId: string): void {
  if (!isLocalStorageAvailable()) return;

  try {
    const key = getStorageKey(kbId);
    localStorage.removeItem(key);
  } catch (error) {
    console.error('Error clearing task IDs:', error);
  }
}

/**
 * Clean up old tasks (older than 24 hours) for all KBs
 */
export function cleanupOldTasks(): void {
  if (!isLocalStorageAvailable()) return;

  try {
    const cutoffTime = Date.now() - 24 * 60 * 60 * 1000; // 24 hours ago

    // Get all KB task keys
    const keys = Object.keys(localStorage).filter((key) =>
      key.startsWith(STORAGE_KEY_PREFIX),
    );

    for (const key of keys) {
      const stored = localStorage.getItem(key);
      if (!stored) continue;

      try {
        const tasks: StoredTask[] = JSON.parse(stored);
        const validTasks = tasks.filter((task) => task.createdAt > cutoffTime);

        if (validTasks.length === 0) {
          localStorage.removeItem(key);
        } else if (validTasks.length !== tasks.length) {
          localStorage.setItem(key, JSON.stringify(validTasks));
        }
      } catch {
        // Remove corrupted entries
        localStorage.removeItem(key);
      }
    }
  } catch (error) {
    console.error('Error cleaning up old tasks:', error);
  }
}
