import React from 'react';

/**
 * Shared utility functions for the Knowledge Base module
 */

/**
 * Extract file extension from a filename
 */
export const getFileExtension = (name: string): string => {
  return name.split('.').pop()?.toLowerCase() || '';
};

/**
 * Extract filename from a full path
 */
export const getFileName = (path: string): string => {
  const parts = path.split('/');
  return parts[parts.length - 1];
};

/**
 * Highlight search matches in text with JSX
 */
export const highlightMatch = (
  text: string,
  query: string | undefined,
): React.ReactNode => {
  if (!query) return text;

  try {
    // Split the text by the search query (case insensitive)
    const parts = text.split(new RegExp(`(${query})`, 'gi'));
    return (
      <>
        {parts.map((part, index) =>
          part.toLowerCase() === query.toLowerCase() ? (
            <span
              key={index}
              className="rounded bg-yellow-100 px-0.5 dark:bg-yellow-900"
            >
              {part}
            </span>
          ) : (
            part
          ),
        )}
      </>
    );
  } catch {
    // If regex fails (e.g., with special characters), return the original text
    return text;
  }
};

/**
 * Simplified pagination state with reduced variables
 */
export interface PaginationState {
  currentPage: number;
  pageSize: number;
  totalItems: number;
}

/**
 * Calculate pagination values
 */
export const calculatePagination = (state: PaginationState) => {
  const totalPages = Math.max(1, Math.ceil(state.totalItems / state.pageSize));
  const skip = state.currentPage * state.pageSize;

  return {
    skip,
    limit: state.pageSize,
    totalPages,
    hasNextPage: state.currentPage < totalPages - 1,
    hasPreviousPage: state.currentPage > 0,
  };
};

/**
 * Get a new pagination state when changing page
 */
export const getNewPaginationState = (
  currentState: PaginationState,
  newPage: number,
): PaginationState => {
  return {
    ...currentState,
    currentPage: Math.max(
      0,
      Math.min(
        newPage,
        Math.ceil(currentState.totalItems / currentState.pageSize) - 1,
      ),
    ),
  };
};

/**
 * Search state interface with reduced variables
 */
export interface SearchState {
  query: string;
  isSearching: boolean;
}

/**
 * Create pagination handlers with reduced state variables
 */
export const createPaginationHandlers = (
  paginationState: PaginationState,
  setPaginationState: (state: PaginationState) => void,
  onPageChange: (skip: number, limit: number) => void,
) => {
  const handlePageChange = (newPage: number) => {
    const newState = getNewPaginationState(paginationState, newPage);
    setPaginationState(newState);
    const { skip, limit } = calculatePagination(newState);
    onPageChange(skip, limit);
  };

  const handlePageSizeChange = (newSize: number) => {
    // When changing page size, try to keep the user viewing similar content
    const currentFirstItemIndex =
      paginationState.currentPage * paginationState.pageSize;
    const newPage = Math.floor(currentFirstItemIndex / newSize);

    const newState = {
      ...paginationState,
      pageSize: newSize,
      currentPage: newPage,
    };

    setPaginationState(newState);
    const { skip, limit } = calculatePagination(newState);
    onPageChange(skip, limit);
  };

  return {
    handlePageChange,
    handlePageSizeChange,
  };
};
