'use client';

import { Button } from '@/components/ui/button';
import { DateFormat, formatUtcDate } from '@/lib/date-utils';
import { formatBytes } from '@/lib/utils';
import { ColumnDef } from '@tanstack/react-table';
import { Eye, Trash2 } from 'lucide-react';

// Import the interface from the context
import { DocumentKBRead } from '../context/KBContext';
import { FileTypeIcon } from './FileTypeIcon';

// Function to extract the file extension from a filename
const getFileExtension = (name: string) => {
  return name.split('.').pop()?.toLowerCase() || '';
};

// Function to extract just the filename from the full path
const getFileName = (path: string) => {
  const parts = path.split('/');
  return parts[parts.length - 1];
};

export const createDocumentColumns = (
  onView: (document: DocumentKBRead) => void,
  onDelete: (document: DocumentKBRead) => void,
  searchQuery?: string,
): ColumnDef<DocumentKBRead>[] => {
  // Function to highlight search matches in text
  const highlightMatch = (text: string, query: string) => {
    if (!query) return text;
    try {
      // Split the text by the search query (case insensitive)
      const parts = text.split(new RegExp(`(${query})`, 'gi'));
      return (
        <>
          {parts.map((part, index) =>
            part.toLowerCase() === query.toLowerCase() ? (
              <span
                key={index}
                className="rounded bg-yellow-100 px-0.5 dark:bg-yellow-900"
              >
                {part}
              </span>
            ) : (
              part
            ),
          )}
        </>
      );
    } catch {
      // If regex fails (e.g., with special characters), return the original text
      return text;
    }
  };

  return [
    {
      accessorKey: 'name',
      header: 'NAME',
      cell: ({ row }) => {
        const doc = row.original;
        const filename = getFileName(doc.name);
        return (
          <div className="flex items-center">
            <FileTypeIcon
              filename={doc.name}
              className="mr-2 h-4 w-4 shrink-0"
            />
            <span className="max-w-[250px] truncate" title={filename}>
              {searchQuery ? highlightMatch(filename, searchQuery) : filename}
            </span>
          </div>
        );
      },
      size: 300, // Fixed width for name column
    },
    {
      accessorKey: 'type',
      header: 'TYPE',
      cell: ({ row }) => {
        const extension = getFileExtension(row.original.name);
        return (
          <span className="text-xs font-medium uppercase">{extension}</span>
        );
      },
      size: 80, // Fixed width for type column
    },
    {
      accessorKey: 'size',
      header: 'SIZE',
      cell: ({ row }) => formatBytes(row.original.size || 0),
      size: 100, // Fixed width for size column
    },
    {
      accessorKey: 'last_modified',
      header: 'LAST MODIFIED',
      cell: ({ row }) => {
        try {
          return formatUtcDate(row.original.updated_at, DateFormat.SHORT_DATE);
        } catch {
          return row.original.updated_at;
        }
      },
      size: 180, // Fixed width for date column
    },
    {
      id: 'actions',
      header: () => <div className="text-right">ACTIONS</div>,
      cell: ({ row }) => {
        const doc = row.original;
        return (
          <div className="flex justify-end space-x-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={(e) => {
                e.stopPropagation();
                onView(doc);
              }}
              aria-label={`View ${getFileName(doc.name)}`}
            >
              <Eye className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={(e) => {
                e.stopPropagation();
                onDelete(doc);
              }}
              aria-label={`Delete ${getFileName(doc.name)}`}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        );
      },
      size: 100, // Fixed width for actions column
    },
  ];
};
