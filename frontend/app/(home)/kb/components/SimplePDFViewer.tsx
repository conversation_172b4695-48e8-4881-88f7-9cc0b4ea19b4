import React from 'react';

import { cn } from '@/lib/utils';

interface SimplePDFViewerProps {
  base64Content?: string;
  url?: string;
  className?: string;
}

export function SimplePDFViewer({
  base64Content,
  url,
  className,
}: SimplePDFViewerProps) {
  const pdfUrl = base64Content || url;

  if (!pdfUrl) {
    return (
      <div className="flex h-full items-center justify-center">
        <p className="text-muted-foreground">No PDF content available</p>
      </div>
    );
  }

  return (
    <div className={cn('flex h-full w-full flex-col', className)}>
      {/* PDF iframe */}
      <div className="relative flex-1">
        <iframe
          src={pdfUrl}
          className="h-full w-full border-0"
          title="PDF Viewer"
          style={{ minHeight: '400px' }}
        />
      </div>
    </div>
  );
}
