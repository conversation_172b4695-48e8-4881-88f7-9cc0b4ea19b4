import { useEffect, useRef, useState } from 'react';

import { KnowledgeBaseService } from '@/client/sdk.gen';
import { MarkdownRenderer } from '@/components/markdown';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Switch } from '@/components/ui/switch';
import { DateFormat, formatUtcDate } from '@/lib/date-utils';
import { formatBytes } from '@/lib/utils';
import {
  ChevronDown,
  Download,
  FileCode,
  FileText,
  Globe,
  Trash2,
} from 'lucide-react';

import {
  cleanupExpiredPresignedUrls,
  getCachedPresignedUrl,
  isPresignedUrlValid,
  setCachedPresignedUrl,
} from '../../utils/presigned-url-cache';
import { FileTypeIcon } from '../FileTypeIcon';
import { SimplePDFViewer } from '../SimplePDFViewer';

interface DocumentKBRead {
  id: string;
  kb_id: string;
  name: string;
  type: 'url' | 'file';
  url?: string;
  deep_crawl: boolean;
  file_name?: string;
  file_type?: string;
  object_name?: string;
  embed_status: 'PENDING' | 'PROGRESS' | 'SUCCESS' | 'FAILURE';
  created_at: string;
  updated_at: string;
  is_deleted: boolean;
  size?: number;
  isDeleting?: boolean;
}

interface KBDocumentDetailDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  document: DocumentKBRead | null;
  content: string | null;
  isLoading: boolean;
  onDelete?: (document: DocumentKBRead) => void;
  kbId?: string;
}

// Constants for content pagination
const INITIAL_CONTENT_SIZE = 15000;
const LOAD_MORE_INCREMENT = 15000;

export function KBDocumentDetailDialog({
  open,
  onOpenChange,
  document,
  content,
  isLoading,
  onDelete,
  kbId,
}: KBDocumentDetailDialogProps) {
  const [renderMarkdown, setRenderMarkdown] = useState(true);
  const [displayedContent, setDisplayedContent] = useState<string | null>(null);
  const [hasMoreContent, setHasMoreContent] = useState(false);
  const [contentSize, setContentSize] = useState(INITIAL_CONTENT_SIZE);
  const [isBinaryContent, setIsBinaryContent] = useState(false);
  const [isContentReady, setIsContentReady] = useState(false);
  const scrollRef = useRef<HTMLDivElement>(null);

  // Determine document type from file extension (moved up for use in useEffect)
  const fileExtension =
    document?.file_name?.split('.').pop()?.toLowerCase() ||
    document?.name.split('.').pop()?.toLowerCase() ||
    '';
  const isPdf = fileExtension === 'pdf';

  // Reset display content when document changes - wait for complete content
  useEffect(() => {
    // Reset content ready state when starting
    setIsContentReady(false);

    // Only process content when it's completely loaded (not loading state)
    if (content && !isLoading) {
      // Special handling for PDFs - they come as data URLs, don't try to parse as JSON
      if (isPdf) {
        if (content.startsWith('data:application/pdf;base64,')) {
          setDisplayedContent(content);
          setHasMoreContent(false);
          setIsBinaryContent(false); // PDFs are handled specially, not as binary content
          setIsContentReady(true);
        } else {
          // Try to parse as JSON in case it's wrapped
          try {
            const contentObj = JSON.parse(content);
            if (contentObj.is_binary && contentObj.content) {
              // Convert base64 to data URL for PDF viewer
              const dataUrl = `data:application/pdf;base64,${contentObj.content}`;
              setDisplayedContent(dataUrl);
              setHasMoreContent(false);
              setIsBinaryContent(false);
              setIsContentReady(true);
            } else {
              setDisplayedContent(null);
              setHasMoreContent(false);
              setIsBinaryContent(false);
              setIsContentReady(false);
            }
          } catch {
            setDisplayedContent(null);
            setHasMoreContent(false);
            setIsBinaryContent(false);
            setIsContentReady(false);
          }
        }
        return;
      }

      // Check if the content is base64 encoded binary data (for non-PDF files)
      try {
        const contentObj = JSON.parse(content);
        setIsBinaryContent(contentObj.is_binary || false);
        const actualContent = contentObj.content || '';

        if (!contentObj.is_binary) {
          const truncated = actualContent.slice(0, contentSize);
          setDisplayedContent(truncated);
          setHasMoreContent(actualContent.length > contentSize);
        } else {
          setDisplayedContent(null);
          setHasMoreContent(false);
        }
        setIsContentReady(true);
      } catch {
        // If not JSON, treat as regular text content
        // Only process if content is a complete string
        if (typeof content === 'string' && content.length > 0) {
          const truncated = content.slice(0, contentSize);
          setDisplayedContent(truncated);
          setHasMoreContent(content.length > contentSize);
          setIsBinaryContent(false);
          setIsContentReady(true);
        } else {
          setDisplayedContent(null);
          setHasMoreContent(false);
          setIsBinaryContent(false);
          setIsContentReady(false);
        }
      }
    } else {
      setDisplayedContent(null);
      setHasMoreContent(false);
      setIsBinaryContent(false);
      setIsContentReady(false);
    }
  }, [content, contentSize, isLoading, isPdf]);

  // Reset content size and set renderMarkdown based on file type when dialog opens
  useEffect(() => {
    if (open && document) {
      setContentSize(INITIAL_CONTENT_SIZE);
      const fileExt = document.name.split('.').pop()?.toLowerCase();
      // For URL documents, default to markdown rendering since they typically contain markdown content
      // For file documents, only render markdown if they have .md extension
      setRenderMarkdown(document.type === 'url' || fileExt === 'md');
    }
  }, [open, document]);

  // Clean up expired presigned URLs when component mounts
  useEffect(() => {
    cleanupExpiredPresignedUrls();
  }, []);

  const handleLoadMore = () => {
    if (!content || isLoading) return;

    // Ensure content is completely loaded before processing more
    let actualContent = content;
    try {
      const contentObj = JSON.parse(content);
      if (!contentObj.is_binary) {
        actualContent = contentObj.content || '';
      }
    } catch {
      // If not JSON, use content as is
      actualContent = content;
    }

    if (typeof actualContent !== 'string') return;

    const newSize = contentSize + LOAD_MORE_INCREMENT;
    setContentSize(newSize);
    const truncated = actualContent.slice(0, newSize);
    setDisplayedContent(truncated);
    setHasMoreContent(actualContent.length > newSize);

    // Mark scroll position to avoid jumping to top
    const scrollPosition = scrollRef.current?.scrollTop || 0;

    // Restore scroll position after re-render
    setTimeout(() => {
      if (scrollRef.current) {
        scrollRef.current.scrollTop = scrollPosition;
      }
    }, 10);
  };

  if (!document) return null;

  // Document type detection (fileExtension and isPdf are already defined above)
  const isTextDocument = ['txt', 'md', 'html', 'json', 'csv'].includes(
    fileExtension,
  );
  const isImage = ['jpg', 'jpeg', 'png', 'gif', 'svg'].includes(fileExtension);
  // URL documents should be treated as markdown, file documents only if they have .md extension
  const isMarkdown = document?.type === 'url' || fileExtension === 'md';

  const getDocumentTypeLabel = () => {
    if (isTextDocument) return 'Text';
    if (isPdf) return 'PDF';
    if (isImage) return 'Image';
    return 'Document';
  };

  const getFileName = (doc: DocumentKBRead) => {
    if (doc.type === 'url') {
      return doc.url || 'Untitled URL';
    }
    return doc.file_name || doc.name || 'Untitled File';
  };

  // Check if document is a URL type
  const isUrlDocument = document?.type === 'url';

  // Handle delete if the callback is provided
  const handleDelete = () => {
    if (onDelete && document) {
      onDelete(document);
    }
  };

  const fileName = getFileName(document);

  // Function to open URL in new tab
  const handleOpenUrl = () => {
    if (document?.url) {
      window.open(document.url, '_blank', 'noopener,noreferrer');
    }
  };

  // Helper function to get presigned URL with caching
  const getPresignedUrlWithCache = async (
    objectName: string,
  ): Promise<string> => {
    if (!kbId) throw new Error('No KB ID provided');

    // Check cache first
    const cachedUrl = getCachedPresignedUrl(kbId, objectName);

    if (cachedUrl) {
      // Verify the cached URL is still valid
      const isValid = await isPresignedUrlValid(cachedUrl);
      if (isValid) {
        return cachedUrl;
      }
    }

    // Fetch new URL if no valid cached URL
    const newPresignedUrl = await KnowledgeBaseService.getDocumentContent({
      kbId,
      objectName,
    });

    if (!newPresignedUrl || typeof newPresignedUrl !== 'string') {
      throw new Error('No presigned URL received');
    }

    // Cache the new URL
    setCachedPresignedUrl(kbId, objectName, newPresignedUrl, 1);

    return newPresignedUrl;
  };

  // Format file size for display (e.g. 1.5MB / 50KB viewed)
  const getContentStats = () => {
    if (!content || !displayedContent || isLoading) return '';

    // Get actual content for size calculation
    let actualContent = content;
    try {
      const contentObj = JSON.parse(content);
      if (!contentObj.is_binary) {
        actualContent = contentObj.content || '';
      }
    } catch {
      // If not JSON, use content as is
      actualContent = content;
    }

    if (typeof actualContent !== 'string') return '';

    const totalBytes = new Blob([actualContent]).size;
    const displayedBytes = new Blob([displayedContent]).size;

    return `${formatBytes(displayedBytes)} / ${formatBytes(totalBytes)} loaded`;
  };

  // --- PDF-ONLY DIALOG MODE ---
  if (isPdf) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="flex max-h-[95vh] flex-col pt-0 pr-10 sm:max-w-[95vw] md:max-w-[60vw]">
          {!displayedContent || !isContentReady ? (
            <div className="flex h-[80vh] w-full items-center justify-center">
              <p>Loading PDF document...</p>
            </div>
          ) : (
            <SimplePDFViewer
              base64Content={displayedContent}
              className="h-[80vh] w-full"
            />
          )}
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="flex max-h-[90vh] flex-col sm:max-w-[900px] md:max-w-[70vw]">
        <DialogHeader className="flex-row items-start gap-3 space-y-0">
          <FileTypeIcon
            filename={fileName}
            className="mt-1 h-10 w-10"
            documentType={document?.type}
          />
          <div className="space-y-1">
            <DialogTitle className="truncate">{fileName}</DialogTitle>
            <DialogDescription>
              {getDocumentTypeLabel()} •{' '}
              {document.size ? formatBytes(document.size) : 'Unknown size'} •{' '}
              {formatUtcDate(document.created_at, DateFormat.SHORT_DATE)}
            </DialogDescription>
          </div>
        </DialogHeader>

        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs font-medium uppercase">
              {isUrlDocument ? 'URL' : fileExtension}
            </Badge>

            {isUrlDocument && (
              <Badge variant="secondary" className="text-xs">
                {document.deep_crawl ? 'Deep crawl' : 'Single page'}
              </Badge>
            )}

            {isMarkdown && displayedContent && (
              <div className="ml-4 flex items-center space-x-2">
                <Switch
                  id="render-markdown"
                  checked={renderMarkdown}
                  onCheckedChange={setRenderMarkdown}
                />
                <Label
                  htmlFor="render-markdown"
                  className="flex cursor-pointer items-center gap-1 text-xs"
                >
                  {renderMarkdown ? (
                    <>
                      <FileText className="h-3.5 w-3.5" /> Rendered
                    </>
                  ) : (
                    <>
                      <FileCode className="h-3.5 w-3.5" /> Raw
                    </>
                  )}
                </Label>
              </div>
            )}
          </div>
          <div className="flex gap-2">
            {isUrlDocument && document?.url && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleOpenUrl}
                className="text-blue-600 hover:bg-blue-50 hover:text-blue-700"
              >
                <Globe className="mr-1 h-4 w-4" />
                Open URL
              </Button>
            )}
            {onDelete && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleDelete}
                className="text-destructive hover:bg-destructive/10 hover:text-destructive"
              >
                <Trash2 className="mr-1 h-4 w-4" />
                Delete
              </Button>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={async () => {
                try {
                  // Get presigned URL with caching
                  const presignedUrl = await getPresignedUrlWithCache(
                    document.object_name || document.name,
                  );

                  // Fetch the actual file content from the presigned URL
                  const response = await fetch(presignedUrl);
                  if (!response.ok) {
                    throw new Error(
                      `Failed to download file: ${response.status}`,
                    );
                  }

                  const blob = await response.blob();

                  // Create download link
                  const url = window.URL.createObjectURL(blob);
                  const link = window.document.createElement('a');
                  link.href = url;
                  link.download = fileName;
                  window.document.body.appendChild(link);
                  link.click();
                  window.document.body.removeChild(link);
                  window.URL.revokeObjectURL(url);
                } catch (error) {
                  console.error('Error downloading file:', error);
                }
              }}
            >
              <Download className="mr-1 h-4 w-4" />
              Download
            </Button>
          </div>
        </div>

        <ScrollArea
          className="bg-muted/30 h-[65vh] grow rounded-lg border p-4"
          ref={scrollRef}
        >
          {isLoading || !isContentReady ? (
            <div className="flex h-full items-center justify-center">
              <p>Loading document content...</p>
            </div>
          ) : !content ? (
            <div className="flex h-full items-center justify-center">
              <p className="overflow-wrap-anywhere text-muted-foreground text-center break-words">
                No content available for this document
              </p>
            </div>
          ) : isBinaryContent && isContentReady && !isPdf ? (
            // Only show binary message for non-PDF binary files
            <div className="flex h-full flex-col items-center justify-center gap-4">
              <p className="overflow-wrap-anywhere text-muted-foreground text-center break-words">
                This is a binary file and cannot be previewed directly.
              </p>
              <Button
                variant="outline"
                size="lg"
                className="overflow-wrap-anywhere break-all"
                onClick={async () => {
                  try {
                    // Get presigned URL with caching
                    const presignedUrl = await getPresignedUrlWithCache(
                      document.object_name || document.name,
                    );

                    // Fetch the actual file content from the presigned URL
                    const response = await fetch(presignedUrl);
                    if (!response.ok) {
                      throw new Error(
                        `Failed to download file: ${response.status}`,
                      );
                    }

                    const blob = await response.blob();

                    // Create download link
                    const url = window.URL.createObjectURL(blob);
                    const link = window.document.createElement('a');
                    link.href = url;
                    link.download = fileName;
                    window.document.body.appendChild(link);
                    link.click();
                    window.document.body.removeChild(link);
                    window.URL.revokeObjectURL(url);
                  } catch (error) {
                    console.error('Error downloading file:', error);
                  }
                }}
              >
                <Download className="mr-2 h-5 w-5" />
                Download {getDocumentTypeLabel()}
              </Button>
            </div>
          ) : displayedContent && content && isContentReady ? (
            // Only render content when displayedContent, original content and isContentReady are all true
            <div className="space-y-4">
              {isMarkdown && renderMarkdown ? (
                <div className="w-full">
                  <MarkdownRenderer
                    content={String(displayedContent)}
                    theme="enhanced"
                  />
                </div>
              ) : (
                <div className="w-full">
                  <pre className="border-border/50 bg-muted/30 overflow-x-auto rounded-lg border p-4 font-mono text-sm whitespace-pre-wrap">
                    {displayedContent}
                  </pre>
                </div>
              )}

              {hasMoreContent && (
                <div className="flex flex-col items-center border-t pt-4">
                  <p className="text-muted-foreground mb-2 text-xs">
                    {getContentStats()}
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleLoadMore}
                    className="flex items-center gap-1"
                  >
                    <ChevronDown className="h-3 w-3" />
                    Load More
                  </Button>
                </div>
              )}
            </div>
          ) : (
            <div className="flex h-full items-center justify-center">
              <p className="overflow-wrap-anywhere text-muted-foreground text-center break-words">
                {isImage
                  ? 'Image preview not available'
                  : 'No content available for this document type'}
              </p>
            </div>
          )}
        </ScrollArea>

        <DialogFooter className="mt-4">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
