import React, { useCallback, useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { zodResolver } from '@hookform/resolvers/zod';
import { AnimatePresence, motion } from 'framer-motion';
import { Tag, X } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { AccessLevelCombobox } from '../access-level-combobox';
import { UsageModeCombobox } from '../usage-mode-combobox';
import { UserSelector } from '../user-selector';

// Define K<PERSON>ccess<PERSON>evel, KBUsageMode, and KBType
type KBAccessLevel = 'private' | 'shared';
type KBUsageMode = 'manual' | 'agent_requested' | 'always';

const formSchema = z.object({
  title: z.string().min(1, 'Title is required').max(255, 'Title is too long'),
  description: z.string().max(1000, 'Description is too long').optional(),
  access_level: z.enum(['private', 'shared']),
  usage_mode: z.enum(['manual', 'agent_requested', 'always']),
  tags: z.array(z.string()).optional(),
  allowed_users: z.array(z.string().uuid('Invalid user ID')).optional(),
});

type FormValues = z.infer<typeof formSchema>;

interface KBCreateDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (values: FormValues) => void;
  initialData?: {
    title: string;
    description: string;
    access_level: KBAccessLevel;
    usage_mode: KBUsageMode;
    tags?: string[];
    allowed_users?: string[];
  };
  mode?: 'create' | 'edit';
  isLoading?: boolean;
}

export function KBCreateDialog({
  open,
  onOpenChange,
  onSubmit,
  initialData,
  mode = 'create',
  isLoading = false,
}: KBCreateDialogProps) {
  const [tagInput, setTagInput] = useState('');

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: initialData?.title || '',
      description: initialData?.description || '',
      access_level: initialData?.access_level || 'private',
      usage_mode: initialData?.usage_mode || 'manual',
      tags: initialData?.tags || [],
      allowed_users: initialData?.allowed_users || [],
    },
  });

  // Update form when initialData changes
  React.useEffect(() => {
    if (initialData && mode === 'edit') {
      form.reset({
        title: initialData.title || '',
        description: initialData.description || '',
        access_level: initialData.access_level || 'private',
        usage_mode: initialData.usage_mode || 'manual',
        tags: initialData.tags || [],
        allowed_users: initialData.allowed_users || [],
      });
    }
  }, [initialData, form, mode]);

  const handleSubmit = (values: FormValues) => {
    onSubmit(values);
    if (mode === 'create') {
      form.reset();
    }
  };

  const handleAddTag = useCallback(
    (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.key === 'Enter' && tagInput.trim()) {
        e.preventDefault();
        const currentTags = form.getValues('tags') || [];
        if (!currentTags.includes(tagInput.trim())) {
          form.setValue('tags', [...currentTags, tagInput.trim()]);
        }
        setTagInput('');
      }
    },
    [tagInput, form],
  );

  const handleRemoveTag = useCallback(
    (tag: string) => {
      const currentTags = form.getValues('tags') || [];
      form.setValue(
        'tags',
        currentTags.filter((t) => t !== tag),
      );
    },
    [form],
  );

  // Get the current access level to conditionally show the UserSelector
  const accessLevel = form.watch('access_level');
  const showUserSelector = accessLevel === 'shared';

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="overflow-hidden p-0 sm:max-w-[550px]">
        <AnimatePresence mode="wait">
          {open && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2, ease: 'easeInOut' }}
            >
              <DialogHeader className="px-6 pt-6 pb-2">
                <DialogTitle className="text-2xl font-semibold">
                  {mode === 'edit'
                    ? 'Edit Knowledge Base'
                    : 'Create Knowledge Base'}
                </DialogTitle>
                <DialogDescription className="text-muted-foreground">
                  {mode === 'edit'
                    ? 'Update your knowledge base details'
                    : 'Create a new knowledge base to store and organize documents'}
                </DialogDescription>
              </DialogHeader>

              <Form {...form}>
                <form onSubmit={form.handleSubmit(handleSubmit)}>
                  <div className="max-h-[70vh] space-y-5 overflow-y-auto px-6 py-4">
                    <FormField
                      control={form.control}
                      name="title"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Title</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="My Knowledge Base"
                              {...field}
                              className="focus-visible:ring-offset-0"
                            />
                          </FormControl>
                          <FormDescription className="text-xs">
                            Give your knowledge base a descriptive title
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Description</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Optional description of your knowledge base"
                              {...field}
                              value={field.value || ''}
                              className="h-24 resize-none focus-visible:ring-offset-0"
                            />
                          </FormControl>
                          <FormDescription className="text-xs">
                            Briefly describe the purpose or contents of this
                            knowledge base
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Access Level and Usage Mode on the same line */}
                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                      <FormField
                        control={form.control}
                        name="access_level"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Access Level</FormLabel>
                            <AccessLevelCombobox
                              value={field.value}
                              onChange={field.onChange}
                            />
                            <FormDescription className="text-xs">
                              Control who can access
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="usage_mode"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Usage Mode</FormLabel>
                            <UsageModeCombobox
                              value={field.value}
                              onChange={field.onChange}
                            />
                            <FormDescription className="text-xs">
                              How agents use this KB
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* User Selector - only shown for shared KBs */}
                    <AnimatePresence>
                      {showUserSelector && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: 'auto' }}
                          exit={{ opacity: 0, height: 0 }}
                          transition={{ duration: 0.2 }}
                        >
                          <FormItem className="bg-muted/20 rounded-lg border p-4 shadow-xs">
                            <FormLabel className="mb-3 text-sm font-medium">
                              User Access
                            </FormLabel>
                            <UserSelector
                              show={showUserSelector}
                              className="pt-0"
                            />
                            <FormDescription className="text-muted-foreground mt-3 text-xs">
                              Select users who can access this knowledge base
                            </FormDescription>
                          </FormItem>
                        </motion.div>
                      )}
                    </AnimatePresence>

                    <FormField
                      control={form.control}
                      name="tags"
                      render={() => (
                        <FormItem>
                          <FormLabel>Tags</FormLabel>
                          <FormControl>
                            <div className="space-y-2">
                              <div className="relative">
                                <Tag className="text-muted-foreground absolute top-2.5 left-3 h-4 w-4" />
                                <Input
                                  placeholder="Add tag and press Enter"
                                  value={tagInput}
                                  onChange={(e) => setTagInput(e.target.value)}
                                  onKeyDown={handleAddTag}
                                  className="pl-9 focus-visible:ring-offset-0"
                                />
                              </div>
                              <div className="flex min-h-10 flex-wrap gap-1.5 pt-2">
                                <AnimatePresence>
                                  {form.watch('tags')?.length ? (
                                    form.watch('tags')?.map((tag) => (
                                      <motion.div
                                        key={tag}
                                        initial={{ opacity: 0, scale: 0.8 }}
                                        animate={{ opacity: 1, scale: 1 }}
                                        exit={{ opacity: 0, scale: 0.8 }}
                                        transition={{ duration: 0.15 }}
                                      >
                                        <Badge
                                          className="rounded-lg px-2 py-1"
                                          variant="secondary"
                                        >
                                          {tag}
                                          <button
                                            type="button"
                                            className="hover:bg-secondary-foreground/20 ml-1.5 rounded-full p-0.5"
                                            onClick={() => handleRemoveTag(tag)}
                                          >
                                            <X className="h-3 w-3" />
                                          </button>
                                        </Badge>
                                      </motion.div>
                                    ))
                                  ) : (
                                    <span className="text-muted-foreground text-xs">
                                      No tags added yet
                                    </span>
                                  )}
                                </AnimatePresence>
                              </div>
                            </div>
                          </FormControl>
                          <FormDescription className="text-xs">
                            Add tags to help categorize your knowledge base
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <DialogFooter className="bg-muted/50 px-6 py-4">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => onOpenChange(false)}
                      disabled={isLoading}
                    >
                      Cancel
                    </Button>
                    <Button type="submit" disabled={isLoading}>
                      {isLoading ? (
                        <>
                          <motion.div
                            className="mr-2 h-4 w-4 rounded-full border-2 border-current border-t-transparent"
                            animate={{ rotate: 360 }}
                            transition={{
                              duration: 1,
                              repeat: Infinity,
                              ease: 'linear',
                            }}
                          />
                          {mode === 'edit' ? 'Updating...' : 'Creating...'}
                        </>
                      ) : mode === 'edit' ? (
                        'Update Knowledge Base'
                      ) : (
                        'Create Knowledge Base'
                      )}
                    </Button>
                  </DialogFooter>
                </form>
              </Form>
            </motion.div>
          )}
        </AnimatePresence>
      </DialogContent>
    </Dialog>
  );
}
