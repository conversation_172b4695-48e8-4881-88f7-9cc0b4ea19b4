import PageContainer from '@/components/layout/page-container';
import { searchParamsCache } from '@/lib/searchparams';

import { KBDetailContent } from './components/kb-detail-content';

type PageProps = {
  params: Promise<{ kb_id: string }>;
  searchParams: Promise<{
    page: string;
    limit: string;
    q: string;
  }>;
};

export const metadata = {
  title: 'Knowledge Base Details',
};

export default async function KBDetailPage(props: PageProps) {
  const searchParams = await props.searchParams;
  const params = await props.params;
  searchParamsCache.parse(searchParams);

  return (
    <PageContainer scrollable>
      <KBDetailContent kbId={params.kb_id} />
    </PageContainer>
  );
}
