'use client';

import { useEffect, useState } from 'react';

import { useRouter } from 'next/navigation';

import type { UploadedFileInfo } from '@/client';
import { KnowledgeBaseService } from '@/client';
import { EnhancedErrorBoundary } from '@/components/enhanced-error-boundary';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { RefreshButton } from '@/components/ui/refresh-button';
import { Separator } from '@/components/ui/separator';
import { <PERSON><PERSON><PERSON><PERSON> } from '@/components/utils/cache-key';
import pathsConfig from '@/config/paths.config';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { FileUp, Globe, Search, X } from 'lucide-react';
import { toast } from 'sonner';

import { KBTypeBadge } from '../../components/KBTypeBadge';
import { KBCreateDialog } from '../../components/dialogs/kb-create-dialog';
import { KBDocumentDetailDialog } from '../../components/dialogs/kb-document-detail-dialog';
import { KBWebsiteIngestionDialog } from '../../components/dialogs/kb-website-ingestion-dialog';
import { KBDocumentList } from '../../components/kb-document-list';
import { KBDocumentUpload } from '../../components/kb-document-upload';
import { KBTaskStatus } from '../../components/kb-task-status';
import {
  KBProvider,
  useKB,
  useKBDocuments,
  useKBPagination,
  useKBSearch,
} from '../../context/KBContext';
import {
  addTaskId,
  cleanupOldTasks,
  getStoredTaskIds,
  removeTaskId,
} from '../../utils/task-storage';
import { useKBDocumentTableFilters } from './use-kb-document-table-filters';

interface KBDetailContentProps {
  kbId: string;
}

// Create smaller components that use specific selectors
function KBHeader() {
  const { selectedKB, isLoadingKB } = useKB();

  return (
    <>
      <div className="shrink-0">
        <div className="flex flex-col gap-1">
          {isLoadingKB ? (
            <div className="bg-muted h-8 w-48 animate-pulse rounded"></div>
          ) : (
            <>
              <h1
                className="truncate text-2xl font-bold"
                title={selectedKB?.title}
              >
                {selectedKB?.title || 'Knowledge Base'}
              </h1>
              <div className="flex flex-wrap items-center gap-2">
                {selectedKB && (
                  <KBTypeBadge
                    type={selectedKB.access_level as 'private' | 'shared'}
                  />
                )}

                {selectedKB &&
                  selectedKB.tags &&
                  selectedKB.tags.length > 0 && (
                    <>
                      <div className="bg-border mx-1 h-4 w-px" />
                      {selectedKB.tags.map((tag) => (
                        <Badge key={tag} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </>
                  )}
              </div>
              {selectedKB?.description && (
                <p
                  className="text-muted-foreground mt-1 line-clamp-2"
                  title={selectedKB.description}
                >
                  {selectedKB.description}
                </p>
              )}
            </>
          )}
        </div>
      </div>
    </>
  );
}

function SearchDocuments({
  onExpandAll,
  onCollapseAll,
  hasExpandControls,
  onUploadClick,
  onWebsiteClick,
  isUploading,
  isProcessingWebsite,
}: {
  onExpandAll?: () => void;
  onCollapseAll?: () => void;
  hasExpandControls?: boolean;
  onUploadClick?: () => void;
  onWebsiteClick?: () => void;
  isUploading?: boolean;
  isProcessingWebsite?: boolean;
}) {
  const { searchQuery, setSearchQuery } = useKBDocumentTableFilters();
  const { refreshDocuments } = useKBDocuments();
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const clearSearchInput = () => {
    setSearchQuery('');
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await refreshDocuments();
    } catch {
      toast.error('Failed to refresh document list');
    } finally {
      setIsRefreshing(false);
    }
  };

  return (
    <div className="flex shrink-0 flex-col justify-between gap-2 sm:flex-row sm:items-center">
      <div className="flex items-center gap-2">
        <div className="relative w-full max-w-sm">
          <Search className="text-muted-foreground absolute top-2.5 left-2.5 h-4 w-4" />
          <Input
            placeholder="Search documents..."
            className="pr-8 pl-8"
            value={searchQuery}
            onChange={handleSearchInputChange}
          />
          {searchQuery && (
            <Button
              variant="ghost"
              size="icon"
              className="absolute top-1.5 right-1.5 h-5 w-5 p-0"
              onClick={clearSearchInput}
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>
        <RefreshButton isRefreshing={isRefreshing} onRefresh={handleRefresh} />
        {/* Expand/Collapse Controls */}
        {hasExpandControls && (
          <>
            <Button
              variant="outline"
              size="sm"
              className="whitespace-nowrap"
              onClick={onExpandAll}
            >
              Expand All
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="whitespace-nowrap"
              onClick={onCollapseAll}
            >
              Collapse All
            </Button>
          </>
        )}
      </div>
      {/* Upload buttons moved here */}
      <div className="flex gap-2">
        {onWebsiteClick && (
          <Button
            variant="outline"
            size="sm"
            onClick={onWebsiteClick}
            disabled={isProcessingWebsite}
          >
            <Globe className="mr-2 h-4 w-4" />
            Add From Website
          </Button>
        )}
        {onUploadClick && (
          <Button size="sm" onClick={onUploadClick} disabled={isUploading}>
            <FileUp className="mr-2 h-4 w-4" />
            Upload Files
          </Button>
        )}
      </div>
    </div>
  );
}

function DocumentsList({
  onUploadClick,
  onWebsiteClick,
  isUploading,
  isProcessingWebsite,
}: {
  onUploadClick?: () => void;
  onWebsiteClick?: () => void;
  isUploading?: boolean;
  isProcessingWebsite?: boolean;
}) {
  const {
    documents,
    isLoadingDocuments,
    documentCount,
    handleViewDocument,
    handleDeleteDocument,
  } = useKBDocuments();

  const { skip, limit, onPaginate } = useKBPagination();
  const { searchState } = useKBSearch();

  // Simplified folder statistics - no folder functionality
  const folderStats = { total: 0, expanded: 0 };

  const expandAll = () => {
    // No-op: Folder functionality disabled
  };

  const collapseAll = () => {
    // No-op: Folder functionality disabled
  };

  return (
    <div className="flex min-h-0 grow flex-col space-y-4">
      <SearchDocuments
        onExpandAll={expandAll}
        onCollapseAll={collapseAll}
        hasExpandControls={folderStats.total > 0}
        onUploadClick={onUploadClick}
        onWebsiteClick={onWebsiteClick}
        isUploading={isUploading}
        isProcessingWebsite={isProcessingWebsite}
      />
      <EnhancedErrorBoundary
        errorTitle="Error loading documents"
        errorDescription="There was a problem loading the documents. Please try again or contact support."
      >
        <KBDocumentList
          documents={documents}
          isLoading={isLoadingDocuments}
          totalDocuments={documentCount}
          onView={handleViewDocument}
          onDelete={handleDeleteDocument}
          onPaginate={onPaginate}
          skip={skip}
          limit={limit}
          searchQuery={searchState.query}
        />
      </EnhancedErrorBoundary>
    </div>
  );
}

// Main component that uses the KB context
function KBDetailContentInner() {
  const router = useRouter();
  const queryClient = useQueryClient();
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [websiteDialogOpen, setWebsiteDialogOpen] = useState(false);
  const [activeTasks, setActiveTasks] = useState<string[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [isProcessingWebsite, setIsProcessingWebsite] = useState(false);

  // Get KB context values
  const {
    selectedKB,
    refreshDocuments,
    selectedDocument,
    documentContent,
    isLoadingContent,
    isDocumentDetailOpen,
    setIsDocumentDetailOpen,
  } = useKB();

  // Load stored tasks on mount and clean up old tasks
  useEffect(() => {
    if (!selectedKB?.id) return;

    cleanupOldTasks();

    const storedTaskIds = getStoredTaskIds(selectedKB.id);
    if (storedTaskIds.length > 0) {
      setActiveTasks(storedTaskIds);

      storedTaskIds.forEach(async (taskId) => {
        try {
          const status = await KnowledgeBaseService.getTaskStatus({ taskId });
          const statusUpper = status.status?.toUpperCase();

          if (
            statusUpper === 'SUCCESS' ||
            statusUpper === 'COMPLETED' ||
            statusUpper === 'FAILED' ||
            statusUpper === 'FAILURE' ||
            statusUpper === 'CANCELLED'
          ) {
            removeTaskId(selectedKB.id, taskId);
            setActiveTasks((prev) => prev.filter((id) => id !== taskId));
          }
        } catch {
          console.warn(
            `Task ${taskId} appears to be invalid, removing from storage`,
          );
          removeTaskId(selectedKB.id, taskId);
          setActiveTasks((prev) => prev.filter((id) => id !== taskId));
        }
      });
    }
  }, [selectedKB?.id]);

  // Update knowledge base mutation
  const updateKBMutation = useMutation({
    mutationFn: (formData: {
      title?: string;
      description?: string;
      access_level?: 'private' | 'shared';
      usage_mode?: 'manual' | 'agent_requested' | 'always';
      tags?: string[];
      allowed_users?: string[];
    }) =>
      KnowledgeBaseService.updateKb({
        kbId: selectedKB?.id || '',
        requestBody: {
          title: formData.title,
          description: formData.description,
          access_level: formData.access_level,
          usage_mode: formData.usage_mode,
          tags: formData.tags,
          allowed_users: formData.allowed_users,
        },
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [CacheKey.KnowledgeBase, selectedKB?.id],
      });
      toast.success('Knowledge base updated successfully');
      setEditDialogOpen(false);
    },
    onError: (error) => {
      console.error('Error updating knowledge base:', error);
      toast.error('Failed to update knowledge base');
    },
  });

  // Delete knowledge base mutation
  const deleteKBMutation = useMutation({
    mutationFn: () =>
      selectedKB?.id
        ? KnowledgeBaseService.deleteKb({
            kbId: selectedKB.id,
          })
        : Promise.reject('No KB selected'),
    onSuccess: () => {
      toast.success('Knowledge base deleted successfully');
      router.push(pathsConfig.app.knowledgeBase);
    },
    onError: (error) => {
      console.error('Error deleting knowledge base:', error);
      toast.error('Failed to delete knowledge base');
    },
  });

  const handleEditSubmit = (formData: {
    title: string;
    description?: string;
    access_level: 'private' | 'shared';
    usage_mode: 'manual' | 'agent_requested' | 'always';
    tags?: string[];
    allowed_users?: string[];
  }) => {
    updateKBMutation.mutate({
      title: formData.title,
      description: formData.description,
      access_level: formData.access_level,
      usage_mode: formData.usage_mode,
      tags: formData.tags,
      allowed_users: formData.allowed_users,
    });
  };

  const handleDeleteKB = () => {
    deleteKBMutation.mutate();
  };

  const handleUploadFiles = async (uploadedFiles: UploadedFileInfo[]) => {
    if (!uploadedFiles.length || !selectedKB?.id) return;

    setIsUploading(true);
    try {
      const response = await KnowledgeBaseService.confirmFileUploads({
        kbId: selectedKB.id,
        requestBody: {
          uploaded_files: uploadedFiles,
        },
      });

      if (response.task_id) {
        setActiveTasks((prev) => [...prev, response.task_id]);
        addTaskId(selectedKB.id, response.task_id, 'file_upload');
      }

      queryClient.invalidateQueries({
        queryKey: [CacheKey.KnowledgeBase, selectedKB.id, 'documents'],
      });

      toast.success('Files uploaded successfully and are being processed');
      setUploadDialogOpen(false);
    } catch (error) {
      console.error('Error during file upload:', error);
      toast.error('Failed to upload files');
    } finally {
      setIsUploading(false);
    }
  };

  const handleWebsiteIngestion = async (data: {
    urls: string[];
    deep_crawls: boolean[];
  }) => {
    if (!selectedKB?.id) return;

    setIsProcessingWebsite(true);
    try {
      const response = await KnowledgeBaseService.uploadUrls({
        kbId: selectedKB.id,
        requestBody: {
          urls: data.urls,
          deep_crawls: data.deep_crawls,
        },
      });

      if (response.task_id) {
        setActiveTasks((prev) => [...prev, response.task_id]);
        addTaskId(selectedKB.id, response.task_id, 'website_ingestion');
      }

      queryClient.invalidateQueries({
        queryKey: [CacheKey.KnowledgeBase, selectedKB.id, 'documents'],
      });

      const urlCount = data.urls.length;
      toast.success(
        `${urlCount} website${urlCount === 1 ? '' : 's'} ${urlCount === 1 ? 'is' : 'are'} being processed`,
      );
      setWebsiteDialogOpen(false);
    } catch (error) {
      console.error('Error processing website:', error);
      toast.error('Failed to process website');
    } finally {
      setIsProcessingWebsite(false);
    }
  };

  return (
    <div className="animate-in fade-in-0 flex flex-col space-y-4 overflow-hidden duration-200">
      <KBHeader />

      <Separator className="shrink-0" />

      {/* Active Tasks */}
      {activeTasks.length > 0 && (
        <div className="bg-muted/30 shrink-0 rounded-lg p-4">
          <div className="space-y-3">
            <h3 className="text-foreground text-sm font-medium">
              Active Tasks ({activeTasks.length})
            </h3>
            <div className="grid max-h-40 grid-cols-1 gap-3 overflow-y-auto md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
              {activeTasks.map((taskId) => (
                <div key={taskId} className="min-w-0">
                  <KBTaskStatus
                    taskId={taskId}
                    onComplete={() => {
                      setActiveTasks((prev) =>
                        prev.filter((id) => id !== taskId),
                      );
                      if (selectedKB?.id) {
                        removeTaskId(selectedKB.id, taskId);
                      }
                      refreshDocuments();
                      toast.success('Task completed successfully');
                    }}
                    onError={(error) => {
                      setActiveTasks((prev) =>
                        prev.filter((id) => id !== taskId),
                      );
                      if (selectedKB?.id) {
                        removeTaskId(selectedKB.id, taskId);
                      }
                      toast.error(`Task failed: ${error}`);
                    }}
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Documents Section */}
      <div className="flex min-h-0 grow flex-col space-y-4 overflow-hidden">
        <DocumentsList
          onUploadClick={() => setUploadDialogOpen(true)}
          onWebsiteClick={() => setWebsiteDialogOpen(true)}
          isUploading={isUploading}
          isProcessingWebsite={isProcessingWebsite}
        />
      </div>

      {/* Dialogs */}
      {selectedKB && (
        <KBCreateDialog
          open={editDialogOpen}
          onOpenChange={setEditDialogOpen}
          initialData={{
            title: selectedKB.title,
            description: selectedKB.description || '',
            access_level: selectedKB.access_level || 'private',
            usage_mode: selectedKB.usage_mode || 'manual',
            tags: selectedKB.tags || [],
            allowed_users: selectedKB.allowed_users || [],
          }}
          onSubmit={handleEditSubmit}
          mode="edit"
        />
      )}

      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Knowledge Base</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this knowledge base? This action
              cannot be undone and all documents will be permanently deleted.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteKB}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Upload Dialog */}
      <Dialog open={uploadDialogOpen} onOpenChange={setUploadDialogOpen}>
        <DialogContent className="flex max-h-[90vh] w-[95vw] max-w-4xl flex-col p-0 sm:w-[90vw] md:w-[80vw] lg:w-[70vw] xl:w-[60vw]">
          <DialogHeader className="shrink-0 p-6 pb-4">
            <DialogTitle className="text-xl font-semibold">
              Upload Files
            </DialogTitle>
            <DialogDescription className="text-muted-foreground">
              Add documents to your knowledge base. Supported formats: PDF, TXT,
              MD, HTML, JSON, CSV.
            </DialogDescription>
          </DialogHeader>
          <div className="min-h-0 flex-1 overflow-y-auto p-6 pt-0">
            <KBDocumentUpload
              kbId={selectedKB?.id || ''}
              onUpload={handleUploadFiles}
              isUploading={isUploading}
              onClose={() => setUploadDialogOpen(false)}
            />
          </div>
        </DialogContent>
      </Dialog>

      {/* Website Ingestion Dialog */}
      <KBWebsiteIngestionDialog
        open={websiteDialogOpen}
        onOpenChange={setWebsiteDialogOpen}
        onSubmit={handleWebsiteIngestion}
        isProcessing={isProcessingWebsite}
      />

      {/* Document Detail Dialog */}
      <KBDocumentDetailDialog
        open={isDocumentDetailOpen}
        onOpenChange={setIsDocumentDetailOpen}
        document={selectedDocument}
        content={documentContent}
        isLoading={isLoadingContent}
        kbId={selectedKB?.id}
        onDelete={
          selectedDocument
            ? () => {
                const { handleDeleteDocument } = useKBDocuments();
                handleDeleteDocument(selectedDocument);
              }
            : undefined
        }
      />
    </div>
  );
}

// Wrapper component that provides the KBContext
export function KBDetailContent({ kbId }: KBDetailContentProps) {
  return (
    <KBProvider kbId={kbId}>
      <KBDetailContentInner />
    </KBProvider>
  );
}
