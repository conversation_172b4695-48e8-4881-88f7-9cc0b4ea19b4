'use client';

import { NewPageContainer } from '@/components/layout/new-page-container';
import { PageHeader } from '@/components/layout/page-header';
import { If } from '@/components/ui/common/if';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useSubscription } from '@/lib/hooks/useSubscription';
import { CreditCardIcon, ReceiptIcon } from 'lucide-react';

import { BillingContent } from './_components/billing/biliing-content';
import { ErrorView } from './_components/error-view';
import { FreePlanCard } from './_components/free-plan-card';
import { LoadingView } from './_components/loading-view';
import { SubscriptionCard } from './_components/subscription-card';

export default function SubscriptionPage() {
  // Use the subscription data from the global state
  const {
    subscriptions,
    activeSubscription,
    hasActiveSubscription,
    expiresAt,
    isLoading,
    error,
    isFetched,
  } = useSubscription();

  const hasCanceledSubscription = subscriptions.some(
    (sub) => sub.status === 'canceled',
  );

  // Loading state
  if (isLoading || !isFetched) {
    return <LoadingView />;
  }

  // Error state
  if (error) {
    return <ErrorView errorMessage={error} />;
  }

  const tabs = [
    {
      label: 'Subscription',
      icon: CreditCardIcon,
      content: (
        <If condition={subscriptions.length} fallback={<FreePlanCard />}>
          <SubscriptionCard
            activeSubscription={activeSubscription ?? undefined}
            hasActiveSubscription={hasActiveSubscription}
            expiresAt={expiresAt}
            hasCanceledSubscription={hasCanceledSubscription}
            showUpgradeButton={true}
          />
        </If>
      ),
    },
    {
      label: 'Billing',
      icon: ReceiptIcon,
      content: <BillingContent />,
    },
  ];

  return (
    <NewPageContainer>
      <PageHeader
        title="Subscription"
        description="Manage your subscription plan and billing details"
      />
      <div className="min-h-0 flex-1">
        <Tabs defaultValue={tabs[0].label} className="flex size-full flex-col">
          <TabsList>
            {tabs.map((tab) => (
              <TabsTrigger key={tab.label} value={tab.label} className="gap-2">
                <tab.icon className="size-4" />
                {tab.label}
              </TabsTrigger>
            ))}
          </TabsList>
          <ScrollArea className="grow">
            {tabs.map((tab) => (
              <TabsContent key={tab.label} value={tab.label} className="h-full">
                {tab.content}
              </TabsContent>
            ))}
          </ScrollArea>
        </Tabs>
      </div>
    </NewPageContainer>
  );
}
