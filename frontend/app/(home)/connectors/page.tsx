import { Metada<PERSON> } from 'next';

import Link from 'next/link';

import { NewPageContainer } from '@/components/layout/new-page-container';
import { PageHeader } from '@/components/layout/page-header';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import pathsConfig from '@/config/paths.config';
import { ToolList } from '@/features/builtin-tools/components/tool-list';
import { BuiltinConnectionPage } from '@/features/connection-v2/components/builtin-connection-page';
// import { BuiltinConnectionList } from '@/features/connection/components/builtin-connection-list';
import { MCPConnectionList } from '@/features/connection/components/mcp-connection-list';
import { NetworkIcon, PlugIcon, WrenchIcon } from 'lucide-react';

type Props = {
  searchParams: Promise<{
    tab: string;
  }>;
};

export const metadata: Metadata = {
  title: 'Connectors',
};

export default async function Page(props: Props) {
  const searchParams = await props.searchParams;

  return (
    <NewPageContainer>
      <PageHeader
        title="Connections"
        description="Manage connections and MCP servers for your environment"
      />

      <Tabs
        value={searchParams.tab ?? tabs[0].value}
        className="overflow-thin-auto flex grow flex-col"
      >
        <TabsList>
          {tabs.map((tab) => (
            <TabsTrigger key={tab.value} value={tab.value} asChild>
              <Link
                href={`${pathsConfig.app.connectors}?tab=${tab.value}`}
                prefetch
                className="gap-2"
              >
                <tab.icon className="size-4" />
                {tab.label}
              </Link>
            </TabsTrigger>
          ))}
        </TabsList>

        {tabs.map((tab) => (
          <TabsContent key={tab.value} value={tab.value}>
            {tab.content}
          </TabsContent>
        ))}
      </Tabs>
    </NewPageContainer>
  );
}

const tabs = [
  {
    value: 'builtin-connections',
    label: 'Builtin Connections',
    icon: PlugIcon,
    content: <BuiltinConnectionPage />,
  },
  {
    value: 'mcp-connections',
    label: 'MCP Connections',
    icon: NetworkIcon,
    content: <MCPConnectionList />,
  },
  {
    value: 'builtin-tools',
    label: 'Builtin Tools',
    icon: WrenchIcon,
    content: <ToolList />,
  },
];
