import { PropsWithChildren } from 'react';

import Link from 'next/link';

import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import pathsConfig from '@/config/paths.config';
import { BoxIcon, LightbulbIcon } from 'lucide-react';

export enum EResourceTab {
  Resources = 'Resources',
  Recommendations = 'Recommendations',
}

const resourceTabs = [
  {
    label: EResourceTab.Resources,
    icon: BoxIcon,
    link: pathsConfig.app.resources,
  },
  {
    label: EResourceTab.Recommendations,
    icon: LightbulbIcon,
    link: pathsConfig.app.resourceRecommendations,
  },
] as const;

export const ResourceTabs = ({
  children,
  resourceTab,
}: PropsWithChildren<{ resourceTab: EResourceTab }>) => (
  <Tabs value={resourceTab} className="overflow-thin-auto flex grow flex-col">
    <TabsList>
      {resourceTabs.map((tab) => (
        <TabsTrigger key={tab.label} value={tab.label} asChild>
          <Link href={tab.link} prefetch className="gap-2">
            <tab.icon className="size-4" />
            {tab.label}
          </Link>
        </TabsTrigger>
      ))}
    </TabsList>

    <TabsContent value={resourceTab} className="flex flex-col gap-4">
      {children}
    </TabsContent>
  </Tabs>
);
