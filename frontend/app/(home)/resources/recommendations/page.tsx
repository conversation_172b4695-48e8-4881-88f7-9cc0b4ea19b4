import { use } from 'react';

import { NewPageContainer } from '@/components/layout/new-page-container';
import { PageHeader } from '@/components/layout/page-header';
import { RecommendationFilters } from '@/features/recommendation/components/recommendation-filters';
import { RecommendationOveral } from '@/features/recommendation/components/recommendation-overal';
import { RecommendationTable } from '@/features/recommendation/components/recommendation-table';
import { RecommendationQueryParams } from '@/features/recommendation/models/recommendation.type';
import { withPaginationDefaults } from '@/utils/with-pagination-defaults';

import { EResourceTab, ResourceTabs } from '../_config/resource-tabs.config';

export const metadata = {
  title: 'Suggestion Recommendations',
};

type PageProps = {
  searchParams: Promise<RecommendationQueryParams>;
};

export default function Page(props: PageProps) {
  const searchParams = withPaginationDefaults(use(props.searchParams));

  return (
    <NewPageContainer>
      <PageHeader
        title="Recommendations"
        description="Identified recommendation for resources"
      />

      <ResourceTabs resourceTab={EResourceTab.Recommendations}>
        <RecommendationOveral />

        <RecommendationFilters defaultValues={searchParams} />
        <div className="overflow-thin-auto grow">
          <RecommendationTable searchParams={searchParams} />
        </div>
      </ResourceTabs>
    </NewPageContainer>
  );
}
