import { use } from 'react';

import { NewPageContainer } from '@/components/layout/new-page-container';
import { PageHeader } from '@/components/layout/page-header';
import { Separator } from '@/components/ui/separator';
import { ResourceTable } from '@/features/resource/components/resource-tables/resoure-table';
import { ResourceQueryParams } from '@/features/resource/models/resource.type';
import { withPaginationDefaults } from '@/utils/with-pagination-defaults';

import { ResourceFilter } from './_components/resource-filter';
import { ResourceStatusSummary } from './_components/resource-status-summary';
import { EResourceTab, ResourceTabs } from './_config/resource-tabs.config';

export const metadata = {
  title: 'Scanned Resources',
};

type PageProps = {
  searchParams: Promise<ResourceQueryParams>;
};

export default function Page(props: PageProps) {
  const searchParams = withPaginationDefaults(use(props.searchParams));

  return (
    <NewPageContainer>
      <PageHeader title="Resources" description="Scanned resources" />
      <ResourceTabs resourceTab={EResourceTab.Resources}>
        <div className="max-md:hidden">
          <ResourceStatusSummary />
          <Separator />
        </div>

        <ResourceFilter defaultValues={searchParams} />
        <div className="overflow-thin-auto grow">
          <ResourceTable searchParams={searchParams} />
        </div>
      </ResourceTabs>
    </NewPageContainer>
  );
}
