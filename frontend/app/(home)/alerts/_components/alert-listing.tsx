'use client';

import { useEffect } from 'react';

import { AlertSeverity, AlertsService } from '@/client';
import { DataTable } from '@/components/ui/table/data-table';
import { DataTableFilterBox } from '@/components/ui/table/data-table-filter-box';
import { DataTableResetFilter } from '@/components/ui/table/data-table-reset-filter';
import { DataTableSearch } from '@/components/ui/table/data-table-search';
import { CacheKey } from '@/components/utils/cache-key';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { parseAsInteger, useQueryState } from 'nuqs';

import { columns } from './alert-tables/columns';

type AlertListingProps = {
  page?: number;
  limit?: number;
  q?: string;
  severity?: string;
};

function getAlertsQueryOptions({
  page,
  limit,
  q,
  severity,
}: {
  page: number;
  limit: number;
  q?: string | null;
  severity?: string | null;
}) {
  const severityValue = severity ? (severity as AlertSeverity) : undefined;

  return {
    queryFn: () =>
      AlertsService.listAlerts({
        skip: (page - 1) * limit,
        limit,
        sortBy: 'created_at',
        sortDesc: true,
        severity: severityValue,
      }),
    queryKey: [CacheKey.Alerts, { page, limit, q, severity }],
  };
}

export default function AlertListing({
  page: initialPage = 1,
  limit = 10,
  q: initialQuery,
  severity: initialSeverity,
}: AlertListingProps) {
  const queryClient = useQueryClient();
  const [page, setPage] = useQueryState(
    'page',
    parseAsInteger.withDefault(initialPage),
  );
  const [searchQuery, setSearchQuery] = useQueryState('q', {
    defaultValue: initialQuery || '',
  });
  const [severityFilter, setSeverityFilter] = useQueryState('severity', {
    defaultValue: initialSeverity || '',
  });

  // Check if any filter is active
  const isAnyFilterActive = !!(searchQuery || severityFilter);

  // Reset all filters
  const resetFilters = () => {
    setSearchQuery(null);
    setSeverityFilter(null);
    setPage(1);
  };

  // Severity filter options
  const severityOptions = [
    { value: 'CRITICAL', label: 'Critical' },
    { value: 'HIGH', label: 'High' },
    { value: 'MEDIUM', label: 'Medium' },
    { value: 'LOW', label: 'Low' },
    { value: 'INFO', label: 'Info' },
  ];

  const {
    data: alerts,
    isPending,
    isPlaceholderData,
  } = useQuery({
    ...getAlertsQueryOptions({
      page,
      limit,
      q: searchQuery,
      severity: severityFilter,
    }),
    placeholderData: (prevData) => prevData,
  });

  const hasNextPage = !isPlaceholderData && alerts?.data.length === limit;

  useEffect(() => {
    if (hasNextPage) {
      queryClient.prefetchQuery(
        getAlertsQueryOptions({
          page: page + 1,
          limit,
          q: searchQuery,
          severity: severityFilter,
        }),
      );
    }
  }, [page, queryClient, hasNextPage, limit, searchQuery, severityFilter]);

  return (
    <div className="space-y-4">
      <div className="flex flex-wrap items-center gap-4">
        <DataTableSearch
          searchKey="alerts"
          searchQuery={searchQuery || ''}
          setSearchQuery={setSearchQuery}
          setPage={setPage}
        />
        <DataTableFilterBox
          filterKey="severity"
          title="Severity"
          options={severityOptions}
          setFilterValue={setSeverityFilter}
          filterValue={severityFilter || ''}
        />
        <DataTableResetFilter
          isFilterActive={isAnyFilterActive}
          onReset={resetFilters}
        />
      </div>
      <DataTable
        columns={columns}
        data={alerts?.data ?? []}
        totalItems={alerts?.total ?? 0}
        loading={isPending}
        customScrollClass="grid h-[calc(80vh-220px)] rounded-lg border md:h-[calc(90dvh-380px)]"
      />
    </div>
  );
}
