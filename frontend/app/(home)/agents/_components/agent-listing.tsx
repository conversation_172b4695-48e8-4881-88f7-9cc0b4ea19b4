'use client';

import { LoadingSkeleton } from '@/features/agent/components';
import { AgentCardList } from '@/features/agent/components/agent-card-list';
import { agentQuery } from '@/features/agent/hooks/agent.query';
import { AgentProvider } from '@/features/agent/provider/agent-provider';
import { AgentType } from '@/openapi-ts/gens';

const AgentListingPage = () => {
  const { data: agentsResponse, isLoading } = agentQuery.query.useList();

  if (agentsResponse) {
    const conversationalAgents = agentsResponse.data.filter(
      (agent) => agent.type === AgentType.conversation_agent,
    );

    return (
      <AgentProvider>
        <div className="flex h-full flex-col gap-4">
          {conversationalAgents.length > 0 && (
            <div className="flex min-h-0 flex-1 flex-col gap-2">
              <div className="flex items-center gap-2">
                <h2 className="text-xl font-semibold tracking-tight">
                  Team Members
                </h2>
              </div>
              <div className="min-h-0 flex-1 overflow-hidden">
                <AgentCardList className="h-full overflow-y-auto px-2 pt-2" />
              </div>
            </div>
          )}
        </div>
      </AgentProvider>
    );
  }

  if (isLoading) {
    return <LoadingSkeleton />;
  }
};

export default AgentListingPage;
