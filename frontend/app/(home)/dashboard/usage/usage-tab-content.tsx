'use client';

import React from 'react';

import { UsageAnalyticsDashboard } from '@/features/usage-analytics';
import { useUserContext } from '@/features/user/provider/user-provider';
import { DateFormat, formatUtcDate } from '@/lib/date-utils';

export default function UsageTabContent({
  currentStartDate,
  currentEndDate,
}: {
  currentStartDate: string;
  currentEndDate: string;
}) {
  const { user } = useUserContext();

  if (!user) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-center">
          <h2 className="mb-2 text-xl font-medium">Loading user data...</h2>
          <p className="text-muted-foreground">
            Please wait while we fetch your information
          </p>
        </div>
      </div>
    );
  }

  // Prepare date range for the analytics dashboard
  const dateRange = {
    start_date: currentStartDate
      ? formatUtcDate(new Date(currentStartDate), DateFormat.ISO_DATE)
      : undefined,
    end_date: currentEndDate
      ? formatUtcDate(new Date(currentEndDate), DateFormat.ISO_DATE)
      : undefined,
  };

  return (
    <div className="space-y-6 p-1">
      <UsageAnalyticsDashboard userId={user.id} dateRange={dateRange} />
    </div>
  );
}
