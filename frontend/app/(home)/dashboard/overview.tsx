'use client';

import { NewPageContainer } from '@/components/layout/new-page-container';
import { PageHeader } from '@/components/layout/page-header';
import { TabItem, UrlTabs } from '@/components/url-tabs';
import { endOfMonth, startOfMonth } from 'date-fns';
import { BarChartIcon, LayoutDashboardIcon } from 'lucide-react';

import { OverviewTabContent } from './overview/overview-tab-content';
import UsageTabContent from './usage/usage-tab-content';

// @depercated
export default function OverViewPage() {
  // Simple default date range for UsageTabContent
  const now = new Date();
  const currentStartDate = startOfMonth(now).toISOString();
  const currentEndDate = endOfMonth(now).toISOString();

  const tabs: TabItem[] = [
    {
      value: 'overview',
      label: 'Overview',
      icon: LayoutDashboardIcon,
      content: <OverviewTabContent />,
    },
    {
      value: 'usage',
      label: 'Usage Analytics',
      icon: BarChartIcon,
      content: (
        <UsageTabContent
          currentStartDate={currentStartDate}
          currentEndDate={currentEndDate}
        />
      ),
    },
  ];

  return (
    <NewPageContainer className="overflow-thin-auto">
      <PageHeader
        title="Hi, Welcome back 👋"
        description="View your cloud resource usage and analytics"
      />
      <UrlTabs tabs={tabs} defaultTab="overview" className="space-y-0" />
    </NewPageContainer>
  );
}
