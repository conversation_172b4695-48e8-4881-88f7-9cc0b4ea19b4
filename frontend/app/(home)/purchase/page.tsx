'use client';

import { useState } from 'react';

import { SubscriptionsService } from '@/client/sdk.gen';
// Types and constants
import {
  ProductResponse as BackendProductResponse,
  SubscriptionsCreateCheckoutSessionResponse,
} from '@/client/types.gen';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { formatUSD } from '@/lib/currency';
import { useSubscription } from '@/lib/hooks/useSubscription';
import { cn } from '@/lib/utils';
import { useMutation, useQuery } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import {
  Check,
  Crown,
  Loader2,
  Rocket,
  Shield,
  Sparkles,
  Star,
  Zap,
} from 'lucide-react';
import { toast } from 'sonner';

import { getPlanFeatures } from '../subscription/_components/types';
import { EnterpriseContactForm } from './_components/enterprise-contact-form';
import { PlanChangeWarning } from './_components/plan-change-warning';

const PLAN_ICONS = {
  starter: Star,
  standard: Shield,
  advanced: Rocket,
  enterprise: Crown,
} as const;

const PLAN_COLORS = {
  starter: {
    light: 'from-blue-500 to-blue-600',
    dark: 'from-blue-600 to-blue-700',
    bg: 'bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400',
    border: 'border-blue-200 dark:border-blue-900/50',
    hover: 'hover:from-blue-600 hover:to-blue-700',
  },
  standard: {
    light: 'from-emerald-500 to-emerald-600',
    dark: 'from-emerald-600 to-emerald-700',
    bg: 'bg-emerald-100 text-emerald-600 dark:bg-emerald-900/30 dark:text-emerald-400',
    border: 'border-emerald-200 dark:border-emerald-900/50',
    hover: 'hover:from-emerald-600 hover:to-emerald-700',
  },
  advanced: {
    light: 'from-purple-500 to-purple-600',
    dark: 'from-purple-600 to-purple-700',
    bg: 'bg-purple-100 text-purple-600 dark:bg-purple-900/30 dark:text-purple-400',
    border: 'border-purple-200 dark:border-purple-900/50',
    hover: 'hover:from-purple-600 hover:to-purple-700',
  },
  enterprise: {
    light: 'from-orange-500 to-orange-600',
    dark: 'from-orange-600 to-orange-700',
    bg: 'bg-orange-100 text-orange-600 dark:bg-orange-900/30 dark:text-orange-400',
    border: 'border-orange-200 dark:border-orange-900/50',
    hover: 'hover:from-orange-600 hover:to-orange-700',
  },
} as const;

export default function PurchasePage() {
  // Track which price ID is currently being processed
  const [processingPriceId, setProcessingPriceId] = useState<
    string | undefined
  >(undefined);
  // State for enterprise contact form modal
  const [showContactForm, setShowContactForm] = useState(false);
  // State for plan change warning modal
  const [showPlanChangeWarning, setShowPlanChangeWarning] = useState(false);
  const [planChangeWarningData, setPlanChangeWarningData] = useState<{
    currentPlan: string;
    newPlan: string;
    priceId: string;
  } | null>(null);

  // Get user's current subscription information
  const { activeSubscription, hasActiveSubscription } = useSubscription();

  const {
    data: plans,
    isLoading: plansLoading,
    error: plansError,
  } = useQuery({
    queryKey: ['pricing-plans'],
    queryFn: () => SubscriptionsService.getAvailablePlans(),
  });

  const checkoutMutation = useMutation({
    mutationFn: (priceId: string) =>
      SubscriptionsService.createCheckoutSession({
        priceId: priceId,
      }),
    onSuccess: (response: SubscriptionsCreateCheckoutSessionResponse) => {
      // Redirect to Stripe checkout
      window.location.href = response.checkout_session_url as string;
    },
    onError: (err: any) => {
      let errorMessage = 'Failed to create checkout session';

      if (err instanceof AxiosError) {
        // Handle Axios error responses
        errorMessage = err.response?.data?.detail || err.message;
      }

      toast.error('Checkout Error', {
        description: errorMessage,
      });

      // Clear the processing state when an error occurs
      setProcessingPriceId(undefined);
    },
  });

  const handleUpgrade = (
    priceId: string | undefined,
    isEnterprise: boolean = false,
    planName: string,
  ) => {
    if (!priceId) return;
    setProcessingPriceId(priceId);

    if (isEnterprise) {
      setShowContactForm(true);
      return;
    }

    // If user has an active subscription and is NOT on Starter plan
    if (
      hasActiveSubscription &&
      activeSubscription?.product_name &&
      !activeSubscription.product_name.toLowerCase().includes('starter')
    ) {
      setPlanChangeWarningData({
        currentPlan: activeSubscription.product_name,
        newPlan: planName,
        priceId: priceId,
      });
      setShowPlanChangeWarning(true);
      return;
    }

    checkoutMutation.mutate(priceId);
  };

  // Determine button text based on plan's position relative to current plan
  const getButtonText = (plan: BackendProductResponse, planIndex: number) => {
    if (plan.is_custom) return 'Contact Us';

    const currentPlanIndex = plans?.findIndex(
      (p) => p.id === activeSubscription?.product_id,
    );

    if (currentPlanIndex === undefined) {
      return planIndex === 0 ? 'Get Started' : 'Upgrade';
    }

    if (planIndex === currentPlanIndex) {
      return 'Current Plan';
    }

    return planIndex > currentPlanIndex ? 'Upgrade' : 'Downgrade';
  };

  // Check if a plan is the current subscribed plan
  const isCurrentPlan = (planId: string) => {
    return hasActiveSubscription && activeSubscription?.product_id === planId;
  };

  const handleContactFormClose = () => {
    setShowContactForm(false);
    setProcessingPriceId(undefined);
  };

  const handlePlanChangeWarningClose = () => {
    setShowPlanChangeWarning(false);
    setPlanChangeWarningData(null);
    setProcessingPriceId(undefined);
  };

  const handlePlanChangeWarningConfirm = () => {
    if (planChangeWarningData?.priceId) {
      checkoutMutation.mutate(planChangeWarningData.priceId);
    }
  };

  if (plansLoading) {
    return (
      <div className="flex h-[calc(100dvh-4rem)] items-center justify-center">
        <div className="text-center">
          <Loader2 className="text-primary mx-auto h-8 w-8 animate-spin" />
          <p className="text-muted-foreground mt-4 text-sm">
            Loading available plans...
          </p>
        </div>
      </div>
    );
  }

  if (plansError) {
    return (
      <div className="flex h-[calc(100dvh-4rem)] items-center justify-center">
        <div className="max-w-md text-center">
          <p className="text-destructive text-lg font-semibold">
            Failed to load pricing plans
          </p>
          <p className="text-muted-foreground mt-2 text-sm">
            Please try again later or contact support if the issue persists.
          </p>
          <Button
            onClick={() => window.location.reload()}
            variant="outline"
            className="mt-4"
          >
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex min-h-[calc(100dvh-4rem)] flex-col">
      <div className="container flex-1 py-8">
        <EnterpriseContactForm
          open={showContactForm}
          onClose={handleContactFormClose}
          productId={processingPriceId}
        />

        {planChangeWarningData && (
          <PlanChangeWarning
            open={showPlanChangeWarning}
            onClose={handlePlanChangeWarningClose}
            onConfirm={handlePlanChangeWarningConfirm}
            isProcessing={checkoutMutation.isPending}
            currentPlan={planChangeWarningData.currentPlan}
            newPlan={planChangeWarningData.newPlan}
          />
        )}

        <div className="relative mx-auto mb-10 text-center">
          <div className="absolute inset-x-0 -top-16 -z-10 flex justify-center overflow-hidden blur-2xl">
            <div className="from-primary aspect-1200/800 w-300 flex-none bg-linear-to-tr to-purple-400 opacity-20" />
          </div>
          <h1 className="from-foreground to-foreground/70 bg-linear-to-br bg-clip-text text-3xl font-bold tracking-tight text-transparent sm:text-4xl">
            Upgrade Your Plan
          </h1>
          <p className="text-muted-foreground mx-auto mt-4 max-w-2xl text-base">
            Choose the perfect plan for your needs. All plans include our core
            features.
          </p>
        </div>

        <div className="mx-auto max-w-[1440px] px-2 pt-4 pb-12">
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
            {plans?.map((plan: BackendProductResponse, index: number) => {
              const features: string[] = getPlanFeatures(plan);
              const currentPlan = isCurrentPlan(plan.id);
              const buttonText = getButtonText(plan, index);
              const priceId = plan?.prices?.[0]?.id || plan.id;
              const isProcessing = processingPriceId === priceId;
              const isEnterprise = plan.is_custom;
              const priceData = plan?.prices?.[0];
              const planKey = plan.name.toLowerCase().includes('starter')
                ? 'starter'
                : plan.name.toLowerCase().includes('standard')
                  ? 'standard'
                  : plan.name.toLowerCase().includes('advanced')
                    ? 'advanced'
                    : 'enterprise';
              const PlanIcon = PLAN_ICONS[planKey];
              const planColors = PLAN_COLORS[planKey];

              return (
                <Card
                  key={plan.id}
                  className={cn(
                    'relative flex flex-col overflow-hidden backdrop-blur-xs transition-all duration-500',
                    currentPlan
                      ? cn(
                          'shadow-lg ring-2',
                          planColors.border,
                          'from-background to-background/80 bg-linear-to-b',
                        )
                      : 'hover:border-primary/50 hover:shadow-md',
                  )}
                >
                  {currentPlan && (
                    <div
                      className={cn(
                        'via-primary/50 absolute inset-x-0 -top-px h-px bg-linear-to-r from-transparent to-transparent',
                      )}
                    />
                  )}

                  <CardHeader className="space-y-2 pt-4 pb-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <div
                            className={cn(
                              'rounded-lg p-2',
                              currentPlan
                                ? planColors.bg
                                : 'bg-muted text-muted-foreground',
                            )}
                          >
                            <PlanIcon className="h-5 w-5" />
                          </div>
                          <CardTitle className="text-lg font-semibold">
                            {plan.name}
                          </CardTitle>
                        </div>
                      </div>
                    </div>
                    <CardDescription className="min-h-[40px] text-sm">
                      {plan.description}
                    </CardDescription>
                  </CardHeader>

                  <CardContent className="flex-1 space-y-3 overflow-y-auto">
                    <div className="flex items-baseline justify-center">
                      {priceData ? (
                        <>
                          <span className="text-2xl font-bold tracking-tight">
                            {formatUSD(priceData?.amount)}
                          </span>
                          <span className="text-muted-foreground ml-1 text-sm font-medium">
                            /{priceData?.interval}
                          </span>
                        </>
                      ) : (
                        <span className="text-lg font-semibold">
                          Custom Pricing
                        </span>
                      )}
                    </div>

                    <div className="space-y-1.5">
                      {features.map((feature: string, idx: number) => (
                        <div
                          key={idx}
                          className={cn(
                            'flex items-start gap-2 rounded-lg px-2 py-0.5 transition-colors',
                            currentPlan && 'hover:bg-primary/5',
                          )}
                        >
                          <Check
                            className={cn(
                              'mt-0.5 h-4 w-4 shrink-0',
                              currentPlan
                                ? planColors.bg.split(' ')[1]
                                : 'text-muted-foreground',
                            )}
                          />
                          <span className="text-xs">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>

                  <CardFooter className="pt-3">
                    {!currentPlan && (
                      <Button
                        className={cn(
                          'group relative w-full overflow-hidden',
                          `bg-linear-to-r ${planColors.light} ${planColors.hover}`,
                        )}
                        onClick={() =>
                          handleUpgrade(priceId, isEnterprise, plan.name)
                        }
                        disabled={
                          checkoutMutation.isPending ||
                          (!isEnterprise && !priceData)
                        }
                      >
                        <span className="relative z-10 flex items-center justify-center gap-2">
                          {isProcessing ? (
                            <>
                              <Loader2 className="h-4 w-4 animate-spin" />
                              Processing...
                            </>
                          ) : (
                            <>
                              <Zap className="h-4 w-4" />
                              {buttonText}
                            </>
                          )}
                        </span>
                        <div className="absolute inset-0 transform bg-linear-to-r from-white/10 via-white/5 to-transparent transition-transform group-hover:translate-x-full" />
                      </Button>
                    )}

                    {currentPlan && (
                      <Button
                        className={cn(
                          'bg-primary/10 text-primary mt-2 w-full justify-center disabled:opacity-100',
                        )}
                        disabled
                      >
                        <Sparkles className="mr-1 h-3 w-3" />
                        Current Plan
                      </Button>
                    )}
                  </CardFooter>
                </Card>
              );
            })}
          </div>
        </div>
        {/* Notice for current plan */}
        {hasActiveSubscription && (
          <div className="border-primary/20 from-primary/5 via-primary/10 to-primary/5 mx-auto mt-4 mb-16 max-w-3xl overflow-hidden rounded-lg border bg-linear-to-r p-2 text-center shadow-xs backdrop-blur-xs">
            <p className="text-muted-foreground text-sm">
              You are currently subscribed to the{' '}
              <span className="text-foreground font-medium">
                {activeSubscription?.product_name}
              </span>{' '}
              plan. Changes will take effect at the end of your billing cycle.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
