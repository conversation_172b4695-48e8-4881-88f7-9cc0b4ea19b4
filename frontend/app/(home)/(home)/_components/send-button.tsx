import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { Loader2Icon, SendIcon } from 'lucide-react';

interface SendButtonProps {
  value: string;
  isGenerating: boolean;
  isSending: boolean;
  onSend: () => void;
}

export const SendButton = ({
  value,
  isGenerating,
  isSending,
  onSend,
}: SendButtonProps) => (
  <Button
    size="icon"
    className={cn(
      'h-8 w-8',
      'bg-primary/90 hover:bg-primary',
      'rounded-full transition-all duration-200',
      (!value.trim() || isSending) && 'opacity-50',
    )}
    disabled={!value.trim() || isGenerating || isSending}
    onClick={onSend}
    title="Send direct message"
  >
    {isSending ? (
      <Loader2Icon className="h-4 w-4 animate-spin" />
    ) : (
      <SendIcon className="h-4 w-4" />
    )}
    <span className="sr-only">Send</span>
  </Button>
);
