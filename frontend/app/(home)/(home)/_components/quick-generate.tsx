'use client';

import { useEffect, useRef, useState } from 'react';

import { ChatInputPlaceholder } from '@/components/chat/chat-input-placeholder';
// Import mention-related components and utilities
import { AgentMentionDropdown } from '@/components/chat/components/agent-mention';
import { AutocompleteDropdown } from '@/components/chat/components/autocomplete-dropdown';
import { ChatHelpDialog } from '@/components/chat/components/chat-help-dialog/chat-help-dialog';
import { resourceCategories } from '@/components/chat/components/data';
import { ResourceMentionDropdown } from '@/components/chat/components/resource-mention';
import {
  getIncompleteAtCursor,
  highlightMentions,
} from '@/components/chat/utils/mention-highlighting';
import { Icons } from '@/components/icons';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { If } from '@/components/ui/common/if';
import { Textarea } from '@/components/ui/textarea';
import { WithTooltip } from '@/components/ui/tooltip';
import { QUICK_PROMPT_LIST } from '@/features/agent/data/prompt.constants';
import { useNavigateAgentDetail } from '@/features/agent/hooks/use-navigate-agent-detail';
import { builtinToolsQuery } from '@/features/builtin-tools/hooks/builtin-tools.query';
import { OperationHubDialog } from '@/features/task/components/operation-hub-dialog';
import { useKnowledgeBases } from '@/hooks/use-knowledge-bases';
import { cn } from '@/lib/utils';
import { SchemaAppSchemasTaskTemplateTaskTemplateResponse } from '@/openapi-ts/gens';
import { flow } from 'lodash';
import { CalendarIcon } from 'lucide-react';
import { toast } from 'sonner';
import { useDebounceValue } from 'usehooks-ts';

import { calculateMentionDropdownPosition } from '../utils/calculate-mention-dropdown-position';
import { ExamplePrompts } from './example-prompts';
import { SendButton } from './send-button';

// New components for buttons

interface QuickGenerateProps {
  messagesRemaining?: number;
  showGitHubButton?: boolean;
  onGitHubClick?: () => void;
}

export const QuickGenerate = ({
  messagesRemaining = 0,
  showGitHubButton = true,
  onGitHubClick,
}: QuickGenerateProps) => {
  const navigateAgentDetail = useNavigateAgentDetail();

  const [autocompleteFilter, setAutocompleteFilter] = useState('');
  const [debouncedAutocompleteFilter] = useDebounceValue(
    autocompleteFilter,
    300,
  );
  // Original state
  const [inputValue, setInputValue] = useState('');
  const [isSending, setIsSending] = useState(false);

  // Mention-related state
  const [showResourceMention, setShowResourceMention] = useState(false);
  const [resourceMentionPosition, setResourceMentionPosition] = useState(
    () => ({
      top: 0,
      left: 0,
    }),
  );
  const [resourceMentionFilter, setResourceMentionFilter] = useState('');
  const [showAgentMention, setShowAgentMention] = useState(false);
  const [agentMentionPosition, setAgentMentionPosition] = useState(() => ({
    top: 0,
    left: 0,
  }));
  const [agentMentionFilter, setAgentMentionFilter] = useState('');

  // Refs
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const overlayRef = useRef<HTMLDivElement>(null);
  const isSubmittingRef = useRef(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Fetch builtin tools and knowledge bases
  const { data: builtinTools, isPending: isBuiltinToolsLoading } =
    builtinToolsQuery.query.useList();
  const { kbs, isLoading: kbsLoading } = useKnowledgeBases();

  // Prepare dynamic resource categories
  const dynamicResourceCategories = [
    {
      id: 'tools',
      name: 'Tools',
      icon: <Icons.settings className="h-5 w-5 text-purple-500" />,
      items: isBuiltinToolsLoading
        ? [
            {
              id: 'loading',
              title: 'Loading tools...',
              description: 'Please wait',
            },
          ]
        : builtinTools?.map((tool) => ({
            id: tool.id,
            title: tool.builtin_tool.display_name,
            description: tool.builtin_tool.description,
          })) || [],
    },
    ...resourceCategories.map((category) => {
      if (category.isDynamic && category.source === 'kb_collections') {
        return {
          ...category,
          items: kbsLoading
            ? [
                {
                  id: 'loading',
                  title: 'Loading collections...',
                  description: 'Please wait',
                },
              ]
            : kbs,
        };
      }
      return category;
    }),
  ];

  const handleDirectMessage = async () => {
    if (!inputValue.trim() || isSending) return;

    setIsSending(true);
    try {
      navigateAgentDetail({
        initialMessage: inputValue.trim(),
      });
      setInputValue('');
    } finally {
      setIsSending(false);
    }
  };

  // Update handleKeyDown to handle mention navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Let the dropdowns handle their own keyboard events
    if (showResourceMention || showAgentMention) {
      if (e.key === 'Escape') {
        e.preventDefault();
        setShowResourceMention(false);
        setShowAgentMention(false);
        return;
      }
      // Don't handle other keys when dropdowns are open
      return;
    }

    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (!isSending && inputValue.trim()) {
        handleDirectMessage();
      }
    }

    // Handle # and @ key presses to trigger mentions
    if (e.key === '#' || e.key === '@') {
      const cursorPos = (e.currentTarget as HTMLTextAreaElement).selectionStart;
      const textBeforeCursor = inputValue.substring(0, cursorPos);

      // Check if the character before is a space or start of line
      const lastChar = textBeforeCursor.slice(-1);
      if (!lastChar || lastChar === ' ' || lastChar === '\n') {
        // Let the character be typed, checkForMentions will handle showing the dropdown
        return;
      }
    }
  };

  // Add effect to handle global keyboard events for dropdowns
  useEffect(() => {
    if (!showResourceMention && !showAgentMention) return;

    const handleGlobalKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        e.preventDefault();
        setShowResourceMention(false);
        setShowAgentMention(false);
      }
    };

    document.addEventListener('keydown', handleGlobalKeyDown);
    return () => {
      document.removeEventListener('keydown', handleGlobalKeyDown);
    };
  }, [showResourceMention, showAgentMention]);

  // Add effect to handle clicks outside dropdowns
  useEffect(() => {
    if (!showResourceMention && !showAgentMention) return;

    const handleClickOutside = (e: MouseEvent) => {
      // Check if click is outside both textarea AND dropdown
      if (
        textareaRef.current &&
        !textareaRef.current.contains(e.target as Node) &&
        dropdownRef.current &&
        !dropdownRef.current.contains(e.target as Node)
      ) {
        setShowResourceMention(false);
        setShowAgentMention(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showResourceMention, showAgentMention]);

  // Add effect to update mention position on window resize and cursor movement
  useEffect(() => {
    if (!showResourceMention && !showAgentMention) return;

    const updateMentionPosition = () => {
      if (textareaRef.current) {
        const cursorPos = textareaRef.current.selectionStart;

        if (showResourceMention) {
          const position = calculateMentionDropdownPosition(
            textareaRef.current,
            cursorPos,
            'resource',
          );
          setResourceMentionPosition(position);
        }

        if (showAgentMention) {
          const position = calculateMentionDropdownPosition(
            textareaRef.current,
            cursorPos,
            'agent',
          );
          setAgentMentionPosition(position);
        }
      }
    };

    const handleResize = updateMentionPosition;
    const handleScroll = updateMentionPosition;

    window.addEventListener('resize', handleResize);
    window.addEventListener('scroll', handleScroll);

    // Also update position when textarea is scrolled
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.addEventListener('scroll', handleScroll);
    }

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('scroll', handleScroll);
      if (textarea) {
        textarea.removeEventListener('scroll', handleScroll);
      }
    };
  }, [showResourceMention, showAgentMention]);

  const { highlightedText, hasMentions } = highlightMentions(inputValue);

  // Generic mention handler to reduce code duplication
  const handleMentionSelect = (
    rawValue: string,
    mentionType: 'resource' | 'agent',
    prefix: string,
    setShowMention: (show: boolean) => void,
    valueTransformer?: (value: string) => string,
  ) => {
    if (!textareaRef.current) return;

    const cursorPos = textareaRef.current.selectionStart;
    const textBeforeCursor = inputValue.substring(0, cursorPos);
    const textAfterCursor = inputValue.substring(cursorPos);

    const mentionInfo = getIncompleteAtCursor(inputValue, cursorPos);

    if (mentionInfo && mentionInfo.type === mentionType) {
      // Apply value transformation if provided
      const processedValue = valueTransformer
        ? valueTransformer(rawValue)
        : rawValue;

      // Construct new text with the replacement
      const mentionText = `${prefix}${processedValue}`;
      const newText =
        textBeforeCursor.substring(0, mentionInfo.startIndex) +
        mentionText +
        ' ' +
        textAfterCursor;

      setInputValue(newText);
      setShowMention(false);

      // Use requestAnimationFrame for smoother focus and scroll
      requestAnimationFrame(() => {
        if (textareaRef.current) {
          const newCursorPos =
            mentionInfo.startIndex + `${mentionText} `.length;
          textareaRef.current.focus();
          textareaRef.current.setSelectionRange(newCursorPos, newCursorPos);

          // Smooth scroll to cursor position
          textareaRef.current.scrollIntoView({
            behavior: 'smooth',
            block: 'nearest',
            inline: 'nearest',
          });
        }
      });
    }
  };

  // Add handlers for mention selection
  const handleResourceSelect = (fullPath: string) => {
    handleMentionSelect(
      fullPath,
      'resource',
      '#',
      setShowResourceMention,
      (path) => path.replace(/\//g, '/').toLowerCase(),
    );
  };

  const handleAgentSelect = (agentName: string) => {
    handleMentionSelect(
      agentName,
      'agent',
      '@',
      setShowAgentMention,
      (name) => name.split(' (')[0], // Remove parenthetical information
    );
  };

  // Add new methods for handling mentions
  const checkForMentions = (text: string, cursorPos: number) => {
    const mentionInfo = getIncompleteAtCursor(text, cursorPos);

    if (!mentionInfo) {
      setShowResourceMention(false);
      setShowAgentMention(false);
      return;
    }

    if (mentionInfo.type === 'resource' && textareaRef.current) {
      setResourceMentionFilter(mentionInfo.filter.toLowerCase());
      const position = calculateMentionDropdownPosition(
        textareaRef.current,
        cursorPos,
        'resource',
      );
      setResourceMentionPosition(position);
      setShowResourceMention(true);
      setShowAgentMention(false);
    } else if (mentionInfo.type === 'agent' && textareaRef.current) {
      setAgentMentionFilter(mentionInfo.filter.toLowerCase());
      const position = calculateMentionDropdownPosition(
        textareaRef.current,
        cursorPos,
        'agent',
      );
      setAgentMentionPosition(position);
      setShowAgentMention(true);
      setShowResourceMention(false);
    }
  };

  // Enhanced handleChange with improved autocomplete logic
  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    if (isSubmittingRef.current) return;

    const newValue = e.target.value;
    setInputValue(newValue);

    // Check for mentions
    checkForMentions(newValue, e.target.selectionStart);

    // Enhanced autocomplete handling
    const cursorPos = e.target.selectionStart;
    const textBeforeCursor = newValue.substring(0, cursorPos);
    const words = textBeforeCursor.split(/\s+/);
    const lastWord = words[words.length - 1] || '';

    // Show autocomplete if user typed at least 2 characters
    if (
      lastWord.length >= 2 &&
      !lastWord.startsWith('#') &&
      !lastWord.startsWith('@')
    ) {
      setAutocompleteFilter(lastWord);
    } else {
      setAutocompleteFilter('');
    }
  };

  // Enhanced autocomplete selection handler
  const handleAutocompleteSelect = (
    suggestion: SchemaAppSchemasTaskTemplateTaskTemplateResponse,
  ) => {
    // Don't select loading or empty state items

    if (textareaRef.current) {
      const cursorPos = textareaRef.current.selectionStart;
      const textBeforeCursor = inputValue.substring(0, cursorPos);

      // Find the last word before cursor
      const words = textBeforeCursor.split(/\s+/);
      const lastWord = words[words.length - 1] || '';

      setInputValue(suggestion.context);
      setAutocompleteFilter('');

      // Focus and position cursor with smooth scroll
      requestAnimationFrame(() => {
        if (textareaRef.current) {
          const newCursorPos =
            textBeforeCursor.length -
            lastWord.length +
            suggestion.context.length +
            1;
          textareaRef.current.focus();
          textareaRef.current.setSelectionRange(newCursorPos, newCursorPos);

          // Smooth scroll to cursor position
          textareaRef.current.scrollIntoView({
            behavior: 'smooth',
            block: 'nearest',
            inline: 'nearest',
          });
        }
      });

      toast.success(`Applied template: ${suggestion.task}`);
    }
  };

  // Handle cursor position changes (click, arrow keys, etc.)
  const handleCursorMove = () => {
    if (textareaRef.current && (showResourceMention || showAgentMention)) {
      const cursorPos = textareaRef.current.selectionStart;

      if (showResourceMention) {
        const position = calculateMentionDropdownPosition(
          textareaRef.current,
          cursorPos,
          'resource',
        );
        setResourceMentionPosition(position);
      }

      if (showAgentMention) {
        const position = calculateMentionDropdownPosition(
          textareaRef.current,
          cursorPos,
          'agent',
        );
        setAgentMentionPosition(position);
      }
    }
  };

  // Add syncScroll for overlay synchronization
  const syncScroll = () => {
    if (overlayRef.current && textareaRef.current) {
      // Use RAF for smooth scrolling without flickering
      overlayRef.current.scrollTop = textareaRef.current.scrollTop;
    }
  };

  // UI Components styled like MessageInput
  const renderGitHubButton = () => {
    if (!showGitHubButton) return null;

    return (
      <Button
        type="button"
        variant="ghost"
        size="icon"
        onClick={onGitHubClick}
        className="text-muted-foreground hover:bg-muted/70 hover:text-foreground dark:hover:bg-muted/30 h-8 w-8 rounded-full transition-all duration-200 active:scale-95"
      >
        <Icons.gitHub className="h-4 w-4" />
      </Button>
    );
  };

  const renderMessagesRemaining = () => {
    if (!messagesRemaining) return null;

    return (
      <div
        className={cn(
          'rounded-full px-2.5 py-0.5 text-xs font-medium text-white shadow-xs',
          'bg-blue-400',
        )}
      >
        {messagesRemaining} messages remaining
      </div>
    );
  };

  return (
    <>
      <div className="space-y-2 rounded-lg transition-all">
        <div
          className={cn(
            'overflow-hidden rounded-lg border-2 shadow-none', // Layout, Border, and Shape
            'border-primary/10 hover:border-primary/40', // Border Color (normal and hover)
            'bg-muted/10', // Background
            'transition-all', // Transition Effects
          )}
        >
          {/* Resource Mention Dropdown */}
          <ResourceMentionDropdown
            isVisible={showResourceMention}
            position={resourceMentionPosition}
            filter={resourceMentionFilter}
            categories={dynamicResourceCategories}
            onSelect={handleResourceSelect}
            onClose={() => setShowResourceMention(false)}
            dropdownRef={dropdownRef}
          />

          {/* Agent Mention Dropdown */}
          <AgentMentionDropdown
            isVisible={showAgentMention}
            position={agentMentionPosition}
            filter={agentMentionFilter}
            onSelect={handleAgentSelect}
            onClose={() => setShowAgentMention(false)}
          />

          {/* Input Area */}
          <div ref={containerRef} className="relative">
            <Textarea
              ref={textareaRef}
              value={inputValue}
              onChange={handleChange}
              onKeyDown={handleKeyDown}
              onPaste={() => {
                // Smooth scroll after paste operation
                requestAnimationFrame(() => {
                  if (overlayRef.current && textareaRef.current) {
                    // Sync overlay scroll with textarea
                    overlayRef.current.scrollTop =
                      textareaRef.current.scrollTop;

                    // Smooth scroll to cursor position
                    textareaRef.current.scrollIntoView({
                      behavior: 'smooth',
                      block: 'nearest',
                      inline: 'nearest',
                    });
                  }
                });
              }}
              onScroll={syncScroll}
              onClick={handleCursorMove}
              onKeyUp={handleCursorMove}
              onSelect={handleCursorMove}
              placeholder=""
              disabled={isSending}
              className={cn(
                'w-full',
                'max-h-[150px] min-h-[150px]',
                'custom-scrollbar resize-none overflow-y-auto',
                'px-4 py-4',
                'border-0 hover:shadow-none focus-visible:ring-0',
                'bg-transparent',
                'text-base',
                'transition-[height] duration-100 ease-out', // Add smooth height transition
                hasMentions
                  ? 'caret-foreground selection:bg-primary/20 text-transparent'
                  : 'caret-foreground',
                'placeholder:opacity-0',
                'border-none! shadow-none!',
              )}
            />

            {/* Highlighted Text Overlay */}
            <div
              ref={overlayRef}
              className={cn(
                'custom-scrollbar pointer-events-none absolute inset-0 h-full w-full resize-none overflow-y-auto px-4 py-4 text-base break-words whitespace-pre-wrap transition-[height] duration-100 ease-out',
                { invisible: !hasMentions },
              )}
            >
              {highlightedText}
            </div>

            {/* Placeholder */}
            {!inputValue && <ChatInputPlaceholder className="px-5 pt-4" />}
          </div>

          {/* Bottom Toolbar */}
          <div className="flex items-center justify-between gap-2 px-4 py-1">
            <div className="flex items-center gap-2">
              {renderGitHubButton()}
            </div>

            {/* Messages Remaining */}
            {renderMessagesRemaining()}
            <div className="flex items-center gap-2">
              <ChatHelpDialog />

              {/* Generate Button */}
              <OperationHubDialog
                taskTemplate={{
                  task: inputValue.split('\n')[0] || 'New Task',
                  context: inputValue.trim(),
                  schedule: '',
                }}
              >
                <WithTooltip tooltip="Create a standard task definition format based on your requirements">
                  <Button
                    size="icon"
                    variant="outline"
                    disabled={!inputValue.trim() || isSending}
                    className="h-8 w-8 rounded-full transition-all duration-200"
                  >
                    <CalendarIcon className="size-4" />
                    <span className="sr-only">Generate</span>
                  </Button>
                </WithTooltip>
              </OperationHubDialog>
              <SendButton
                value={inputValue}
                isGenerating={false}
                isSending={isSending}
                onSend={handleDirectMessage}
              />
            </div>
          </div>
        </div>

        <div className="flex flex-wrap gap-2">
          {QUICK_PROMPT_LIST.map((prompt) => (
            <WithTooltip
              key={prompt.title}
              tooltip={
                <div className="space-y-1">
                  <p className="text-sm font-medium">{prompt.title}</p>
                  <p className="text-muted-foreground text-xs">
                    {prompt.description}
                  </p>
                  <p className="text-muted-foreground text-xs">
                    &quot;{highlightMentions(prompt.question).highlightedText}
                    &quot;
                  </p>
                </div>
              }
              contentClassName="max-w-[300px] bg-card text-card-foreground"
            >
              <button
                onClick={() => {
                  setInputValue(prompt.question);
                }}
              >
                <Badge
                  className="hover:border-primary hover:text-primary gap-2 px-3 py-2"
                  variant={
                    inputValue.includes(prompt.question) ? 'default' : 'outline'
                  }
                >
                  <prompt.icon className="size-4" />
                  <p>{prompt.title}</p>
                </Badge>
              </button>
            </WithTooltip>
          ))}
        </div>
      </div>
      <div className="bg-primary/10 pointer-events-none absolute inset-0 -z-10 rounded-lg blur-lg" />

      <If
        condition={autocompleteFilter}
        fallback={
          <ExamplePrompts
            setInputValue={flow(setInputValue, () => {
              // Use requestAnimationFrame for smoother focus and scroll
              requestAnimationFrame(() => {
                if (textareaRef.current) {
                  // textareaRef.current.focus();

                  // Smooth scroll to cursor position
                  textareaRef.current.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center',
                    inline: 'nearest',
                  });
                }
              });
            })}
          />
        }
      >
        <AutocompleteDropdown
          onSelect={handleAutocompleteSelect}
          searchQuery={debouncedAutocompleteFilter}
          onClose={() => setAutocompleteFilter('')}
        />
      </If>
    </>
  );
};
