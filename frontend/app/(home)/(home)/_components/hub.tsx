'use client';

import { useCallback, useEffect, useMemo, useState } from 'react';

import { Icons } from '@/components/icons';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { If } from '@/components/ui/common/if';
import { PageSkeleton } from '@/components/ui/common/page';
import { Heading } from '@/components/ui/heading';
import { Input } from '@/components/ui/input';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { taskTemplateQuery } from '@/features/task-template/hooks/task-template.query';
import { OperationHubCard } from '@/features/task/components/operation-hub-card';
import { taskCategoryConfig } from '@/features/task/config/task-category.config';
import { taskServiceConfig } from '@/features/task/config/task-service.config';
import { cn } from '@/lib/utils';
import { TaskCategoryEnum, TaskServiceEnum } from '@/openapi-ts/gens';
import { debounce } from 'lodash';
import { RocketIcon } from 'lucide-react';
import { useInView } from 'react-intersection-observer';

export function Hub() {
  const [selectedCategory, setSelectedCategory] =
    useState<TaskCategoryEnum | null>(null);
  const [selectedServices, setSelectedServices] = useState<TaskServiceEnum[]>(
    [],
  );
  const [searchQuery, setSearchQuery] = useState('');
  const [includeDefaults, setIncludeDefaults] = useState(true);

  const { ref, inView } = useInView({
    threshold: 0.1,
  });

  const { data, fetchNextPage, hasNextPage, isFetchingNextPage, isLoading } =
    taskTemplateQuery.query.useInfiniteList({
      search_query: searchQuery,
      category: selectedCategory ? [selectedCategory] : undefined,
      services: selectedServices.length > 0 ? selectedServices : undefined,
      include_defaults: includeDefaults,
    });

  // Load more when the load more element comes into view
  useEffect(() => {
    if (inView && hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [inView, hasNextPage, isFetchingNextPage, fetchNextPage]);

  // Debounced search handler
  const handleSearch = useMemo(
    () =>
      debounce((value: string) => {
        setSearchQuery(value);
      }, 100),
    [],
  );

  const onSearchChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      handleSearch(e.target.value);
    },
    [handleSearch],
  );

  const toggleCategory = (categoryValue: TaskCategoryEnum) => {
    if (categoryValue === selectedCategory) {
      setSelectedCategory(null);
    } else {
      setSelectedCategory(categoryValue);
    }
  };

  const allTemplates = data?.pages.flatMap((page) => page.data);

  // Count active advanced filters
  const advancedFilterCount = useMemo(() => {
    let count = 0;
    if (selectedServices.length > 0) count += selectedServices.length;
    if (includeDefaults !== true) count += 1;
    return count;
  }, [selectedServices, includeDefaults]);

  // Reset advanced filters
  const resetAdvancedFilters = () => {
    setSelectedServices([]);
    setIncludeDefaults(true);
  };

  return (
    <div className="space-y-4">
      {/* Title section - not sticky */}
      <div className="flex items-center justify-between gap-2 max-sm:flex-col-reverse">
        <div className="flex items-center gap-2">
          <Heading level={2}>Operations Hub</Heading>
          <If condition={data?.pages[0].total}>
            {(total) => <Badge>{total}</Badge>}
          </If>
        </div>

        <Badge variant="outline-primary" className="w-fit gap-2">
          <RocketIcon className="size-4" />
          <span>Ready to deploy</span>
        </Badge>
      </div>

      <div className="bg-background/50 sticky top-0 z-10 flex flex-col items-center backdrop-blur-xs">
        <div className="flex w-full gap-2">
          <div className="relative grow">
            <Icons.search className="text-muted-foreground absolute top-1/2 left-3 h-5 w-5 -translate-y-1/2 transform" />
            <Input
              type="text"
              placeholder="Search operations..."
              className="h-12 w-full rounded-lg pl-10"
              onChange={onSearchChange}
              aria-label="Search operations"
            />
          </div>

          {/* Advanced Filters Popover */}
          <Popover modal>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="group relative flex h-12 items-center gap-2"
                aria-label="Advanced Filters"
              >
                <Icons.settings className="group-hover:text-primary h-5 w-5 transition-colors" />
                <span className="text-primar text-foreground font-medium">
                  Advanced Filters
                </span>
                {advancedFilterCount > 0 && (
                  <span className="bg-primary/10 text-primary absolute -top-1 -right-1 flex h-6 min-w-6 items-center justify-center rounded-full px-1 py-0 text-[10px]">
                    {advancedFilterCount}
                  </span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent
              className="border-border/50 overflow-thin-auto z-100 max-h-80 rounded-lg p-0 shadow-md"
              align="end"
              sideOffset={8}
            >
              <div className="flex flex-col">
                <div className="space-y-4 p-4">
                  {/* Default Templates Checkbox */}
                  <div className="flex items-center space-x-2 px-2 py-1">
                    <Checkbox
                      id="show-only-default"
                      checked={includeDefaults}
                      onCheckedChange={(checked) =>
                        setIncludeDefaults(!!checked)
                      }
                    />
                    <label
                      htmlFor="show-only-default"
                      className="text-foreground cursor-pointer text-xs"
                    >
                      Default templates
                    </label>
                  </div>
                  {/* Service Filter */}
                  <div>
                    <div className="text-muted-foreground mb-2 text-xs font-medium">
                      Services
                    </div>
                    <div className="flex flex-col gap-1 pr-1">
                      {taskServiceConfig.LIST.map((service) => (
                        <label
                          key={service.value}
                          className="focus-within:ring-primary/30 hover:bg-accent flex cursor-pointer items-center gap-2 rounded px-2 py-1 transition-colors focus-within:ring-2"
                        >
                          <Checkbox
                            checked={selectedServices.includes(service.value)}
                            onCheckedChange={(checked) => {
                              setSelectedServices((prev) =>
                                checked
                                  ? [...prev, service.value]
                                  : prev.filter((s) => s !== service.value),
                              );
                            }}
                            id={`service-${service.value}`}
                          />
                          <span className="text-foreground flex items-center gap-1 text-xs">
                            <service.icon className="h-5 w-5" />
                            {service.label}
                          </span>
                        </label>
                      ))}
                    </div>
                  </div>
                </div>
                <div className="border-border/50 border-t" />
                <div className="p-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-muted-foreground hover:text-primary w-full"
                    onClick={resetAdvancedFilters}
                  >
                    Reset Filters
                  </Button>
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </div>

        {/* Category badges */}
        <div className="mt-2 flex w-full flex-wrap gap-2">
          {taskCategoryConfig.LIST.map((category) => {
            const isSelected = selectedCategory === category.value;

            return (
              <Badge
                size="lg"
                key={category.value}
                variant={isSelected ? 'default' : 'outline'}
                className={cn(
                  'gap-1.5',
                  !isSelected && 'hover:bg-primary/10 hover:text-primary',
                )}
                onClick={() => toggleCategory(category.value)}
              >
                <category.icon />
                {category.label}
              </Badge>
            );
          })}
        </div>
      </div>

      {/* Main content */}
      <If
        condition={allTemplates}
        fallback={
          <If condition={isLoading}>
            <PageSkeleton />
          </If>
        }
      >
        {(allTemplates) => (
          <If
            condition={allTemplates.length}
            fallback={
              <div className="h-full rounded-lg text-center">
                <Icons.search className="text-muted-foreground/50 mx-auto mb-4 h-12 w-12 py-3" />
                <h3 className="mb-1 text-xl font-medium">
                  No matching operations found
                </h3>
                <p className="text-muted-foreground">
                  Try adjusting your filter criteria
                </p>
              </div>
            }
          >
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
              {allTemplates.map((taskTemplate) => (
                <OperationHubCard
                  key={taskTemplate.id}
                  taskTemplate={taskTemplate}
                />
              ))}
            </div>

            {/* Infinite scroll trigger */}
            <div
              ref={ref}
              className="mt-8 flex h-16 items-center justify-center"
            >
              {isFetchingNextPage && (
                <Icons.spinner className="text-muted-foreground h-6 w-6 animate-spin" />
              )}
            </div>
          </If>
        )}
      </If>
    </div>
  );
}
