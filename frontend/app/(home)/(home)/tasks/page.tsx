import { use } from 'react';

import { NewPageContainer } from '@/components/layout/new-page-container';
import { PageHeader } from '@/components/layout/page-header';
import { TaskCardList } from '@/features/task/components/task-card-list';
import { TaskFilters } from '@/features/task/components/task-filters';
import { TaskQueryParams } from '@/features/task/models/task.type';
import { withPaginationDefaults } from '@/utils/with-pagination-defaults';

export const metadata = {
  title: 'Tasks',
};

type PageProps = {
  searchParams: Promise<TaskQueryParams>;
};

export default function Page(props: PageProps) {
  const searchParams = withPaginationDefaults(use(props.searchParams));

  return (
    <NewPageContainer>
      <PageHeader title="Tasks" description="Manage Tasks" />
      <TaskFilters defaultValues={searchParams} />
      <div className="overflow-thin-auto mt-4 grow">
        <TaskCardList searchParams={searchParams} />
      </div>
    </NewPageContainer>
  );
}
