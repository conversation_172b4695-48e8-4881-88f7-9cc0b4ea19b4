// Function to calculate cursor-based dropdown position for mentions
export function calculateMentionDropdownPosition(
  textareaRef: HTMLTextAreaElement,
  cursorPosition: number,
  dropdownType: 'agent' | 'resource' = 'agent',
): { top: number; left: number } {
  const rect = textareaRef.getBoundingClientRect();
  const scrollY = window.scrollY;
  const scrollX = window.scrollX;
  const computedStyle = window.getComputedStyle(textareaRef);

  // Get padding values
  const paddingLeft = parseInt(computedStyle.paddingLeft) || 0;
  const paddingTop = parseInt(computedStyle.paddingTop) || 0;

  // Get the text before cursor to calculate the position
  const textBeforeCursor = textareaRef.value.substring(0, cursorPosition);
  const lines = textBeforeCursor.split('\n');
  const currentLine = lines[lines.length - 1];

  // Create a temporary span to measure text width
  const span = document.createElement('span');
  span.style.visibility = 'hidden';
  span.style.position = 'absolute';
  span.style.whiteSpace = 'pre';
  span.style.font = computedStyle.font;
  span.style.fontSize = computedStyle.fontSize;
  span.style.fontFamily = computedStyle.fontFamily;
  span.style.fontWeight = computedStyle.fontWeight;
  span.style.letterSpacing = computedStyle.letterSpacing;
  span.textContent = currentLine;
  document.body.appendChild(span);

  // Calculate the position based on text width and line height
  const lineHeight =
    parseInt(computedStyle.lineHeight) ||
    parseInt(computedStyle.fontSize) * 1.5;
  const textWidth = span.offsetWidth;
  const lineNumber = lines.length - 1;

  // Clean up
  document.body.removeChild(span);

  // Account for textarea scroll position
  const scrollTop = textareaRef.scrollTop;

  // Calculate the position relative to the textarea with padding and scroll offset
  const top =
    scrollY +
    rect.top +
    paddingTop +
    lineNumber * lineHeight -
    scrollTop +
    lineHeight +
    8;
  // If cursor is at the beginning of line, position dropdown at text start, otherwise at cursor position
  const left =
    scrollX + rect.left + paddingLeft + (textWidth > 0 ? textWidth + 8 : 0);

  // Dropdown dimensions based on type
  const dropdownWidth = dropdownType === 'resource' ? 750 : 500;
  const dropdownHeight = 320;

  // Adjust horizontal position if it would go off-screen
  const maxLeft = window.innerWidth - dropdownWidth - 20;
  const adjustedLeft = Math.min(Math.max(left, scrollX + 20), maxLeft);

  // Adjust vertical position if it would go off-screen
  const maxTop = window.innerHeight + scrollY - dropdownHeight - 20;
  const minTop = scrollY + 20;
  const adjustedTop = Math.min(Math.max(top, minTop), maxTop);

  return {
    top: adjustedTop,
    left: adjustedLeft,
  };
}
