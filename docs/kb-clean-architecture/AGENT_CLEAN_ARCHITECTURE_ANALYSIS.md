# CloudThinker Agent System - Clean Architecture Analysis & Recommendations

## Executive Summary

This document provides a comprehensive analysis of the CloudThinker agent system architecture and offers detailed recommendations for implementing Clean Architecture principles. The current system shows good separation at the service level but has significant opportunities for improvement in terms of SOLID principles, testability, and maintainability.

## Current Architecture Overview

### System Components

The CloudThinker agent system consists of several key components:

#### 1. **Agent Services Layer** (`backend/app/services/agent/`)
- `AgentService`: Main orchestration service for agent execution
- `AgentBaseService`: Base class with shared functionality and dependencies
- `MessageHandler`: Handles message creation and management
- `ComponentHandler`: Manages UI components and interactions
- `StreamHandler`: Handles real-time streaming responses
- `TokenUsageHandler`: Tracks and manages token consumption

#### 2. **Multi-Agent Core** (`backend/app/modules/multi_agents/`)
- `AgentFactory`: Singleton factory for managing agent instances
- `BaseAgent`: Abstract base class for all agents
- `CoordinatorAgent`: Main orchestration agent
- `ConversationalAgent`: Handles conversational interactions
- `GlobalState`: Manages shared state across agents

#### 3. **Tool Management System**
- `ToolManager`: Central tool registry and management
- Built-in tools (KB search, planning, reporting, etc.)
- MCP (Model Context Protocol) integration
- Cloud provider tool integration

#### 4. **Knowledge Base Integration**
- `SearchService`: Vector search and result synthesis
- `IngestionService`: Document processing and indexing
- `DeletionService`: Document removal operations
- `BaseVectorStore`: Vector database abstraction

#### 5. **Executor Service** (`executor/`)
- Sandboxed execution environment
- Network isolation and security
- Code execution capabilities

### Current Architecture Strengths

✅ **Good Service Separation**: Clear boundaries between different concerns  
✅ **Factory Pattern**: Centralized agent creation and management  
✅ **Tool Abstraction**: Extensible tool system with clear interfaces  
✅ **Async Support**: Comprehensive async/await usage throughout  
✅ **Configuration Management**: Centralized configuration system  
✅ **State Management**: Structured state handling with LangGraph  

## SOLID Principles Analysis

### 1. Single Responsibility Principle (SRP) - ⚠️ VIOLATIONS FOUND

**Major Issues:**

#### `AgentBaseService` - Multiple Responsibilities
```python
class AgentBaseService:
    def __init__(self, session: SessionDep, async_session: SessionAsyncDep):
        # Database operations
        self.session = session
        self._async_session = async_session
        
        # Service dependencies
        self.agent_builtin_tool_service = AgentBuiltinToolService(self._async_session)
        self.attachment_service = AttachmentService(self._async_session)
        self.connection_service = ConnectionService(self._async_session)
        
        # Multiple handlers
        self.message_handler = MessageHandler(self.session)
        self.component_handler = ComponentHandler(self.session)
        self.token_usage_handler = TokenUsageHandler(self.session, self._async_session)
        self.stream_handler = StreamHandler(self.message_handler)
        
        # Multiple repositories
        self.conversation_repository = ConversationRepository(...)
        self.message_repository = MessageRepository(...)
        # ... 6 more repositories
```

**Problems:**
- Handles database operations, service orchestration, and business logic
- Manages 10+ different repositories and services
- Mixes infrastructure concerns with domain logic
- Difficult to test individual components

#### `ToolManager` - Tool Registry + Execution Logic
```python
class ToolManager:
    builtin_tools: dict[str, BaseTool] = AVAILABLE_TOOLS
    tool_policies: dict[str, str] = {}
    tool_tags: dict[str, str] = {}
    
    def __init__(self, mcp_servers, connections_tools):
        self._extract_tool_metadata()  # Metadata extraction
        self.mcp_client = MCPClientConnector(mcp_servers)  # Client management
        self.connections_tools = connections_tools  # Tool storage
```

**Problems:**
- Tool registration, metadata extraction, and client management in one class
- Static class variables mixed with instance state
- Hard to extend with new tool types

### 2. Open/Closed Principle (OCP) - ⚠️ VIOLATIONS FOUND

**Issues:**

#### Hard-coded Agent Types
```python
# In AgentFactory._register_all_graphs()
from app.modules.multi_agents.agents import CoordinatorAgent
cls.register_agent("coordinator_agent", CoordinatorAgent)
```

**Problems:**
- Adding new agent types requires code changes
- No plugin architecture for agents
- Tight coupling to specific agent implementations

#### Fixed Tool Registration
```python
AVAILABLE_TOOLS: dict[str, BaseTool] = {
    "push_alert": push_alert,
    "create_chart": create_chart,
    "recommendation": RecommendationTool,
    # ... hard-coded tool list
}
```

**Problems:**
- New tools require code modifications
- No dynamic tool discovery
- Cannot disable tools without code changes

### 3. Liskov Substitution Principle (LSP) - ✅ MOSTLY COMPLIANT

**Good Examples:**
```python
class BaseAgent(ABC):
    @abstractmethod
    def build_graph(self) -> None: ...
    
    @abstractmethod
    def get_graph(self) -> StateGraph: ...
    
    @abstractmethod
    def compile(self, **kwargs): ...
```

The agent hierarchy properly follows LSP with clear contracts.

### 4. Interface Segregation Principle (ISP) - ⚠️ VIOLATIONS FOUND

**Issues:**

#### Large Service Interfaces
```python
class AgentBaseService:
    # 50+ methods mixing different concerns
    async def _prepare_agent_config(self, conversation)
    async def update_agents_config(self, agents_config, resource_context, agents)
    async def update_connections_config(self, agents_config, agent_id_to_name, agents)
    async def update_builtin_tools_config(self, agents_config, agent_id_to_name)
    async def get_kb_config(self, workspace_id, user_id, message_content)
    # ... many more methods
```

**Problems:**
- Clients depend on methods they don't use
- No role-based interfaces
- Difficult to mock for testing

### 5. Dependency Inversion Principle (DIP) - ❌ MAJOR VIOLATIONS

**Critical Issues:**

#### Direct Database Dependencies
```python
class AgentBaseService:
    def __init__(self, session: SessionDep, async_session: SessionAsyncDep):
        # Direct dependency on SQLAlchemy sessions
        self.session = session
        self._async_session = async_session
        
        # Direct instantiation of repositories
        self.conversation_repository = ConversationRepository(
            async_session=self._async_session, session=self.session
        )
```

#### Hard-coded Service Dependencies
```python
# In search_knowledge_base tool
async for session in get_async_session():
    kb_services = create_kb_services(session=session)  # Factory function
    kb_service_manager = kb_services["manager"]  # Dictionary access
```

**Problems:**
- High-level modules depend on low-level modules
- No dependency injection container
- Impossible to swap implementations
- Extremely difficult to unit test

## Current Architecture Diagram

```mermaid
graph TB
    subgraph "API Layer"
        REST[REST Endpoints]
        WebSocket[WebSocket Handlers]
    end

    subgraph "Service Layer"
        AS[AgentService]
        ABS[AgentBaseService]
        MH[MessageHandler]
        SH[StreamHandler]
        TUH[TokenUsageHandler]
    end

    subgraph "Multi-Agent System"
        AF[AgentFactory]
        CA[CoordinatorAgent]
        ConvA[ConversationalAgent]
        GS[GlobalState]
    end

    subgraph "Tool System"
        TM[ToolManager]
        BT[Built-in Tools]
        MCP[MCP Tools]
        CT[Cloud Tools]
    end

    subgraph "Knowledge Base"
        SS[SearchService]
        IS[IngestionService]
        DS[DeletionService]
        BVS[BaseVectorStore]
    end

    subgraph "Infrastructure"
        DB[(PostgreSQL)]
        Vector[(Qdrant)]
        Redis[(Redis)]
        S3[(MinIO)]
    end

    subgraph "External Services"
        Bedrock[AWS Bedrock]
        OpenAI[OpenAI API]
        Executor[Executor Service]
    end

    REST --> AS
    WebSocket --> AS
    AS --> ABS
    ABS --> MH
    ABS --> SH
    ABS --> TUH
    
    AS --> AF
    AF --> CA
    AF --> ConvA
    CA --> GS
    ConvA --> GS
    
    ABS --> TM
    TM --> BT
    TM --> MCP
    TM --> CT
    
    BT --> SS
    SS --> IS
    SS --> DS
    SS --> BVS
    
    BVS --> Vector
    ABS --> DB
    ABS --> Redis
    IS --> S3
    
    SS --> Bedrock
    SS --> OpenAI
    CA --> Executor

    classDef api fill:#e3f2fd
    classDef service fill:#f3e5f5
    classDef agent fill:#e8f5e8
    classDef tool fill:#fff3e0
    classDef kb fill:#fce4ec
    classDef infra fill:#f1f8e9
    classDef external fill:#ffebee

    class REST,WebSocket api
    class AS,ABS,MH,SH,TUH service
    class AF,CA,ConvA,GS agent
    class TM,BT,MCP,CT tool
    class SS,IS,DS,BVS kb
    class DB,Vector,Redis,S3 infra
    class Bedrock,OpenAI,Executor external
```

## Key Architectural Problems

### 1. **Tight Coupling**
- Services directly instantiate their dependencies
- Hard-coded references to specific implementations
- No abstraction layers between components

### 2. **Poor Testability**
- Dependencies cannot be easily mocked
- Integration tests required for unit-level testing
- Complex setup required for isolated testing

### 3. **Limited Extensibility**
- New agents require code changes in factory
- Tool registration is static and hard-coded
- No plugin architecture for extensions

### 4. **Mixed Concerns**
- Business logic mixed with infrastructure code
- Database operations scattered throughout services
- Configuration management embedded in business logic

### 5. **State Management Issues**
- Global state shared across components
- No clear ownership of state mutations
- Difficult to reason about state changes

## Clean Architecture Recommendations

### Proposed Architecture Overview

The recommended clean architecture follows the dependency inversion principle with clear separation of concerns:

```mermaid
graph TB
    subgraph "External Interfaces Layer"
        REST[REST API]
        WS[WebSocket API]
        CLI[CLI Interface]
        Scheduler[Task Scheduler]
    end

    subgraph "Application Layer"
        subgraph "Use Cases"
            ExecuteAgent[ExecuteAgentUseCase]
            CreateAgent[CreateAgentUseCase]
            SearchKB[SearchKBUseCase]
            ManageTools[ManageToolsUseCase]
        end

        subgraph "DTOs"
            AgentDTO[AgentExecutionRequest/Response]
            ToolDTO[ToolRequest/Response]
            KBDTO[KBSearchRequest/Response]
        end
    end

    subgraph "Domain Layer"
        subgraph "Entities"
            Agent[Agent Entity]
            Tool[Tool Entity]
            Conversation[Conversation Entity]
            Message[Message Entity]
        end

        subgraph "Domain Services"
            AgentOrchestrator[AgentOrchestrator]
            ToolRegistry[ToolRegistry]
            ConversationManager[ConversationManager]
        end

        subgraph "Repository Ports"
            AgentRepo[AgentRepositoryInterface]
            ToolRepo[ToolRepositoryInterface]
            ConvRepo[ConversationRepositoryInterface]
            VectorStore[VectorStoreInterface]
        end
    end

    subgraph "Infrastructure Layer"
        subgraph "Repository Adapters"
            SQLAgentRepo[SQLAlchemyAgentRepository]
            QdrantStore[QdrantVectorStore]
            RedisCache[RedisConversationCache]
        end

        subgraph "External Services"
            BedrockLLM[BedrockLLMService]
            OpenAILLM[OpenAILLMService]
            ExecutorService[ExecutorService]
            MCPConnector[MCPConnector]
        end
    end

    %% Dependencies flow inward
    REST --> ExecuteAgent
    WS --> ExecuteAgent
    CLI --> CreateAgent
    Scheduler --> ManageTools

    ExecuteAgent --> AgentOrchestrator
    CreateAgent --> Agent
    SearchKB --> ToolRegistry
    ManageTools --> Tool

    AgentOrchestrator --> AgentRepo
    AgentOrchestrator --> VectorStore
    ToolRegistry --> ToolRepo
    ConversationManager --> ConvRepo

    %% Infrastructure implements interfaces
    SQLAgentRepo -.->|implements| AgentRepo
    QdrantStore -.->|implements| VectorStore
    RedisCache -.->|implements| ConvRepo
    BedrockLLM -.->|implements| LLMInterface
    OpenAILLM -.->|implements| LLMInterface

    classDef external fill:#e1f5fe
    classDef application fill:#f3e5f5
    classDef domain fill:#e8f5e8
    classDef infrastructure fill:#fff3e0

    class REST,WS,CLI,Scheduler external
    class ExecuteAgent,CreateAgent,SearchKB,ManageTools,AgentDTO,ToolDTO,KBDTO application
    class Agent,Tool,Conversation,Message,AgentOrchestrator,ToolRegistry,ConversationManager,AgentRepo,ToolRepo,ConvRepo,VectorStore domain
    class SQLAgentRepo,QdrantStore,RedisCache,BedrockLLM,OpenAILLM,ExecutorService,MCPConnector infrastructure
```

### Layer Definitions

#### 1. **Domain Layer** (Core Business Logic)

**Entities** - Core business objects with rich behavior:

```python
# domain/agent/entities/agent.py
@dataclass
class Agent:
    """Rich domain entity representing an AI agent"""
    
    id: AgentId
    name: str
    role: str
    goal: str
    instructions: str
    workspace_id: WorkspaceId
    is_active: bool
    capabilities: List[Capability]
    constraints: List[Constraint]
    created_at: datetime
    updated_at: datetime

    def __post_init__(self):
        self._validate_configuration()
        self._ensure_required_capabilities()

    def can_execute_tool(self, tool: Tool) -> bool:
        """Business rule: Check if agent can execute a specific tool"""
        return any(
            capability.allows_tool(tool) 
            for capability in self.capabilities
        )

    def add_capability(self, capability: Capability) -> 'Agent':
        """Add new capability while maintaining business rules"""
        if capability in self.capabilities:
            return self
            
        # Business rule: Some capabilities are mutually exclusive
        if self._has_conflicting_capability(capability):
            raise ConflictingCapabilityError(
                f"Capability {capability} conflicts with existing capabilities"
            )
            
        new_capabilities = self.capabilities + [capability]
        return self._copy_with_updates(capabilities=new_capabilities)

    def execute_with_context(self, context: ExecutionContext) -> ExecutionResult:
        """Execute agent with given context - core business logic"""
        if not self.is_active:
            raise InactiveAgentError(f"Agent {self.name} is not active")
            
        # Apply constraints
        validated_context = self._apply_constraints(context)
        
        # Execute with validated context
        return ExecutionResult(
            agent_id=self.id,
            context=validated_context,
            timestamp=datetime.utcnow()
        )
```

**Domain Services** - Complex business logic that doesn't belong to a single entity:

```python
# domain/agent/services/agent_orchestrator.py
class AgentOrchestrator:
    """Domain service for complex agent orchestration logic"""
    
    def __init__(self, 
                 agent_repo: AgentRepositoryInterface,
                 tool_registry: ToolRegistryInterface,
                 conversation_manager: ConversationManagerInterface):
        self._agent_repo = agent_repo
        self._tool_registry = tool_registry
        self._conversation_manager = conversation_manager

    async def execute_multi_agent_workflow(
        self, 
        request: MultiAgentRequest
    ) -> MultiAgentResult:
        """Orchestrate execution across multiple agents"""
        
        # Business rule: Validate agent compatibility
        agents = await self._get_and_validate_agents(request.agent_ids)
        
        # Business rule: Check tool permissions across agents
        available_tools = await self._get_available_tools(agents)
        
        # Business rule: Determine execution order
        execution_plan = self._create_execution_plan(agents, request.task)
        
        # Execute plan
        results = []
        for step in execution_plan.steps:
            result = await self._execute_step(step, available_tools)
            results.append(result)
            
            # Business rule: Stop on critical failure
            if result.is_critical_failure():
                break
                
        return MultiAgentResult(
            request_id=request.id,
            results=results,
            execution_plan=execution_plan
        )

    async def _get_and_validate_agents(
        self, 
        agent_ids: List[AgentId]
    ) -> List[Agent]:
        """Get agents and validate they can work together"""
        agents = []
        for agent_id in agent_ids:
            agent = await self._agent_repo.get_by_id(agent_id)
            if not agent:
                raise AgentNotFoundError(f"Agent {agent_id} not found")
            if not agent.is_active:
                raise InactiveAgentError(f"Agent {agent.name} is not active")
            agents.append(agent)
            
        # Business rule: Check for conflicting agents
        self._validate_agent_compatibility(agents)
        
        return agents
```

#### 2. **Application Layer** (Use Cases and Orchestration)

**Use Cases** - Application-specific business rules and workflows:

```python
# application/agent/use_cases/execute_agent_use_case.py
class ExecuteAgentUseCase:
    """Use case for executing an agent with proper authorization and logging"""
    
    def __init__(self, 
                 agent_orchestrator: AgentOrchestrator,
                 access_control: AccessControlService,
                 audit_logger: AuditLogger,
                 metrics: MetricsCollector):
        self._orchestrator = agent_orchestrator
        self._access_control = access_control
        self._audit_logger = audit_logger
        self._metrics = metrics

    async def execute(self, request: ExecuteAgentRequest) -> ExecuteAgentResponse:
        """Execute agent with full application workflow"""
        
        # Application concern: Authorization
        await self._access_control.ensure_can_execute_agent(
            request.user_id, 
            request.agent_id,
            request.workspace_id
        )
        
        # Application concern: Audit logging
        execution_id = await self._audit_logger.log_execution_start(request)
        
        try:
            # Application concern: Metrics collection
            with self._metrics.time_execution():
                # Delegate to domain service
                result = await self._orchestrator.execute_agent(
                    agent_id=request.agent_id,
                    context=request.execution_context
                )
            
            # Application concern: Success logging
            await self._audit_logger.log_execution_success(execution_id, result)
            
            return ExecuteAgentResponse(
                execution_id=execution_id,
                result=result,
                status=ExecutionStatus.SUCCESS
            )
            
        except Exception as e:
            # Application concern: Error handling and logging
            await self._audit_logger.log_execution_failure(execution_id, e)
            
            # Application concern: Error translation
            if isinstance(e, DomainError):
                raise ApplicationError(f"Execution failed: {e}") from e
            else:
                raise SystemError("Unexpected execution failure") from e
```

#### 3. **Infrastructure Layer** (External Concerns)

**Repository Implementations**:

```python
# infrastructure/agent/repositories/sqlalchemy_agent_repository.py
class SQLAlchemyAgentRepository(AgentRepositoryInterface):
    """SQLAlchemy implementation of agent repository"""
    
    def __init__(self, session_factory: SessionFactory):
        self._session_factory = session_factory

    async def get_by_id(self, agent_id: AgentId) -> Optional[Agent]:
        """Get agent by ID with proper error handling"""
        async with self._session_factory() as session:
            try:
                stmt = select(AgentModel).where(AgentModel.id == agent_id.value)
                result = await session.execute(stmt)
                agent_model = result.scalar_one_or_none()
                
                if not agent_model:
                    return None
                    
                return self._to_domain_entity(agent_model)
                
            except SQLAlchemyError as e:
                raise RepositoryError(f"Failed to get agent {agent_id}") from e

    async def save(self, agent: Agent) -> Agent:
        """Save agent with optimistic locking"""
        async with self._session_factory() as session:
            try:
                agent_model = self._to_model(agent)
                
                # Optimistic locking
                if agent_model.version != agent.version:
                    raise ConcurrencyError("Agent was modified by another process")
                
                agent_model.version += 1
                agent_model.updated_at = datetime.utcnow()
                
                session.add(agent_model)
                await session.commit()
                
                return self._to_domain_entity(agent_model)
                
            except SQLAlchemyError as e:
                await session.rollback()
                raise RepositoryError(f"Failed to save agent {agent.id}") from e

    def _to_domain_entity(self, model: AgentModel) -> Agent:
        """Convert database model to domain entity"""
        return Agent(
            id=AgentId(model.id),
            name=model.name,
            role=model.role,
            goal=model.goal,
            instructions=model.instructions,
            workspace_id=WorkspaceId(model.workspace_id),
            is_active=model.is_active,
            capabilities=[
                Capability.from_dict(cap) for cap in model.capabilities_json
            ],
            constraints=[
                Constraint.from_dict(const) for const in model.constraints_json
            ],
            created_at=model.created_at,
            updated_at=model.updated_at
        )
```

**External Service Adapters**:

```python
# infrastructure/llm/bedrock_llm_service.py
class BedrockLLMService(LLMServiceInterface):
    """AWS Bedrock implementation of LLM service"""
    
    def __init__(self, config: BedrockConfig, metrics: MetricsCollector):
        self._client = boto3.client('bedrock-runtime', region_name=config.region)
        self._config = config
        self._metrics = metrics

    async def generate_response(
        self, 
        request: LLMRequest
    ) -> LLMResponse:
        """Generate response using Bedrock with proper error handling"""
        
        try:
            with self._metrics.time_llm_request():
                response = await self._client.invoke_model(
                    modelId=self._config.model_id,
                    body=json.dumps({
                        'prompt': request.prompt,
                        'max_tokens': request.max_tokens,
                        'temperature': request.temperature
                    })
                )
            
            response_body = json.loads(response['body'].read())
            
            return LLMResponse(
                text=response_body['completion'],
                tokens_used=response_body.get('usage', {}).get('total_tokens', 0),
                model=self._config.model_id,
                provider='bedrock'
            )
            
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == 'ThrottlingException':
                raise RateLimitError("Bedrock rate limit exceeded") from e
            elif error_code == 'ValidationException':
                raise InvalidRequestError("Invalid request to Bedrock") from e
            else:
                raise LLMServiceError(f"Bedrock error: {error_code}") from e
        except Exception as e:
            raise LLMServiceError("Unexpected Bedrock error") from e

### Dependency Injection Container

Implement a proper dependency injection container to manage dependencies:

```python
# infrastructure/di/container.py
class DIContainer:
    """Dependency injection container for the agent system"""
    
    def __init__(self):
        self._services: Dict[Type, Any] = {}
        self._factories: Dict[Type, Callable] = {}
        self._singletons: Dict[Type, Any] = {}

    def register_singleton(self, interface: Type[T], implementation: Type[T]) -> None:
        """Register a singleton service"""
        self._factories[interface] = lambda: implementation()

    def register_factory(self, interface: Type[T], factory: Callable[[], T]) -> None:
        """Register a factory function"""
        self._factories[interface] = factory

    def register_instance(self, interface: Type[T], instance: T) -> None:
        """Register a specific instance"""
        self._singletons[interface] = instance

    def get(self, interface: Type[T]) -> T:
        """Get service instance"""
        # Check for registered instance first
        if interface in self._singletons:
            return self._singletons[interface]
            
        # Check for factory
        if interface in self._factories:
            instance = self._factories[interface]()
            # Cache singletons
            if self._is_singleton(interface):
                self._singletons[interface] = instance
            return instance
            
        raise ServiceNotRegisteredError(f"Service {interface} not registered")

    def _is_singleton(self, interface: Type) -> bool:
        """Check if service should be cached as singleton"""
        return hasattr(interface, '__singleton__') and interface.__singleton__


# infrastructure/di/agent_container.py
class AgentContainer:
    """Agent-specific dependency injection setup"""
    
    @staticmethod
    def configure(container: DIContainer, config: AgentConfig) -> None:
        """Configure all agent-related dependencies"""
        
        # Infrastructure layer
        container.register_singleton(
            SessionFactory,
            lambda: create_async_session_factory(config.database_url)
        )
        
        container.register_factory(
            AgentRepositoryInterface,
            lambda: SQLAlchemyAgentRepository(container.get(SessionFactory))
        )
        
        container.register_factory(
            VectorStoreInterface,
            lambda: QdrantVectorStore(config.vector_store_config)
        )
        
        container.register_factory(
            LLMServiceInterface,
            lambda: BedrockLLMService(config.llm_config, container.get(MetricsCollector))
        )
        
        # Domain services
        container.register_factory(
            AgentOrchestrator,
            lambda: AgentOrchestrator(
                container.get(AgentRepositoryInterface),
                container.get(ToolRegistryInterface),
                container.get(ConversationManagerInterface)
            )
        )
        
        container.register_factory(
            ToolRegistry,
            lambda: ToolRegistry(
                container.get(ToolRepositoryInterface),
                container.get(LLMServiceInterface)
            )
        )
        
        # Application services
        container.register_factory(
            ExecuteAgentUseCase,
            lambda: ExecuteAgentUseCase(
                container.get(AgentOrchestrator),
                container.get(AccessControlService),
                container.get(AuditLogger),
                container.get(MetricsCollector)
            )
        )
        
        # Cross-cutting concerns
        container.register_singleton(MetricsCollector, PrometheusMetricsCollector)
        container.register_singleton(AuditLogger, StructuredAuditLogger)
        container.register_singleton(AccessControlService, RBACAccessControlService)


# Usage in FastAPI
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Setup
    container = DIContainer()
    config = load_agent_config()
    AgentContainer.configure(container, config)
    
    app.state.container = container
    yield
    
    # Cleanup
    await container.cleanup()

app = FastAPI(lifespan=lifespan)

@app.post("/agents/execute")
async def execute_agent(
    request: ExecuteAgentRequest,
    container: DIContainer = Depends(get_container)
) -> ExecuteAgentResponse:
    use_case = container.get(ExecuteAgentUseCase)
    return await use_case.execute(request)
```

### Configuration Management

Implement configuration-driven architecture:

```python
# core/config/agent_config.py
class AgentSystemConfig(BaseModel):
    """Complete agent system configuration"""
    
    # Database configuration
    database: DatabaseConfig
    
    # Vector store configuration  
    vector_store: VectorStoreConfig
    
    # LLM provider configuration
    llm_providers: Dict[str, LLMProviderConfig]
    
    # Tool configuration
    tools: ToolSystemConfig
    
    # Security configuration
    security: SecurityConfig
    
    # Monitoring configuration
    monitoring: MonitoringConfig

class LLMProviderConfig(BaseModel):
    """LLM provider configuration"""
    provider: Literal["bedrock", "openai", "anthropic", "local"]
    model_name: str
    api_key: Optional[str] = None
    region: Optional[str] = None
    endpoint_url: Optional[str] = None
    max_tokens: int = 4096
    temperature: float = 0.7
    timeout_seconds: int = 30

class ToolSystemConfig(BaseModel):
    """Tool system configuration"""
    builtin_tools: List[str] = Field(default_factory=list)
    mcp_servers: List[MCPServerConfig] = Field(default_factory=list)
    tool_timeout_seconds: int = 30
    max_concurrent_tools: int = 5

# core/config/provider_factory.py
class ProviderFactory:
    """Factory for creating provider instances based on configuration"""
    
    def create_llm_service(self, config: LLMProviderConfig) -> LLMServiceInterface:
        """Create LLM service based on configuration"""
        match config.provider:
            case "bedrock":
                return BedrockLLMService(
                    BedrockConfig(
                        model_id=config.model_name,
                        region=config.region,
                        max_tokens=config.max_tokens,
                        temperature=config.temperature
                    )
                )
            case "openai":
                return OpenAILLMService(
                    OpenAIConfig(
                        api_key=config.api_key,
                        model=config.model_name,
                        max_tokens=config.max_tokens,
                        temperature=config.temperature
                    )
                )
            case "anthropic":
                return AnthropicLLMService(
                    AnthropicConfig(
                        api_key=config.api_key,
                        model=config.model_name,
                        max_tokens=config.max_tokens
                    )
                )
            case _:
                raise UnsupportedProviderError(f"Provider {config.provider} not supported")

    def create_vector_store(self, config: VectorStoreConfig) -> VectorStoreInterface:
        """Create vector store based on configuration"""
        match config.provider:
            case "qdrant":
                return QdrantVectorStore(config)
            case "pinecone":
                return PineconeVectorStore(config)
            case "weaviate":
                return WeaviateVectorStore(config)
            case _:
                raise UnsupportedProviderError(f"Vector store {config.provider} not supported")
```

### Testing Strategy

Implement comprehensive testing with proper mocking:

```python
# tests/unit/domain/test_agent_entity.py
class TestAgentEntity:
    """Unit tests for Agent domain entity"""
    
    def test_agent_creation_with_valid_data(self):
        """Test agent creation with valid data"""
        agent = Agent(
            id=AgentId("test-agent-1"),
            name="Test Agent",
            role="Assistant",
            goal="Help users with tasks",
            instructions="Be helpful and accurate",
            workspace_id=WorkspaceId("workspace-1"),
            is_active=True,
            capabilities=[Capability.SEARCH, Capability.ANALYSIS],
            constraints=[Constraint.NO_EXTERNAL_CALLS],
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        assert agent.name == "Test Agent"
        assert agent.is_active
        assert len(agent.capabilities) == 2

    def test_agent_can_execute_tool_with_valid_capability(self):
        """Test agent can execute tool when it has required capability"""
        agent = self._create_test_agent([Capability.SEARCH])
        search_tool = Tool(name="search", required_capability=Capability.SEARCH)
        
        assert agent.can_execute_tool(search_tool)

    def test_agent_cannot_execute_tool_without_capability(self):
        """Test agent cannot execute tool without required capability"""
        agent = self._create_test_agent([Capability.ANALYSIS])
        search_tool = Tool(name="search", required_capability=Capability.SEARCH)
        
        assert not agent.can_execute_tool(search_tool)

    def test_add_conflicting_capability_raises_error(self):
        """Test adding conflicting capability raises error"""
        agent = self._create_test_agent([Capability.READ_ONLY])
        
        with pytest.raises(ConflictingCapabilityError):
            agent.add_capability(Capability.WRITE_ACCESS)

# tests/unit/application/test_execute_agent_use_case.py
class TestExecuteAgentUseCase:
    """Unit tests for ExecuteAgentUseCase"""
    
    @pytest.fixture
    def mock_dependencies(self):
        """Create mock dependencies for testing"""
        return {
            'orchestrator': Mock(spec=AgentOrchestrator),
            'access_control': Mock(spec=AccessControlService),
            'audit_logger': Mock(spec=AuditLogger),
            'metrics': Mock(spec=MetricsCollector)
        }

    @pytest.fixture
    def use_case(self, mock_dependencies):
        """Create use case with mocked dependencies"""
        return ExecuteAgentUseCase(**mock_dependencies)

    async def test_execute_with_valid_request(self, use_case, mock_dependencies):
        """Test successful agent execution"""
        # Arrange
        request = ExecuteAgentRequest(
            user_id=UserId("user-1"),
            agent_id=AgentId("agent-1"),
            workspace_id=WorkspaceId("workspace-1"),
            execution_context=ExecutionContext(prompt="Hello")
        )
        
        mock_dependencies['access_control'].ensure_can_execute_agent.return_value = None
        mock_dependencies['audit_logger'].log_execution_start.return_value = "exec-1"
        mock_dependencies['orchestrator'].execute_agent.return_value = ExecutionResult(
            agent_id=request.agent_id,
            response="Hello! How can I help you?",
            status=ExecutionStatus.SUCCESS
        )
        
        # Act
        response = await use_case.execute(request)
        
        # Assert
        assert response.status == ExecutionStatus.SUCCESS
        assert response.result.response == "Hello! How can I help you?"
        mock_dependencies['access_control'].ensure_can_execute_agent.assert_called_once()
        mock_dependencies['audit_logger'].log_execution_start.assert_called_once()
        mock_dependencies['orchestrator'].execute_agent.assert_called_once()

    async def test_execute_with_access_denied(self, use_case, mock_dependencies):
        """Test execution with access denied"""
        # Arrange
        request = ExecuteAgentRequest(
            user_id=UserId("user-1"),
            agent_id=AgentId("agent-1"),
            workspace_id=WorkspaceId("workspace-1"),
            execution_context=ExecutionContext(prompt="Hello")
        )
        
        mock_dependencies['access_control'].ensure_can_execute_agent.side_effect = \
            AccessDeniedError("User cannot execute this agent")
        
        # Act & Assert
        with pytest.raises(AccessDeniedError):
            await use_case.execute(request)
        
        # Verify orchestrator was not called
        mock_dependencies['orchestrator'].execute_agent.assert_not_called()

# tests/integration/test_agent_execution_flow.py
class TestAgentExecutionFlow:
    """Integration tests for complete agent execution flow"""
    
    @pytest.fixture
    async def test_container(self):
        """Create test container with real implementations"""
        container = DIContainer()
        
        # Use in-memory implementations for testing
        container.register_instance(
            AgentRepositoryInterface,
            InMemoryAgentRepository()
        )
        container.register_instance(
            VectorStoreInterface,
            InMemoryVectorStore()
        )
        container.register_instance(
            LLMServiceInterface,
            MockLLMService()
        )
        
        return container

    async def test_complete_agent_execution_flow(self, test_container):
        """Test complete flow from request to response"""
        # Arrange
        use_case = test_container.get(ExecuteAgentUseCase)
        
        # Create test agent
        agent_repo = test_container.get(AgentRepositoryInterface)
        test_agent = Agent(
            id=AgentId("test-agent"),
            name="Test Agent",
            role="Assistant",
            goal="Help with testing",
            instructions="Be helpful",
            workspace_id=WorkspaceId("test-workspace"),
            is_active=True,
            capabilities=[Capability.SEARCH],
            constraints=[],
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        await agent_repo.save(test_agent)
        
        request = ExecuteAgentRequest(
            user_id=UserId("test-user"),
            agent_id=AgentId("test-agent"),
            workspace_id=WorkspaceId("test-workspace"),
            execution_context=ExecutionContext(prompt="Hello, test!")
        )
        
        # Act
        response = await use_case.execute(request)
        
        # Assert
        assert response.status == ExecutionStatus.SUCCESS
        assert response.result is not None
        assert response.execution_id is not None

## Implementation Roadmap

### Phase 1: Foundation (Weeks 1-2)

#### 1.1 Create Core Abstractions
- [ ] Define domain entities (Agent, Tool, Conversation, Message)
- [ ] Create repository interfaces
- [ ] Implement value objects (AgentId, WorkspaceId, etc.)
- [ ] Set up basic dependency injection container

#### 1.2 Infrastructure Layer Setup
- [ ] Implement SQLAlchemy repository adapters
- [ ] Create LLM service abstractions and implementations
- [ ] Set up vector store abstractions
- [ ] Implement configuration management system

### Phase 2: Domain Layer (Weeks 3-4)

#### 2.1 Domain Services
- [ ] Implement AgentOrchestrator
- [ ] Create ToolRegistry with plugin architecture
- [ ] Build ConversationManager
- [ ] Add domain event system

#### 2.2 Business Rules Implementation
- [ ] Agent capability and constraint system
- [ ] Tool permission and validation logic
- [ ] Conversation state management
- [ ] Error handling and domain exceptions

### Phase 3: Application Layer (Weeks 5-6)

#### 3.1 Use Cases
- [ ] ExecuteAgentUseCase with full workflow
- [ ] CreateAgentUseCase with validation
- [ ] ManageToolsUseCase with permissions
- [ ] SearchKBUseCase integration

#### 3.2 Cross-cutting Concerns
- [ ] Authentication and authorization
- [ ] Audit logging system
- [ ] Metrics and monitoring
- [ ] Rate limiting and quotas

### Phase 4: Integration and Testing (Weeks 7-8)

#### 4.1 API Layer Refactoring
- [ ] Update REST endpoints to use use cases
- [ ] Implement WebSocket handlers
- [ ] Add proper error handling and responses
- [ ] Update OpenAPI documentation

#### 4.2 Testing Implementation
- [ ] Unit tests for all domain logic
- [ ] Integration tests for use cases
- [ ] End-to-end API tests
- [ ] Performance and load testing

## Recommended Directory Structure

```
backend/app/
├── domain/
│   ├── agent/
│   │   ├── entities/
│   │   │   ├── __init__.py
│   │   │   ├── agent.py
│   │   │   ├── capability.py
│   │   │   └── constraint.py
│   │   ├── services/
│   │   │   ├── __init__.py
│   │   │   ├── agent_orchestrator.py
│   │   │   ├── tool_registry.py
│   │   │   └── conversation_manager.py
│   │   ├── repositories/
│   │   │   ├── __init__.py
│   │   │   ├── agent_repository.py
│   │   │   ├── tool_repository.py
│   │   │   └── conversation_repository.py
│   │   ├── events/
│   │   │   ├── __init__.py
│   │   │   ├── agent_events.py
│   │   │   └── conversation_events.py
│   │   └── exceptions/
│   │       ├── __init__.py
│   │       ├── agent_exceptions.py
│   │       └── tool_exceptions.py
│   ├── tool/
│   │   ├── entities/
│   │   │   ├── __init__.py
│   │   │   ├── tool.py
│   │   │   └── tool_execution.py
│   │   ├── services/
│   │   │   ├── __init__.py
│   │   │   ├── tool_executor.py
│   │   │   └── tool_validator.py
│   │   └── repositories/
│   │       ├── __init__.py
│   │       └── tool_repository.py
│   └── shared/
│       ├── value_objects/
│       │   ├── __init__.py
│       │   ├── identifiers.py
│       │   └── execution_context.py
│       └── events/
│           ├── __init__.py
│           └── domain_events.py
├── application/
│   ├── agent/
│   │   ├── use_cases/
│   │   │   ├── __init__.py
│   │   │   ├── execute_agent.py
│   │   │   ├── create_agent.py
│   │   │   ├── update_agent.py
│   │   │   └── delete_agent.py
│   │   ├── dto/
│   │   │   ├── __init__.py
│   │   │   ├── agent_dto.py
│   │   │   └── execution_dto.py
│   │   └── services/
│   │       ├── __init__.py
│   │       ├── access_control.py
│   │       ├── audit_logger.py
│   │       └── metrics_collector.py
│   ├── tool/
│   │   ├── use_cases/
│   │   │   ├── __init__.py
│   │   │   ├── register_tool.py
│   │   │   ├── execute_tool.py
│   │   │   └── manage_permissions.py
│   │   └── dto/
│   │       ├── __init__.py
│   │       └── tool_dto.py
│   └── shared/
│       ├── interfaces/
│       │   ├── __init__.py
│       │   ├── use_case.py
│       │   └── event_handler.py
│       └── exceptions/
│           ├── __init__.py
│           └── application_exceptions.py
├── infrastructure/
│   ├── agent/
│   │   ├── repositories/
│   │   │   ├── __init__.py
│   │   │   ├── sqlalchemy_agent_repository.py
│   │   │   └── redis_conversation_cache.py
│   │   └── services/
│   │       ├── __init__.py
│   │       ├── bedrock_llm_service.py
│   │       ├── openai_llm_service.py
│   │       └── anthropic_llm_service.py
│   ├── tool/
│   │   ├── repositories/
│   │   │   ├── __init__.py
│   │   │   └── sqlalchemy_tool_repository.py
│   │   ├── executors/
│   │   │   ├── __init__.py
│   │   │   ├── mcp_tool_executor.py
│   │   │   ├── builtin_tool_executor.py
│   │   │   └── cloud_tool_executor.py
│   │   └── connectors/
│   │       ├── __init__.py
│   │       ├── mcp_connector.py
│   │       └── executor_connector.py
│   ├── vector_store/
│   │   ├── __init__.py
│   │   ├── qdrant_store.py
│   │   ├── pinecone_store.py
│   │   └── weaviate_store.py
│   ├── config/
│   │   ├── __init__.py
│   │   ├── agent_config.py
│   │   ├── provider_factory.py
│   │   └── environment.py
│   ├── di/
│   │   ├── __init__.py
│   │   ├── container.py
│   │   └── agent_container.py
│   └── monitoring/
│       ├── __init__.py
│       ├── prometheus_metrics.py
│       ├── structured_logger.py
│       └── health_checks.py
├── api/
│   ├── v1/
│   │   ├── __init__.py
│   │   ├── agents.py
│   │   ├── tools.py
│   │   ├── conversations.py
│   │   └── health.py
│   ├── dependencies/
│   │   ├── __init__.py
│   │   ├── container.py
│   │   ├── auth.py
│   │   └── validation.py
│   └── middleware/
│       ├── __init__.py
│       ├── error_handler.py
│       ├── request_logger.py
│       └── rate_limiter.py
└── tests/
    ├── unit/
    │   ├── domain/
    │   │   ├── agent/
    │   │   └── tool/
    │   ├── application/
    │   │   ├── agent/
    │   │   └── tool/
    │   └── infrastructure/
    │       ├── repositories/
    │       └── services/
    ├── integration/
    │   ├── use_cases/
    │   ├── repositories/
    │   └── api/
    ├── e2e/
    │   ├── agent_workflows/
    │   └── tool_execution/
    └── fixtures/
        ├── __init__.py
        ├── agent_fixtures.py
        └── tool_fixtures.py
```

## Migration Strategy

### Gradual Migration Approach

#### Step 1: Create New Architecture Alongside Existing
- Implement new clean architecture components
- Keep existing code running in parallel
- Use feature flags to gradually switch components

#### Step 2: Migrate Core Components
- Start with domain entities and value objects
- Move repository interfaces and implementations
- Update dependency injection gradually

#### Step 3: Refactor Services
- Replace existing services with use cases
- Update API endpoints one by one
- Maintain backward compatibility during transition

#### Step 4: Complete Migration
- Remove old code after full migration
- Update documentation and deployment scripts
- Conduct thorough testing and performance validation

### Risk Mitigation

#### 1. **Backward Compatibility**
```python
# Create adapter layer for existing code
class LegacyAgentServiceAdapter:
    """Adapter to maintain compatibility with existing code"""
    
    def __init__(self, execute_agent_use_case: ExecuteAgentUseCase):
        self._use_case = execute_agent_use_case
    
    async def execute(self, ctx: ChatServiceContext) -> AsyncGenerator[dict, None]:
        """Legacy method signature maintained"""
        # Convert legacy context to new request format
        request = self._convert_context_to_request(ctx)
        
        # Execute using new use case
        response = await self._use_case.execute(request)
        
        # Convert response back to legacy format
        async for chunk in self._convert_response_to_legacy_format(response):
            yield chunk
```

#### 2. **Incremental Testing**
- Implement comprehensive test coverage before migration
- Use contract tests to ensure interface compatibility
- Perform A/B testing between old and new implementations

#### 3. **Performance Monitoring**
- Monitor performance metrics during migration
- Set up alerts for performance degradation
- Have rollback plan ready for each migration step

## Benefits of Clean Architecture Implementation

### 1. **Improved Maintainability**
- Clear separation of concerns makes code easier to understand
- Changes to business logic don't affect infrastructure
- Reduced coupling between components

### 2. **Enhanced Testability**
- Domain logic can be tested in isolation
- Easy to mock dependencies for unit testing
- Faster test execution with proper layering

### 3. **Better Extensibility**
- New agents can be added without modifying existing code
- Tool system supports plugins and extensions
- Provider implementations can be swapped easily

### 4. **Increased Reliability**
- Proper error handling and domain validation
- Clear boundaries prevent cascading failures
- Better monitoring and observability

### 5. **Team Productivity**
- Clear architecture guidelines for developers
- Reduced onboarding time for new team members
- Parallel development on different layers

### 6. **Future-Proofing**
- Architecture supports evolving requirements
- Easy to adopt new technologies and patterns
- Scalable design for growing complexity

## Conclusion

The current CloudThinker agent system has a solid foundation but suffers from common architectural issues that limit its maintainability, testability, and extensibility. By implementing Clean Architecture principles with proper dependency injection, clear layer separation, and comprehensive testing, the system will become more robust, scalable, and developer-friendly.

The proposed migration strategy allows for gradual implementation while maintaining system stability and backward compatibility. The investment in clean architecture will pay dividends in reduced maintenance costs, faster feature development, and improved system reliability.
```
```
