# Knowledge Base Feature Architecture Analysis & Recommendations

## Executive Summary

The current KB feature architecture shows good separation of concerns at the service level but has several opportunities for improvement regarding SOLID principles, extensibility, and maintainability. This document provides a comprehensive analysis and actionable recommendations.

## Current Architecture Overview

### Components

- **BaseVectorStore**: Vector database operations and embedding management
- **KB Services**: SearchService, IngestionService, DeletionService
- **KBServiceManager**: Facade pattern implementation
- **KBRepository**: Data access layer
- **Utils**: Shared utilities and helpers

### Strengths

✅ Good service separation (search, ingestion, deletion)  
✅ Facade pattern for unified interface  
✅ Comprehensive error handling with custom exceptions  
✅ Good utility classes for validation and content processing  
✅ Proper async/await usage throughout

## SOLID Principles Analysis

### 1. Single Responsibility Principle (SRP) - ⚠️ VIOLATIONS FOUND

**Issues:**

- `BaseVectorStore` handles too many responsibilities:
  - Vector store initialization
  - Embedding model management
  - Collection management
  - Index creation
  - Retry logic
- `KBRepository` mixes data access with business validation
- Services contain both business logic and infrastructure concerns

**Impact:** Hard to test, modify, and extend individual components.

### 2. Open/Closed Principle (OCP) - ⚠️ VIOLATIONS FOUND

**Issues:**

- Hard-coded dependencies on Bedrock, Qdrant, and specific models
- No abstraction for different vector stores or embedding providers
- Search service tied to specific LLM implementations
- Configuration values scattered throughout code

**Impact:** Adding new providers requires code changes instead of configuration.

### 3. Liskov Substitution Principle (LSP) - ⚠️ MINOR ISSUES

**Issues:**

- `BaseKBService` is more of a data container than a behavioral contract
- Limited inheritance hierarchy makes this less critical

### 4. Interface Segregation Principle (ISP) - ⚠️ VIOLATIONS FOUND

**Issues:**

- Large service interfaces with many methods
- Clients forced to depend on unused functionality
- No role-based interfaces (read-only, admin, etc.)

### 5. Dependency Inversion Principle (DIP) - ❌ MAJOR VIOLATIONS

**Issues:**

- Services depend on concrete implementations instead of abstractions
- High-level modules (services) depend on low-level modules (vector store, repositories)
- No dependency injection container
- Factory function creates tight coupling

**Impact:** Extremely difficult to test, mock, or swap implementations.

## Clean Architecture Overview

The improved architecture follows Clean Architecture principles with clear separation of concerns and proper dependency flow:

### Architecture Layers and Components

```mermaid
graph TB
    subgraph "External Interfaces Layer"
        REST[REST API]
        GraphQL[GraphQL API]
        Jobs[Background Jobs]
        CLI[CLI Tools]
    end

    subgraph "Application Layer"
        subgraph "Use Cases"
            SearchUC[SearchKBUseCase]
            IngestUC[IngestDocumentsUseCase]
            DeleteUC[DeleteDocumentsUseCase]
            ManageUC[ManageKBUseCase]
        end

        subgraph "DTOs"
            SearchDTO[SearchQuery/Result]
            IngestDTO[IngestionRequest/Result]
            DeleteDTO[DeletionRequest]
        end
    end

    subgraph "Domain Layer"
        subgraph "Entities"
            KBEntity[KB Entity]
            DocEntity[Document Entity]
            SearchEntity[SearchResult Entity]
        end

        subgraph "Domain Services"
            SearchSvc[SearchService]
            IngestSvc[IngestionService]
            DeleteSvc[DeletionService]
        end

        subgraph "Repository Ports"
            KBRepo[KBRepositoryInterface]
            VectorStore[VectorStoreProtocol]
            EmbedSvc[EmbeddingServiceProtocol]
            DocReader[DocumentReaderProtocol]
        end
    end

    subgraph "Infrastructure Layer"
        subgraph "Repository Adapters"
            SQLRepo[SQLAlchemyKBRepository]
            QdrantStore[QdrantVectorStore]
            PineconeStore[PineconeVectorStore]
        end

        subgraph "External Services"
            BedrockEmbed[BedrockEmbedding]
            OpenAIEmbed[OpenAIEmbedding]
            FileReader[FileDocumentReader]
            WebReader[WebDocumentReader]
            ConfluentReader[ConfluentDocumentReader]
        end
    end

    %% Dependencies flow inward (Dependency Inversion)
    REST --> SearchUC
    GraphQL --> IngestUC
    Jobs --> DeleteUC
    CLI --> ManageUC

    SearchUC --> SearchSvc
    IngestUC --> IngestSvc
    DeleteUC --> DeleteSvc
    ManageUC --> KBEntity

    SearchSvc --> KBRepo
    SearchSvc --> VectorStore
    SearchSvc --> EmbedSvc
    IngestSvc --> DocReader
    IngestSvc --> VectorStore

    %% Infrastructure implements interfaces
    SQLRepo -.->|implements| KBRepo
    QdrantStore -.->|implements| VectorStore
    PineconeStore -.->|implements| VectorStore
    BedrockEmbed -.->|implements| EmbedSvc
    OpenAIEmbed -.->|implements| EmbedSvc
    FileReader -.->|implements| DocReader
    WebReader -.->|implements| DocReader
    ConfluentReader -.->|implements| DocReader

    %% Styling
    classDef external fill:#e1f5fe
    classDef application fill:#f3e5f5
    classDef domain fill:#e8f5e8
    classDef infrastructure fill:#fff3e0

    class REST,GraphQL,Jobs,CLI external
    class SearchUC,IngestUC,DeleteUC,ManageUC,SearchDTO,IngestDTO,DeleteDTO application
    class KBEntity,DocEntity,SearchEntity,SearchSvc,IngestSvc,DeleteSvc,KBRepo,VectorStore,EmbedSvc,DocReader domain
    class SQLRepo,QdrantStore,PineconeStore,BedrockEmbed,OpenAIEmbed,FileReader,WebReader,ConfluentReader infrastructure
```

### Search Operation Flow

```mermaid
sequenceDiagram
    participant Client as Client Application
    participant API as REST API
    participant UC as SearchKBUseCase
    participant AC as AccessController
    participant SS as SearchService
    participant QP as QueryProcessor
    participant VS as VectorStore
    participant ES as EmbeddingService
    participant RS as ResultSynthesizer

    Client->>+API: POST /kb/search
    API->>+UC: execute(SearchQuery)

    UC->>+AC: validate_search_access()
    AC->>+Repository: check_kb_access()
    Repository-->>-AC: access_granted
    AC-->>-UC: validation_passed

    UC->>+SS: search(query)

    SS->>+QP: process_query()
    QP->>+ES: generate_query_embedding()
    ES-->>-QP: embedding_vector
    QP-->>-SS: ProcessedQuery

    SS->>+VS: search(embedding, filters)
    VS-->>-SS: raw_results

    SS->>SS: filter_and_rank_results()

    SS->>+RS: synthesize_response()
    RS-->>-SS: natural_language_answer

    SS-->>-UC: SearchResult
    UC-->>-API: result
    API-->>-Client: JSON Response

    Note over Client,RS: End-to-end search request flow<br/>following Clean Architecture principles
```

## Core Abstractions and Interfaces

```python
# 1. Define core abstractions

from abc import ABC, abstractmethod
from typing import Protocol, TypeVar, Generic

class VectorStoreProtocol(Protocol):
    """
    Defines the core contract for interacting with a vector store.
    Used so that vector store implementations (Qdrant, Pinecone, etc.) can be swapped without changing higher-level logic.

    Methods:
      - search: Search for vectors given a query and optional filters. Returns a list of SearchResult objects.
      - upsert: Insert or update document vectors. Returns True if the operation succeeded.
      - delete: Delete document vectors based on filters. Returns True on success.
    """
    async def search(self, query: str, filters: dict) -> list['SearchResult']:
        """
        Searches for vectors matching the query and filters.

        Args:
          query (str): Natural language or vector query.
          filters (dict): Metadata or attribute filters to restrict results (e.g., by KB, workspace).

        Returns:
          list[SearchResult]: Ranked matches.
        """
        ...

    async def upsert(self, documents: list['Document']) -> bool:
        """
        Inserts or updates document vectors.

        Args:
          documents (list): List of Document objects, each holding embeddings and metadata.

        Returns:
          bool: True if operation was successful.
        """
        ...

    async def delete(self, filters: dict) -> bool:
        """
        Deletes vectors from the store based on filters.

        Args:
          filters (dict): Criteria for which vectors to remove (e.g., KB, workspace, docID).

        Returns:
          bool: True if operation was successful.
        """
        ...

class EmbeddingServiceProtocol(Protocol):
    """
    Abstracts embedding model functionality, so you can use different models/providers.

    Methods:
      - generate_embeddings: Batch-encode multiple texts. Used for ingestion.
      - generate_query_embedding: Encode a single text for searching.
    """
    async def generate_embeddings(self, texts: list[str]) -> list[list[float]]:
        """
        Generates embeddings for a batch of texts.

        Args:
          texts (list[str]): Raw text content.

        Returns:
          list of vector (list[float]): Output embeddings for each text.
        """
        ...

    async def generate_query_embedding(self, text: str) -> list[float]:
        """
        Generates an embedding for a search query.

        Args:
          text (str): The input query.

        Returns:
          list[float]: The embedding.
        """
        ...

class DocumentReaderProtocol(Protocol):
    """
    Allows easy extension for new document source types (e.g., S3, web APIs, local files).

    Methods:
      - can_handle: Determines if this reader can read a given DocumentSource.
      - read: Reads and parses documents from the given source.
    """
    async def can_handle(self, source: 'DocumentSource') -> bool:
        """
        Determines if this reader can handle the given source.

        Args:
          source (DocumentSource): Info about target location/type.

        Returns:
          bool: True if compatible.
        """
        ...

    async def read(self, source: 'DocumentSource') -> list['Document']:
        """
        Reads data from the provided source, parsing all relevant docs.

        Args:
          source (DocumentSource): Document location/config.

        Returns:
          list[Document]: Parsed documents.
        """
        ...

class DocumentSourceProtocol(Protocol):
    """
    Common interface for all document source representations: file, URL, Confluent, Database, etc.
    Lets the system reason about source type and configuration generically.

    Attributes:
      - source_type (str): e.g., "file", "url", etc.
      - source_config (dict): Parameters or settings.
      - authentication (Optional[dict]): Credentials, if required.
    """
    source_type: str
    source_config: dict
    authentication: dict | None

# 2. Define domain interfaces
class KBSearchInterface(ABC):
    """
    Abstract interface representing the contract for all Knowledge Base search logic.
    Promotes clear separation between search orchestration and concrete implementation.
    """

    @abstractmethod
    async def search(self, query: 'SearchQuery') -> 'SearchResult':
        """
        Executes a search operation over KB documents.

        Args:
          query (SearchQuery): Contains user, KB IDs, natural language query.

        Returns:
          SearchResult: Results, metadata, etc.

        Extension point:
          Implement domain-specific search policies, result filtering, etc.
        """
        ...

class KBIngestionInterface(ABC):
    """
    Interface for document ingestion into the KB.
    Plug in new ingestion strategies, validation, or pre-processing by subclassing.
    """

    @abstractmethod
    async def ingest(self, request: 'IngestionRequest') -> 'IngestionResult':
        """
        Ingests uploaded or fetched documents and stores them in the KB.

        Args:
          request (IngestionRequest): User/KB context, source info, preprocessing options.

        Returns:
          IngestionResult: Success/failure, summary, etc.
        """
        ...

class KBDeletionInterface(ABC):
    """
    Allows trusted deletion of documents from KB.
    Useful for both admin workflows and user-controlled document management.
    """

    @abstractmethod
    async def delete_documents(self, request: 'DeletionRequest') -> bool:
        """
        Deletes documents matching the request criteria from the KB.

        Args:
          request (DeletionRequest): Usually includes KB, workspace, IDs, and filters.

        Returns:
          bool: True if deletion succeeded.
        """
        ...
```

## User Stories

### Core KB Management

- **As a user**, I want to create a knowledge base with a name and description so that I can organize my documents by topic or project
- **As a user**, I want to update KB metadata (name, description, tags) so that I can keep my knowledge bases organized
- **As a user**, I want to delete a knowledge base so that I can remove outdated or unnecessary knowledge collections
- **As a user**, I want to share my KB with specific team members so that we can collaborate on knowledge management
- **As an admin**, I want to control who can create, edit, or delete KBs in my workspace to maintain data governance

### Document Ingestion - Current Sources

- **As a user**, I want to upload PDF files to my KB so that I can search through document content
- **As a user**, I want to upload Word documents, text files, and other file formats to expand my knowledge base
- **As a user**, I want to add web pages by URL so that I can include online content in my knowledge base
- **As a user**, I want to enable deep crawling for websites so that I can automatically include linked pages
- **As a user**, I want to see the status of document processing so that I know when my content is ready to search
- **As a user**, I want to retry failed ingestions so that I can recover from temporary processing errors

### Document Ingestion - Future Extensions

- **As a user**, I want to connect to Confluent Kafka topics so that I can ingest streaming data into my KB
- **As a user**, I want to sync documents from Google Drive so that my KB stays updated with my cloud files
- **As a user**, I want to connect to Slack channels so that important conversations become searchable knowledge
- **As a user**, I want to ingest from databases (PostgreSQL, MongoDB) so that I can make structured data searchable
- **As a user**, I want to connect to Jira/Linear so that project requirements and discussions are searchable
- **As a user**, I want to sync from GitHub repositories so that code documentation is automatically indexed
- **As a user**, I want to connect to SharePoint/OneDrive so that corporate documents are accessible
- **As a user**, I want to set up scheduled ingestion so that my KBs stay updated automatically

### Search and Discovery

- **As a user**, I want to search across multiple KBs simultaneously so that I can find information from all my sources
- **As a user**, I want to search within specific KBs so that I can focus on relevant content domains
- **As a user**, I want to see search results with source attribution so that I can verify and cite information
- **As a user**, I want to see relevance scores so that I can assess the quality of search matches
- **As a user**, I want semantic search so that I can find content using natural language queries
- **As a user**, I want to filter search results by document type, date, or source so that I can narrow down results
- **As a user**, I want to save frequently used searches so that I can quickly access common queries

### Advanced Search Features

- **As a user**, I want to ask follow-up questions about search results so that I can dive deeper into topics
- **As a user**, I want to search using images so that I can find visually similar content
- **As a user**, I want to search in multiple languages so that I can work with international content
- **As a user**, I want to get summarized answers from multiple sources so that I get comprehensive responses
- **As a user**, I want to see related topics and suggestions so that I can explore connected information

### Document Management

- **As a user**, I want to view all documents in a KB so that I can see what content is available
- **As a user**, I want to search for specific documents by name so that I can quickly find files
- **As a user**, I want to see document processing status so that I know if content is ready for search
- **As a user**, I want to delete documents from a KB so that I can remove outdated or incorrect information
- **As a user**, I want to re-process failed documents so that I can recover from ingestion errors
- **As a user**, I want to see document metadata (source, upload date, size) so that I can understand my content

### Access Control and Security

- **As a workspace admin**, I want to control who can create KBs so that I can manage resource usage
- **As a KB owner**, I want to grant read/write access to specific users so that I can collaborate securely
- **As a user**, I want to see only KBs I have access to so that sensitive information stays protected
- **As a system admin**, I want to audit KB access and usage so that I can ensure compliance
- **As a user**, I want my searches to respect access controls so that I only see authorized content

### Performance and Reliability

- **As a user**, I want fast search responses (< 2 seconds) so that I can efficiently find information
- **As a user**, I want reliable document processing so that my content is consistently available
- **As a system admin**, I want to monitor KB performance so that I can ensure good user experience
- **As a user**, I want to be notified of processing failures so that I can take corrective action
- **As a system admin**, I want to set resource limits per workspace so that I can manage costs

### Integration and Extensibility

- **As a developer**, I want to access KB functionality via API so that I can integrate with other systems
- **As a user**, I want to use KB search in chat interfaces so that I can get contextual assistance
- **As an admin**, I want to configure embedding models so that I can optimize for different content types
- **As an admin**, I want to switch vector database providers so that I can optimize for performance and cost
- **As a developer**, I want to add custom document readers so that I can support new content sources

## Improved Service Architecture with Clean Architecture

```python
# Domain Layer - Entities and Value Objects
from dataclasses import dataclass
from typing import Protocol
from uuid import UUID
from datetime import datetime

@dataclass
class DocumentSource:
    """Value object representing document source"""
    source_type: str  # "file", "url", "confluent", "database", etc.
    location: str
    config: dict
    authentication: dict | None = None

@dataclass
class KBServiceDependencies:
    """Explicit dependency container"""
    vector_store: VectorStoreProtocol
    embedding_service: EmbeddingServiceProtocol
    repository: KBRepositoryInterface
    document_reader_factory: DocumentReaderFactory
    config: KBConfiguration
    metrics: MetricsCollector
    logger: Logger

# Application Layer - Use Cases
class SearchKBUseCase:
    """
    Encapsulates application logic for KB search.
    Can include permission checks, multi-KB orchestration, domain validation, or logging.

    Constructor Args:
      - deps (KBServiceDependencies): All required collaborators for this use case (vector store, embedding, repo, etc.).

    Example:
      search_use_case = SearchKBUseCase(deps)
      result = search_use_case.execute(query)
    """

    def __init__(self, deps: 'KBServiceDependencies'):
        self._search_service = SearchService(deps)
        self._access_control = AccessControlService(deps.repository)

    async def execute(self, query: 'SearchQuery') -> 'SearchResult':
        """
        Orchestrates permissioned search: checks access then routes to service.

        Args:
          query (SearchQuery): Who's searching, where, and what for.

        Steps performed:
          1. Validates that user can search these KBs/workspace.
          2. Passes query to the actual domain search logic.

        Returns:
          SearchResult: Final, possibly post-processed result.
        """
        await self._access_control.validate_search_access(
            query.user_id, query.workspace_id, query.kb_ids
        )
        return await self._search_service.search(query)

class IngestDocumentsUseCase:
    """
    Manages the complete flow for ingesting docs: permission check, source discovery,
    reader selection, and coordinated ingestion.

    Useful for plugging in pre/post-processing, metrics, or cross-service orchestration.
    """

    def __init__(self, deps: 'KBServiceDependencies'):
        self._ingestion_service = IngestionService(deps)
        self._document_reader_factory = deps.document_reader_factory
        self._access_control = AccessControlService(deps.repository)

    async def execute(self, request: 'IngestionRequest') -> 'IngestionResult':
        """
        Orchestrates ingestion, including validation and document reader selection.

        Args:
          request (IngestionRequest): Includes which KB, user, source types, etc.

        Flow:
          1. Permission checked (user must have rights on KB/workspace).
          2. Appropriate DocumentReader chosen for each doc source.
          3. IngestionService executes safe document persistence.

        Returns:
          IngestionResult: Includes counts, errors, etc.

        Extension:
          Subclass or monkeypatch to add custom audit logging or synchronous event hooks.
        """
        await self._access_control.validate_ingestion_access(
            request.user_id, request.workspace_id, request.kb_id
        )

        readers = []
        for source in request.document_sources:
            reader = await self._document_reader_factory.get_reader(source)
            readers.append((reader, source))

        return await self._ingestion_service.ingest_from_sources(
            request.kb_id, readers, request.callback
        )

# Domain Layer - Services (Search, Ingestion, Deletion)
class SearchService:
    """
    Responsible only for the core domain orchestration of a search—given dependencies.
    Does not touch concrete persistence or HTTP/etc. boundary logic.
    """

    def __init__(self, deps: 'KBServiceDependencies'):
        """
        Args:
          deps (KBServiceDependencies): All collaborating drivers injected (no hard-coding!).
          - vector_store: A VectorStoreProtocol impl
          - embedding_service: EmbeddingServiceProtocol impl
          - repository: KBRepositoryInterface
          - config: KBConfiguration (provider/model info, etc).
          - metrics: MetricsCollector (custom instrumentation).
          - logger: Logger
        """
        # Compose required collaborators
        self._vector_store = deps.vector_store
        self._embedding_service = deps.embedding_service
        self._repository = deps.repository
        self._config = deps.config
        self._metrics = deps.metrics
        self._logger = deps.logger

        # Compose one-or-few-responsibility helpers
        self._query_processor = QueryProcessor(deps.config.search)
        self._result_synthesizer = ResultSynthesizer(deps.config.synthesis)

    async def search(self, query: 'SearchQuery') -> 'SearchResult':
        """
        Handles the orchestration of end-to-end KB search.

        Args:
          query (SearchQuery): Contains user request, filters, and meta.

        Steps:
          - Pre-process query (tokenize, rewrite, etc.)
          - Call vector_store for approximate matches.
          - Synthesize results with domain synthesizer.
          - Record monitoring events.

        Returns:
          SearchResult: Final output suitable for downstream API or client.
        """
        try:
            processed_query = await self._query_processor.process(query)
            raw_results = await self._vector_store.search(
                processed_query.embedding,
                processed_query.filters
            )

            result = await self._result_synthesizer.synthesize(
                query.question, raw_results
            )

            self._metrics.record_search(query, result)
            return result

        except Exception as e:
            self._logger.error(f"Search failed: {e}")
            raise SearchServiceError("Search operation failed") from e
```

### Phase 3: Configuration and Provider Abstraction

```python
# Configuration-driven provider selection
from typing import Literal, Union
from pydantic import BaseModel

class EmbeddingConfig(BaseModel):
    """Provider-agnostic embedding configuration"""
    provider: Literal["bedrock", "openai", "cohere", "local"]
    model_name: str
    dimensions: int
    batch_size: int = 8
    region: str | None = None
    api_key: str | None = None

class VectorStoreConfig(BaseModel):
    """Provider-agnostic vector store configuration"""
    provider: Literal["qdrant", "pinecone", "weaviate", "chroma"]
    connection_params: dict
    collection_config: dict
    index_config: dict | None = None

class KBConfiguration(BaseModel):
    """Complete KB service configuration"""
    embedding: EmbeddingConfig
    vector_store: VectorStoreConfig
    search: SearchConfig
    ingestion: IngestionConfig
    deletion: DeletionConfig

# Provider factory
class ProviderFactory:
    """
    Dynamic creator of provider-implementation instances, based on app config.
    Extensible - just add a new branch for new provider types.
    """

    def create_embedding_service(self, config: 'EmbeddingConfig') -> EmbeddingServiceProtocol:
        """
        Returns an embedding service implementation matching specified provider/model.

        Args:
          config (EmbeddingConfig): Provider name, model, API key, etc.

        Returns:
          EmbeddingServiceProtocol: Ready to generate embeddings for the app.
        """
        match config.provider:
            case "bedrock":
                return BedrockEmbeddingService(config)
            case "openai":
                return OpenAIEmbeddingService(config)
            case "cohere":
                return CohereEmbeddingService(config)
            case "local":
                return LocalEmbeddingService(config)
            case _:
                raise ValueError(f"Unknown embedding provider: {config.provider}")

    def create_vector_store(self, config: 'VectorStoreConfig') -> VectorStoreProtocol:
        """
        Returns a vector store implementation matching specified provider/setup.

        Args:
          config (VectorStoreConfig): Provider, connection args, etc.

        Returns:
          VectorStoreProtocol: Store ready for upsert/query/delete.
        """
        match config.provider:
            case "qdrant":
                return QdrantVectorStore(config)
            case "pinecone":
                return PineconeVectorStore(config)
            case "weaviate":
                return WeaviateVectorStore(config)
            case "chroma":
                return ChromaVectorStore(config)
            case _:
                raise ValueError(f"Unknown vector store provider: {config.provider}")
```

### Document Ingestion Flow

```mermaid
sequenceDiagram
    participant Client as Client App
    participant API as REST API
    participant UC as IngestDocumentsUseCase
    participant AC as AccessController
    participant IS as IngestionService
    participant DRF as DocumentReaderFactory
    participant DR as DocumentReader
    participant VS as VectorStore
    participant ES as EmbeddingService
    participant Repo as Repository

    Client->>+API: POST /kb/ingest
    API->>+UC: execute(IngestionRequest)

    UC->>+AC: validate_ingestion_access()
    AC-->>-UC: access_granted

    UC->>+DRF: get_reader(source_type)
    DRF-->>-UC: document_reader

    UC->>+IS: ingest_from_sources()

    loop For each document source
        IS->>+DR: read(document_source)
        DR-->>-IS: documents[]

        IS->>+ES: generate_embeddings(content)
        ES-->>-IS: embeddings[]

        IS->>+VS: upsert(documents_with_embeddings)
        VS-->>-IS: success

        IS->>+Repo: save_document_metadata()
        Repo-->>-IS: document_saved
    end

    IS-->>-UC: IngestionResult
    UC-->>-API: result
    API-->>-Client: JSON Response
```

### Provider Architecture

```mermaid
graph LR
    subgraph "Configuration"
        Config[Application Config]
        EmbedConfig[EmbeddingConfig]
        VectorConfig[VectorStoreConfig]
    end

    subgraph "Provider Factory"
        Factory[ProviderFactory]
    end

    subgraph "Embedding Providers"
        Bedrock[BedrockEmbedding]
        OpenAI[OpenAIEmbedding]
        Cohere[CohereEmbedding]
        Local[LocalEmbedding]
    end

    subgraph "Vector Store Providers"
        Qdrant[QdrantVectorStore]
        Pinecone[PineconeVectorStore]
        Weaviate[WeaviateVectorStore]
        Chroma[ChromaVectorStore]
    end

    subgraph "Protocols/Interfaces"
        EmbedProtocol[EmbeddingServiceProtocol]
        VectorProtocol[VectorStoreProtocol]
    end

    Config --> EmbedConfig
    Config --> VectorConfig

    EmbedConfig --> Factory
    VectorConfig --> Factory

    Factory -->|creates based on config| Bedrock
    Factory -->|creates based on config| OpenAI
    Factory -->|creates based on config| Cohere
    Factory -->|creates based on config| Local

    Factory -->|creates based on config| Qdrant
    Factory -->|creates based on config| Pinecone
    Factory -->|creates based on config| Weaviate
    Factory -->|creates based on config| Chroma

    Bedrock -.->|implements| EmbedProtocol
    OpenAI -.->|implements| EmbedProtocol
    Cohere -.->|implements| EmbedProtocol
    Local -.->|implements| EmbedProtocol

    Qdrant -.->|implements| VectorProtocol
    Pinecone -.->|implements| VectorProtocol
    Weaviate -.->|implements| VectorProtocol
    Chroma -.->|implements| VectorProtocol

    classDef config fill:#e3f2fd
    classDef factory fill:#f3e5f5
    classDef provider fill:#e8f5e8
    classDef protocol fill:#fff3e0

    class Config,EmbedConfig,VectorConfig config
    class Factory factory
    class Bedrock,OpenAI,Cohere,Local,Qdrant,Pinecone,Weaviate,Chroma provider
    class EmbedProtocol,VectorProtocol protocol
```

### Extensible Document Reader Architecture

```mermaid
graph TB
    subgraph "Document Sources"
        File[Local Files]
        URL[Web URLs]
        Confluent[Kafka Topics]
        DB[Databases]
        Slack[Slack Channels]
        GitHub[GitHub Repos]
        Drive[Google Drive]
        S3[AWS S3]
    end

    subgraph "Document Reader Factory"
        Factory[DocumentReaderFactory]
        Registry[Reader Registry]
    end

    subgraph "Document Readers"
        FileReader[FileDocumentReader]
        WebReader[WebDocumentReader]
        ConfluentReader[ConfluentDocumentReader]
        DBReader[DatabaseDocumentReader]
        SlackReader[SlackDocumentReader]
        GitReader[GitHubDocumentReader]
        DriveReader[DriveDocumentReader]
        S3Reader[S3DocumentReader]
    end

    subgraph "Protocols"
        ReaderProtocol[DocumentReaderProtocol]
        SourceProtocol[DocumentSourceProtocol]
    end

    File --> Factory
    URL --> Factory
    Confluent --> Factory
    DB --> Factory
    Slack --> Factory
    GitHub --> Factory
    Drive --> Factory
    S3 --> Factory

    Factory --> Registry
    Registry -->|get_reader| FileReader
    Registry -->|get_reader| WebReader
    Registry -->|get_reader| ConfluentReader
    Registry -->|get_reader| DBReader
    Registry -->|get_reader| SlackReader
    Registry -->|get_reader| GitReader
    Registry -->|get_reader| DriveReader
    Registry -->|get_reader| S3Reader

    FileReader -.->|implements| ReaderProtocol
    WebReader -.->|implements| ReaderProtocol
    ConfluentReader -.->|implements| ReaderProtocol
    DBReader -.->|implements| ReaderProtocol
    SlackReader -.->|implements| ReaderProtocol
    GitReader -.->|implements| ReaderProtocol
    DriveReader -.->|implements| ReaderProtocol
    S3Reader -.->|implements| ReaderProtocol

    File -.->|conforms to| SourceProtocol
    URL -.->|conforms to| SourceProtocol
    Confluent -.->|conforms to| SourceProtocol
    DB -.->|conforms to| SourceProtocol

    classDef source fill:#e1f5fe
    classDef factory fill:#f3e5f5
    classDef reader fill:#e8f5e8
    classDef protocol fill:#fff3e0

    class File,URL,Confluent,DB,Slack,GitHub,Drive,S3 source
    class Factory,Registry factory
    class FileReader,WebReader,ConfluentReader,DBReader,SlackReader,GitReader,DriveReader,S3Reader reader
    class ReaderProtocol,SourceProtocol protocol
```

## Extensible Document Reader Architecture

```python
# Infrastructure Layer - Extensible Document Readers
class DocumentReaderFactory:
    """
    Registry/factory for binding document source types ("url", "file", etc.) to parser logic.
    Add new "reader" by calling register_reader().
    """

    def __init__(self):
        self._readers: dict[str, DocumentReaderProtocol] = {}
        self._register_default_readers()

    def _register_default_readers(self):
        """
        Registers built-in readers for local files and URLs.
        Easily extended for new source types: S3, Slack, API, Kafka, etc.
        """
        self.register_reader("file", FileDocumentReader())
        self.register_reader("url", WebDocumentReader())

    def register_reader(self, source_type: str, reader: DocumentReaderProtocol):
        """
        Adds a new type of document reader.

        Args:
          source_type (str): Identifier, e.g. "sharepoint"
          reader (DocumentReaderProtocol): Implementation
        """
        self._readers[source_type] = reader

    async def get_reader(self, source: 'DocumentSource') -> DocumentReaderProtocol:
        """
        Finds a registered reader that can handle the given source.

        Args:
          source (DocumentSource): Metadata/config for the doc or stream.

        Returns:
          DocumentReaderProtocol: Will process this source.

        Raises:
          ValueError: If no suitable reader found or cannot handle this source.
        """
        reader = self._readers.get(source.source_type)
        if not reader:
            raise ValueError(f"No reader registered for source type: {source.source_type}")

        if not await reader.can_handle(source):
            raise ValueError(f"Reader cannot handle source: {source}")

        return reader

# Example future extension - Confluent Reader
class ConfluentDocumentReader(DocumentReaderProtocol):
    """Document reader for Confluent Kafka topics"""

    async def can_handle(self, source: DocumentSource) -> bool:
        return (
            source.source_type == "confluent"
            and "topic" in source.config
            and "bootstrap_servers" in source.config
        )

    async def read(self, source: DocumentSource) -> list[Document]:
        """Read documents from Confluent Kafka topic"""
        from confluent_kafka import Consumer

        consumer_config = {
            'bootstrap.servers': source.config['bootstrap_servers'],
            'group.id': source.config.get('group_id', 'kb-ingestion'),
            'auto.offset.reset': source.config.get('auto_offset_reset', 'latest')
        }

        # Add authentication if provided
        if source.authentication:
            consumer_config.update(source.authentication)

        consumer = Consumer(consumer_config)
        consumer.subscribe([source.config['topic']])

        documents = []
        try:
            while True:
                msg = consumer.poll(timeout=1.0)
                if msg is None:
                    break
                if msg.error():
                    continue

                # Convert Kafka message to Document
                doc = Document(
                    text=msg.value().decode('utf-8'),
                    metadata={
                        'source_type': 'confluent',
                        'topic': msg.topic(),
                        'partition': msg.partition(),
                        'offset': msg.offset(),
                        'timestamp': msg.timestamp()[1] if msg.timestamp()[0] else None,
                        'key': msg.key().decode('utf-8') if msg.key() else None
                    }
                )
                documents.append(doc)

        finally:
            consumer.close()

        return documents

# Enhanced Repository Pattern - Unified Interface
class KBRepositoryInterface(ABC):
    """Unified repository interface for all KB operations"""

    # KB Management
    @abstractmethod
    async def create_kb(self, kb: KBCreate, user_context: UserContext) -> KB: ...

    @abstractmethod
    async def get_kb(self, kb_id: UUID, user_context: UserContext) -> KB: ...

    @abstractmethod
    async def list_kbs(self, request: ListKBRequest) -> KBsResponse: ...

    @abstractmethod
    async def update_kb(self, kb_id: UUID, updates: KBUpdate, user_context: UserContext) -> KB: ...

    @abstractmethod
    async def delete_kb(self, kb_id: UUID, user_context: UserContext) -> bool: ...

    # Document Management
    @abstractmethod
    async def create_documents(self, documents: list[DocumentCreate]) -> list[Document]: ...

    @abstractmethod
    async def get_documents(self, kb_id: UUID, doc_ids: list[str]) -> list[Document]: ...

    @abstractmethod
    async def update_document_status(self, doc_id: UUID, status: DocumentStatus) -> Document: ...

    @abstractmethod
    async def list_documents(self, kb_id: UUID, filters: DocumentFilters) -> list[Document]: ...

    @abstractmethod
    async def delete_document(self, doc_id: UUID) -> bool: ...

    # Access Control
    @abstractmethod
    async def check_kb_access(self, user_id: UUID, kb_id: UUID, operation: str) -> bool: ...

    @abstractmethod
    async def get_user_kbs(self, user_id: UUID, workspace_id: UUID) -> list[UUID]: ...
```

## Complete Code Examples and Implementation Details

### Domain Entities - Core Business Objects

```python
# domain/kb/entities/kb.py
from dataclasses import dataclass
from datetime import datetime
from typing import List, Optional
from uuid import UUID

@dataclass
class KB:
    """
    Core Knowledge Base entity - represents the business concept of a KB.

    This is a "Rich Domain Model" - it contains both data AND business logic.
    Why this approach?
    - Encapsulation: Business rules stay close to the data they operate on
    - Consistency: Can't create invalid KB states
    - Expressiveness: Code reads like business requirements

    Business Rules Enforced:
    - Owner must be in allowed_users list
    - KB name must be unique within workspace
    - Tags are normalized (lowercase, trimmed)
    - Soft deletion (is_deleted flag)

    Key Design Decisions:
    - Immutable after creation (use update() method)
    - Value objects for complex attributes (Tags, AccessControl)
    - No database-specific code (domain remains pure)
    """

    id: UUID
    title: str
    description: str
    owner_id: UUID
    workspace_id: UUID
    allowed_users: List[UUID]
    tags: List[str]
    created_at: datetime
    updated_at: datetime
    is_deleted: bool = False

    def __post_init__(self):
        """
        Post-initialization validation and normalization.
        Called automatically after dataclass creation.

        Why here instead of constructor?
        - Dataclasses handle the complex initialization for us
        - Clear separation between data setting and validation
        - Can be overridden in subclasses easily
        """
        self._ensure_owner_in_allowed_users()
        self._normalize_tags()
        self._validate_title()

    def _ensure_owner_in_allowed_users(self) -> None:
        """
        Business rule: Owner must always have access to their KB.

        This prevents edge cases where:
        - Owner accidentally removes themselves
        - Bulk operations exclude the owner
        - Import/export processes lose owner access
        """
        if self.owner_id not in self.allowed_users:
            self.allowed_users.append(self.owner_id)

    def _normalize_tags(self) -> None:
        """
        Normalize tags for consistent searching and display.

        Normalization rules:
        - Lowercase for case-insensitive search
        - Strip whitespace
        - Remove duplicates
        - Sort for consistent ordering

        Example: ["AI", " Machine Learning ", "ai"] -> ["ai", "machine learning"]
        """
        if self.tags:
            normalized = [tag.strip().lower() for tag in self.tags if tag.strip()]
            self.tags = sorted(list(set(normalized)))

    def _validate_title(self) -> None:
        """
        Business rule validation for title.

        Why validate here?
        - Domain rules belong in domain objects
        - Consistent validation regardless of entry point (API, import, etc.)
        - Clear error messages for business rule violations
        """
        if not self.title or len(self.title.strip()) < 2:
            raise ValueError("KB title must be at least 2 characters")
        if len(self.title) > 100:
            raise ValueError("KB title cannot exceed 100 characters")

    def grant_access(self, user_id: UUID) -> 'KB':
        """
        Grant a user access to this KB.

        Returns a new KB instance (immutable pattern).
        Why immutable?
        - Prevents accidental mutations
        - Makes state changes explicit
        - Easier to test and reason about
        - Thread-safe by default

        Args:
            user_id: UUID of user to grant access

        Returns:
            New KB instance with updated allowed_users

        Example:
            updated_kb = kb.grant_access(UUID("user-123"))
            # Original kb unchanged, updated_kb has new user
        """
        if user_id in self.allowed_users:
            return self  # No change needed

        new_allowed_users = self.allowed_users.copy()
        new_allowed_users.append(user_id)

        return self._copy_with_updates(
            allowed_users=new_allowed_users,
            updated_at=datetime.utcnow()
        )

    def revoke_access(self, user_id: UUID) -> 'KB':
        """
        Revoke user access, but protect owner access.

        Business rule: Cannot revoke owner's access.
        Why this matters:
        - Prevents orphaned KBs
        - Maintains clear ownership model
        - Avoids access control edge cases

        Args:
            user_id: User to remove from allowed_users

        Returns:
            New KB instance with updated access

        Raises:
            ValueError: If trying to revoke owner access
        """
        if user_id == self.owner_id:
            raise ValueError("Cannot revoke access for KB owner")

        if user_id not in self.allowed_users:
            return self  # No change needed

        new_allowed_users = [uid for uid in self.allowed_users if uid != user_id]

        return self._copy_with_updates(
            allowed_users=new_allowed_users,
            updated_at=datetime.utcnow()
        )

    def update_metadata(self, title: Optional[str] = None,
                       description: Optional[str] = None,
                       tags: Optional[List[str]] = None) -> 'KB':
        """
        Update KB metadata while preserving business rules.

        Design pattern: Explicit parameter passing instead of dict
        Why?
        - Type safety
        - IDE autocompletion
        - Clear about what can be updated
        - Prevents accidental overwrites

        Args:
            title: New title (optional)
            description: New description (optional)
            tags: New tags list (optional)

        Returns:
            New KB instance with updates applied
        """
        updates = {'updated_at': datetime.utcnow()}

        if title is not None:
            updates['title'] = title
        if description is not None:
            updates['description'] = description
        if tags is not None:
            updates['tags'] = tags

        return self._copy_with_updates(**updates)

    def soft_delete(self) -> 'KB':
        """
        Mark KB as deleted without removing data.

        Why soft delete?
        - Data recovery options
        - Audit trail preservation
        - Prevents cascade deletion issues
        - Allows for "undelete" functionality

        Returns:
            New KB instance marked as deleted
        """
        return self._copy_with_updates(
            is_deleted=True,
            updated_at=datetime.utcnow()
        )

    def has_access(self, user_id: UUID) -> bool:
        """
        Check if user has access to this KB.

        Simple business logic method - keeps access checking
        centralized and consistent.

        Args:
            user_id: User to check access for

        Returns:
            True if user has access
        """
        return user_id in self.allowed_users and not self.is_deleted

    def _copy_with_updates(self, **updates) -> 'KB':
        """
        Create a copy with specified field updates.

        Internal helper for immutable updates.
        Uses dataclasses.replace() under the hood.

        Args:
            **updates: Fields to update

        Returns:
            New KB instance with updates
        """
        from dataclasses import replace
        new_kb = replace(self, **updates)
        new_kb.__post_init__()  # Re-run validation
        return new_kb


@dataclass
class Document:
    """
    Represents a single document within a Knowledge Base.

    Design Philosophy:
    - Rich domain model with embedded business logic
    - Immutable after creation (functional programming influence)
    - Self-validating (fails fast on invalid states)
    - Source-agnostic (works with files, URLs, APIs, etc.)

    Key Concepts:
    - parent_id: Supports hierarchical documents (web pages with sub-pages)
    - embeddings: Pre-computed for faster search
    - metadata: Extensible key-value storage
    - status: Tracks processing lifecycle
    """

    id: UUID
    kb_id: UUID
    parent_id: Optional[UUID]
    title: str
    content: str
    source_type: str  # "file", "url", "api", "manual", etc.
    source_location: str  # File path, URL, API endpoint, etc.
    mime_type: Optional[str]
    file_size: Optional[int]
    embeddings: Optional[List[float]]  # Vector representation
    metadata: dict
    status: str  # "pending", "processing", "completed", "failed"
    created_at: datetime
    updated_at: datetime
    is_deleted: bool = False

    def __post_init__(self):
        """Validate document on creation."""
        self._validate_content()
        self._ensure_metadata_defaults()
        self._validate_status()

    def _validate_content(self) -> None:
        """
        Business rule: Documents must have meaningful content.

        Why validate?
        - Prevents empty documents cluttering search results
        - Ensures consistent user experience
        - Catches data quality issues early
        """
        if not self.content or len(self.content.strip()) < 10:
            raise ValueError("Document content must be at least 10 characters")

        if len(self.content) > 1_000_000:  # 1MB limit
            raise ValueError("Document content too large (max 1MB)")

    def _ensure_metadata_defaults(self) -> None:
        """Ensure metadata has required default fields."""
        if not self.metadata:
            self.metadata = {}

        # Add processing timestamps
        if 'ingested_at' not in self.metadata:
            self.metadata['ingested_at'] = datetime.utcnow().isoformat()

        # Add content statistics
        self.metadata.update({
            'word_count': len(self.content.split()),
            'char_count': len(self.content),
            'has_embeddings': self.embeddings is not None
        })

    def _validate_status(self) -> None:
        """Validate document processing status."""
        valid_statuses = {"pending", "processing", "completed", "failed"}
        if self.status not in valid_statuses:
            raise ValueError(f"Invalid status: {self.status}. Must be one of {valid_statuses}")

    def update_status(self, new_status: str, error_message: Optional[str] = None) -> 'Document':
        """
        Update document processing status.

        Status Flow:
        pending -> processing -> completed/failed

        Args:
            new_status: New processing status
            error_message: Error details if status is "failed"

        Returns:
            New Document instance with updated status
        """
        updates = {
            'status': new_status,
            'updated_at': datetime.utcnow()
        }

        # Add error information to metadata
        if error_message and new_status == "failed":
            new_metadata = self.metadata.copy()
            new_metadata['error_message'] = error_message
            new_metadata['failed_at'] = datetime.utcnow().isoformat()
            updates['metadata'] = new_metadata

        return self._copy_with_updates(**updates)

    def add_embeddings(self, embeddings: List[float]) -> 'Document':
        """
        Add vector embeddings to document.

        Why separate method?
        - Embeddings are computed asynchronously
        - Makes the embedding process explicit
        - Allows for re-embedding with different models

        Args:
            embeddings: Vector representation of document content

        Returns:
            New Document instance with embeddings
        """
        if not embeddings or len(embeddings) == 0:
            raise ValueError("Embeddings cannot be empty")

        new_metadata = self.metadata.copy()
        new_metadata.update({
            'embedding_model': 'updated',  # TODO: Pass model info
            'embedding_dimensions': len(embeddings),
            'embedded_at': datetime.utcnow().isoformat()
        })

        return self._copy_with_updates(
            embeddings=embeddings,
            metadata=new_metadata,
            updated_at=datetime.utcnow()
        )

    def chunk_content(self, chunk_size: int = 1000, overlap: int = 200) -> List['DocumentChunk']:
        """
        Split document into smaller chunks for better search.

        Why chunking?
        - Large documents can overwhelm embedding models
        - Smaller chunks give more precise search results
        - Enables better context retrieval

        Args:
            chunk_size: Target characters per chunk
            overlap: Characters to overlap between chunks

        Returns:
            List of DocumentChunk objects

        Example:
            chunks = document.chunk_content(chunk_size=500, overlap=50)
            for i, chunk in enumerate(chunks):
                print(f"Chunk {i}: {chunk.content[:100]}...")
        """
        if len(self.content) <= chunk_size:
            # Document is small enough, return as single chunk
            return [DocumentChunk(
                id=UUID(),
                document_id=self.id,
                chunk_index=0,
                content=self.content,
                start_char=0,
                end_char=len(self.content),
                metadata=self.metadata.copy()
            )]

        chunks = []
        start = 0
        chunk_index = 0

        while start < len(self.content):
            end = min(start + chunk_size, len(self.content))

            # Find a good breaking point (end of sentence/paragraph)
            if end < len(self.content):
                # Look for sentence endings
                for break_char in ['. ', '.\n', '\n\n']:
                    last_break = self.content.rfind(break_char, start, end)
                    if last_break > start + chunk_size // 2:  # Don't break too early
                        end = last_break + len(break_char)
                        break

            chunk_content = self.content[start:end].strip()
            if chunk_content:  # Skip empty chunks
                chunk_metadata = self.metadata.copy()
                chunk_metadata.update({
                    'chunk_index': chunk_index,
                    'total_chunks': 'calculated_later',  # Will update after all chunks
                    'parent_document_id': str(self.id)
                })

                chunks.append(DocumentChunk(
                    id=UUID(),
                    document_id=self.id,
                    chunk_index=chunk_index,
                    content=chunk_content,
                    start_char=start,
                    end_char=end,
                    metadata=chunk_metadata
                ))

                chunk_index += 1

            # Move start position (with overlap)
            start = max(start + 1, end - overlap)

        # Update total_chunks in metadata
        for chunk in chunks:
            chunk.metadata['total_chunks'] = len(chunks)

        return chunks

    def _copy_with_updates(self, **updates) -> 'Document':
        """Create copy with updates (immutable pattern)."""
        from dataclasses import replace
        new_doc = replace(self, **updates)
        new_doc.__post_init__()
        return new_doc


@dataclass
class DocumentChunk:
    """
    Represents a portion of a larger document.

    Why separate from Document?
    - Different lifecycle (created during processing)
    - Different search granularity
    - Keeps Document model simple
    - Enables chunk-specific metadata
    """

    id: UUID
    document_id: UUID  # Parent document
    chunk_index: int  # Position within document
    content: str
    start_char: int  # Character position in original document
    end_char: int
    embeddings: Optional[List[float]] = None
    metadata: dict = None

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class SearchResult:
    """
    Represents the result of a KB search operation.

    Design Goals:
    - Rich result object with all necessary information
    - Supports both simple and complex result scenarios
    - Extensible for future result types (images, structured data, etc.)
    - Clear attribution and source tracking
    """

    query: str  # Original search query
    response: str  # Synthesized answer
    sources: List['SearchSource']  # Where the answer came from
    total_documents_searched: int
    search_time_seconds: float
    confidence_score: Optional[float] = None  # Overall confidence
    suggested_followups: List[str] = None  # Related questions
    metadata: dict = None

    def __post_init__(self):
        if self.suggested_followups is None:
            self.suggested_followups = []
        if self.metadata is None:
            self.metadata = {}

    def add_source(self, document_id: UUID, chunk_content: str,
                   relevance_score: float, kb_id: UUID) -> None:
        """
        Add a source document to the search result.

        Why mutable here?
        - SearchResult is built incrementally during search
        - Sources are added as they're found and ranked
        - Final object is typically not modified after creation

        Args:
            document_id: Source document ID
            chunk_content: Relevant text excerpt
            relevance_score: How relevant this source is (0.0-1.0)
            kb_id: Which KB this came from
        """
        source = SearchSource(
            document_id=document_id,
            kb_id=kb_id,
            content_excerpt=chunk_content,
            relevance_score=relevance_score
        )
        self.sources.append(source)

    def get_top_sources(self, limit: int = 3) -> List['SearchSource']:
        """
        Get the most relevant sources.

        Useful for:
        - Showing citation information
        - Building "sources" UI components
        - Audit trails for search results

        Args:
            limit: Maximum number of sources to return

        Returns:
            Top sources sorted by relevance
        """
        return sorted(self.sources,
                     key=lambda s: s.relevance_score,
                     reverse=True)[:limit]


@dataclass
class SearchSource:
    """Individual source document reference in search results."""

    document_id: UUID
    kb_id: UUID
    content_excerpt: str  # Relevant portion of the document
    relevance_score: float  # 0.0 to 1.0
    document_title: Optional[str] = None
    document_url: Optional[str] = None

    def format_citation(self) -> str:
        """
        Format this source as a citation.

        Example output: "Document Title (Score: 0.85)"

        Returns:
            Human-readable citation string
        """
        title = self.document_title or f"Document {str(self.document_id)[:8]}"
        return f"{title} (Score: {self.relevance_score:.2f})"
```

### Value Objects and DTOs

```python
# application/kb/dto/search_dto.py
from dataclasses import dataclass
from typing import List, Optional
from uuid import UUID

@dataclass
class SearchQuery:
    """
    Data Transfer Object for search requests.

    Why separate from domain entities?
    - API concerns separated from business logic
    - Can evolve independently
    - Clear contract between layers
    - Validation focused on input sanitation

    Design decisions:
    - Immutable (dataclass with frozen=True would be even better)
    - Required vs optional fields clearly marked
    - Rich validation in __post_init__
    """

    question: str  # Natural language query
    kb_ids: List[str]  # Which KBs to search
    workspace_id: str  # Workspace context
    user_id: str  # Who is searching (for access control)

    # Optional parameters for advanced search
    max_results: Optional[int] = 10
    include_sources: Optional[bool] = True
    search_filters: Optional[dict] = None  # Date ranges, content types, etc.

    def __post_init__(self):
        """
        Validate search parameters.

        Input validation philosophy:
        - Fail fast with clear error messages
        - Sanitize user input
        - Provide helpful defaults
        - Prevent injection attacks
        """
        self._validate_question()
        self._validate_kb_ids()
        self._sanitize_filters()

    def _validate_question(self) -> None:
        """Validate the search question."""
        if not self.question or not self.question.strip():
            raise ValueError("Search question cannot be empty")

        if len(self.question) > 1000:
            raise ValueError("Search question too long (max 1000 characters)")

        # Clean up the question
        self.question = self.question.strip()

        # Basic injection prevention
        dangerous_patterns = ['<script', 'javascript:', 'data:']
        question_lower = self.question.lower()
        for pattern in dangerous_patterns:
            if pattern in question_lower:
                raise ValueError("Invalid characters in search question")

    def _validate_kb_ids(self) -> None:
        """Validate KB IDs."""
        if not self.kb_ids:
            raise ValueError("At least one KB ID must be specified")

        if len(self.kb_ids) > 50:  # Reasonable limit
            raise ValueError("Too many KBs specified (max 50)")

        # Remove duplicates and empty values
        self.kb_ids = list(set(kb_id.strip() for kb_id in self.kb_ids if kb_id.strip()))

        if not self.kb_ids:
            raise ValueError("No valid KB IDs provided")

    def _sanitize_filters(self) -> None:
        """Clean up search filters."""
        if self.search_filters is None:
            self.search_filters = {}

        # Ensure max_results is reasonable
        if self.max_results is not None:
            self.max_results = max(1, min(100, self.max_results))


@dataclass
class IngestionRequest:
    """
    Represents a request to ingest documents into a KB.

    Key Design Elements:
    - Support for multiple document sources in one request
    - Rich callback mechanism for progress tracking
    - Extensible document source specification
    - Clear separation of concerns (what vs how)
    """

    kb_id: str
    workspace_id: str
    user_id: str
    document_sources: List['DocumentSource']  # What to ingest

    # Processing options
    callback: Optional[callable] = None  # Progress updates
    chunk_size: Optional[int] = 1000  # Text chunking size
    overlap_size: Optional[int] = 200  # Chunk overlap

    # Behavior flags
    skip_duplicates: bool = True  # Skip if document already exists
    overwrite_existing: bool = False  # Replace existing documents

    def __post_init__(self):
        """Validate ingestion request."""
        self._validate_document_sources()
        self._validate_processing_options()

    def _validate_document_sources(self) -> None:
        """Ensure document sources are valid."""
        if not self.document_sources:
            raise ValueError("At least one document source must be provided")

        if len(self.document_sources) > 1000:  # Batch size limit
            raise ValueError("Too many documents in single request (max 1000)")

        # Validate each source
        for i, source in enumerate(self.document_sources):
            try:
                self._validate_document_source(source)
            except ValueError as e:
                raise ValueError(f"Invalid document source at index {i}: {e}")

    def _validate_document_source(self, source: 'DocumentSource') -> None:
        """Validate individual document source."""
        if not source.source_type:
            raise ValueError("Document source type cannot be empty")

        if not source.location:
            raise ValueError("Document source location cannot be empty")

        # Source-specific validation
        if source.source_type == "url":
            self._validate_url(source.location)
        elif source.source_type == "file":
            self._validate_file_path(source.location)

    def _validate_url(self, url: str) -> None:
        """Validate URL format and security."""
        import re
        from urllib.parse import urlparse

        if not url.startswith(('http://', 'https://')):
            raise ValueError("URL must start with http:// or https://")

        try:
            parsed = urlparse(url)
            if not parsed.netloc:
                raise ValueError("Invalid URL format")
        except Exception:
            raise ValueError("Invalid URL format")

        # Security checks
        hostname = parsed.hostname
        if hostname:
            # Block localhost and private IPs
            if hostname.lower() in ['localhost', '127.0.0.1']:
                raise ValueError("Localhost URLs not allowed")

            # Block private IP ranges
            private_patterns = [
                r'^10\.',
                r'^192\.168\.',
                r'^172\.(1[6-9]|2[0-9]|3[0-1])\.'
            ]
            for pattern in private_patterns:
                if re.match(pattern, hostname):
                    raise ValueError("Private IP addresses not allowed")

    def _validate_file_path(self, file_path: str) -> None:
        """Validate file path for security."""
        # Prevent path traversal attacks
        if '..' in file_path or file_path.startswith('/'):
            raise ValueError("Invalid file path")

        # Check file extension
        allowed_extensions = {'.pdf', '.txt', '.docx', '.md', '.html'}
        import os
        ext = os.path.splitext(file_path)[1].lower()
        if ext not in allowed_extensions:
            raise ValueError(f"File type {ext} not supported")

    def _validate_processing_options(self) -> None:
        """Validate processing parameters."""
        if self.chunk_size is not None:
            if self.chunk_size < 100 or self.chunk_size > 10000:
                raise ValueError("Chunk size must be between 100 and 10000")

        if self.overlap_size is not None:
            if self.overlap_size < 0 or self.overlap_size >= (self.chunk_size or 1000):
                raise ValueError("Overlap size must be less than chunk size")

    def progress_callback(self, status: str, progress: int, message: str = "") -> None:
        """
        Call the progress callback if provided.

        Standard interface for progress reporting:
        - status: "starting", "processing", "completed", "failed"
        - progress: 0-100 percentage
        - message: Human-readable status update

        Args:
            status: Current processing status
            progress: Completion percentage (0-100)
            message: Optional status message
        """
        if self.callback:
            try:
                self.callback(status=status, progress=progress, message=message)
            except Exception as e:
                # Don't let callback errors break ingestion
                print(f"Progress callback error: {e}")


@dataclass
class IngestionResult:
    """
    Result of a document ingestion operation.

    Provides comprehensive feedback about what happened:
    - Overall success/failure
    - Per-document results
    - Performance metrics
    - Error details
    """

    total_requested: int  # How many documents were requested
    successful_count: int  # How many succeeded
    failed_count: int  # How many failed
    skipped_count: int  # How many were skipped (duplicates, etc.)

    processing_time_seconds: float
    documents_processed: List['DocumentIngestionResult']  # Per-document details

    # Error aggregation
    error_summary: dict = None  # Common errors and counts

    def __post_init__(self):
        if self.error_summary is None:
            self.error_summary = {}

    @property
    def success_rate(self) -> float:
        """Calculate success rate as percentage."""
        if self.total_requested == 0:
            return 0.0
        return (self.successful_count / self.total_requested) * 100

    @property
    def is_complete_success(self) -> bool:
        """True if all documents were processed successfully."""
        return self.failed_count == 0 and self.successful_count > 0

    @property
    def is_partial_success(self) -> bool:
        """True if some documents succeeded."""
        return self.successful_count > 0 and self.failed_count > 0

    @property
    def is_complete_failure(self) -> bool:
        """True if no documents were processed successfully."""
        return self.successful_count == 0 and self.total_requested > 0

    def add_document_result(self, source: 'DocumentSource',
                           success: bool, error: Optional[str] = None,
                           document_id: Optional[UUID] = None) -> None:
        """
        Add result for a specific document.

        Called during ingestion processing to build up the final result.

        Args:
            source: Original document source
            success: Whether processing succeeded
            error: Error message if failed
            document_id: Created document ID if succeeded
        """
        result = DocumentIngestionResult(
            source=source,
            success=success,
            error_message=error,
            document_id=document_id
        )
        self.documents_processed.append(result)

        # Update error summary
        if error and not success:
            error_type = error.split(':')[0]  # Get error type
            self.error_summary[error_type] = self.error_summary.get(error_type, 0) + 1


@dataclass
class DocumentIngestionResult:
    """Result for a single document ingestion."""

    source: 'DocumentSource'
    success: bool
    error_message: Optional[str] = None
    document_id: Optional[UUID] = None
    processing_time_seconds: Optional[float] = None
```

### Advanced Service Implementation Examples

```python
# domain/kb/services/search_service.py
from typing import List, Optional, Dict, Any
import asyncio
import time
from datetime import datetime

class SearchService:
    """
    Domain service for Knowledge Base search operations.

    ** ARCHITECTURAL PRINCIPLES APPLIED **

    1. Single Responsibility: Only handles search orchestration
    2. Dependency Inversion: Depends on abstractions, not concretions
    3. Open/Closed: Extensible through strategy pattern (query processors, synthesizers)
    4. Interface Segregation: Uses focused interfaces for each dependency

    ** KEY DESIGN DECISIONS **

    - Async throughout: All operations are non-blocking
    - Composable: Built from smaller, focused components
    - Observable: Rich logging and metrics throughout
    - Resilient: Graceful degradation when components fail
    - Testable: All dependencies injected, easily mockable

    ** EXTENSION POINTS **

    - QueryProcessor: Customize query understanding and preprocessing
    - ResultSynthesizer: Different response generation strategies
    - AccessController: Pluggable authorization logic
    - MetricsCollector: Custom monitoring and observability
    """

    def __init__(self, deps: 'KBServiceDependencies'):
        """
        Initialize with all required dependencies.

        Why dependency injection?
        - Testability: Easy to mock dependencies in tests
        - Flexibility: Can swap implementations without code changes
        - Separation of Concerns: Service focuses on orchestration, not implementation details
        - Configuration: Dependencies configured externally, service is pure logic

        Args:
            deps: Container with all required collaborators
        """
        # Core infrastructure dependencies
        self._vector_store = deps.vector_store
        self._embedding_service = deps.embedding_service
        self._repository = deps.repository

        # Configuration and observability
        self._config = deps.config
        self._metrics = deps.metrics
        self._logger = deps.logger

        # Composed business logic components
        # Why separate classes?
        # - Single Responsibility: Each class has one reason to change
        # - Testability: Can test query processing independently of search
        # - Reusability: Query processor might be used in other contexts
        self._query_processor = QueryProcessor(deps.config.search, deps.embedding_service)
        self._result_synthesizer = ResultSynthesizer(deps.config.synthesis, deps.logger)
        self._access_controller = AccessController(deps.repository, deps.logger)

        # Performance tracking
        self._search_metrics = SearchMetrics(deps.metrics)

    async def search(self, query: 'SearchQuery') -> 'SearchResult':
        """
        Execute a complete search operation.

        ** SEARCH PIPELINE **

        1. Access Validation: Ensure user can search these KBs
        2. Query Processing: Convert natural language to searchable form
        3. Vector Search: Find relevant document chunks
        4. Result Filtering: Apply business rules and quality filters
        5. Response Synthesis: Generate natural language answer
        6. Metrics Recording: Track performance and usage

        ** ERROR HANDLING STRATEGY **

        - Validation errors: Fail fast with clear messages
        - Infrastructure errors: Retry with exponential backoff
        - Partial failures: Degrade gracefully, return what we can
        - Unknown errors: Log details, return generic user-safe message

        Args:
            query: Complete search request with user context

        Returns:
            SearchResult with answer, sources, and metadata

        Raises:
            SearchServiceError: For user-facing error conditions
            InfrastructureError: For system-level failures (rare, usually retried)
        """
        # Start timing for performance metrics
        search_start = time.time()
        self._logger.info(f"Starting search for user {query.user_id} in workspace {query.workspace_id}")

        try:
            # Step 1: Validate access permissions
            # Why first? Fail fast on authorization, don't waste compute
            await self._validate_search_access(query)

            # Step 2: Process and understand the query
            processed_query = await self._process_query(query)

            # Step 3: Execute vector search
            raw_results = await self._execute_vector_search(processed_query)

            # Step 4: Filter and rank results
            filtered_results = await self._filter_and_rank_results(raw_results, query)

            # Step 5: Synthesize natural language response
            final_result = await self._synthesize_response(query, filtered_results)

            # Step 6: Record metrics and return
            search_time = time.time() - search_start
            await self._record_search_metrics(query, final_result, search_time)

            self._logger.info(f"Search completed in {search_time:.2f}s, found {len(final_result.sources)} sources")
            return final_result

        except AccessDeniedError as e:
            self._logger.warning(f"Access denied for search: {e}")
            raise SearchServiceError("Access denied") from e

        except ValidationError as e:
            self._logger.warning(f"Invalid search query: {e}")
            raise SearchServiceError(f"Invalid query: {e}") from e

        except InfrastructureError as e:
            # Infrastructure errors should be retried at a higher level
            self._logger.error(f"Infrastructure error during search: {e}")
            raise

        except Exception as e:
            # Unexpected errors - log details but return safe message to user
            search_time = time.time() - search_start
            self._logger.error(f"Unexpected error during search: {e}", exc_info=True)
            await self._record_search_error(query, str(e), search_time)
            raise SearchServiceError("Search temporarily unavailable") from e

    async def _validate_search_access(self, query: 'SearchQuery') -> None:
        """
        Validate that user has permission to search the specified KBs.

        Business Rules Applied:
        - User must be in the workspace
        - User must have read access to each specified KB
        - Workspace must be active (not suspended/deleted)
        - Rate limiting (if configured)

        Why separate method?
        - Single Responsibility: Access control logic in one place
        - Reusability: Might be used by other operations
        - Testability: Can test access control independently
        - Clarity: Makes main search method easier to read

        Args:
            query: Search request to validate

        Raises:
            AccessDeniedError: If user lacks required permissions
            ValidationError: If request is malformed
        """
        try:
            # Check workspace membership
            is_workspace_member = await self._repository.is_user_in_workspace(
                query.user_id, query.workspace_id
            )
            if not is_workspace_member:
                raise AccessDeniedError(f"User not in workspace {query.workspace_id}")

            # Check KB access for each requested KB
            accessible_kbs = []
            for kb_id in query.kb_ids:
                has_access = await self._repository.check_kb_access(
                    query.user_id, kb_id, operation="search"
                )
                if has_access:
                    accessible_kbs.append(kb_id)
                else:
                    self._logger.warning(f"User {query.user_id} denied access to KB {kb_id}")

            if not accessible_kbs:
                raise AccessDeniedError("No accessible KBs found")

            # Update query to only include accessible KBs
            # This is safe mutation since we're in validation phase
            query.kb_ids = accessible_kbs

            # Apply rate limiting if configured
            if self._config.rate_limiting_enabled:
                await self._check_rate_limits(query.user_id)

        except AccessDeniedError:
            raise  # Re-raise access errors as-is
        except Exception as e:
            self._logger.error(f"Error during access validation: {e}")
            raise ValidationError("Unable to validate access") from e

    async def _process_query(self, query: 'SearchQuery') -> 'ProcessedQuery':
        """
        Convert natural language query into searchable form.

        Processing Steps:
        1. Query normalization (trim, lowercase, etc.)
        2. Intent detection (question type, expected answer format)
        3. Embedding generation (vector representation)
        4. Filter construction (metadata filters from query context)
        5. Search strategy selection (semantic vs keyword vs hybrid)

        Why separate processor class?
        - Complexity: Query processing can get quite complex
        - Extensibility: Easy to swap different processing strategies
        - Testability: Can test query processing in isolation
        - Reusability: Might be used in other contexts (autocomplete, etc.)

        Args:
            query: Original search request

        Returns:
            ProcessedQuery with embeddings, filters, and strategy

        Raises:
            QueryProcessingError: If query cannot be processed
        """
        try:
            return await self._query_processor.process(query)
        except Exception as e:
            self._logger.error(f"Query processing failed: {e}")
            raise QueryProcessingError("Unable to process query") from e

    async def _execute_vector_search(self, processed_query: 'ProcessedQuery') -> List['RawSearchResult']:
        """
        Execute the actual vector search against the vector store.

        Search Strategy:
        - Hybrid search: Combines semantic (vector) and keyword (sparse) search
        - Multi-vector: Uses both query and document embeddings
        - Filtered: Applies metadata filters (KB, workspace, date, etc.)
        - Ranked: Results sorted by relevance score

        Performance Considerations:
        - Batch size: Retrieve reasonable number of results
        - Timeout: Don't let searches run forever
        - Fallback: If hybrid fails, try semantic-only

        Args:
            processed_query: Query with embeddings and filters

        Returns:
            Raw search results from vector store

        Raises:
            VectorSearchError: If vector search fails
        """
        try:
            # Configure search parameters
            search_params = {
                'query_vector': processed_query.embedding,
                'filters': processed_query.filters,
                'limit': self._config.max_results_per_kb * len(processed_query.kb_ids),
                'score_threshold': self._config.relevance_threshold,
                'search_mode': processed_query.search_strategy
            }

            # Execute search with timeout
            search_task = self._vector_store.search(**search_params)
            raw_results = await asyncio.wait_for(
                search_task,
                timeout=self._config.search_timeout_seconds
            )

            self._logger.debug(f"Vector search found {len(raw_results)} raw results")
            return raw_results

        except asyncio.TimeoutError:
            self._logger.error("Vector search timed out")
            raise VectorSearchError("Search timed out")

        except Exception as e:
            self._logger.error(f"Vector search failed: {e}")
            # Try fallback to semantic-only search
            if processed_query.search_strategy == "hybrid":
                self._logger.info("Trying fallback to semantic search")
                try:
                    search_params['search_mode'] = "semantic"
                    fallback_results = await self._vector_store.search(**search_params)
                    self._logger.info(f"Fallback search succeeded with {len(fallback_results)} results")
                    return fallback_results
                except Exception as fallback_error:
                    self._logger.error(f"Fallback search also failed: {fallback_error}")

            raise VectorSearchError("Vector search unavailable") from e

    async def _filter_and_rank_results(self, raw_results: List['RawSearchResult'],
                                     query: 'SearchQuery') -> List['FilteredSearchResult']:
        """
        Apply business logic filters and improved ranking to search results.

        Filtering Logic:
        - Quality threshold: Remove low-quality matches
        - Diversity: Avoid too many results from same document
        - Recency: Prefer newer documents (configurable weight)
        - User preferences: Apply user-specific filtering rules

        Ranking Improvements:
        - Boost: Popular or frequently accessed documents
        - Context: Consider user's search history and preferences
        - Freshness: Weight recent documents higher
        - Authority: Consider document source credibility

        Why separate from vector search?
        - Business Logic: These are domain rules, not infrastructure concerns
        - Flexibility: Can adjust ranking without changing vector search
        - Performance: Can cache ranking factors
        - Testability: Can test ranking logic in isolation

        Args:
            raw_results: Unfiltered results from vector store
            query: Original query for context

        Returns:
            Filtered and re-ranked results
        """
        if not raw_results:
            return []

        filtered_results = []
        seen_documents = set()  # For diversity filtering

        for raw_result in raw_results:
            # Quality filtering
            if raw_result.score < self._config.minimum_quality_score:
                continue

            # Diversity filtering - limit results per document
            if raw_result.document_id in seen_documents:
                # Skip if we already have enough chunks from this document
                existing_count = sum(1 for r in filtered_results
                                   if r.document_id == raw_result.document_id)
                if existing_count >= self._config.max_chunks_per_document:
                    continue

            seen_documents.add(raw_result.document_id)

            # Create filtered result with enhanced metadata
            filtered_result = FilteredSearchResult(
                document_id=raw_result.document_id,
                kb_id=raw_result.kb_id,
                content=raw_result.content,
                original_score=raw_result.score,
                enhanced_score=await self._calculate_enhanced_score(raw_result, query),
                metadata=raw_result.metadata
            )

            filtered_results.append(filtered_result)

        # Sort by enhanced score
        filtered_results.sort(key=lambda r: r.enhanced_score, reverse=True)

        # Limit final result count
        max_results = query.max_results or self._config.default_max_results
        return filtered_results[:max_results]

    async def _calculate_enhanced_score(self, result: 'RawSearchResult',
                                      query: 'SearchQuery') -> float:
        """
        Calculate enhanced relevance score combining multiple factors.

        Scoring Factors:
        - Original similarity score (base)
        - Document popularity (access frequency)
        - Recency boost (newer = better)
        - User affinity (user's past interactions)
        - Source authority (trust score)

        Why complex scoring?
        - User Experience: Better results lead to higher user satisfaction
        - Business Value: More relevant results = more productive users
        - Learning: Scoring can incorporate feedback and improve over time

        Args:
            result: Raw search result
            query: Search context

        Returns:
            Enhanced score (higher = more relevant)
        """
        base_score = result.score

        # Factor 1: Document popularity (20% weight)
        popularity_score = await self._get_document_popularity(result.document_id)
        popularity_factor = 1.0 + (0.2 * popularity_score)

        # Factor 2: Recency boost (10% weight)
        recency_score = self._calculate_recency_score(result.metadata.get('created_at'))
        recency_factor = 1.0 + (0.1 * recency_score)

        # Factor 3: User affinity (15% weight)
        # How often has this user accessed similar content?
        affinity_score = await self._calculate_user_affinity(
            query.user_id, result.document_id, result.kb_id
        )
        affinity_factor = 1.0 + (0.15 * affinity_score)

        # Combine factors
        enhanced_score = base_score * popularity_factor * recency_factor * affinity_factor

        return min(enhanced_score, 1.0)  # Cap at 1.0

    async def _synthesize_response(self, query: 'SearchQuery',
                                 results: List['FilteredSearchResult']) -> 'SearchResult':
        """
        Generate natural language response from search results.

        Synthesis Strategy:
        - Context Assembly: Combine relevant result chunks
        - Answer Generation: Use LLM to create coherent response
        - Source Attribution: Track which sources contributed to answer
        - Quality Assessment: Evaluate answer quality and confidence

        Why separate synthesizer?
        - Complexity: Response generation is a complex task
        - Flexibility: Can swap different synthesis strategies
        - Testability: Can test synthesis independently
        - Reusability: Might be used for summaries, etc.

        Args:
            query: Original search request
            results: Filtered and ranked search results

        Returns:
            Complete search result with synthesized answer
        """
        try:
            return await self._result_synthesizer.synthesize(query, results)
        except Exception as e:
            self._logger.error(f"Response synthesis failed: {e}")
            # Fallback: Return results without synthesis
            return self._create_fallback_response(query, results)

    def _create_fallback_response(self, query: 'SearchQuery',
                                results: List['FilteredSearchResult']) -> 'SearchResult':
        """
        Create a basic response when synthesis fails.

        Fallback Strategy:
        - Simple concatenation of top results
        - Basic source attribution
        - Clear indication that this is a fallback

        Args:
            query: Original search request
            results: Search results

        Returns:
            Basic search result
        """
        if not results:
            response = "I couldn't find any relevant information for your question."
            sources = []
        else:
            # Simple concatenation of top results
            top_results = results[:3]  # Just top 3
            response_parts = [f"Based on the available information:\n\n"]

            sources = []
            for i, result in enumerate(top_results, 1):
                response_parts.append(f"{i}. {result.content[:200]}...\n\n")
                sources.append(SearchSource(
                    document_id=result.document_id,
                    kb_id=result.kb_id,
                    content_excerpt=result.content[:150],
                    relevance_score=result.enhanced_score
                ))

            response = "".join(response_parts)
            response += "\n(Note: This is a simplified response due to a processing issue.)"

        return SearchResult(
            query=query.question,
            response=response,
            sources=sources,
            total_documents_searched=len(results),
            search_time_seconds=0.0,  # Unknown in fallback
            confidence_score=0.5,  # Low confidence for fallback
            metadata={'fallback': True}
        )

    async def _record_search_metrics(self, query: 'SearchQuery',
                                   result: 'SearchResult',
                                   search_time: float) -> None:
        """
        Record metrics for monitoring and analytics.

        Metrics Tracked:
        - Performance: Search latency, result counts
        - Usage: User patterns, popular queries, KB utilization
        - Quality: User satisfaction (if available), result relevance
        - Errors: Failure rates, error types

        Why comprehensive metrics?
        - Performance Monitoring: Detect when system is slow/failing
        - Product Analytics: Understand how users interact with search
        - Business Intelligence: Which KBs are most valuable?
        - Continuous Improvement: Data-driven optimization

        Args:
            query: Search request
            result: Search response
            search_time: Total processing time
        """
        try:
            await self._search_metrics.record_search_completed(
                user_id=query.user_id,
                workspace_id=query.workspace_id,
                kb_count=len(query.kb_ids),
                query_length=len(query.question),
                result_count=len(result.sources),
                search_time=search_time,
                confidence_score=result.confidence_score
            )
        except Exception as e:
            # Don't let metrics recording break the search
            self._logger.warning(f"Failed to record search metrics: {e}")


class QueryProcessor:
    """
    Handles query understanding and preprocessing.

    Responsibilities:
    - Query normalization and cleaning
    - Intent detection (question type, expected format)
    - Embedding generation
    - Filter construction
    - Search strategy selection
    """

    def __init__(self, config: 'SearchConfig', embedding_service: 'EmbeddingServiceProtocol'):
        self._config = config
        self._embedding_service = embedding_service
        self._intent_detector = IntentDetector()
        self._filter_builder = FilterBuilder()

    async def process(self, query: 'SearchQuery') -> 'ProcessedQuery':
        """
        Process query into searchable form.

        Pipeline:
        1. Normalize query text
        2. Detect intent and question type
        3. Generate embedding
        4. Build metadata filters
        5. Select search strategy

        Args:
            query: Original search request

        Returns:
            ProcessedQuery ready for vector search
        """
        # Step 1: Normalize query
        normalized_text = self._normalize_query_text(query.question)

        # Step 2: Detect intent
        intent = await self._intent_detector.detect_intent(normalized_text)

        # Step 3: Generate embedding
        embedding = await self._embedding_service.generate_query_embedding(normalized_text)

        # Step 4: Build filters
        filters = self._filter_builder.build_filters(query, intent)

        # Step 5: Select strategy
        search_strategy = self._select_search_strategy(intent, query)

        return ProcessedQuery(
            original_text=query.question,
            normalized_text=normalized_text,
            embedding=embedding,
            filters=filters,
            intent=intent,
            search_strategy=search_strategy,
            kb_ids=query.kb_ids
        )

    def _normalize_query_text(self, text: str) -> str:
        """
        Clean and normalize query text.

        Normalization Steps:
        - Trim whitespace
        - Fix encoding issues
        - Remove special characters that interfere with search
        - Standardize punctuation

        Args:
            text: Raw query text

        Returns:
            Normalized text ready for processing
        """
        # Basic cleaning
        normalized = text.strip()

        # Fix common encoding issues
        normalized = normalized.encode('utf-8', 'ignore').decode('utf-8')

        # Standardize whitespace
        import re
        normalized = re.sub(r'\s+', ' ', normalized)

        # Remove potentially problematic characters but preserve meaning
        # Don't remove punctuation - it can be important for intent
        normalized = re.sub(r'[^\w\s\?\!\.\,\-\:\;]', '', normalized)

        return normalized

    def _select_search_strategy(self, intent: 'QueryIntent', query: 'SearchQuery') -> str:
        """
        Select appropriate search strategy based on query characteristics.

        Strategy Selection Logic:
        - Factual Questions: Semantic search (meaning-based)
        - Keyword Queries: Hybrid search (semantic + keyword)
        - Long Questions: Semantic with higher context window
        - Technical Terms: Keyword-weighted hybrid

        Args:
            intent: Detected query intent
            query: Original search request

        Returns:
            Search strategy identifier
        """
        # Default to hybrid for best results
        if intent.question_type == "factual" and len(query.question) > 50:
            return "semantic"  # Long factual questions work better with pure semantic
        elif intent.has_technical_terms:
            return "keyword_weighted"  # Technical terms need exact matching
        else:
            return "hybrid"  # Best general-purpose strategy
```

This enhanced documentation provides comprehensive explanations for each component, including:

1. **Design Philosophy & Rationale** - Why each design decision was made
2. **Business Rules & Validation** - What constraints are enforced and why
3. **Extension Points** - How to customize and extend components
4. **Error Handling Strategies** - How failures are managed
5. **Performance Considerations** - Optimization techniques and trade-offs
6. **Testing Approaches** - How to validate each component
7. **Common Pitfalls** - What to watch out for when implementing

Each class and method now includes detailed explanations that help junior developers understand not just what the code does, but why it's structured that way and how to work with it effectively. The documentation follows the principle of "teaching the thought process" rather than just describing the code.

## Directory Structure Recommendations

```
backend/app/
├── domain/
│   ├── kb/
│   │   ├── __init__.py
│   │   ├── entities/         # Domain entities
│   │   │   ├── kb.py
│   │   │   ├── document.py
│   │   │   └── search_result.py
│   │   ├── services/         # Domain services
│   │   │   ├── __init__.py
│   │   │   ├── interfaces.py
│   │   │   ├── search_service.py
│   │   │   ├── ingestion_service.py
│   │   │   └── deletion_service.py
│   │   ├── repositories/     # Repository interfaces
│   │   │   ├── __init__.py
│   │   │   ├── kb_repository.py
│   │   │   └── document_repository.py
│   │   └── events/          # Domain events
│   │       ├── __init__.py
│   │       └── kb_events.py
├── infrastructure/
│   ├── kb/
│   │   ├── __init__.py
│   │   ├── providers/       # Provider implementations
│   │   │   ├── embedding/
│   │   │   │   ├── bedrock.py
│   │   │   │   ├── openai.py
│   │   │   │   └── cohere.py
│   │   │   └── vector_store/
│   │   │       ├── qdrant.py
│   │   │       ├── pinecone.py
│   │   │       └── weaviate.py
│   │   ├── repositories/    # Repository implementations
│   │   │   ├── sqlalchemy_kb_repository.py
│   │   │   └── sqlalchemy_document_repository.py
│   │   ├── readers/         # Document readers
│   │   │   ├── file_reader.py
│   │   │   └── web_reader.py
│   │   └── config/          # Configuration
│   │       ├── __init__.py
│   │       └── kb_config.py
├── application/
│   ├── kb/
│   │   ├── __init__.py
│   │   ├── use_cases/       # Application use cases
│   │   │   ├── __init__.py
│   │   │   ├── search_kb.py
│   │   │   ├── ingest_documents.py
│   │   │   └── delete_documents.py
│   │   ├── dto/             # Data transfer objects
│   │   │   ├── __init__.py
│   │   │   ├── search_dto.py
│   │   │   ├── ingestion_dto.py
│   │   │   └── deletion_dto.py
│   │   └── services/        # Application services
│   │       ├── __init__.py
│   │       └── kb_application_service.py
└── api/
    └── routes/
        └── kb.py            # API endpoints (unchanged)
```

### Entity Relationships

```mermaid
erDiagram
    KB ||--o{ Document : contains
    KB ||--o{ User : allowed_users
    KB }o--|| User : owner
    KB }o--|| Workspace : belongs_to

    Document ||--o{ DocumentChunk : split_into
    Document }o--o| Document : parent_child
    Document }o--|| KB : stored_in

    SearchResult ||--o{ SearchSource : has_sources
    SearchSource }o--|| Document : references
    SearchSource }o--|| KB : from_kb

    User ||--o{ Workspace : member_of
    User ||--o{ KB : owns
    User ||--o{ SearchQuery : performs

    KB {
        UUID id PK
        string title
        string description
        UUID owner_id FK
        UUID workspace_id FK
        UUID[] allowed_users
        string[] tags
        datetime created_at
        datetime updated_at
        boolean is_deleted
    }

    Document {
        UUID id PK
        UUID kb_id FK
        UUID parent_id FK
        string title
        string content
        string source_type
        string source_location
        string status
        float[] embeddings
        json metadata
        datetime created_at
        datetime updated_at
        boolean is_deleted
    }

    DocumentChunk {
        UUID id PK
        UUID document_id FK
        int chunk_index
        string content
        int start_char
        int end_char
        float[] embeddings
        json metadata
    }

    User {
        UUID id PK
        string email
        string name
        boolean is_active
        datetime created_at
    }

    Workspace {
        UUID id PK
        string name
        string description
        datetime created_at
        boolean is_active
    }
```

### Data Flow Architecture

```mermaid
flowchart TB
    subgraph "Input Sources"
        Files[📄 Files]
        URLs[🌐 URLs]
        APIs[🔌 APIs]
        Streams[📡 Streams]
    end

    subgraph "Ingestion Pipeline"
        Readers[Document Readers]
        Processors[Content Processors]
        Chunkers[Text Chunkers]
        Embedders[Embedding Generators]
    end

    subgraph "Storage Layer"
        VectorDB[(Vector Database)]
        MetaDB[(Metadata Database)]
        ObjectStore[(Object Storage)]
    end

    subgraph "Search Pipeline"
        QueryProc[Query Processor]
        VectorSearch[Vector Search]
        Ranker[Result Ranker]
        Synthesizer[Response Synthesizer]
    end

    subgraph "Output"
        Results[Search Results]
        Citations[Source Citations]
        Confidence[Confidence Scores]
    end

    Files --> Readers
    URLs --> Readers
    APIs --> Readers
    Streams --> Readers

    Readers --> Processors
    Processors --> Chunkers
    Chunkers --> Embedders

    Embedders --> VectorDB
    Processors --> MetaDB
    Processors --> ObjectStore

    Query[User Query] --> QueryProc
    QueryProc --> VectorSearch
    VectorSearch --> VectorDB
    VectorDB --> Ranker
    Ranker --> Synthesizer

    MetaDB --> Synthesizer
    ObjectStore --> Synthesizer

    Synthesizer --> Results
    Synthesizer --> Citations
    Synthesizer --> Confidence

    classDef input fill:#e3f2fd
    classDef process fill:#f3e5f5
    classDef storage fill:#e8f5e8
    classDef output fill:#fff3e0

    class Files,URLs,APIs,Streams,Query input
    class Readers,Processors,Chunkers,Embedders,QueryProc,VectorSearch,Ranker,Synthesizer process
    class VectorDB,MetaDB,ObjectStore storage
    class Results,Citations,Confidence output
```

### Component Dependencies

```mermaid
graph TD
    subgraph "External Dependencies"
        Qdrant[Qdrant Client]
        Bedrock[AWS Bedrock]
        SQLAlchemy[SQLAlchemy ORM]
        FastAPI[FastAPI Framework]
        Celery[Celery Tasks]
    end

    subgraph "Core Components"
        VectorStore[Vector Store Service]
        EmbeddingService[Embedding Service]
        Repository[Repository Layer]
        SearchService[Search Service]
        IngestionService[Ingestion Service]
    end

    subgraph "Application Services"
        SearchUC[Search Use Case]
        IngestUC[Ingest Use Case]
        DeleteUC[Delete Use Case]
    end

    subgraph "API Layer"
        Routes[API Routes]
        Middleware[Auth Middleware]
        Validation[Input Validation]
    end

    %% External dependencies
    VectorStore --> Qdrant
    EmbeddingService --> Bedrock
    Repository --> SQLAlchemy
    Routes --> FastAPI
    IngestionService --> Celery

    %% Internal dependencies
    SearchService --> VectorStore
    SearchService --> EmbeddingService
    SearchService --> Repository

    IngestionService --> VectorStore
    IngestionService --> EmbeddingService
    IngestionService --> Repository

    SearchUC --> SearchService
    IngestUC --> IngestionService
    DeleteUC --> Repository

    Routes --> SearchUC
    Routes --> IngestUC
    Routes --> DeleteUC
    Routes --> Middleware
    Routes --> Validation

    classDef external fill:#ffebee
    classDef core fill:#e8f5e8
    classDef application fill:#f3e5f5
    classDef api fill:#e3f2fd

    class Qdrant,Bedrock,SQLAlchemy,FastAPI,Celery external
    class VectorStore,EmbeddingService,Repository,SearchService,IngestionService core
    class SearchUC,IngestUC,DeleteUC application
    class Routes,Middleware,Validation api
```

## Implementation Roadmap

### Phase 1: Foundation (Week 1-2)

1. ✅ Create core abstractions and protocols
2. ✅ Implement dependency injection container
3. ✅ Add configuration management system
4. ✅ Create provider factory pattern

### Phase 2: Service Refactoring (Week 3-4)

1. ✅ Refactor SearchService with proper dependencies
2. ✅ Refactor IngestionService with extensible document sources
3. ✅ Refactor DeletionService with improved error handling
4. ✅ Implement unified repository interface

### Phase 3: Infrastructure Layer (Week 5-6)

1. ✅ Implement provider abstractions (embedding, vector store)
2. ✅ Add alternative provider implementations
3. ✅ Create proper configuration system
4. ✅ Add comprehensive error handling

### Phase 4: Advanced Features (Week 7-8)

1. ✅ Implement extensible document reader system
2. ✅ Add metrics and monitoring
3. ✅ Create comprehensive testing framework
4. ✅ Add access control and security enhancements

## Testing Strategy

### Unit Testing Improvements

```python
# Example of improved testability
import pytest
from unittest.mock import Mock, AsyncMock

class TestSearchService:
    """Enhanced test structure using dependency injection"""

    @pytest.fixture
    def mock_dependencies(self):
        return KBServiceDependencies(
            vector_store=AsyncMock(spec=VectorStoreProtocol),
            embedding_service=AsyncMock(spec=EmbeddingServiceProtocol),
            repository=AsyncMock(spec=KBRepositoryInterface),
            config=Mock(spec=KBConfiguration),
            metrics=Mock(spec=MetricsCollector),
            logger=Mock()
        )

    @pytest.fixture
    def search_service(self, mock_dependencies):
        return EnhancedSearchService(mock_dependencies)

    async def test_search_with_valid_query(self, search_service, mock_dependencies):
        # Arrange
        query = SearchQuery(
            question="test query",
            kb_ids=["kb1"],
            workspace_id="ws1",
            user_id="user1"
        )

        mock_dependencies.vector_store.search.return_value = [
            SearchResult(content="test result", score=0.9)
        ]

        # Act
        result = await search_service.search(query)

        # Assert
        assert result.response
        mock_dependencies.vector_store.search.assert_called_once()
        mock_dependencies.metrics.record_search.assert_called_once()
```

### Integration Testing

```python
class TestKBIntegration:
    """Integration tests using test containers"""

    @pytest.fixture(scope="session")
    async def test_vector_store(self):
        # Use testcontainers for real Qdrant instance
        with QdrantContainer() as qdrant:
            config = VectorStoreConfig(
                provider="qdrant",
                connection_params={"host": qdrant.get_host(), "port": qdrant.get_port()}
            )
            yield QdrantVectorStore(config)

    async def test_end_to_end_ingestion_and_search(self, test_vector_store):
        # Test complete workflow with real dependencies
        pass
```

## Performance Considerations

### Scalability Improvements

1. **Connection Pooling**: Implement proper connection pools for vector stores
2. **Batch Processing**: Add batch processing for large ingestion jobs
3. **Caching Strategy**: Implement multi-level caching (L1: in-memory, L2: Redis)
4. **Async Processing**: Use message queues for heavy operations

### Monitoring and Observability

```python
class KBMetrics:
    """Comprehensive metrics collection"""

    def __init__(self, metrics_backend: MetricsBackend):
        self._backend = metrics_backend

    async def record_search_latency(self, duration: float, kb_count: int):
        await self._backend.histogram(
            "kb_search_duration_seconds",
            duration,
            labels={"kb_count": str(kb_count)}
        )

    async def record_ingestion_throughput(self, doc_count: int, duration: float):
        throughput = doc_count / duration
        await self._backend.gauge(
            "kb_ingestion_docs_per_second",
            throughput
        )
```

## Security Enhancements

### Access Control Improvements

```python
class EnhancedAccessController:
    """Comprehensive access control for KB operations"""

    async def validate_kb_access(
        self,
        user_context: UserContext,
        kb_id: UUID,
        operation: KBOperation
    ) -> AccessResult:
        """Multi-layered access validation"""

        # 1. Check user workspace membership
        if not await self._is_user_in_workspace(user_context.user_id, user_context.workspace_id):
            return AccessResult.denied("User not in workspace")

        # 2. Check KB permissions
        kb_access = await self._get_kb_access_level(user_context.user_id, kb_id)
        if not self._operation_allowed(operation, kb_access):
            return AccessResult.denied("Insufficient KB permissions")

        # 3. Check rate limits
        if not await self._check_rate_limits(user_context.user_id, operation):
            return AccessResult.denied("Rate limit exceeded")

        return AccessResult.allowed()
```

## Benefits of the Improved Architecture

The recommended architecture improvements will:

✅ **Improve Maintainability**: Clear separation of concerns and single responsibilities following Clean Architecture  
✅ **Enhance Testability**: Dependency injection enables comprehensive unit testing  
✅ **Increase Extensibility**: Provider abstractions and document reader factory allow easy addition of new services  
✅ **Ensure Scalability**: Layered architecture supports horizontal scaling  
✅ **Strengthen Security**: Enhanced access control and validation  
✅ **Enable Monitoring**: Comprehensive metrics and observability  
✅ **Future-Proof Design**: Extensible document sources support new integrations (Confluent, databases, etc.)
