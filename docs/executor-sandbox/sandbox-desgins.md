flowchart TD
    subgraph User["User Interface"]
        FE[Frontend<br/>Chat Interface]
        noVNC[noVNC Client<br/>Visual Output]
        ApprovalUI[File Change<br/>Approval Interface]
    end

    subgraph Backend["Backend Services"]
        ChatAPI[Chat API<br/>User ↔ AI Communication]
        Agent[AI Agent<br/>Decision Making]
        FileAPI[File Change API<br/>Approval Management]
        RedisChannel[Redis Pub/Sub<br/>Event Broadcasting]
        SSEService[SSE Stream Service<br/>Real-time Notifications]
    end

    subgraph Executor["Sandbox Executor (per workspace)"]
        ExecAPI[Executor API<br/>Command Interface]

        subgraph Sandbox["Isolated Sandbox Environment"]
            BwrapSandbox[Bubblewrap Container<br/>Security Isolation]
            VNCServer[VNC Server<br/>Display Output]
            FileWatcher[File System Watcher<br/>inotify monitoring]

            subgraph Tools["Available Tools"]
                BashTool[Bash/Shell<br/>Command Execution]
                CodeTool[Code Editor<br/>File Manipulation]
                ComputerTool[Computer Use<br/>GUI Interaction]
                WebTool[Web Browser<br/>Internet Access]
            end
        end

        subgraph Network["Network Namespace"]
            NetNS[Isolated Network<br/>Controlled Internet]
        end
    end

    subgraph Storage["Persistent Storage"]
        PVC[Shared Volume<br/>User Files & State]
        TempFS[Temporary Storage<br/>Sandbox Runtime]
    end

    subgraph Security["Security & Monitoring"]
        AuthService[Authentication<br/>User Sessions]
        AuditLog[Audit Logging<br/>Action History]
        ResourceMonitor[Resource Monitor<br/>CPU/Memory Limits]
    end

    %% User Interaction Flow
    FE -->|Chat Messages| ChatAPI
    ChatAPI -->|Process Request| Agent
    Agent -->|Execute Commands| ExecAPI

    %% Sandbox Execution Flow
    ExecAPI -->|Initialize| BwrapSandbox
    BwrapSandbox -->|Mount| PVC
    BwrapSandbox -->|Network| NetNS
    BwrapSandbox -->|Display| VNCServer
    VNCServer -->|Stream| noVNC

    %% Tool Execution
    ExecAPI -->|Command| BashTool
    ExecAPI -->|Edit Files| CodeTool
    ExecAPI -->|GUI Actions| ComputerTool
    ExecAPI -->|Web Access| WebTool

    %% File Change Detection & Approval Flow
    FileWatcher -->|Detect Changes| RedisChannel
    RedisChannel -->|Publish Event| SSEService
    SSEService -->|SSE Stream| ApprovalUI
    ApprovalUI -->|User Decision| FileAPI
    FileAPI -->|Approve/Reject| Agent
    FileAPI -->|Update Status| RedisChannel

    %% Storage & Security
    PVC <-->|Persistent Data| BwrapSandbox
    TempFS <-->|Runtime Data| BwrapSandbox
    AuthService -->|Validate| ChatAPI
    AuditLog <-->|Log Actions| ExecAPI
    ResourceMonitor -->|Monitor| BwrapSandbox

    %% Bidirectional Communication
    Agent <-->|Status Updates| RedisChannel
    SSEService <-->|Real-time Updates| FE

    %% Styling
    classDef userClass fill:#e1f5fe
    classDef backendClass fill:#f3e5f5
    classDef executorClass fill:#e8f5e8
    classDef storageClass fill:#fff3e0
    classDef securityClass fill:#ffebee

    class FE,noVNC,ApprovalUI userClass
    class ChatAPI,Agent,FileAPI,RedisChannel,SSEService backendClass
    class ExecAPI,BwrapSandbox,VNCServer,FileWatcher,BashTool,CodeTool,ComputerTool,WebTool,NetNS executorClass
    class PVC,TempFS storageClass
    class AuthService,AuditLog,ResourceMonitor securityClass
