## Dashboard Design Best Practices

This guide focuses on grid hygiene, widget clarity, and consistency for readable, actionable dashboards.

### What “good” looks like

- Audience-focused: KPIs first, then diagnostic visuals.
- Consistency: same units, colors, scales for the same metric across widgets.
- Clarity: concise titles and labels; avoid clutter.

### Layout and grid

- Use a 12-column grid. Reserve full-row width for crowded categorical charts (categories > 5).
- Avoid overlaps: every widget must include `layout: { x, y, w, h }` and fit the grid.
- Keep vertical rhythm even; avoid tall/narrow tables.

### Widget guidelines

- KPI cards: limit to 3–6; short labels; emphasize change vs. target when relevant.
- Charts: match chart to task (trend=line/area, comparison=bar, composition=stacked). Use direct labels, clear units, and timeframes.
- Tables: minimal columns, clear headers, avoid line wraps where possible.

### Filters and context

- Make timeframe and scope explicit. Keep cross-widget filters consistent.

### Accessibility and performance

- Ensure color contrast. Limit expensive widgets and heavy queries; paginate tables.

### Validation checklist

- No widget overlaps; all layouts well-formed.
- KPI set concise; charts readable; tables scannable.
- Colors/scales consistent across related widgets.


