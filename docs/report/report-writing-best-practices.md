## Report Writing and Data Storytelling Best Practices

This guide consolidates proven practices for analytics/business reports and how to integrate charts, tables, and KPI cards with narrative so the result reads like a report, not a dashboard.

### What “good” looks like

- **Audience-first**: Executive readers want outcomes and decisions; practitioners want method and detail.
- **Narrative-led**: Each section has a clear claim, supporting evidence, and a takeaway.
- **One idea per block**: Use short paragraphs; avoid dumping multiple charts with no text between them.
- **Takeaway titles**: Prefer assertion-style titles (e.g., “Storage spiked in EU due to backup policy change”) over neutral titles (“Storage usage by region”).
- **Consistent visuals**: Same units, colors, and scales across comparable charts.

### Canonical report structure

1. Executive summary
   - Key findings (3–6 bullets)
   - Business impact (what it means)
   - Top recommendations (prioritized, action-oriented)
2. Context & methodology (optional but short)
3. Findings & analysis (multiple sections)
   - For each section: lead-in paragraph → visual(s) → interpretation paragraph → implication(s)
4. Recommendations & next steps
5. Appendix (data notes, assumptions, definitions)

### Section pattern (enforced in tool output)

- Start with a 1–2 sentence paragraph that states the point of the section.
- Add at most one chart/table at a time.
- Follow immediately with a paragraph that interprets the visual (what/so-what/now-what).
- Only place charts side-by-side when the goal is explicit comparison. If you must compare, add a comparison paragraph first and a conclusion paragraph after. Otherwise, stack vertically.

### Visual guidelines

- Match chart to task (trend → line, comparison → bar, composition → stacked/100% stacked, distribution → histogram/box, part-to-whole → pie/donut only when parts are few and clear).
- Use direct labels over legends when space allows; always include units and timeframe.
- Use reference lines/bands for thresholds and annotate anomalies or step changes.
- Keep decimals consistent and meaningful (e.g., 1 decimal for percentages; K/M/B for currency).
- Keep color palette consistent across the whole report; use the same hue for the same metric.

### Writing guidelines around visuals

- Each visual must have: a clear title (assertion-style), a short caption (1–2 sentences), and a follow-up paragraph that interprets the data and ties it to business impact.
- Never place two non-paragraph items (chart/table/card) consecutively without a paragraph in between that frames or interprets.
- If a visual compares against a target/threshold, explicitly state pass/fail and the gap.

### KPI cards

- Use for the 3–5 most important metrics only.
- Titles should be the metric; the description or adjacent paragraph should state the reason and implication of movement.

### Recommendations

- Be specific, prioritized, and testable. Tie each recommendation to a finding and quantify impact when possible.

### Validation checklist (use before returning a report)

- Executive summary present and consistent with body.
- No adjacent visuals without an intervening paragraph.
- Every visual has assertion-style title, units, and timeframe.
- Comparable charts share scales and colors.
- Each section ends with a clear takeaway and optional action.

### Sources

- Datylon — 10 report design tips: [datylon.com/blog/10-report-design-tips](https://www.datylon.com/blog/10-report-design-tips)
- Venngage — Report design ideas & tips: [venngage.com/blog/report-design](https://venngage.com/blog/report-design/)
- Dolphin Analytics — Data viz best practices for reports: [dolphinanalytics.co.uk/insights/5-data-visualization-best-practices-for-marketing-reports](https://www.dolphinanalytics.co.uk/insights/5-data-visualization-best-practices-for-marketing-reports/)
- Metrics Watch — Data visualization best practices: [metricswatch.com/7-best-practices-in-data-visualization-for-marketing-reports](https://metricswatch.com/7-best-practices-in-data-visualization-for-marketing-reports/)
- R-bloggers — Structured report architecture: [r-bloggers.com/2024/09/blueprint-for-success-the-architecture-of-structured-reports](https://www.r-bloggers.com/2024/09/blueprint-for-success-the-architecture-of-structured-reports/)

---

### Proposed prompt updates for the Report Tool

Replace the current instructions with the following stronger constraints for content ordering and narrative:

```text
Report generation rules (must follow):

1) Structure:
   - Executive summary → (optional) context/method → findings sections → recommendations → appendix.

2) Section pattern (strict):
   - Begin with a short paragraph that states the claim of the section.
   - Then include at most one visual (chart/table/card).
   - Immediately follow with a paragraph that interprets the visual and explains business impact.
   - Do not place two visuals back-to-back. Always insert a paragraph between non-paragraph content types.
   - Only place visuals side-by-side if explicitly comparing; include a comparison paragraph before and an interpretation paragraph after.

3) Visual requirements:
   - Use assertion-style titles that express the takeaway (e.g., "Storage spiked in EU after backup policy change").
   - Include units, timeframe, and labeled axes. Prefer direct labels; keep color/scale consistent across comparable visuals.
   - Add reference lines for targets/thresholds where relevant and annotate anomalies.

4) KPI cards:
   - Limit to 3–5 critical metrics. Each card requires a nearby paragraph explaining the implication.

5) Recommendations:
   - Prioritize and tie each recommendation to a specific finding; quantify expected impact when possible.

6) Output constraints for this tool’s schema:
   - The `sections[i].content` list must not contain two consecutive items where `type` ∈ {chart, table, card}. Insert a `paragraph` item between them that frames or interprets.
   - Every chart/table must be preceded by a `paragraph` and followed by a `paragraph` summarizing the insight and impact.
   - Keep paragraph text plain (no markdown) as required by the schema.
```

These rules directly target the failure mode where multiple charts are returned without narrative, forcing a readable report with explanations between visuals.
