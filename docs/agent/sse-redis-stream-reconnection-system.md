# SSE and Redis Queue Stream Reconnection System

## Overview

This document outlines the comprehensive SSE (Server-Sent Events) and Redis queue-based streaming system implemented for autonomous agent conversations. The system provides robust stream reconnection capabilities, ensuring users can seamlessly reconnect to active streams when returning to conversation pages or experiencing connection interruptions.

## System Architecture

The streaming system consists of several key components working together:

1. **Backend Stream Processing** - Manages agent execution and event queuing
2. **Redis Queue Management** - Provides persistent event storage and real-time messaging
3. **Frontend Stream Management** - Handles UI updates and reconnection logic
4. **Stream Reconnection System** - Enables seamless reconnection to active streams

```mermaid
graph TB
    subgraph "Frontend Layer"
        UI[React UI Components]
        MSH[useMessageStream Hook]
        SRH[useStreamReconnection Hook]
        ESC[EventSource Connection]
    end

    subgraph "Backend API Layer"
        CAR[Chat Stream Route]
        RCR[Reconnect Route]
        SSR[Stream Status Route]
        CSR[Cancel Stream Route]
    end

    subgraph "Stream Processing Layer"
        SPP[Stream Persistence Process]
        BTP[Background Task Pool]
        AS[Agent Service]
    end

    subgraph "Redis Layer"
        RQ[Redis Queue]
        RM[Redis Manager]
        RSS[Redis Stream Status]
        RPS[Redis Pub/Sub]
    end

    subgraph "Database Layer"
        PG[(PostgreSQL)]
        MSG[Message Storage]
        CONV[Conversation Storage]
    end

    UI --> MSH
    MSH --> SRH
    MSH --> ESC
    ESC --> CAR
    ESC --> RCR

    CAR --> SPP
    RCR --> SPP
    SSR --> RM
    CSR --> BTP

    SPP --> AS
    SPP --> RM
    BTP --> RM

    RM --> RQ
    RM --> RSS
    RM --> RPS

    AS --> PG
    SPP --> MSG
    SPP --> CONV
```

## Core Components

### 1. Backend Stream Processing (`/backend/app/api/routes/autonomous_agent.py`)

The main API route handler manages stream lifecycle and provides endpoints for:

#### Key Endpoints:

- **`/chat/stream`** - Initiates new streaming conversations
- **`/chat/{conversation_id}/reconnect-stream`** - Reconnects to active streams
- **`/chat/{conversation_id}/stream-status`** - Checks stream status
- **`/chat/{conversation_id}/cancel-stream`** - Cancels active streams

#### Stream Initialization Flow:

```mermaid
sequenceDiagram
    participant Client
    participant API
    participant RedisManager
    participant BackgroundTask
    participant AgentService

    Client->>API: POST /chat/stream
    API->>RedisManager: Check existing stream status
    API->>RedisManager: Complete previous message if active
    API->>BackgroundTask: Create background stream task
    API->>Client: Return SSE stream

    par Background Processing
        BackgroundTask->>AgentService: Execute agent
        AgentService->>RedisManager: Queue stream events
        RedisManager->>Client: Publish real-time events
    and Client Stream Consumption
        Client->>RedisManager: Consume from Redis queue
        RedisManager->>Client: Stream historical + live events
    end

    BackgroundTask->>RedisManager: Mark stream complete
    RedisManager->>Client: Send complete event
```

### 2. Stream Persistence Service (`/backend/app/services/agent/stream_persistence.py`)

Manages background stream processing and Redis queue operations:

#### Key Functions:

- **`process_and_queue_stream()`** - Processes agent execution and queues events
- **`stream_from_redis_queue()`** - Streams events from Redis to clients
- **`create_background_stream_task()`** - Creates async background tasks
- **Task lifecycle management** - Handles task cancellation and cleanup

#### Event Processing Flow:

```mermaid
flowchart TD
    START[Stream Request] --> INIT[Initialize Background Task]
    INIT --> STORE_USER[Store User Message in Redis]
    STORE_USER --> EXEC[Execute Agent Service]

    EXEC --> PROCESS{Process Agent Events}
    PROCESS --> QUEUE[Queue Event to Redis]
    QUEUE --> PUB[Publish Real-time Event]
    PUB --> PROCESS

    PROCESS --> ERROR{Error Occurred?}
    ERROR -->|Yes| ERROR_EVENT[Queue Error Event]
    ERROR_EVENT --> COMPLETE

    PROCESS --> DONE{Agent Complete?}
    DONE -->|No| PROCESS
    DONE -->|Yes| COMPLETE[Queue Complete Event]

    COMPLETE --> CLEANUP[Mark Stream Inactive]
    CLEANUP --> END[Task Finished]
```

### 3. Frontend Stream Reconnection (`/frontend/features/chat/hooks/use-stream-reconnection.ts`)

Provides reconnection capabilities with the following features:

#### Key Functions:

- **`checkStreamStatus()`** - Verifies if a stream is active
- **`reconnectToStream()`** - Reconnects to existing streams
- **`attemptAutoReconnection()`** - Automatically reconnects when possible
- **`abortReconnection()`** - Cancels ongoing reconnection attempts

#### Reconnection Logic:

```mermaid
flowchart TD
    LOAD[Page Load/Navigation] --> CHECK[Check Stream Status]
    CHECK --> ACTIVE{Stream Active?}

    ACTIVE -->|No| LOAD_STATIC[Load Static Messages]
    ACTIVE -->|Yes| ATTEMPT[Attempt Reconnection]

    ATTEMPT --> CALL[Call /reconnect-stream API]
    CALL --> SUCCESS{Reconnection Success?}

    SUCCESS -->|Yes| STREAM[Process Reconnected Stream]
    SUCCESS -->|No| FALLBACK[Fallback to Static Messages]

    STREAM --> SHOW_INDICATOR[Show Streaming Indicator]
    STREAM --> PROCESS_EVENTS[Process Historical + Live Events]

    FALLBACK --> LOAD_STATIC
    LOAD_STATIC --> DISPLAY[Display Static Messages]
```

### 4. Frontend Message Stream Management (`/frontend/hooks/use-autonomous-message-stream.ts`)

Comprehensive hook managing the entire streaming experience:

#### Core Capabilities:

- **Stream Processing** - Handles all event types from SSE
- **Message Management** - Maintains conversation state
- **Reconnection Integration** - Seamlessly integrates with reconnection system
- **Error Handling** - Robust error recovery mechanisms

#### Message Processing Flow:

```mermaid
sequenceDiagram
    participant UI
    participant Hook
    participant Reconnection
    participant API
    participant Redis

    UI->>Hook: Load conversation
    Hook->>Reconnection: Check stream status

    alt Active Stream Detected
        Reconnection->>API: GET /reconnect-stream
        API->>Redis: Fetch conversation events
        Redis->>API: Return complete history
        API->>Hook: Stream all events
        Hook->>UI: Display historical messages
        Hook->>UI: Show streaming indicator

        loop Live Events
            Redis->>API: New event available
            API->>Hook: Stream live event
            Hook->>UI: Update message display
        end
    else No Active Stream
        Hook->>API: GET /messages/{conversation_id}
        API->>Hook: Return static messages
        Hook->>UI: Display static messages
    end
```

## Redis Queue System

### Data Structures

The system uses several Redis data structures:

#### 1. Stream Events Queue

```
Key: stream_events:{conversation_id}
Type: List
Content: JSON events with position metadata
```

#### 2. Stream Status Metadata

```
Key: stream_status:{conversation_id}
Type: Hash
Fields:
  - is_streaming_active: boolean
  - stream_status: "in_progress" | "completed" | "error"
  - last_stream_position: integer
  - current_message_id: string
```

#### 3. Message Events

```
Key: message_events:{conversation_id}:{message_id}
Type: List
Content: Events specific to a message
```

#### 4. Conversation History Events

```
Key: conversation_history:{conversation_id}
Type: List
Content: Complete conversation event history
```

### Event Types

The system handles multiple event types:

```mermaid
graph TD
    subgraph "User Events"
        UM[user_message]
        CID[conversation_id]
    end

    subgraph "Agent Events"
        MID[message_id]
        ST[stream]
        FC[function_call]
        FR[function_result]
        TH[thinking]
        PL[planning]
    end

    subgraph "Display Events"
        DC[display_component]
        CD[chart_data]
        RG[recommendation_generation]
        RPG[report_generation]
        DG[dashboard_generation]
    end

    subgraph "Control Events"
        INT[interrupt]
        ERR[error]
        COMP[complete]
    end

    UM --> Redis[(Redis Queue)]
    CID --> Redis
    MID --> Redis
    ST --> Redis
    FC --> Redis
    FR --> Redis
    TH --> Redis
    PL --> Redis
    DC --> Redis
    CD --> Redis
    RG --> Redis
    RPG --> Redis
    DG --> Redis
    INT --> Redis
    ERR --> Redis
    COMP --> Redis
```

## Stream Lifecycle Management

### 1. Stream Initialization

```mermaid
stateDiagram-v2
    [*] --> Inactive

    Inactive --> Initializing: New stream request
    Initializing --> Active: Background task created
    Initializing --> Error: Task creation failed

    Active --> Processing: Agent executing
    Processing --> Active: Continue processing
    Processing --> Completing: Agent finished
    Processing --> Error: Processing error

    Completing --> Inactive: Stream completed
    Error --> Inactive: Error handled

    Active --> Cancelled: Manual cancellation
    Cancelled --> Inactive: Cleanup complete
```

### 2. Reconnection Process

```mermaid
flowchart TD
    START[User Returns to Page] --> CHECK_STATUS[Check Stream Status API]

    CHECK_STATUS --> STATUS_ACTIVE{Is Stream Active?}
    STATUS_ACTIVE -->|No| LOAD_STATIC[Load Static Messages]
    STATUS_ACTIVE -->|Yes| START_RECONNECT[Start Reconnection]

    START_RECONNECT --> CALL_API[Call Reconnect Stream API]
    CALL_API --> API_SUCCESS{API Call Success?}

    API_SUCCESS -->|No| SHOW_ERROR[Show Reconnection Error]
    SHOW_ERROR --> LOAD_STATIC

    API_SUCCESS -->|Yes| PROCESS_STREAM[Process Stream Response]
    PROCESS_STREAM --> PARSE_EVENTS[Parse Historical Events]
    PARSE_EVENTS --> UPDATE_UI[Update UI with History]
    UPDATE_UI --> ENABLE_STREAMING[Enable Streaming Indicator]
    ENABLE_STREAMING --> LISTEN_LIVE[Listen for Live Events]

    LISTEN_LIVE --> NEW_EVENT{New Event?}
    NEW_EVENT -->|Yes| UPDATE_MESSAGE[Update Message Display]
    UPDATE_MESSAGE --> LISTEN_LIVE

    NEW_EVENT -->|Complete Event| DISABLE_STREAMING[Disable Streaming Indicator]
    DISABLE_STREAMING --> LOAD_STATIC

    LOAD_STATIC --> DISPLAY_FINAL[Display Final Messages]
    DISPLAY_FINAL --> END[End]
```

## Key Features

### 1. Seamless Reconnection

- **Automatic Detection**: Detects active streams on page load
- **Complete Context**: Provides full conversation history during reconnection
- **Live Continuation**: Seamlessly transitions from historical to live events
- **Error Recovery**: Falls back to static messages if reconnection fails

### 2. Event Deduplication

- **Message ID Tracking**: Prevents duplicate message processing
- **Position-based Filtering**: Uses event positions to avoid duplicates
- **Session State Management**: Maintains processed message sets

### 3. Background Processing

- **Async Task Management**: Uses asyncio for background processing
- **Session Isolation**: Creates separate database sessions for background tasks
- **Resource Cleanup**: Proper cleanup of tasks and sessions

### 4. Error Handling

- **Graceful Degradation**: Falls back to static content on errors
- **Client-side Recovery**: Handles network interruptions gracefully
- **Server-side Resilience**: Continues processing even if clients disconnect

## Configuration and Setup

### Backend Dependencies

```python
# Key dependencies for stream processing
from fastapi import StreamingResponse
from app.core.redis.redis_manager import RedisManager
from app.services.agent.stream_persistence import stream_from_redis_queue
```

### Frontend Dependencies

```typescript
// Key dependencies for stream management
import { useCallback, useEffect, useRef, useState } from "react";
import { OpenAPI } from "@/client";
```

### Redis Configuration

The system requires Redis with the following capabilities:

- **Lists**: For event queues
- **Hashes**: For metadata storage
- **Pub/Sub**: For real-time event publishing
- **TTL**: For automatic cleanup of old events

## Best Practices

### 1. Stream Management

- Always check stream status before creating new streams
- Properly cancel existing streams when starting new ones
- Use position-based event filtering to prevent duplicates
- Implement proper cleanup for background tasks

### 2. Error Handling

- Provide fallback mechanisms for reconnection failures
- Implement exponential backoff for retry attempts
- Show clear user feedback during reconnection
- Log detailed error information for debugging

### 3. Performance Optimization

- Limit the number of historical events stored
- Use efficient Redis data structures
- Implement client-side event buffering
- Optimize event serialization/deserialization

### 4. Security Considerations

- Validate conversation ownership before reconnection
- Implement proper authentication for all stream endpoints
- Sanitize event data before storage and transmission
- Use secure WebSocket connections where possible

## Monitoring and Debugging

### Key Metrics to Track

- **Stream Duration**: How long streams remain active
- **Reconnection Success Rate**: Percentage of successful reconnections
- **Event Processing Latency**: Time from event generation to client display
- **Redis Queue Depth**: Number of queued events per conversation
- **Background Task Performance**: Execution time and resource usage

### Debugging Tools

- **Stream Status Endpoint**: Real-time stream status information
- **Redis Monitoring**: Monitor queue sizes and event flow
- **Client-side Logging**: Detailed reconnection and event processing logs
- **Server-side Tracing**: Background task execution tracking

## Future Enhancements

### Planned Improvements

1. **Enhanced Error Recovery**: More sophisticated retry mechanisms
2. **Performance Optimization**: Event compression and batching
3. **Scalability Improvements**: Multi-instance Redis coordination
4. **Advanced Monitoring**: Real-time stream health dashboards
5. **WebSocket Upgrade**: Migration to WebSocket for bidirectional communication

### Potential Extensions

- **Stream Sharing**: Multiple clients connected to same stream
- **Event Replay**: Ability to replay historical events
- **Stream Branching**: Support for conversation forks
- **Advanced Filtering**: Client-side event filtering capabilities

---

This documentation provides a comprehensive overview of the SSE and Redis queue stream reconnection system. The architecture ensures robust, scalable, and user-friendly streaming experiences for autonomous agent conversations.
