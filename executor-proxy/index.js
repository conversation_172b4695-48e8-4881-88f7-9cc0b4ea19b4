import http from 'http';
import httpProxy from 'http-proxy';
import Redis from 'ioredis';

const port = 8080;
const fallbackHost = process.env.EXECUTOR_HOST || 'executor';
const fallbackPort = process.env.EXECUTOR_PORT || '8000';
const registryUrl = process.env.REGISTRY_URL || 'redis://redis:6379';

const proxy = httpProxy.createProxyServer({
  ws: true,
  xfwd: true,
  timeout: 30000,  // 30 second timeout
  proxyTimeout: 30000,  // 30 second proxy timeout
  followRedirects: false
});
const redis = new Redis(registryUrl);

const cache = new Map();
const DEFAULT_TTL_MS = 60_000;

function subdomainFromHost(host) {
  if (!host) return null;
  const name = host.split(':')[0];
  const parts = name.split('.');
  if (parts.length < 2) return parts[0];
  return parts[0];
}

async function getRoute(sub) {
  const now = Date.now();
  const hit = cache.get(sub);
  if (hit && (hit.expiresAt === Infinity || hit.expiresAt > now)) return hit.value;

  const raw = await redis.get(`route:${sub}`);
  if (!raw) return null;
  const value = JSON.parse(raw);
  // ttl_ms === -1 means infinite (do not expire from cache); otherwise respect provided TTL with a lower bound
  if (value.ttl_ms === -1) {
    // Do not cache to always reflect latest registry state
    return value;
  }
  const ttlMs = Math.max(5_000, (value.ttl_ms || DEFAULT_TTL_MS));
  cache.set(sub, { value, expiresAt: now + ttlMs });
  return value;
}

function buildTarget(route) {
  if (route?.target_host && route?.target_port) {
    return `http://${route.target_host}:${route.target_port}`;
  }
  return null; // do not fallback when no route
}

const server = http.createServer(async (req, res) => {
  try {
    const host = req.headers.host;
    const sub = subdomainFromHost(host);
    const route = sub ? await getRoute(sub) : null;
    const target = buildTarget(route);

    const started = Date.now();
    console.log(`[HTTP] id=${started} host=${host} sub=${sub || '-'} target=${target || 'NONE'} ${req.method} ${req.url}`);

    if (!target) {
      res.statusCode = 404;
      res.end('Route not found');
      return;
    }

    proxy.web(req, res, { target }, (err) => {
      res.statusCode = 502;
      res.end(`Proxy error: ${err?.message || 'unknown'}`);
    });
    res.on('finish', () => {
      const ms = Date.now() - started;
      console.log(`[HTTP-END] id=${started} status=${res.statusCode} ms=${ms}`);
    });
  } catch (e) {
    res.statusCode = 500;
    res.end('Internal proxy error');
  }
});

server.on('upgrade', async (req, socket, head) => {
  try {
    const host = req.headers.host;
    const sub = subdomainFromHost(host);
    const route = sub ? await getRoute(sub) : null;
    const target = buildTarget(route);
    console.log(`[WS] host=${host} sub=${sub || '-'} target=${target || 'NONE'} ${req.url}`);
    if (!target) {
      socket.destroy();
      return;
    }
    proxy.ws(req, socket, head, { target });
  } catch (e) {
    socket.destroy();
  }
});

server.listen(port, () => {
  console.log(`Executor proxy on :${port}. Fallback to ${fallbackHost}:${fallbackPort}. Registry ${registryUrl}`);
});
